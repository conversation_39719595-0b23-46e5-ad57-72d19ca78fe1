.ui-enum-tree{
    &.ui-control {
        >.container{
           // padding: 15px 0;
           //margin-left: 21px;
           >.control{
               flex-direction: column;
               align-items: flex-start;

               >.loader{
                overflow: hidden;
                position: relative;
                height: 3px;
                background: #E3E3E3;
                width: 100%;
                &::before{
                    position: absolute;
                    width: 25%;
                    height: 3px;
                    display: block;
                    content: '';
                    background: @primary-link;
                    transform-origin: 0 0;
                    transform:scaleX(0); 
                    animation: PageProgressAnimation 1s infinite;
                    animation-timing-function: ease;
                    animation-delay: 0s;
                    animation-timing-function: cubic-bezier(0.4,0.0,1,1);
                    animation-delay: .1s;
        
                }
            }
           }
           input{
               margin-left: 0;
           }
        }
    }
    .label,label{
        cursor: pointer;
        user-select: none;    
    }
    .link{
        user-select: none;    
        color: @primary-link;
        &:hover{
            text-decoration: underline;
        }
    }
    .ui-control {
        border: none;
        margin-bottom: 0;
        border-left: 1px solid #eee;
        border-radius: 0;
        margin-left: 8px;
    }
}