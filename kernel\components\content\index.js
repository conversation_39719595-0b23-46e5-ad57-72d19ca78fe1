const ContentComponent = {
    props: {
        value: String,
        readonly: <PERSON><PERSON><PERSON>,
        has_del: <PERSON><PERSON><PERSON>
    },
    watch: {
        value: {
            immediate: false,
            async handler(value, oldValue) {
                console.log("SSSSSSSS")
            }
        }
    },
    data: function(){
        return {
            markdown: this.value,
            text: this.value
        }
    },
    methods: {
        onChange(e){
            
            let value = e.target.innerText
            this.$emit("change", value ? value : undefined)
        },
        onInput(e){
            let value = e.target.innerText
            this.markdown = value
            this.$emit("input", value ? value : undefined)
        }
    },
    template: `
    <div style='display: flex;'>
        <div style='white-space: pre; flex: 1 1 50%;overflow-x: scroll; padding: 8px;' :contentEditable=!readonly v-on:input='onInput' v-on:blur='onChange'>{{text}}</div>
        <ui-markdown-view style='flex: 1 1 50%; padding: 8px;' :content="markdown" />
    </div>
`
}


Vue.component('iac-content', ContentComponent);
Vue.Fields.content = { is: 'iac-content' }