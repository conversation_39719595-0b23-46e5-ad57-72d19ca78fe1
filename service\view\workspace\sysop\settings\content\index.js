
import { Assets, Http } from '@iac/core';
import { Entity } from '@iac/data';

class EditModel extends Entity{
    constructor(context={}){
        super(context);
        if(context.code && typeof context.code != 'string'){
            context.code = JSON.stringify(context.code, null, '  ')
        }
        this.code = context.code;
    }
    props(){
        return {
            code: {
                label: "!",
                type: "code-editor"
            }
        }
    }
    async save(){
        let data;
        try{
            data = JSON.parse(this.code);

        }catch(e){
            return {
                error: {
                    //code: "444",
                    message: e
                }
            }
        }

        return await Http.api.rpc("set_page", {
            id: "content",
            data: data
        });
    }
}

export default {

    data: function(){
        return {
            loading:true,
            model: new EditModel()
         }
    },
    async mounted() {
        this.$wait(async ()=>{
            await Assets.script('iac.doc.js',true);
            await Assets.css('https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/vs.min.css');
            this.loading=false

            let {error,data} = await Http.api.rpc("get_page", {id: "content"});
            if(error){
                Vue.Dialog.MessageBox.Error(error)
            }else if(data){
                this.model = new EditModel({
                    code: data.data
                })
            }


        })

    },
    methods: {
        save(){
            if(!this.model)
                return;
            this.$wait(async ()=>{
                let {error,data} = await this.model.save();
                if(error){
                    Vue.Dialog.MessageBox.Error(error);
                }
            })
        }
    },
    template: `
    <iac-access :access='$policy.system_cm_content_view'>
    <iac-section>
      <ol class='breadcrumb'>
        <li><router-link to='/'>{{$t('home')}}</router-link></li>
        <li>{{$t('nav.settings.content')}}</li>
      </ol>
      <div class='title'>
        <h2>{{$t('nav.settings.content')}}</h2>
      </div>
    </iac-section>
    <iac-section v-if='$policy.system_cm_content_view'>
        <div>
            <ui-layout v-if='!loading && model' :fields='model.fields'/>
            <div class='sticky' style='bottom: 0px; text-align: right'>
                <ui-btn type='primary' v-on:click.native='save'>{{$t('save')}}</ui-btn>
            </div>
        </div>

    </iac-section>
  </iac-access>
    `
}
