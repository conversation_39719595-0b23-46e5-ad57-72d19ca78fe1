import { DataSource } from '@iac/data'
import { Http } from '@iac/core'
import { UserDevelop } from '@iac/kernel'
// import dlg_issue_permit from "./dlg_issue_permit"




export default {
    data: function () {
        return {
            dataSource: new DataSource({
                query: {
                    roles: {
                        type: "enum",
                        label: "!",
                        group: "role",
                        dataSource: [
                            "seller",
                            "buyer",
                            "broker",
                            { id: "exchanger", name: "exchange_client" },
                        ]
                    },
                    ignore_limits_of_brv_for_selection: {
                        type: "bool",
                        label: "selection_above_25000_brv",
                        group: "access.additional_func",
                    }
                },
                store: {
                    method: 'company_ref',
                    ref: "companies",
                    injectQuery: params => {
                        //params.query = params.queryText
                        //delete params.queryText
                        return params
                    },
                    context: (context) => {
                        context.meta = context.meta || {}
                        Object.defineProperty(context, "status_type", {
                            configurable: true,
                            enumerable: true,
                            get: () => {
                                if (context.deleted_at) {
                                    return 'danger'
                                }
                                if (context.blocked) {
                                    return 'warning'
                                }
                            }
                        })

                        context.actions = [
                            {
                                label: 'block',
                                hidden: () => {
                                    return context.blocked
                                },
                                handler: async () => {
                                    let block = await Vue.Dialog({
                                        props: ["company_id"],
                                        data: function () {
                                            return {
                                                error: undefined,
                                                fields: [
                                                    {
                                                        name: "date_from",
                                                        type: "date",
                                                        group: '!date-',
                                                        has_del: true,
                                                        status: undefined,
                                                    },
                                                    {
                                                        name: "date_to",
                                                        type: "date",
                                                        group: '!date-',
                                                        has_del: true,
                                                        status: undefined,
                                                    },
                                                    {
                                                        name: "reason",
                                                        type: "text",
                                                        group: "!text-",
                                                        status: undefined,
                                                    },
                                                ]
                                            }
                                        },
                                        methods: {
                                            block() {
                                                let fields = this.fields.reduce((prop, curr) => {
                                                    prop[curr.name] = curr.value; // && new Date(curr.value + "T00:00:00")
                                                    return prop
                                                }, {
                                                    company_id: this.company_id
                                                })

                                                this.$wait(async () => {
                                                    let { error, data } = await Http.api.rpc("company_block", fields)
                                                    if (error) {
                                                        this.error = error.message;
                                                        if (!error.data) {
                                                            this.fields.forEach(field => {
                                                                field.status = undefined;
                                                            });
                                                            return;
                                                        }
                                                        let fields_error = error.data.reduce((prop, curr) => {
                                                            let name = curr.name || curr.field_id;
                                                            if (name) {
                                                                prop[name] = curr.message
                                                            }
                                                            return prop
                                                        }, {})
                                                        this.fields.forEach(field => {
                                                            field.status = undefined;
                                                            if (fields_error[field.name]) {
                                                                field.status = {
                                                                    type: 'error',
                                                                    message: fields_error[field.name],
                                                                }
                                                            }
                                                        });
                                                    } else {
                                                        if (data.blocked != undefined) {
                                                            this.Close(data.blocked)
                                                        }
                                                        else if (fields.date_from != undefined) {
                                                            this.Close(false)
                                                        } else
                                                            this.Close(true)
                                                    }
                                                })
                                            }
                                        },
                                        template: `
                                            <div>
                                                <header>{{$t("comp_blocking")}}</header>
                                                <main v-if='error' class='error'>{{error}}</main>
                                                <main>
                                                    <ui-layout :fields='fields' />
                                                </main>
                                                <footer>
                                                    <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('cancel')}}</ui-btn>
                                                    <ui-btn type='primary' v-on:click.native='block'>{{$t('block')}}</ui-btn>
                                                </footer>
                                            </div>
                                        `
                                    }).Modal({
                                        company_id: context.id,
                                        //size: 'lg'
                                    })

                                    if (block) {
                                        context.blocked = true;
                                    }
                                }
                            },
                            {
                                label: 'unblock',
                                hidden: () => {
                                    return !context.blocked
                                },
                                handler: async () => {
                                    if (await Vue.Dialog.MessageBox.Question("Внимание! Вы действительно хотите разблокировать данную компанию") == Vue.Dialog.MessageBox.Result.Yes) {
                                        let { error, data } = await Http.api.rpc("company_unblock", {
                                            company_id: context.id
                                        })
                                        if (error) {
                                            Vue.Dialog.MessageBox.Error(error)
                                        } else {
                                            context.blocked = false;
                                        }
                                    }
                                }
                            },
                            {
                                label: "configuration",
                                handler: async () => {

                                    await UserDevelop.edit(context, "company")
                                }
                            },
                            //    {
                            //         label: 'issue_permit',
                            //         handler: async () => {
                            //           let params =  await dlg_issue_permit.Modal({
                            //                item: context,
                            //             });
                            //             if (params) {
                            //                 context.meta = {
                            //                     ...context.meta, ...params
                            //                 }
                            //             }                                                                    
                            //         },

                            //     },                  
                        ]

                        return context
                    }
                },
                template: 'template-company'
            })
        }
    },
    template: `
    <iac-access :access='$policy.system_company_list || $policy.company_foreign_list' class='page-report'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.company_list')}}</li>
            </ol>
            <div class='title'>
                <h1 style='margin: 0;'>{{$t('nav.company_list')}}</h1>
            </div>
        </iac-section>

        <iac-section>
            <ui-data-view :dataSource='dataSource'/>
        </iac-section>
    </iac-access>
    `
}
