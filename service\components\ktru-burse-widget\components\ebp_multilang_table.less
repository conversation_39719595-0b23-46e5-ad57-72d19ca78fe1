.iac--ebp-multilang-table {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  margin-top: 40px;
  margin-bottom: 8px;
  border: 1px solid transparent;
  border-color: #006f85;
  border-radius: 4px;

  >button {
    position: absolute;
    font-size: 24px;
    color: #006f85;
    right: -20px;
    top: -15px;
    cursor: pointer;
    border: none;
    background: transparent;

    &:hover {
      color: red
    }
  }

  &>.label,
  &>label {
    flex: 0 0 auto;
    position: absolute;
    left: 15px;
    top: -15px;
    margin-right: 3px;
    padding: 0 2px;
    background-color: white;
  }

  >.data {
    flex: 1 1 auto;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: stretch;
    padding-top: 30px;

    >div {
      flex: 1 1 auto;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: stretch;

      >div {
        flex: 0 0 auto;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        height: 40px;
        padding: 2px;

        >* {
          margin: 0;
        }

        &:first-child {
          margin-top: 6px;
          border-bottom: 1px solid transparent;
          border-color: #006f85;
          font-size: 12px;
          height: 30px;
        }

        &:not(:first-child) {
          position: relative;

          >b {
            position: absolute;
            font-size: 24px;
            color: red;
            left: -15px;
            top: 3px;

          }

          >button {
            position: absolute;
            font-size: 24px;
            color: #006f85;
            right: -20px;
            top: 3px;
            cursor: pointer;
            border: none;
            background: transparent;

            &:hover {
              color: red
            }
          }

          >input {
            &[type='checkbox'] {}

            &[type='number'] {
              border: 1px solid #006f85;
              border-radius: 4px;
              overflow: hidden;
              height: 35px;
              max-width: 260px;
              flex: 0 0 auto;
              width: 100%;
              outline: none;

              appearance: none;
              -moz-appearance: textfield;

              &::-webkit-outer-spin-button,
              &::-webkit-inner-spin-button {
                -webkit-appearance: none;
                margin: 0;
              }

              &:disabled {
                background-color: #f6f6f6;
                border-color: #e6e6e6;
                cursor: unset;
                color: #201D1D;
              }
            }
          }
        }
      }
    }
  }

  >.actions {
    flex: 0 0 auto;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: stretch;
    padding: 2px;
  }
}