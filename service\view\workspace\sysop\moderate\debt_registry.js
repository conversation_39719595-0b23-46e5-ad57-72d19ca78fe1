import { RefStore, DataSource, Query } from '@iac/data';
import { Http, Language } from '@iac/core';

export default {
    data() {
        return {
            debtRegistryColumns: [
                {label: "company",field: "company_id",style:'text-align: left;'},
                {label: "procedure",field: "procedure",style:'width: 100%;text-align: left;'},
                {label: "summa",field: "summa",style:'text-align: right;'},
            ],
            debtRegistryDetails: new DataSource({
                query: new Query({
                  queryText: {
                    label: "ID",
                    group: '!filter-',
                    //description: "$query_billing_desc",
                  },
                  company_id: {
                    group: '!filter-',
                    type: "entity",
                    label: "company",
                    has_del: true,
                    dataSource: new DataSource({
                        displayExp: "title",
                        search: true,
                        store: {
                            ref: "companies",
                            method: "company_ref",
                            injectQuery: (params) => {
                                // params.op = "my_subs"
                                return params;
                            }
                        }
                    })
                  },
                  status: {
                    group: '!filter-',
                    type: "entity",
                    has_del: true,
                    dataSource: [
                      {id: "created", name: "debt_registry.created"},
                      {id: "completed", name: "debt_registry.completed"}
                      
                    ]
                  }
                }),
                store: new RefStore({
                  method: 'billing_ref',
                  ref: "get_debt_registry",
                  context: (context)=>{
        
                    context.proc_id = context.proc_id || context.entity_id || ""
                    if(typeof context.proc_id == 'string' && context.proc_id.indexOf('.') >0){
                      context.label = [`contract.${context.proc_type}`,"contract"]
                      context.link = `/workspace/contract/${context.proc_id}/core`
                    }else{
                      context.label = context.proc_type
                      context.link = `/procedure/${context.proc_id}/core`
                    }
        
                    Object.defineProperty(context, "bindClass", {
                      configurable: true,
                      enumerable: true,
                      get: () => {
        
                        if (context.status == "completed")
                          return "ui-alert ui-alert-success";
        
                        //if ((context.status & 1) != 0)
                        //  return "ui-alert ui-alert-success";
        
                        //if ((context.status & 2) != 0)
                        //  return "ui-alert ui-alert-warning";
        
                      },
                    });
                    return context
                  }
                })
              })
        }
    },
    template: `
    <iac-access :access='$policy.debt_registry_moderate'>
      <iac-section type='header'>
        <ol class='breadcrumb'>
          <li><router-link to='/'>{{ $t('home') }}</router-link></li>
          <li>{{ $t('nav.moderate') }}</li>
          <li>{{ $t('debt_registry') }}</li>
        </ol>
        <div class='title'>
          <h1>{{ $t('debt_registry') }}</h1>
        </div>
      </iac-section>
      <iac-section>
        <ui-layout-group v-if='$policy.debt_registry_moderate'>
         <ui-data-grid :readonly='true' buttons class='top_filter' :dataSource='debtRegistryDetails' :columns='debtRegistryColumns'>
            <template slot='procedure' slot-scope='props'>
              <router-link :to='props.item.link'> {{ $t(props.item.label) }} № {{props.item.proc_id}}</router-link>
            </template>
            <template slot='summa' slot-scope='props'><div v-if='props.item.amount'  style='white-space: nowrap; text-align: right;'>
              <iac-number :value='props.item.amount' delimiter=' ' part='2'/>
              <span v-if='props.item.currency'>&nbsp;{{props.item.currency}}</span>
            </div></template>
          </ui-data-grid> 
        </ui-layout-group>
      </iac-section>
    </iac-access>
    `
}