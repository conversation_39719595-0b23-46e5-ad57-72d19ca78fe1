let addNull = function (num) {
    if (num < 10) {
        return `0${num}`;
    }
    return num;
}

export default {
    props: ['current', 'count', 'calendarIDs', "dataSource"],
    data: function () {
        return {
            weekdays: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
            data1: undefined,
            events: undefined,
            start: undefined,
            end: undefined,
        }
    },

    mounted() {
        this.update_data();
    },
    watch: {
        count: {
            immediate: false,
            handler: function handler(value) {
                this.update_data();
            }
        },
        current: {
            immediate: false,
            handler: function handler(value) {
                this.update_data();
            }
        },
        calendarIDs: {
            immediate: false,
            handler: function handler(value) {
                
            }
        }
    },
    computed: {
        data_empty(){
            let data = {}
            for (let d = new Date(this.start); d < this.end; d.setDate(d.getDate() + 1)) {
                let key = `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()}`
                data[key] = {
                    date: new Date(d),
                    core: [],
                    top: []
                }
            }
            return data
        },
        data() {
            if(this.dataSource.state == undefined || ((this.dataSource.state & 1) != 0))
                return;
            let data = {}
            let style = {
                "burse": "green",
                "events": "blue"
            }

            this.events = this.dataSource.items.filter((_event) => {
                if (!((this.calendarIDs || []).includes(_event.calendar_id))) {
                    return false
                }
                return true;
            }).map((event) => {
                return { ...event, context: event };
            }).sort((a, b) => {
                return a.start < b.start ? -1 : 1
            })

            // Отбираем события из массива  
            let _events = this.events.reduce((_events, _event) => {
                _event.start = new Date(_event.start);
                _event.end = new Date(_event.end);

                if (_event.start < this.start) {
                    _event.begin = _event.start;
                    _event.start = new Date(this.start);
                }
                if (_event.end > this.end) {
                    _event.finish = _event.end;
                    _event.end = new Date(this.end);
                }

                _event.time_range = `${addNull(_event.start.getHours())}:${addNull(_event.start.getMinutes())} - ${addNull(_event.end.getHours())}:${addNull(_event.end.getMinutes())}`;
                _event.class = ["event_item", _event.type ? _event.type : style[_event.calendar_id]];

                let count = _event.end.getDate() - _event.start.getDate();
                if (((_event.finish || _event.end) - (_event.begin || _event.start)) / (60 * 60 * 1000) >= 24) {

                    let evt = {
                        ..._event,
                        span: count + 1,
                    }

                    if (evt.begin) {
                        evt.class.push("begin")
                    }
                    if (evt.finish) {
                        evt.class.push("finish")
                    }

                    evt.class = evt.class.join(" ")

                    let key = `${evt.start.getFullYear()}-${evt.start.getMonth() + 1}-${evt.start.getDate()}`
                    _events.top = _events.top || {};
                    _events.top[key] = _events.top[key] || [];
                    _events.top[key].push(evt);
                } else {
                    for (let i = 0; i <= count; i++) {
                        let evt = {
                            ..._event,
                            start: new Date(_event.start.getFullYear(), _event.start.getMonth(), _event.start.getDate() + i),
                            end: new Date(_event.start.getFullYear(), _event.start.getMonth(), _event.start.getDate() + i, 23, 59, 59),
                        }
                        evt.class = evt.class.join(" ")

                        if (i == 0) {
                            evt.start = new Date(_event.start);
                        }
                        if (i == count) {
                            evt.end = new Date(_event.end);
                        }

                        evt.top = evt.start.getHours() * 60 + evt.start.getMinutes();
                        evt.bottom = evt.end.getHours() * 60 + evt.end.getMinutes();
                        evt.left = 0;
                        evt.width = 1;

                        let key = `${evt.start.getFullYear()}-${evt.start.getMonth() + 1}-${evt.start.getDate()}`
                        _events.core = _events.core || {};
                        _events.core[key] = _events.core[key] || [];
                        _events.core[key].push(evt);
                    }
                }
                return _events;
            }, {})

            // Формируем элементы
            for (let d = new Date(this.start); d < this.end; d.setDate(d.getDate() + 1)) {
                let key = `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()}`
                data[key] = {
                    date: new Date(d),
                    core: _events.core && _events.core[key],
                    top: _events.top && _events.top[key]
                }
            }

            let reCalc_positions = (items) => {
                items = items.sort((a, b) => {
                    return a.start > b.start ? 1 : -1
                });
                items.forEach(current => {
                    let inr = items.filter((item) => {
                        if (item.level == undefined)
                            return;
                        if (item.top > current.top && item.top < current.bottom)
                            return true
                        if (item.bottom > current.top && item.bottom < current.bottom)
                            return true

                        if (current.top > item.top && current.top < item.bottom)
                            return true

                        if (current.bottom > item.top && current.bottom < item.bottom)
                            return true
                    }).sort((a, b) => {
                        return a.level > b.level ? 1 : -1
                    })
                    current.level = undefined;
                    var i = 0;
                    for (i = 0; i < inr.length; i++) {
                        if (inr[i] && i != inr[i].level) {
                            if (current.level == undefined)
                                current.level = i;
                            break;
                        }
                        if (inr[i]) {
                            inr[i].level_count = inr[i].level_count || 0;
                            inr[i].level_count++;
                        }
                    }
                    if (current.level == undefined)
                        current.level = i;
                    current.level_count = current.level;
                });
                return items.sort((a, b) => {
                    return a.level > b.level ? 1 : -1
                });
            }

            if (_events.core)
                Object.keys(_events.core).forEach((key) => {
                    reCalc_positions(_events.core[key])
                })

            return data;
        }
    },
    methods: {
        update_data() {
            this.$wait(async () => {
                // Получаем даты
                let start_offset = 0;
                if (this.count == 7) {
                    start_offset = this.current.getDay();
                    if (start_offset == 0)
                        start_offset = 7;
                    start_offset--;
                }

                this.start = new Date(this.current.getFullYear(), this.current.getMonth(), this.current.getDate() - start_offset);
                this.end = new Date(this.current.getFullYear(), this.current.getMonth(), this.current.getDate() - start_offset + this.count - 1, 23, 59, 59);

                this.dataSource.query.set({
                    start: this.start,
                    end: this.end
                }, false)

                this.dataSource._items = [];
                await this.dataSource.load();
            })
        },
        async onClickItem(item) {

            let event = await Vue.Dialog.EditEvent.Modal({
                size: "lg",
                event: item.context
            })
            if(!event)
                return;

            if (event.id) {
                item.context.title = event.title;
                item.context.start = event.start;
                item.context.end = event.end;
                item.context.calendar_id = event.calendar_id;
                item.context.products = event.products;
            }else{
                let index = -1;
                for (let i = 0; i < this.dataSource.items.length; i++) {
                    if (item.id == this.dataSource.items[i].id) {
                        index = i
                        break;
                    }
                }
                if (index >= 0) {
                    this.dataSource.items.splice(index, 1)
                }
            }
        }
    },
    template: `
<div :key='current+"_"+count' :class='"event_days count_"+count'>
    <div class='sticky header'>
        <slot name='header' />

        <div class='content'>
            <div class='gmt'>
                <span>GMT+3</span>
            </div>
            
            <div class='days'>
                <div class='container'>
                    <div class='day' :key='key'  v-for='day,key in data || data_empty'>
                        <div class='week'>{{$t(weekdays[day.date.getDay()])}}</div>
                        <div class='number'>{{day.date.getDate()}}</div>
                    </div>               
                </div>

                <div class='events'>
                    <template :key='key'  v-for='day,key,index in data'>
                        <div class='column' :style='"grid-column: "+(key+1)+" / span 1;"'/>
                        <div v-if='day.top' 
                            :class='item.class' 
                            v-on:click='onClickItem(item,index)'
                            v-for='item in day.top' 
                            :style='"grid-column: "+(index+1)+" / span "+item.span+";"'>
                                <div class='title'>{{item.title}}</div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>

    
    <div class='main'>
        <div class='hours'>
            <div v-for='h,key in 24'>
                <span v-if='h!=1'>{{key}}</span>
            </div>
        </div>
        <div class='content'>
            <div class='rows'><div v-for='h in 24' /></div>
            <div class='days'>
                <div class='day' :key='key'  v-for='day,key,index in data || data_empty'>
                <div v-if='day && day.core'
                    :class='item.class'  
                    v-on:click='onClickItem(item)'
                    :style='"top: "+(item.top)+"px;height: "+(item.bottom-item.top)+"px;left: calc((100% - 0px) * "+(item.level*0.2)+" + 2px);width: calc((100% - 0px) * "+(1 - item.level_count*0.2)+" - 4px);"'
                    v-for='item in day.core'>
                        <div class='title'>{{item.title}}</div>
                        <div class='time'>{{item.time_range}}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div> 
    `
}