// dependencies component "jodit-vue" is loaded asyncronously
export var Html = {
    name: "ui-html",
    props: ["icon", "label", "status", "value", "readonly", "disabled", "name", "type", "wait", "actions", "required"],
    data() {
        return {
            config: {
                uploader: {"insertImageAsBase64URI": true},
                disabled: this.disabled,
                readonly: this.readonly,
            },
            ready: false
        }
    },
    async created(){
      // здесь важна последовательность
      await iac.Core.Assets.css("jodit.min.css");
      await iac.Core.Assets.script("jodit.min.js");
      await iac.Core.Assets.script("jodit-vue.umd.min.js");
      this.ready = true;
    },
    computed: {
        buttons(){
            return this.readonly ? ['print'] : undefined            
        },
        classes() {
            return [

            ]
        },
        opened() {
            if (this.value || this.value===0){
                return true
            }
            return false;
        },
        inputListeners: function () {
            var vm = this
            return Object.assign({},
                this.$listeners,
                {
                    input: function (value) {
                        vm.value = value
                        vm.$emit('input', value)
                    },
                    change: function (value) {
                        vm.value = value
                        vm.$emit('change', value)
                    }
                }
            )
        }
    },
    methods:{
        action(event){
            if(event == 'clear'){
                this.value = undefined;
                this.$emit('input', undefined)
                this.$emit('change', undefined)
            }
        }
    },
    template: `
    <ui-control v-bind:class="classes" class="ui-html" :label="label"
    :icon='icon' :opened='true' :status='status' v-on:action='action' :wait='wait' :readonly='readonly' :disabled='disabled' :actions='actions' :required='required'>
        <jodit-vue ref='jodit' :buttons='buttons' v-if='ready' :value="value" v-on="inputListeners" :config="config"/>
        <div v-else class='iac-wait'  style="height: 100%; color: #b2e0ea"></div>
    </ui-control>`
}
