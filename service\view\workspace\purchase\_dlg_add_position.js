import Position from './position'

export default Vue.Dialog("PurchaseAddPosition",{
    props: ['position'],
    data: function () {
        return {
            model: this.position ? new Position({
                status: 'draft',
                name: this.position.name,
                year: this.position.year,
                month_begin: this.position.month_begin,
                month_end: this.position.month_end,
                reference_id: this.position.id,
            }) : new Position()
        }
    },
    methods: {
        async save() {
            await this.wait(async e => {
                let { error, data } = await this.model.save()
                if (!error) {
                    this.Close(data && data.id);
                    if (data && data.message)
                        await Vue.Dialog.MessageBox.Success(data.message);
                } else {
                    await Vue.Dialog.MessageBox.Error(error);
                }
            })
        }
    },
    template: `
        <div>
            <header>{{$t(!model.id ? 'schedule.add' : 'schedule.edit')}}</header>
            <main>
                <ui-layout :fields='model.fields'/>
            </main>
            <footer>
                <ui-btn type='secondary' @click.native='Close()'>{{$t('cancel')}}</ui-btn>
                <ui-btn type='primary' v-on:click.native='save'>{{$t(!model.id ? 'create' : 'save')}}</ui-btn>
            </footer>
        </div>               
    `
})