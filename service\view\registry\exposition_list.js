import { DataSource } from '@iac/data'

export default {
    props: ["scope"],
    data() {
        return {
            scope: this.scope,
            source: new DataSource({
                request_count: true,
                query: {
                    status: {
                        group: "status",
                        label: "!",
                        type: "enum",
                        dataSource: {
                            store: {
                                ref: "ref_status_exchanges_exposition",
                                params: {
                                    scope: this.scope
                                }
                            }
                        }
                    }
                },
                store: {
                    ref: "ref_exchanges_exposition",
                    injectQuery: (params) => {
                        params.op = "read";
                        params.filters = {...params.filters, scope:  this.scope};
                        params.data = {"raw_json": true};
                        return params;
                    }
                },
                template: "template-exposition_list"
            }),
            source_participants: new DataSource({
                request_count: true,
                query: {
                    status: {
                        group: "status",
                        label: "!",
                        type: "enum",
                        dataSource: {
                            store: {
                                ref: "ref_status_exchanges_exposition"
                            }
                        }

                    }
                },
                store: {
                    ref: "ref_exchanges_exposition_participate",
                    injectQuery: (params)=>{
                        params.filters = {...params.filters,scope: this.scope}
                        return params;
                    }
                }
            }),
            source_archive: new DataSource({
                request_count: true,
                query: {
                    status: {
                        group: "status",
                        label: "!",
                        type: "enum",
                        dataSource: {
                            store: {
                                ref: "ref_status_exchanges_exposition_archive"
                            }
                        }

                    }
                },
                store: {
                    ref: "ref_exchanges_exposition_archive",
                    injectQuery: (params)=>{
                        params.filters = {...params.filters,scope: this.scope}
                        return params;
                    }
                }
            })
        }
    },
    computed: {
        access_policy(){
            if(this.scope =='private')
                return this.$policy.exchange_exchange_exposition_my_crud || this.$policy.exchange_exchange_exposition_observe || this.$policy.exchange_scheduled_exchange_contract_bet
            return true
        }
    },
    template: `
    <iac-access :access='access_policy'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.exposition_list')}}</li>
            </ol>
            <div class='title'>
                <h1>{{$t('nav.exposition_list')}}</h1>
            </div>
        </iac-section>

        <iac-section>
            <ui-layout-tab>
                <ui-layout-group key='exposition_list' label='Organize'>
                    <ui-layout-group>
                        <ui-data-view :dataSource='source'/>
                    </ui-layout-group>
                </ui-layout-group>
                <ui-layout-group key='Participate' v-if='scope=="private"' label='Participate'>
                    <ui-layout-group>
                        <ui-data-view  :dataSource='source_participants'/>
                    </ui-layout-group>
                </ui-layout-group>
                <ui-layout-group key='Archive' v-if='scope=="private"' label='archive_tab'>
                    <ui-layout-group>
                        <ui-data-view  :dataSource='source_archive'/>
                    </ui-layout-group>
                </ui-layout-group>
            </ui-layout-tab>
        </iac-section>
    </iac-access>
    `
}
