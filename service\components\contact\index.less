.iac-service-social {
  display: flex;
  margin: 0 -4px;
  padding: 4px 0 0!important;
  list-style: none;

  li {
    padding: 0 4px;
    margin-bottom: 12px;
  }

  a {
    display: flex;
    background-color: #fff;
    align-items: center;
    text-decoration: none;
    min-width: 28px;
    overflow: hidden;
    border-radius: 50%;
    
    svg {
      vertical-align: top;
      
      path {
        transition: fill 0.1s ease;
      }
    }
    
    &:hover svg path {
      fill: #141414;
    }

    span {
      text-indent: -9999px;
    }
  }
  &.contact-icon {
    li {
      padding: 0 8px 0 0;
      }
    a {
      border: 1px solid #ccc;
      padding: 8px;
      border-radius: 4px;
    }
    &:hover {
      a {
        background-color: #fff!important;
        &:hover {
          background-color: #eee!important;
           .insta-circle {
              fill: #eee;
            }
        }
      }
      .insta-circle {
      fill: #fff;
    }
    }
  }
  &:hover {
    a {
      background-color: #bbb;
      
      &:hover {
        background-color: #fff;
      }
    }

    .insta-circle {
      fill: #bbb;
    }
  }
}
