import { DataSource, RefStore, Query } from "@iac/data"
import { Context } from "@iac/kernel"

const org_company = ctx => {
  return {
    inn: ctx?.initiator?.company_details?.inn,
    company_title: ctx?.initiator?.company_details?.title,
    company_id: ctx?.initiator?.company_details?.id
  }
};

const contragent_company = ctx => {
  return {
    inn: ctx?.contragent?.company_details?.inn,
    company_title: ctx?.contragent?.company_details?.title,
    company_id: ctx?.contragent?.company_details?.id
  }
};

const company = ctx => {
  return {
    org_company: org_company(ctx),
    contragent_company: contragent_company(ctx)
  }
}

export default {
  data: function () {
    return {
      searchDataSourceExchange: new DataSource({
        // valueExp: "number",
        // query: new Query({
        //   status: {
        //     group: "status",
        //     label: "!",
        //     type: "enum-tree",
        //     dataSource: {
        //       valueExp: "id",
        //       store: {
        //         key: "id",
        //         ref: "exchange_contract_status", 
        //         method: "contract_ref",
        //         injectQuery: (params) => {
        //           params.filters = params.filters || {};
        //           params.filters.tree = true;
        //           return params;
        //         }
        //       }
        //     },
        //   },
        // }),
        store: new RefStore({
          method: "contract_ref",
          ref: "exchange_contract_public_registry",
          context: (context) => {
            const goodName = context.good?.product_name || '';
            const contractType = context.type_of_contract?.name || '';
            
            return {
              proc_type: 'schedule_exchange_contract',
              ...context,
              ...company(context),
              ref_status: "exchange_contract_status",
              // Дата сделки/договора
              contract_close_at: context.inserted_at || context.contract_close_at,
              // Наименование товара
              contract_name: goodName,
              // Тип контракта
              contract_type: contractType
            }           
          }, 
          injectQuery: (params) => {
            params.filters.totalcost_error = undefined;
            params.filters.close_at_error = undefined;
            return params;
          },
        }),
        template: "template-exchange_contract",
      })
    }
  },
  template: `
    <div>
      <iac-section type='header'>
          <ol class='breadcrumb'>
              <li><router-link to='/'>{{$t('home')}}</router-link></li>
              <li>{{$t('nav.exchange_deals')}}</li>
          </ol>
        <h1>{{$t('nav.exchange_deals')}}</h1>
      </iac-section>
      <iac-section>
        <ui-data-view :dataSource='searchDataSourceExchange'/>
      </iac-section>
    </div>
  `
}