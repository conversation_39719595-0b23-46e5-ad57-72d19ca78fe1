import { DataSource, RemoteStore, Query, RefStore } from '@iac/data';
import { Http, Language } from '@iac/core'
import { Context, Develop, Settings } from '@iac/kernel'

var multilot_query = new Query({
  multilot: {
    type: "entity",
    label: "!multipos_procedures",
    group: "multipos_procedures",
    has_del: true,
    dataSource: [{ id: true, name: "yes" }, { id: false, name: "no" }]
  },
  is_new_multilot: {
    type: "entity",
    label: "!multilot_procedures",
    group: "multilot_procedures",
    has_del: true,
    hidden: ()=>!Settings.procedures?._multilot,
    dataSource: [{ id: true, name: "yes" }, { id: false, name: "no" }]
  }
})

var ktru_product = new Query({
  product_id: {
    type: 'entity',
    group: 'choose_product',
    label: '!choose_product',
    has_del: true,
    dataSource: new DataSource({
      valueExp: 'product_id',
      displayExp: "product_name",
      search: true,
      store: new RefStore({
        ref: "ref_enkt_products",
        key: "product_id"
      })
    }),
    multiple: true,
    //hidden: () => {
    //  return !Settings.procedures?._filter_product
    //}
  },

})

var ad_query = new Query({
  country: {
    group: 'country',
    label: '!choose_country',
    type: 'entity',
    dataSource: 'ref_country',
    multiple: true,
    has_del: true,
  },

  min_price: {
    group: 'price_unit/<min-max>',
    type: 'float',
    label: '!from',
    has_del: true,
    min: 0,
    bind: {
      status: 'price_error && {"type":"error"}'
    },
  },
  max_price: {
    group: 'price_unit/<min-max>',
    type: 'float',
    label: '!to',
    has_del: true,
    min: 0,
    bind: {
      status: 'price_error && {"type":"error"}'
    },

  },
  price_error: {
    sync: false,
    group: 'price_unit',
    type: "model",
    label: "!",
    bind: {
      value: "min_price > max_price",
      status: (model) => {
        return model.price_error && { "type": "error", "message": Language.t("to_from_error") }
      }
    }
  },
  min_amount_gte: {
    group: 'delivery_amount_min/<min>',
    type: 'float',
    label: '!from',
    has_del: true,
    min: 0,
    bind: {
      status: 'min_amount_error && {"type":"error"}'
    }
  },
  min_amount_lte: {
    group: 'delivery_amount_min/<min>',
    type: 'float',
    label: '!to',
    has_del: true,
    min: 0,
    bind: {
      status: 'min_amount_error && {"type":"error"}'
    }
  },
  min_amount_error: {
    sync: false,
    group: 'delivery_amount_min',
    type: "model",
    label: "!",
    bind: {
      value: "min_amount_gte > min_amount_lte",
      status: (model) => {
        return model.min_amount_error && { "type": "error", "message": Language.t("to_from_error") }
      }
    }
  },

  max_amount_gte: {
    group: 'delivery_amount_max/<max>',
    type: 'float',
    label: '!from',
    has_del: true,
    min: 0,
    bind: {
      status: 'max_amount_error && {"type":"error"}'
    }
  },
  max_amount_lte: {
    group: 'delivery_amount_max/<max>',
    type: 'float',
    label: '!to',
    has_del: true,
    min: 0,
    bind: {
      status: 'max_amount_error && {"type":"error"}'
    }
  },
  max_amount_error: {
    sync: false,
    group: 'delivery_amount_max',
    type: "model",
    label: "!",
    bind: {
      value: "max_amount_gte > max_amount_lte",
      status: (model) => {
        return model.max_amount_error && { "type": "error", "message": Language.t("to_from_error") }
      }
    }
  },
})

var reduction_query = new Query({
  min_price: {
    group: 'price/<min-max>',
    type: 'float',
    label: '!from',
    has_del: true,
    min: 0,
    bind: {
      status: 'price_error && {"type":"error"}'
    }
  },
  max_price: {
    group: 'price/<min-max>',
    type: 'float',
    label: '!to',
    has_del: true,
    min: 0,
    bind: {
      status: 'price_error && {"type":"error"}'
    }
  },
  price_error: {
    sync: false,
    group: 'price',
    type: "model",
    label: "!",
    bind: {
      value: "min_price > max_price",
      status: (model) => {
        return model.price_error && { "type": "error", "message": Language.t("to_from_error") }
      }
    }
  },
  close_at_gte: {
    group: 'close_at',
    type: 'date',
    label: 'from',
    has_del: true,
    bind: {
      status: 'close_at_error && {"type":"error"}'
    }
  },
  close_at_lte: {
    group: 'close_at',
    type: 'date',
    label: 'to',
    has_del: true,
    bind: {
      status: 'close_at_error && {"type":"error"}'
    }
  },
  close_at_error: {
    sync: false,
    group: 'close_at',
    type: "model",
    label: "!",
    bind: {
      value: "close_at_gte > close_at_lte",
      status: (model) => {
        return model.close_at_error && { "type": "error", "message": Language.t("to_from_error") }
      }
    }
  },
  part_from: {
    group: 'participants/<min-max>',
    type: 'float',
    label: '!from',
    has_del: true,
    min: 0,
    bind: {
      status: 'part_error && {"type":"error"}'
    }
  },
  part_to: {
    group: 'participants/<min-max>',
    type: 'float',
    label: '!to',
    has_del: true,
    min: 0,
    bind: {
      status: 'part_error && {"type":"error"}'
    }
  },
  part_error: {
    sync: false,
    group: 'participants',
    type: "model",
    label: "!",
    bind: {
      value: "part_from > part_to",
      status: (model) => {
        return model.part_error && { "type": "error", "message": Language.t("to_from_error") }
      }
    }
  },
  prop_from: {
    group: 'proposal/<min-max>',
    type: 'float',
    label: '!from',
    has_del: true,
    min: 0,
    bind: {
      status: 'prop_error && {"type":"error"}'
    }
  },
  prop_to: {
    group: 'proposal/<min-max>',
    type: 'float',
    label: '!to',
    has_del: true,
    min: 0,
    bind: {
      status: 'prop_error && {"type":"error"}'
    }
  },
  prop_error: {
    sync: false,
    group: 'proposal',
    type: "model",
    label: "!",
    bind: {
      value: "prop_from > prop_to",
      status: (model) => {
        return model.prop_error && { "type": "error", "message": Language.t("to_from_error") }
      }
    }
  }
})

var company_query = new Query({
  company_id: {
    type: "entity",
    label: "!company",
    group: "company",
    has_del: true,
    dataSource: {
      search: true,
      displayExp: "title",
      store: {
        method: "company_ref",
        ref: "companies",
      }
    },
  }
});

export default {
  props: ['type', 'query'],
  data() {
    return {
      source: {
        tender: new DataSource({
          query: new Query({
            status: {
              group: 'status',
              label: '!',
              type: 'enum',
              dataSource: 'public_status_tender',
            },
          }, [multilot_query, Settings.procedures?._filter_product && ktru_product, this.query.area, company_query, this.query.search, this.query.green]),
          store: new RefStore({
            ref: 'ref_tender_public',
            injectQuery: (params) => {

              params.fields = ["green", "id", "publicated_at", "status", "name", "good_count", "close_at", "totalcost", "currency", "lang", "part_count", "meta", "remain_time", "lot_count"]

              params.filters.delivery_regions = params.filters.area_path;
              params.filters.area_path = undefined;

              return params;
            }
          }),
          template: "template-tender"
        }),
        contest: new DataSource({
          query: new Query({
            status: {
              group: 'status',
              label: '!',
              type: 'enum',
              dataSource: 'public_status_contest',
            },
          }, [Settings.procedures?._filter_product && ktru_product, this.query.area, company_query, this.query.search, this.query.green]),
          store: new RefStore({
            ref: 'ref_contest_public',
            injectQuery: (params) => {

              params.fields = ["green", "id", "publicated_at", "status", "name", "good_count", "close_at", "totalcost", "currency", "lang", "part_count", "meta", "remain_time"]

              params.filters.delivery_regions = params.filters.area_path;
              params.filters.area_path = undefined;

              return params;
            }
          }),
          template: "template-contest"
        }),
        selection: new DataSource({
          query: new Query({
            status: {
              group: 'status',
              label: '!',
              type: 'enum',
              dataSource: 'ref_selection_status_public',
            },
          }, [multilot_query, Settings.procedures?._filter_product && ktru_product, this.query.area, company_query, this.query.search, this.query.green]),
          store: new RefStore({
            ref: 'ref_selection_public',
            injectQuery: (params) => {

              params.fields = ["green", "id", "publicated_at", "status", "name", "good_count", "close_at", "totalcost", "currency", "lang", "part_count", "meta", "remain_time", "lot_count"]

              params.filters.delivery_regions = params.filters.area_path;
              params.filters.area_path = undefined;

              return params;
            }
          }),
          template: 'template-selection'
        }),
        master_agreement: new DataSource({
          query: new Query({
            status: {
              group: 'status',
              label: '!',
              type: 'enum',
              dataSource: 'ref_master_agreement_status_public',
            },
          }, [Settings.procedures?._filter_product && ktru_product, this.query.area, this.query.search]),
          store: new RefStore({
            ref: 'ref_master_agreement_public',
            injectQuery: (params) => {
              params.fields = ["green", "id", "publicated_at", "status", "name", "good_count", "close_at", "totalcost", "currency", "lang", "part_count", "meta"]

              params.filters.delivery_regions = params.filters.area_path;
              params.filters.area_path = undefined;

              return params;
            }
          }),
          template: 'template-master_agreement'
        }),
        ad: new DataSource({
          limit: 9,
          query: new Query({
            is_national: {
              value: false,
              hidden: true,
              sync: false,
            },
          }, [ktru_product, ad_query, this.query.area, this.query.search, this.query.green]),
          store: new RefStore({
            ref: 'ref_online_shop_public',
            injectQuery: (params) => {

              params.fields = ["green", "product", "unit", "debug_info", "id", "publicated_at", "status", "name", "price", "close_at", "totalcost", "currency", "amount", "min_amount", "images", "owner_legal_area_id", "product_name", "remain_time", "is_self_ad", "cart_proc_keys"]
              params.filters.price_error = undefined;
              params.filters.min_amount_error = undefined;
              params.filters.max_amount_error = undefined;
              params.filters.delivery_regions = params.filters.area_path;
              params.filters.area_path = undefined;
              params.filters = params.filters || {};
              params.filters.is_gos_shop = true;

              return params;
            },
            context: (context) => {

              Object.defineProperty(context, "checkbox", {
                configurable: true,
                enumerable: true,
                get: () => {
                  if (Context.Access.develop["cart_develop"] &&
                    (Context.Access.policy["request_list_own"] || Context.Access.policy["comm_request_list_own"]) &&
                    !context.is_self_ad &&
                    !(context.cart_proc_keys || []).includes("ad"))
                    return true;
                },
              });

              context.actions = [
                {
                  label: "add_to_the_cart",
                  hidden: () => {
                    if (Context.Access.develop["cart_develop"] &&
                      (Context.Access.policy["request_list_own"] || Context.Access.policy["comm_request_list_own"]) &&
                      !context.is_self_ad &&
                      !(context.cart_proc_keys || []).includes("ad"))
                      return false;
                    return true;
                  },
                  handler: async () => {
                    this.addToCart([context.id], "ad");
                  }
                }
              ]
              return context;
            }
          }),
          actions: [
            {
              label: "add_to_the_cart",
              hidden: () => {
                if (!this.source.ad.checkedItems || this.source.ad.checkedItems.length <= 0)
                  return true;
              },
              handler: async () => {
                this.addToCart(this.source.ad.checkedItems, "ad");
              }
            }
          ]
        }),
        nad: new DataSource({
          limit: 9,
          query: new Query({
            is_national: {
              value: true,
              hidden: true,
              sync: false,
            },
          }, [ktru_product, ad_query, this.query.area, this.query.search, this.query.green]),
          store: new RefStore({
            ref: 'ref_online_shop_public',
            injectQuery: (params) => {
              params.fields = ["green", "product", "unit", "debug_info", "id", "publicated_at", "status", "name", "price", "close_at", "totalcost", "currency", "amount", "min_amount", "images", "owner_legal_area_id", "product_name", "remain_time"]
              params.filters.price_error = undefined;
              params.filters.min_amount_error = undefined;
              params.filters.max_amount_error = undefined;
              params.filters.delivery_regions = params.filters.area_path;
              params.filters.area_path = undefined;
              params.filters = params.filters || {};
              params.filters.is_gos_shop = true;

              return params;
            },
          }),
        }),
        reduction: new DataSource({
          query: new Query({

          }, [Settings.procedures?._filter_product && ktru_product, reduction_query, this.query.area, this.query.search, this.query.green]),
          store: new RefStore({
            ref: 'ref_reduction_object_public',
            injectQuery: (params) => {
              params.fields = ["green", "id", "contract_pay_percent", "publicated_at", "status", "name", "good_count", "close_at", "totalcost", "currency", "lang", "part_count", "meta", "start_price", "last_price", "remain_time"]
              params.filters.price_error = undefined;
              params.filters.part_error = undefined;
              params.filters.prop_error = undefined;
              params.filters.close_at_error = undefined;
              
              params.filters.delivery_regions = params.filters.area_path;
              params.filters.area_path = undefined;

              return params;
            }
          }),
          template: 'template-reduction'
        }),
      },
    };
  },
  methods: {
    addToCart(ids, proc_key) {

      this.$wait(async () => {
        let { error, data } = await Http.api.rpc("ref", {
          op: "create_many",
          ref: "ref_cart",
          data: {
            id: ids,
            proc_key: proc_key
          }
        });
        if (error)
          return Vue.Dialog.MessageBox.Error(error)

        this.source[proc_key].checkedItems = [];
        ids, this.source[proc_key].items.filter((item) => {
          return ids.includes(item.id)
        }).forEach(item => {
          item.cart_proc_keys = item.cart_proc_keys || [];
          item.cart_proc_keys.push(proc_key);
        })

        let message = data?.message

        if (await Vue.Dialog.MessageBox.Question(`${message || ''} ${Language.t('go_to_cart')}?`) == Vue.Dialog.MessageBox.Result.Yes) {

          this.$router.push({ path: '/workspace/cart' })
        }
      })
    }
  },
  template: `
    <ui-data-view v-if='type == "tender"' :key='type' :dataSource='source[type]'/>
    <ui-data-view v-else-if='type == "contest"' :key='type' :dataSource='source[type]'/>
    
    <ui-data-view v-else-if='type == "selection"' :key='type' :dataSource='source[type]'/>
    <ui-data-view v-else-if='type == "reduction"' :key='type' :dataSource='source[type]'/>
    <ui-data-view v-else-if='type == "master_agreement"' :key='type' :dataSource='source[type]'/>
    
    <ui-data-view v-else :key='type' type='tile' :dataSource='source[type]'>
      <widget-shop :type='type' slot='item' slot-scope='props' :props='props' :item='props.item' />
    </ui-data-view>
    
    <ui-data-list v-if='0' v-else :key='type' search='queryText' :dataSource='source[type]'>
      <template slot='items' slot-scope='props'>
        <ui-list :dataSource='source[type]'>
          <div slot='items' slot-scope='props' class='iac-grid'>
            <widget-shop :type='type' v-for='item in props.items' :key='item.id' :item='item' />
          </div>
        </ui-list>
      </template>
    </ui-data-list>
  `,
};
