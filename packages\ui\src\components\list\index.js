import Data from "../../../../data/src";
import Query from "../../../../data/src/query";

export var List = {
    name: "ui-list",
    props: {
        dataSource: Object,
        raw: <PERSON>olean,
        check: <PERSON><PERSON><PERSON>,
        select: <PERSON><PERSON>an,
        sync: {
            type: Boolean,
            default: true
        },
        not_found: {
            type: String,
            default: "result_not_found"
        }
    },
    watch: {
        dataSource: {
            immediate: false,
            async handler(val, oldVal) {
                if (oldVal && oldVal.onQueryUpdate)
                    oldVal.onQueryUpdate.unbind(this.onQueryUpdate);

                if (val && val.query && this.sync) {
                    val.query.set(this.$router.currentRoute.query, false)
                }

                if (val.onQueryUpdate)
                    val.onQueryUpdate.bind(this.onQueryUpdate);
            }
        }
    },
    beforeCreate() {

        let { dataSource, sync } = this.$options.propsData;
        if (sync == undefined)
            sync = true;

        if (dataSource && dataSource.query && sync) {
            dataSource.query.set(this.$router.currentRoute.query, false)
        }
    },
    mounted: function () {
        if (this.dataSource.onQueryUpdate)
            this.dataSource.onQueryUpdate.bind(this.onQueryUpdate);
    },
    destroyed: function () {
        if (this.dataSource.onQueryUpdate)
            this.dataSource.onQueryUpdate.unbind(this.onQueryUpdate);
    },
    computed: {
        error() {
            return this.dataSource.error || {};
        },

        showError() {
            return (this.dataSource.state & Data.DataSource.STATE_ERROR) != 0;
        },
        showMore() {
            return this.dataSource.items && this.dataSource.items.length > 0 && (this.dataSource.state & (Data.DataSource.STATE_ERROR | Data.DataSource.STATE_REQUEST | Data.DataSource.STATE_EOF)) == 0;
        },
        showLoader() {
            return (this.dataSource.state & Data.DataSource.STATE_REQUEST) != 0;
        },
        notFound() {
            if (this.showLoader || this.showError)
                return false;
            if (!this.dataSource.items || this.dataSource.items.length <= 0) {
                return this.not_found
            }
        }
    },
    methods: {
        async next() {
            await this.dataSource.next();
        },
        showSelect(item) {
            //if(!this.select)
            //    return false;


            let key = Array.isArray(this.dataSource.valueExp) ? this.dataSource.valueExp[0] : this.dataSource.valueExp;
            if (this.select && key && item[key] && item.has_children && item.select_btn != false) {

                return true
            }
            return false;
        },
        onClick(position, item) {
            if (item.has_children) {
                if (item.dataSource) {
                    item.dataSource = undefined
                    //item.show_child = !item.show_child;
                }
                else{
                    item.dataSource = item.dataSource || new Data.DataSource({
                        limit: this.dataSource.take,
                        query: new Query({
                            parent_id: item.id
                        }),
                        valueExp: this.dataSource.valueExp,
                        displayExp: this.dataSource.displayExp,
                        iconExp: this.dataSource.iconExp,
                        descExp: this.dataSource.descExp,
                        store: this.dataSource.store
                    })
                    //Vue.set(item,"show_child",true)
                }
            } else {
                this.dataSource.position = position;
                this.onItem(item);
            }
        },
        onSelect(item) {
            this.$emit("select", item);
        },
        onItem(item) {
            this.$emit("item", item);
        },
        onQueryUpdate() {
            if (this.sync) {

                let query = { ...this.$router.currentRoute.query };

                for (let name in query) {
                    if (name.indexOf("tabid_") != 0) {
                        query[name] = undefined;
                    }
                }

                query = {
                    ...query,
                    ...this.dataSource.query.address
                }

                this.$router.replace({ query: query }).catch(error => {

                });
            }
        }
    },
    template: `<div class='ui-list'>
        
        <div class='items'>
            <slot name='items' :items='dataSource.items'>
                <template :key='item.key' v-for='(item,position) in dataSource.items'>
                    <slot name='item':item='item'>
                        <div :class='"ui-list-item "+(item.status_type ? ("status_"+item.status_type) : "")'>
                            
                            <slot v-if='raw' name='template' :item='item'  :title='$t(item.exp.display)'>
                                {{item.exp.display}}
                            </slot>
                            <label v-else-if='check' class='content'>
                                    <input type='checkbox' :id="item.id" :value="item.id" v-model="dataSource.checkedItems" />
                                    
                                    <slot name='template' :item='item'  :title='$t(item.exp.display)'>
                                        {{item.exp.display}}
                                    </slot>
                            </label>
                            <div v-else class='content' v-on:click='onClick(position, item)'>
                                <slot name='template' :item='item'>
                                    <div v-if='0 && item.icon' style='margin-right: 19px;'><icon>{{item.icon}}</icon></div>
                                    <div class='display' :title='$t(item.exp.display)'>
                                        {{$t(item.exp.display)}}
                                        <div v-if='item.exp.desc' class='desc'>{{$t(item.exp.desc)}}</div>
                                    </div>
                                    <div v-if='1 && item.icon' style='margin-left: 18px;'><icon>{{item.icon}}</icon></div>
                                    <div v-if='showSelect(item)' class='select'>
                                        <ui-btn type='primary xs' v-on:click.native.stop.prevent='onSelect(item)'>{{$t('select')}}</ui-btn>
                                    </div>
                                </slot>
                            </div>
                            <div v-if='item.dataSource' style='padding-left: 20px;'>
                                <ui-list :key='"child."+item.key' select='select'  :dataSource='item.dataSource' v-on:item='onItem' v-on:select='onSelect' />
                            </div>
                        </div>
                    </slot>
                </template>
            </slot>
            <ui-error style='margin: 10px' v-if='showError' :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
            <div v-if='showMore' class='ui-list-item more'><ui-btn type='info' class='content' v-on:click.native='next'><icon>spinner</icon> {{$t('more')}}</ui-btn></div>   
        </div>
        <div v-if='showLoader' class='loader'></div>
        <div class='not-found' v-if='notFound'>
            <slot name='not-found'>
                {{$t(notFound)}}
            </slot>
        </div>
    </div>`
}