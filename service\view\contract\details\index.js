import Model from './model';

export default {
    data() {
        return {
          model: undefined,
          error: undefined,
        };
      },
    mounted(){
        this.update(this.$route.params.id);
    },
    beforeRouteUpdate(to, from, next) {
        this.update(to.params.id);
        next();
    },
    methods:{
        update(id) {
            this.$wait(async () => {
                let { error, data } = await Model.get(id);
                this.model = data;
                this.error = error;
            });
        }
    },
    template: `
        <div v-if='model'>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{ $t('home') }}</router-link></li>
                    <li><router-link to='/contract'>{{ $t('nav.contracts') }}</router-link></li>
                    <li>{{ $t('contract.page_title') }}</li>
                </ol>
                <div class='title'>
                    <h1>{{ $t('contract') }} № {{ model.number }}</h1>
                </div>
            </iac-section>
            <iac-section>
                <div class='page-contract' style='background: #fff; padding: 16px; border-radius: 8px;'>
                    <ui-layout :fields='model.fields' />
                </div>
            </iac-section>  
        </div>
        <div v-else>
            <iac-section>
                <ui-error v-if='error' style='margin: 10px' :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
            </iac-section>            
        </div>
    `
}