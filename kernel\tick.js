import { Event } from '@iac/core';

export default class Tick {
  @Event static onTick() {};

  static startTick() {
    this.tick = setInterval(() => {
      this.onTick();
    }, 1000);
  }

  static stopTick() {
    clearInterval(this.tick);
  }
  
  static onEventListener(event) {
    if (event === 'bind' && this.listeners.length === 1) {
      this.startTick();
    } else if (event === 'unbind' && this.listeners.length <= 0) {
      this.stopTick();
    }
  }
}
