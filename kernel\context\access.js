import { Event, Http } from '@iac/core'
import Develop from '../user_develop';
import Develop_0 from '../develop';

export default class Access {
    @Event onUpdate;
    @Event async onUserUpdate() {
        await this.updateData();
    }

    constructor(context) {
        this.Develop = new Develop(context.User);
        this.User = context.User;
        this.User.onUpdate.bind(this.onUserUpdate);
        //this.updateData();

        this._policy = {

        };
    }

    get debug() {
        return Develop_0.access_debug;
    }

    get develop() {

        return this.Develop.fields.map((field) => {
            let value = field.value;
            if (field.value && field.value.exp && field.value.exp.value != undefined) {
                value = field.value.exp.value
            }

            if (Array.isArray(value) && value.length <= 0)
                value = undefined;

            return {
                name: field.name,
                value: value
            }
        }).reduce((prev, curr) => {
            prev[curr.name] = curr.value
            return prev;
        }, {})
    }

    get policy() {
        return this._policy;
    }

    setPolicy(policy) {
        this._policy = policy.reduce((prev, curr) => {
            if(curr){
                let perm = curr.replace(/\./g, '_')
                prev[perm] = true;
            }
            return prev;
        }, {})
    }

    async updateData() {

        if (this.Develop.company_id != this.User.team_id || this.Develop.user_id != this.User.id)
            await this.Develop.updateData(this.User)

        if (!this.User.access_token || !this.User.id) {
            this._policy = {

            }
            this.onUpdate();
            return;
        }

        let { error, data } = await Http.api.rpc("get_permissions")
        if (!error) {
            this.setPolicy(data)
        } else {
            this._policy = {

            }
        }
        this.onUpdate();
    }

}
