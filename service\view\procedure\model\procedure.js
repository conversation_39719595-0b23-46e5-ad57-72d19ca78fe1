
import { Http, Event, Action, Language } from '@iac/core'
import { DataSource, ArrayStore, Query, RemoteStore } from '@iac/data'
import { ecp } from '@iac/kernel'

import ProcedureEntity from './entity';
import Report from './report';
import TenderSocket from './socket'
import Lot from './lot'
import ProcedureCreateDlg from './_dlg_procedure_create'

import './add_field_info'
import './add_field'

import ProcedureActions from './actions'

export default class Procedure extends ProcedureEntity {
    @Event onReload;
    @Event async onProcedureMessage(event) {
        let { name, data } = event.data;
        if (!data || !name)
            return;

        if (name == 'upd' || name == 'set') {

            if (data.status) {
                this.status = this.context.status = data.status;
            }

            // Обновление филдов у главной модели
            if (data.fields) {
                this.updateFields(data.fields);
            }

            if (!this._lots_)
                return;

            if(data.lots){
                let change_value = false;
                for (let lot_id in data.lots) {
                    let lot = this._lots_.find((lot)=>{return lot.id == lot_id});
                    if(data.lots[lot_id].fields){
                        lot.updateFields(data.lots[lot_id].fields);
                    }
                    if(data.lots[lot_id].items){
                        for (let item in data.lots[lot_id].items) {
                            let fields_status_item = this.fields_status[lot_id]?.[item];
                            if (fields_status_item && data.lots[lot_id].items[item].fields){
                                for (let field in data.lots[lot_id].items[item].fields) {
                                    let _value = data.lots[lot_id].items[item].fields[field].value;
                                    if(_value!= null && _value != undefined && fields_status_item[field]){
                                        change_value = true;
                                        delete fields_status_item[field];
                                    }
                                }
                            }
                        }
                        if (lot.updateItemsFields(data.lots[lot_id].items)) {
                            change_value = true;
                        }
                    }
                }
                if(change_value){
                    this.updateErrors();
                }
            }

            // Обратная совместимость
            if(!data.items){
                return;
            }

            let lot = this.lots.items[0];
            if (!lot || !data.items)
                return;
            let change_value = false;
            for (let item in data.items) {
                let fields_status_item = this.fields_status[1]?.[item];
                if (fields_status_item && data.items[item].fields)
                    for (let field in data.items[item].fields) {
                        let _value = data.items[item].fields[field].value;
                        if(_value!= null && _value != undefined && fields_status_item[field]){
                            change_value = true;
                            delete fields_status_item[field];
                        }
                    }
            }
            if (lot.updateItemsFields(data.items) || change_value) {
                this.updateErrors();
            }

        } else if (name == 'del' && data && data.fields) {
            if (data && data.fields)
                this.deleteFields(data.fields);
            if (!this.lots)
                return;
            let lot = this.lots.items[0];
            if (!lot || !data.items)
                return;
            lot.deleteItemsFields(data.items);
        } else if (name == 'actions' && data) {
            let actions = data.reduce((prev, curr) => {
                prev[curr] = true
                return prev;
            }, {})
            if (actions['get_proc']) {
                return this.onReload();
            }
            if (actions['get_rights']) {
                await this.updateRights();
            }
            if (actions['get_items']) {
                if (this.lots && this.lots.items) {
                    let lot = this.lots.items[0];
                    if (lot && lot._items) {
                        lot._items.reload()
                    }
                }
            }
        }
        //if (this.onMessage)
        //    await this.onMessage(event.data);
    }

    constructor(context = {}) {

        let actions = {
            create_proposal: {},
            back_to_prev: {},
            continue_procedure: {},
            delete_proposal: { group: 'cancel', type: 'danger' },
            activate_proposal: { subscribe: "calc_proposal" },
            deactivate_proposal: {},
            cancel_procedure: { group: 'cancel', type: 'danger' },
            publish_procedure: {},
            republish_procedure: {},
            stop_procedure: { group: 'cancel', type: 'danger' },
            close_procedure: { group: 'cancel' },
            chose_procedure: {},
            quality_checking_procedure: {},
            upgrade_procedure_period: {},
            tech_checking_procedure: {},
            check_docs_procedure: {},
            commercial_checking_procedure: {},
            draft_agree_procedure: { subscribe: "calc_docs" },
            template_procedure: {},
            draft_procedure: {},
        }

        context.actions = {
            ...actions,
            ...context.actions
        }

        super(context);

        this.part_id = context.part_id;
        this.proc_key = context.proc_key;
        this.raw_id = context.raw_id;
        this.title_part = context.title_part;
        this._lots = undefined;
        this.status = context.status;
        this.rights = context.rights;
        this.procedure = context.procedure;
        this.fields_status = {};

        this.company_is_green = context.company_is_green;
        this.product_is_green = context.product_is_green;

        this._new_criteria = context._new_criteria;
        this.gos = context.gos;
        this.gup = context.gup;

        this._participants = new DataSource({
            query: new Query({
                proc_id: {
                    value: this.id,
                    hidden: true
                }
            }),
            store: new RemoteStore({
                host: Http.proc,
                method: "get_participants"
            })
        })

        this._lots_ = context.lots?.map((lot) => {
            return new Lot(lot, this)
        })

     //  this._lots_ = [1,2,3].map((id)=>{
     //       return new Lot({...context.lots[0],id: id,name: "Наименование лота #"+id}, this)
     //   })

        if (context.lots) {
            this._lots = new DataSource({
                store: new ArrayStore({
                    data: this._lots_
                })
            })
            this._lots.next();
        }
        this.unread_messages_count = 0;
        this.update_unread_messages_count();
    }

    update_unread_messages_count() {

        if (this.rights?.get_chats != 'granted') {
            return;
        }

        Http.api.rpc("get_chats", {
            object_id: this.id,
            object_type: this.procedure,
        }).then(({ error, data }) => {
            this.unread_messages_count = (data || []).reduce((pre, curr) => {
                return pre + curr.unread_messages_count
            }, 0)
        })
    }

    props() {

        let procedure_actions = this.procedure_actions;
        if (!procedure_actions)
            return {}

        return procedure_actions.reduce((prev, item, index) => {
            if (!item)
                return prev;
            prev[`procedure_actions_${index}`] = {
                group: `${item.group}/!custom`,
                type: 'action',
                buttons: true,
                system: true,
                order: item.order,
                label: "!",
                hidden: function () {
                    return !this.access('set_field')
                },
                actions: []
            }

            if ((item.buttons & 1) != 0) {
                prev[`procedure_actions_${index}`].actions.push({
                    label: "add_info_requirement",
                    type: "action",
                    hidden: () => {
                        if (item.hidden)
                            return item.hidden(1);
                    },
                    handler: async () => {
                        await this.info_requirement({
                            size: "lg",
                            group: `${item.group}/!custom`,
                            order: (item.order - 0.5),
                            proc_id: this.id
                        })
                    }
                })
            }

            if ((item.buttons & 2) != 0) {
                prev[`procedure_actions_${index}`].actions.push({
                    label: "add_requirement",
                    type: "action",
                    hidden: () => {
                        if (item.hidden)
                            return item.hidden(2);
                    },
                    handler: async () => {
                        await this.requirement({
                            size: "lg",
                            method_marks: this.method_marks,
                            group: `${item.group}/!custom`,
                            order: (item.order - 0.5),
                            proc_id: this.id,
                            category: item.category || item.group
                        })
                    }
                })
            }

            return prev;
        }, {})

    }

    updateErrors() {

        let settings = this.getAllGroupSettings();
        if (settings) {
            settings.forEach((setting) => {
                setting.status = undefined;
            })
        }


        let group_status = {}

        Object.keys(this.fields_status).forEach((lot_id) => {

            let lot_status = group_status[lot_id] = group_status[lot_id] || {};
            let lot_status_item = this.lots.items[lot_id-1].status = {}

            Object.keys(this.fields_status[lot_id]).forEach((item_id) => {
                for (let field_id in this.fields_status[lot_id][item_id]) {
                    let field = this.fields_status[lot_id][item_id][field_id];

                    let group = field.group;
                    if (Array.isArray(group)) {
                        group.forEach((item) => {
                            lot_status[item] = lot_status[item] || {};
                            lot_status[item][field.type] = lot_status[item][field.type] || 0;
                            lot_status[item][field.type]++

                            lot_status_item[item_id] = lot_status_item[item_id] || {};
                            lot_status_item[item_id][item] = lot_status_item[item_id][item] || {};
                            lot_status_item[item_id][item][field.type] = lot_status_item[item_id][item][field.type] || 0;
                            lot_status_item[item_id][item][field.type]++

                        })
                    } else {
                        lot_status[group] = lot_status[group] || {};
                        lot_status[group][field.type] = lot_status[group][field.type] || 0;
                        lot_status[group][field.type]++

                        lot_status_item[item_id] = lot_status_item[item_id] || {};
                        lot_status_item[item_id][group] = lot_status_item[item_id][group] || {};
                        lot_status_item[item_id][group][field.type] = lot_status_item[item_id][group][field.type] || 0;
                        lot_status_item[item_id][group][field.type]++
                    }
                }
            })
        })

        Object.keys(group_status).forEach((lot_id) => {
            for (let group in group_status[lot_id]) {
                let setting = this.getGroupSetting(`${group}/{lots}/lot_${lot_id}`)
                setting.status = group_status[lot_id][group];
            }
        });
    }

    getItemGroups(group = "") {
        group = group.split('/')[0];
        group = group.replace(/[-!\}\{\\<\\>]+/gi, '');

        if (group == 'commercial_fields') {
            return "commercial_fields";
        } 

        return "tech_fields";
    }

    getAllGroupSettings() {
        return this.fields.filter((field) => {
            return field.type == 'setting'
        })
    }

    getGroupSetting(group) {
        let setting_name = `${group.replace(/[\/\{\}]/gm, '_')}_setting`

        this.properties[setting_name] = this.properties[setting_name] || this.addField({
            id: setting_name,
            group: group,
            type: "setting",
            order: 3000,
        })

        return this.properties[setting_name];
    }

    async info_requirement(params) {
        let fields = await Vue.Dialog.AddInfoField.Modal(params);

        if (!params.id && fields && fields.length > 0) {
            fields.forEach((field) => {
                this.addField(field)
            })
        }
    }
  
    async requirement(params) {
        let field = await Vue.Dialog.AddField.Modal({...params, extra_langs: this.extra_langs, hide_mark:this.hide_mark,_new_criteria: this._new_criteria, gos: this.gos, gup: this.gup, green: this.company_is_green && this.product_is_green});
        if (!field)
            return;
        if (!params.id){
            this.addField(field)
        }
    }


    /*get raw_id() {
        if (!this._raw_id) {
            this._raw_id = [this.id]
            if (this.part_id)
                this._raw_id.push(this.part_id);
            this._raw_id = this._raw_id.join(':');
        }
        return this._raw_id;
    }*/

    get participants() {
        return this._participants;
    }

    get lots() {
        if (!this._lots || !this._lots.items || !this._lots.items[0])
            return;
        return this._lots;
    }

    get report() {
        if (!this._report) {
            this._report = new Report(this.id)
        }
        return this._report;
    }

    get actions() {
        if (!this._actions) {
            this._actions = ProcedureActions.call(this)
        }
        return this._actions;
    }

    clearError() {


    }

    setError(calcError) {

        if (!calcError || !calcError.data)
            return;

        var get_lot = (id) => {
            if (!this._lots || !this._lots.items)
                return;
            let lot = this.lots.items.filter((i) => {
                return i.id == id
            });
            return lot && lot[0];
        }
        var get_entity = (attributes) => {
            let { item_id: item, lot_id: lot } = attributes;
            if (item) {

                lot = get_lot(lot);
                if (lot && lot._items && lot._items.items) {
                    item = lot._items.items.filter((i) => {
                        return i.id == item
                    });
                    return item && item[0];
                }else if (lot) {
                    return lot
                }
            }
            return this;
        }

        calcError.data.forEach((data) => {

            let { field_id, message, item_id, lot_id, group } = data;

            let entity = get_entity(data);

            if (entity) {
                let property = entity.properties[field_id];
                if (property) {
                    property.status = {
                        type: 'error',
                        message: message
                    }
                    group = property.group
                }
                else {
                    console.log("FIELD NOT FOUND", field_id);
                }
            }

            if (group) {
                group = group.split('/')[0];
                group = group.replace(/[-!\}\{\\<\\>]+/gi, '');
            }

            if (entity != this) {
                this.fields_status[lot_id] = this.fields_status[lot_id] || {};
                this.fields_status[lot_id][item_id] = this.fields_status[lot_id][item_id] || {};

                this.fields_status[lot_id][item_id][field_id] = {
                    group: this.getItemGroups(group || "tech_fields"),
                    name: field_id,
                    type: "error",
                    message: message,
                }

            }
        })

        this.updateErrors();
    }

    async setStatus(status) {
        return this.onReload();
    }

    async updateRights(rights) {
        if (!rights) {
            let { data, error } = await Http.proc.rpc("get_rights", { proc_id: this.id, proc_key: this.proc_key });
            if (!error) {
                rights = data.rights_list;
            }
        }
        this.rights = rights;
    }

    async watch() {
        this.curr_channel = await TenderSocket.join(this.id, this.onProcedureMessage);
    }

    async unwatch() {
        if (this.curr_channel) {
            await TenderSocket.leave_channel(this.curr_channel);
        } else {
            await TenderSocket.leave(this.id);
        }
        this.curr_channel = undefined;
    }

    async digest(method) {
        let { data: calc, error: calcError } = await Http.proc.rpc(method, { proc_id: this.id, proc_key: this.proc_key });
        this.clearError();
        if (calcError) {
            this.setError(calcError);
            return { error: calcError }
        }
        return await ecp.subscribe(JSON.stringify({
            digest_id: calc.digest_id,
            data_size: calc.data_size,
            data_hash: calc.data_hash
        }));
    }

    async refresh() {
        let { error, data } = await this.refresh_context();

        if (error)
            return {
                error: error
            }
        await this.updateRights();
        return await this.refresh_items();
    }

    async refresh_context() {
        let { error, data: context } = await Procedure.get_context(this.raw_id, this.proc_key);
        if (error)
            return { error }

        this.status = context.status;
        this.part_id = context.part_id;

        this.deleteFields(this._own_properties);
        this.updateFields(context.props);
        return {

        }
    }

    async refresh_items() {
        if (!this.lots)
            return;
        let lot = this.lots.items[0];
        await lot.refresh_items();
        return {}
    }

    static Registration(name) {
        return (target) => {
            Procedure.reg_procedures = Procedure.reg_procedures || {};
            Procedure.reg_procedures[name] = target
        }
    }


    static async get_context(id, proc_key) {
        let raw_id = id;
        id = id.split(":");

        // Получаем данные конкурса
        let response = await Http.proc.rpc("get_proc", {
            proc_id: id[0],
            part_id: id[1],
            proc_key: proc_key
        });
        if (response.error) {
            return { error: response.error }
        }

        if (response.data.fields.fields) {
            response.data.fields.fields = undefined;
            delete response.data.fields.fields;
        }

        let context = {};
        context.id = response.data.proc_id;
        context.part_id = response.data.part_id || id[1];
        context.proc_key = proc_key;
        context.raw_id = raw_id;
        context.procedure = response.data.procedure;
        context.title_part = response.data.title_part;
        context.status = response.data.status;
        context.actions = response.data.actions;
        context.props = { ...response.data.fields }

        context.company_is_green = response.data.company_is_green;
        context.product_is_green = response.data.product_is_green;

        context._new_criteria = response.data.I954;
        context.gos = response.data.gos;
        context.gup = response.data.gup;

        let root_groups = {};
        context.props = Object.keys(context.props).reduce((props, name, i) => {
            let prop = { ...context.props[name] }

            let group = (prop.group || "").split("/")[0];
            root_groups[group] = true;

            prop.label = prop.label || prop.name;
            prop.name = name;

            props[name] = prop;
            return props;
        }, {})


        let target = Procedure.reg_procedures && Procedure.reg_procedures[context.procedure];
        if (target && target.init_context) {
            let { error } = await target.init_context(context)
            if (error) {
                return {
                    error
                }
            }
            context.target = target;
        }

        return {
            data: context
        }
    }

    static async get_proc(id, proc_key) {
        let id_a = id.split(":");
        let proc_id = id_a[0];
        let part_id = id_a[1];

        let t1 = Procedure.get_context(id, proc_key);
        let t2 = Http.proc.rpc("get_rights", { proc_id: proc_id, proc_key: proc_key });
        

        let r_1 = await t1;
        let r_2 = await t2;
        
        
        let error = r_1.error || r_2.error;
        if (error)
            return { error }

        let context = {}

        if (r_1.data && r_2.data) {
            context = {
                ...r_1.data,
                rights: r_2.data.rights_list
            } 
        }
        
        if (context.rights && context.rights.get_lots && context.rights.get_lots == 'granted') {
            let r_3 = await Http.proc.rpc("get_lots", { proc_id: proc_id, part_id: part_id, proc_key: proc_key });
            if(r_3?.error)
                return {error: r_3.error}
            if(r_3?.data)
                context.lots = r_3.data.lots;
        }
        return {data:context};
    }

    @Action("procedure.get")
    static async get(id, proc_key,ind = 0) {
        let t = (++Procedure.t);
        console.time("get: PROC&RIGHT_"+ind);
        let response = await Procedure.get_proc(id, proc_key);
        console.timeEnd("get: PROC&RIGHT_"+ind);
        if (response.error)
            return response;
        
        let context = response.data;

        if (context.target) {
            return {
                data: new context.target(context)
            }
        } else {
            return {
                data: new Procedure(context)
            }
        }

        /*let target = Procedure.reg_procedures && Procedure.reg_procedures[context.procedure];

        if (target && target.init_context) {
            let { error } = await target.init_context(context)
            if (error) {
                return {
                    error
                }
            } else {
                let procedure = new target(context);
                //await procedure.updateRights();
                return {
                    data: procedure
                }
            }
        } else {
            return {
                data: new Procedure(context)
            }
        }*/
    }

    @Action("procedure.create")
    static async create(context) {

        //if(context && !context.source){
        //    context.source = [{ ...context.object }]
        //}

        let result = await Http.proc.rpc("create_procedure", {
            type: context.type,
            object: context.object && { ...context.object },
            source: context.source && [...context.source]
        });

        if (!result) {
            return {
                error: {
                    code: "AbortError"
                }
            }
        } else if (result && result.error && result.error.code != "AbortError") {
            await Vue.Dialog.MessageBox.Error(result.error);
        }

        if (result && result.data) {
            if (!Array.isArray(result.data))
                result.data = [result.data]
        }


        return result;
    }
}