.billing_container{
  padding: 8px 0 6px;
}

.table-wrap {
  overflow: auto;
}

.table-collapsed {
  border-collapse: collapse;
  border-spacing: 0;

  td {
    padding: 0;
  }
}

.billing-card {
  margin-bottom: 24px;
  border: 1px solid @light-gray;
  font-size: @14px;
  background-color: @white;
  color: @gray;
  line-height: 1.429;
  border-radius: 4px;

  &__header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid @light-gray;
    padding: 20px 12px 19px;

    .ui-field {
      margin-bottom: 0;
    }

    &--with-action {
      display: flex;
      padding-top: 10px;
      padding-bottom: 9px;
      justify-content: space-between;
      align-items: center;
    }
  }

  &__title {
    margin: 0;
    font-size: @20px;
    font-weight: 500;
    color: @black;
    line-height: 1.2;
  }

  &__desc {
    padding-top: 4px;
  }

  &__body {
    padding: 16px 24px 8px;    
  }
  &__body1 {
    margin: -1px; 
  }
  
  &__balance {

    a {
      text-decoration: none;
    }

    td {
      padding-bottom: 12px;
      vertical-align: top;
    }
  }
}

.balance-updated {
  display: inline-block;
  margin-right: 6px;
}

.refresh {
  display: inline-block;
  position: relative;
  padding-left: 16px;
  text-decoration: none;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 12px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg width='12' height='16' viewBox='0 0 12 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6.00002 4.00008V6.00008L8.66669 3.33341L6.00002 0.666748V2.66675C3.05335 2.66675 0.666687 5.05341 0.666687 8.00008C0.666687 9.04675 0.973354 10.0201 1.49335 10.8401L2.46669 9.86675C2.16669 9.31341 2.00002 8.67341 2.00002 8.00008C2.00002 5.79341 3.79335 4.00008 6.00002 4.00008ZM10.5067 5.16008L9.53335 6.13341C9.82669 6.69341 10 7.32675 10 8.00008C10 10.2067 8.20669 12.0001 6.00002 12.0001V10.0001L3.33335 12.6667L6.00002 15.3334V13.3334C8.94669 13.3334 11.3334 10.9467 11.3334 8.00008C11.3334 6.95341 11.0267 5.98008 10.5067 5.16008Z' fill='%23009AB8'/%3E%3C/svg%3E");
    transform: translateY(-50%);
  }
}

.color-red {
  color: @red;
}
.color-green {
  color: @green;
}

.mr-12 {
  margin-right: 12px;
}

.link-style {
  text-decoration: underline;
  cursor: pointer;
  
  &:hover {
    color: @brand-primary;
  }
}