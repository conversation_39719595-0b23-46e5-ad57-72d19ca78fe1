.iac-section{
    >.iac-section-container{
        padding: 20px 40px;
        margin: 0 auto;
        width: 100%;
        max-width: 1500px;
    }
    &-header{
        padding-top: 80px;
        margin-top: -80px;
        //background: #1f6fb9 url(../img/hero-bg.png) center / cover;
        color: #201D1D;
        &::before{
            content: " ";
            height: 1px;
            display: block;
            margin-top: -1px;
        }
        &::after{
            content: " ";
            height: 1px;
            display: block;
            margin-bottom: -1px;
        }
        >.iac-section-container{
            padding-bottom: 0;
            h1{
                color: #201D1D;
                line-height: 52px;
                font-size: 44px;
                font-weight: 500;
                font-style: normal;
                margin: 0;
                padding: 0;
            }
            >.image{
                height: 100px;
                margin-bottom: 20px;
                background-repeat: no-repeat;
                background-size: cover;
                background-position: 50% 50%;
                transition: all 0.5s;
            }
            >.title{
                display: flex;
                align-items: center;
                justify-content: space-between;

            }
            >.description{
                margin-bottom: 15px;
                font-size: 16px;
                line-height: 24px;
                color: #969595;
            }
            >.links{
                border-bottom: 1px solid #F3F3F3;
                text-transform: uppercase;
                a{
                    font-style: normal;
                    font-weight: 500;
                    font-size: 13px;
                    line-height: 16px;

                    display: inline-block;
                    cursor: pointer;
                    padding: 0 16px;
                    line-height: 48px;

                    border-bottom: 1px solid transparent;
                    margin-bottom: -1px;
                    text-decoration: none;
                    color: #969595;
        
                    &.router-link-active{
                        border-color: @primary-link;
                        color: @primary-link;
                        font-weight: bold;
                    }
                }
            }
        }
    }
}

@media screen and (min-width:1321px) {
    .side_bar .iac-section >.iac-section-container {
        max-width:1820px;
        padding-left:320px
    }
}

@media (min-width:400px) {
    .iac-section-header .iac-section-container .image {
        height: 200px;
    }
}

@media (min-width:700px) {
    .iac-section-header .iac-section-container .image {
        height: 250px;
    }
}

@media (min-width:900px) {
    .iac-section-header .iac-section-container .image {
        height: 336px;
    }
}

.iac-page {
    &.full{
        position: fixed !important;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background: #fff;
        z-index: 500 !important;
        .sticky{
            top: 0 !important;
        }
        .iac-section-container {
            padding: 8px !important;
            max-width: unset;
            h1{
                font-size: 30px;
                line-height: 40px;
            }
        }

    }
}