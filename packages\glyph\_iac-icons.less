@font-face {
    font-family: 'iac-icon';
    src: url('/fonts/iac-icons.woff?v=1.0.27') format('woff'),
         url('/fonts/iac-icons.ttf?v=1.0.27') format('truetype'),
         url('/fonts/iac-icons.svg#iac-icons?v=1.0.27') format('svg');
    font-style: normal;
    font-weight: 400;
}

icon {
    display: inline-block;
    font: normal normal normal 14px/1 'iac-icon';
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: "liga" 1;
    text-transform: none!important;
    user-select: none;
    white-space: nowrap;
    //overflow: hidden;
}

@keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(-360deg);
    }
}

icon.spin{
    animation-name: spin;
    animation-duration: 4000ms;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}
