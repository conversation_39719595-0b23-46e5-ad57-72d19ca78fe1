import { ecp } from '@iac/kernel'
import { Language } from '@iac/core'
import '../../components/contact'

export default {
  props: ['model'],
  data() {
    return {
      store_icons: {
        googleplay: `<svg width="130" height="40" viewBox="0 0 163 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M 157.07392,48 H 6.041322 C 2.718592,48 0,45.3 0,42 V 6 C 0,2.7 2.718592,0 6.041322,0 H 157.07392 c 3.323,0 6.042,2.7 6.042,6 v 36 c 0,3.3 -2.719,6 -6.042,6 z" fill="#ffffff" id="path2" />
        <path d="m 157.07392,0.96 c 2.804,0 5.075,2.256 5.075,5.04 v 36 c 0,2.784 -2.271,5.04 -5.075,5.04 H 6.041322 C 3.238152,47.04 0.96661199,44.784 0.96661199,42 V 6 c 0,-2.784 2.27154001,-5.04 5.07471001,-5.04 z m 0,-0.96 H 6.041322 C 2.718592,0 0,2.7 0,6 v 36 c 0,3.3 2.718592,6 6.041322,6 H 157.07392 c 3.323,0 6.042,-2.7 6.042,-6 V 6 c 0,-3.3 -2.719,-6 -6.042,-6 z" fill="#000000" id="path4" />
        <path d="m 82.329922,26.1 c -2.8394,0 -5.1593,2.148 -5.1593,5.1 0,2.94 2.3199,5.1 5.1593,5.1 2.8394,0 5.1593,-2.16 5.1593,-5.1 0,-2.952 -2.3199,-5.1 -5.1593,-5.1 z m 0,8.196 c -1.5587,0 -2.8998,-1.272 -2.8998,-3.096 0,-1.836 1.3411,-3.096 2.8998,-3.096 1.5587,0 2.8998,1.26 2.8998,3.096 0,1.824 -1.3532,3.096 -2.8998,3.096 z m -11.261,-8.196 c -2.8395,0 -5.1593,2.148 -5.1593,5.1 0,2.94 2.3198,5.1 5.1593,5.1 2.8394,0 5.1593,-2.16 5.1593,-5.1 0,-2.952 -2.3199,-5.1 -5.1593,-5.1 z m 0,8.196 c -1.5587,0 -2.8999,-1.272 -2.8999,-3.096 0,-1.836 1.3412,-3.096 2.8999,-3.096 1.5586,0 2.8998,1.26 2.8998,3.096 0,1.824 -1.3412,3.096 -2.8998,3.096 z m -13.3876,-6.624 v 2.16 h 5.2197 c -0.1571,1.212 -0.5679,2.112 -1.1841,2.724 -0.7612,0.756 -1.9453,1.584 -4.0235,1.584 -3.214,0 -5.7272,-2.568 -5.7272,-5.76 0,-3.192 2.5132,-5.76 5.7272,-5.76 1.7278,0 2.9965,0.672 3.9268,1.548 l 1.5345,-1.524 c -1.3049,-1.236 -3.0327,-2.184 -5.4734,-2.184 -4.3981,0 -8.0954,3.552 -8.0954,7.932 0,4.368 3.6973,7.932 8.0954,7.932 2.3803,0 4.1685,-0.768 5.5701,-2.22 1.4378,-1.428 1.8849,-3.444 1.8849,-5.064 0,-0.504 -0.0363,-0.972 -0.1208,-1.356 h -7.3342 z m 54.746598,1.68 c -0.423,-1.14 -1.728,-3.252 -4.398,-3.252 -2.646,0 -4.845,2.064 -4.845,5.1 0,2.856 2.175,5.1 5.099,5.1 2.356,0 3.721,-1.428 4.277,-2.256 l -1.752,-1.164 c -0.58,0.852 -1.378,1.416 -2.525,1.416 -1.148,0 -1.97,-0.528 -2.49,-1.548 l 6.876,-2.82 z m -7.008,1.704 c -0.06,-1.968 1.534,-2.976 2.682,-2.976 0.894,0 1.656,0.444 1.909,1.08 z M 99.837922,36 h 2.258998 V 21 h -2.258998 z m -3.6975,-8.76 h -0.0846 c -0.5075,-0.6 -1.4741,-1.14 -2.7065,-1.14 -2.5736,0 -4.9298,2.244 -4.9298,5.124 0,2.856 2.3562,5.088 4.9298,5.088 1.2203,0 2.199,-0.54 2.7065,-1.164 h 0.0725 v 0.732 c 0,1.956 -1.0512,3 -2.7428,3 -1.3774,0 -2.2353,-0.984 -2.5857,-1.812 l -1.9694,0.816 c 0.5678,1.356 2.0661,3.012 4.5551,3.012 2.6461,0 4.8814,-1.548 4.8814,-5.316 v -9.168 h -2.1386 v 0.828 z m -2.5978,7.056 c -1.5587,0 -2.8636,-1.296 -2.8636,-3.072 0,-1.8 1.3049,-3.108 2.8636,-3.108 1.5345,0 2.7428,1.32 2.7428,3.108 0.012,1.776 -1.1962,3.072 -2.7428,3.072 z M 123.01192,21 h -5.401 v 15 h 2.26 v -5.688 h 3.153 c 2.501,0 4.954,-1.8 4.954,-4.656 0,-2.856 -2.477,-4.656 -4.966,-4.656 z m 0.049,7.224 h -3.202 v -5.148 h 3.202 c 1.691,0 2.646,1.392 2.646,2.568 0,1.176 -0.955,2.58 -2.646,2.58 z m 13.943,-2.148 c -1.631,0 -3.323,0.72 -4.024,2.292 l 2.006,0.828 c 0.423,-0.828 1.221,-1.104 2.054,-1.104 1.16,0 2.356,0.696 2.368,1.932 v 0.156 c -0.41,-0.228 -1.28,-0.576 -2.356,-0.576 -2.162,0 -4.349,1.176 -4.349,3.372 0,2.004 1.764,3.3 3.745,3.3 1.511,0 2.356,-0.672 2.876,-1.464 h 0.072 v 1.164 h 2.175 v -5.748 c 0.012,-2.664 -1.993,-4.152 -4.567,-4.152 z m -0.278,8.22 c -0.737,0 -1.764,-0.372 -1.764,-1.272 0,-1.152 1.281,-1.596 2.392,-1.596 0.991,0 1.462,0.216 2.054,0.504 -0.169,1.38 -1.377,2.364 -2.682,2.364 z m 12.784,-7.896 -2.586,6.504 h -0.073 l -2.682,-6.504 h -2.429 l 4.024,9.096 -2.296,5.052 h 2.356 l 6.199,-14.148 z m -20.299,9.6 h 2.259 V 21 h -2.259 z" fill="#000000" id="path6" />
        <path d="m 25.036022,23.3042 -12.8681,13.56 c 0,0 0,0 0,0.012 0.3988,1.476 1.752,2.556 3.359,2.556 0.6404,0 1.2445,-0.168 1.7641,-0.48 l 0.0362,-0.024 14.475,-8.292 z" fill="#ea4335" id="path8" />
        <path d="m 38.046122,21.0002 -0.012,-0.012 -6.2468,-3.6 -7.0442,6.228 7.0684,7.02 6.2226,-3.564 c 1.0874,-0.588 1.8244,-1.728 1.8244,-3.036 0,-1.32 -0.737,-2.448 -1.8124,-3.036 z" fill="#fbbc04" id="path10" />
        <path d="m 12.167722,11.1357 c -0.0725,0.288 -0.1208,0.576 -0.1208,0.888 v 23.964 c 0,0.312 0.0362,0.6 0.1208,0.888 l 13.303,-13.212 z" fill="#4285f4" id="path12" />
        <path d="m 25.132622,23.9999 6.6576,-6.612 -14.463,-8.32803 c -0.5195,-0.312 -1.1357,-0.492 -1.8003,-0.492 -1.607,0 -2.9602,1.092 -3.359,2.55603 z" fill="#34a853" id="path14" />
        <text xml:space="preserve" style="font-size:9.7896px;font-family:System-ui;-inkscape-font-specification:System-ui;fill:#000000;stroke:#000000;stroke-width:0.229804;stroke-linecap:round;stroke-linejoin:round" x="49.402874" y="15.911539" id="text375"><tspan id="tspan373" x="49.402874" y="15.911539" style="stroke-width:0.229804">${Language.t("google_play_button_text")}</tspan></text>
        </svg>`,
        appstore: `<svg width="120" height="40" viewBox="0 0 120 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="0.5" y="0.5" width="119" height="39" rx="5.5" fill="#ffffff" id="rect2" />
        <rect x="0.5" y="0.5" width="119" height="39" rx="5.5" stroke="#000000" id="rect4" />
        <path d="m 24.7045,20.7631 c 0.0121,-0.9199 0.2624,-1.8218 0.7276,-2.6219 0.4651,-0.8001 1.13,-1.4724 1.9327,-1.9542 -0.51,-0.711 -1.1827,-1.2962 -1.9648,-1.709 -0.7822,-0.4128 -1.6521,-0.6419 -2.5408,-0.669 -1.8957,-0.1943 -3.7334,1.1074 -4.6994,1.1074 -0.9847,0 -2.472,-1.0881 -4.0736,-1.056 -1.036,0.0327 -2.0456,0.3268 -2.9305,0.8537 -0.8849,0.5269 -1.61495,1.2686 -2.11896,2.1528 -2.18322,3.6904 -0.55473,9.114 1.53666,12.0971 1.0463,1.4607 2.2692,3.0924 3.8694,3.0345 1.5658,-0.0634 2.1506,-0.9748 4.0407,-0.9748 1.8726,0 2.4213,0.9748 4.0539,0.938 1.6802,-0.0266 2.7388,-1.4672 3.7485,-2.9417 0.7518,-1.0408 1.3303,-2.1912 1.7141,-3.4084 -0.9762,-0.4031 -1.8092,-1.0778 -2.3952,-1.94 -0.5861,-0.8622 -0.8992,-1.8737 -0.9003,-2.9085 z" fill="#000000" id="path6" />
        <path d="M 21.6208,11.8471 C 22.5369,10.7734 22.9883,9.39335 22.879,8 21.4793,8.14352 20.1865,8.7966 19.258,9.82911 18.804,10.3335 18.4563,10.9203 18.2348,11.556 c -0.2216,0.6357 -0.3126,1.3078 -0.2679,1.9778 0.7,0.007 1.3926,-0.1411 2.0255,-0.4333 0.6329,-0.2921 1.1897,-0.7207 1.6284,-1.2534 z" fill="#000000" id="path8" />
        <path d="m 38.2061,30.5 h -2.0303 l 4.1308,-11.9971 h 2.2325 L 46.6611,30.5 h -2.1093 l -1.0635,-3.3223 h -4.21 z m 3.2255,-9.9229 h -0.0791 l -1.6259,5.0713 H 43.04 Z m 10.8328,10.0547 c -1.2041,0 -2.1182,-0.5713 -2.6104,-1.4238 h -0.0703 v 4.377 H 47.6325 V 21.21 h 1.8985 v 1.3623 h 0.0703 c 0.5098,-0.8877 1.459,-1.4854 2.707,-1.4854 2.083,0 3.5684,1.5733 3.5684,4.3506 v 0.8262 c 0,2.7597 -1.4678,4.3681 -3.6123,4.3681 z m -0.4483,-1.6084 c 1.2393,0 2.083,-0.9931 2.083,-2.8652 v -0.6504 c 0,-1.8017 -0.791,-2.8301 -2.1181,-2.8301 -1.3623,0 -2.2149,1.1075 -2.2149,2.8213 v 0.6592 c 0,1.7578 0.8614,2.8652 2.25,2.8652 z m 10.3669,1.6084 c -1.2041,0 -2.1181,-0.5713 -2.6103,-1.4238 h -0.0703 v 4.377 H 57.5512 V 21.21 h 1.8984 v 1.3623 H 59.52 c 0.5097,-0.8877 1.4589,-1.4854 2.707,-1.4854 2.083,0 3.5684,1.5733 3.5684,4.3506 v 0.8262 c 0,2.7597 -1.4678,4.3681 -3.6124,4.3681 z m -0.4482,-1.6084 c 1.2393,0 2.083,-0.9931 2.083,-2.8652 v -0.6504 c 0,-1.8017 -0.791,-2.8301 -2.1182,-2.8301 -1.3623,0 -2.2148,1.1075 -2.2148,2.8213 v 0.6592 c 0,1.7578 0.8613,2.8652 2.25,2.8652 z m 8.1039,-1.8545 h 1.9512 c 0.0879,1.0372 1.002,1.9249 2.6983,1.9249 1.5556,0 2.4785,-0.7295 2.4785,-1.8633 0,-0.9141 -0.6153,-1.4063 -1.8985,-1.7139 l -1.9687,-0.4922 c -1.5469,-0.3603 -2.9356,-1.2832 -2.9356,-3.2344 0,-2.2939 2.004,-3.5507 4.3331,-3.5507 2.3291,0 4.2714,1.2568 4.3154,3.498 h -1.916 c -0.0879,-1.0195 -0.8702,-1.8633 -2.4258,-1.8633 -1.3711,0 -2.3027,0.6504 -2.3027,1.7666 0,0.7823 0.5449,1.3448 1.6611,1.5996 l 1.9599,0.4834 c 1.8018,0.4395 3.1729,1.292 3.1729,3.3311 0,2.3555 -1.9072,3.6826 -4.6231,3.6826 -3.3398,0 -4.456,-1.9512 -4.5,-3.5684 z M 81.3395,21.21 v -2.2588 h 1.916 V 21.21 h 1.8105 v 1.5644 h -1.8105 v 4.957 c 0,1.0108 0.3779,1.292 1.4238,1.292 0.167,0 0.3252,0 0.4395,-0.0175 V 30.5 c -0.1583,0.0264 -0.5274,0.0615 -0.9229,0.0615 -2.2588,0 -2.8828,-0.8086 -2.8828,-2.6719 V 22.7744 H 80.0299 V 21.21 Z m 8.9958,-0.1582 c 2.6718,0 4.122,1.8808 4.122,4.4121 v 0.747 c 0,2.6192 -1.4414,4.4473 -4.122,4.4473 -2.6807,0 -4.1397,-1.8281 -4.1397,-4.4473 v -0.747 c 0,-2.5225 1.4678,-4.4121 4.1397,-4.4121 z m 0,1.5644 c -1.4502,0 -2.1709,1.1865 -2.1709,2.874 v 0.7119 c 0,1.6612 0.7119,2.8829 2.1709,2.8829 1.459,0 2.1621,-1.2129 2.1621,-2.8829 v -0.7119 c 0,-1.6963 -0.7119,-2.874 -2.1621,-2.874 z M 96.1055,30.5 v -9.29 h 1.9512 v 1.2216 h 0.0703 c 0.2373,-0.58 0.9316,-1.3535 2.224,-1.3535 0.255,0 0.474,0.0176 0.659,0.0528 v 1.7226 c -0.167,-0.0439 -0.51,-0.0703 -0.835,-0.0703 -1.5646,0 -2.092,0.9668 -2.092,2.2148 V 30.5 Z m 9.6375,0.1582 c -2.487,0 -4.069,-1.6436 -4.069,-4.3945 v -0.9405 c 0,-2.5927 1.546,-4.2714 3.99,-4.2714 2.478,0 3.972,1.7402 3.972,4.3593 v 0.8877 h -6.038 v 0.2198 c 0,1.5644 0.844,2.6015 2.171,2.6015 0.993,0 1.67,-0.4922 1.908,-1.292 h 1.854 c -0.281,1.503 -1.494,2.8301 -3.788,2.8301 z m -2.145,-5.7217 h 4.131 v -0.0176 c 0,-1.3183 -0.817,-2.3554 -2.056,-2.3554 -1.257,0 -2.075,1.0371 -2.075,2.3554 z" fill="#000000" id="path12" />
        <text xml:space="preserve" style="font-size:8.2773px;font-family:System-ui;-inkscape-font-specification:Systemui;fill:#000000;stroke:#000000;stroke-width:0.194304;stroke-linecap:round;stroke-linejoin:round" x="36.230461" y="13.75399" id="text402"><tspan id="tspan400" x="36.230461" y="13.75399" style="stroke-width:0.194304">${Language.t("app_store_button_text")}</tspan></text>
        </svg>`
      }
    }
  },
  computed: {
    contacts() {
      return (this.$settings._contacts || []).map((contact) => {
        let url = undefined;
        if (contact.indexOf('@') >= 0) {
          url = `mailto:${contact}`
        } else if (contact.indexOf('+') == 0) {
          url = `tel:${contact.replace(/[ \-]/g, '')}`
        }
        return {
          url: url,
          title: contact
        }
      });
    },
    
    stores() {
      return (this.$settings._stores || []).map((store) => {
        let key = undefined
        if (store.indexOf('play.google.com') >= 0) {
          key = 'googleplay'
        } else if (store.indexOf('apps.apple.com') >= 0) {
          key = 'appstore'
        } else {
          return {}
        }
        return {
          url: `https://${store}`,
          icon: this.store_icons[key],
          title: key
        }
      })
    },
    links() {
      return [
        {
          url: '/info/policy',
          title: 'footer.policy',
        },
        {
          url: '/info/user_agreement',
          title: 'user_agreement',
        },
        {
          url: '/info/public_offer',
          title: 'public_offer',
        },
        {
          url: '/info/etika',
          title: 'ethical_rules',
        },
        (ecp.provider && ecp.provider.name == "eimzo") ? {
          url: 'https://e-imzo.uz/#how_to_get',
          title: 'get_eimzo',
        } : undefined,
      ]
    }
  },

  methods: {
    isExternalLink(url) {
      if (url.slice(0, 4) !== 'http') {
        return false;
      }
      return !url.includes(location.href);
    }
  },
  template: `
    <footer class='iac-service-footer'>
      <section class='section iac-service-footer__info'>
        <div class='iac-container'>
          <div class='iac-row'>
            <div class='iac-col-md-4'>
              <div class=''>
                
                <div style="margin-bottom: 20px; white-space: pre-line;" v-if='$content.additional'>
                    {{$content.additional}}
                </div>
              
              </div>
            </div>

            <div class='iac-col-md-4'>
              
              <div class='iac-service-footer__item'>
                <div class='footer-contact'>
                  <h3 class='footer-title'><router-link to='/contact'>{{ $t('footer_contact.title') }}</router-link></h3>
                  <ul class='list'>
                    <li v-for='contact in contacts' class='footer-contact__item'>
                      <a class='footer-contact__link' :href='contact.url'>{{ $t(contact.title) }}</a>
                    </li>
                  </ul>
                  <template>
                    <h3 class='footer-title d-none d-md-block'>{{ $t('social.title') }}</h3>
                    <iac-social-icons/>
                  </template>
                  <template v-if='stores && stores.length > 0'>
                    <h3 class='footer-title d-none d-md-block'>{{ $t('stores.title') }}</h3>
                    <ul class='iac-service-footer__stores'>
                      <li v-if='store && store.url' v-for='store in stores'>
                        <a :title='$t(store.title)' :href='store.url' target='_blank'>
                          <div class='h-100' v-html='store.icon'>
                          </div>
                          <span>{{ $t(store.title) }}</span>
                        </a>
                      </li>
                    </ul>
                  </template>
                </div>
              </div>

            </div>

            <div class='iac-col-md-4'>
              <div class='iac-service-footer__item'>

                <ul class='list mb-24'>
                  <li v-if='item' v-for='item in links'>
                    <a v-if='isExternalLink(item.url)' :href='item.url' class='link-inherit' target='_blank'>
                      {{ $t(item.title) }}
                    </a>
                    <router-link v-else :to='item.url' class='link-inherit'>
                      {{ $t(item.title) }}
                    </router-link>
                  </li>
                </ul>
                <h3 class='footer-title'>{{ $t('find_error.title') }}</h3>
                <div class='find-error mb-24'>{{ $t('find_error.desc') }}</div>

              </div>
            </div>

          </div>
        </div>
      </section>
      <section class='section copyright'>
        <div class='iac-container'>
          <div class='iac-row align-items-center'>
            <div v-if='0' class='iac-col-lg-3 iac-col-md-auto'>
              <ul class='list'>
                <li v-for='item in links'>
                  <a v-if='isExternalLink(item.url)' :href='item.url' class='link-inherit' target='_blank'>
                    {{ $t(item.title) }}
                  </a>
                  <router-link v-else :to='item.url' class='link-inherit'>
                    {{ $t(item.title) }}
                  </router-link>
                </li>
              </ul>
            </div>
            <div class='iac-col-lg-6 iac-col-md-auto'>
              <div class=''>
                <p class='m-0'> {{ $t('footer.copyright') }}</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </footer>
  `,
};
