import { DataSource } from '@iac/data'

export default {
    props: ["model"],
    data: function(){
        return {
            fields: undefined,
            field: undefined
        }
    },
    mounted: function () {
        this.updateFields();
        this.model.onField.bind(this.updateFields);
    },
    destroyed: function () {
        this.model.onField.unbind(this.updateFields);
    },
    methods: {
        updateFields(){
            this.fields = DataSource.get(this.model.fields.map((field)=>{
                return {
                    id: field.name,
                    name: field.name
                }
            }))
        }
    },
    template: `
        <div>
            <ui-layout-group label='properties'>
                <ui-entity v-if='fields' label='Выберите поле' :has_del='true' :dataSource='fields' v-model='field' style='background: #faebcc' />
                <ui-layout v-if='field && model.properties[field.name]' :fields='model.properties[field.name].fields' />               
            </ui-layout-group>
        </div>
    `
}