import Vue from "vue"
import renderTable from './table'
import renderTree from './tree'
import specCreator from './spec_creator'
import { ktruSearch, ktruGetAdditionalSpecData } from './ktru_helper'

let searchTimeoutID = undefined

const KtruWidget = {
  components: {
    'renderTree': renderTree,
    'renderTable': renderTable,
    'specCreator': specCreator
  },
  props: {
    mode: {
      type: String,
      default: "product" //"spec"||"product"
    },
    onSelect: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      searchQuery: "",
      searchResult: undefined,
      searchLoading: false,
      currentItem: undefined
    }
  },
  watch: {
    searchQuery(_newVal, _oldVal) {
      clearTimeout(searchTimeoutID)
      searchTimeoutID = setTimeout(() => this.search(), 300)
    },
    currentItem(newVal, oldVal) {
      if (newVal != oldVal && newVal && newVal.blocked) {
        this.currentItem = undefined
        Vue.Dialog({
          template: `
            <div>
            <main>{{$t("ktru_this_product_blocked")}}</main>
            <footer><ui-btn type='secondary' v-on:click.native='Close()'>{{$t("close")}}</ui-btn></footer>
            </div>
          `
        }).Modal()
      }
    }
  },
  methods: {
    clearQuery() {
      this.searchQuery = ""
      this.searchResult = undefined
      this.searchLoading = false
      this.currentItem = undefined
    },
    async search() {
      const { searchQuery: query = "" } = this
      if (query.length < 3) {
        return
      }
      this.searchLoading = true
      const { error, data } = await ktruSearch(query)
      if (!error && data) {
        this.searchResult = data
        this.currentItem = undefined
      }
      this.searchLoading = false
    },
    async setCurrentItem(item = undefined) {
      const fullItem = await ktruGetAdditionalSpecData(item)
      if (fullItem) {
        if (this.mode === 'spec') {
          this.onSelect(fullItem)
        } else {
          this.currentItem = fullItem
        }
      }
    }
  },
  mounted() {
    setTimeout(() => this.$refs?.search?.focus?.(), 300);
  },
  template: `
    <div class='iac--ktru' @keydown.esc="e=>setCurrentItem()">
      <div class="iac--ktru__search">
        <div>
          <icon v-if="searchLoading" class="iac--to-spin">spinner</icon>
          <icon v-else>search</icon>
        </div>
        <input ref="search" :placeholder="$t('search_for_product')" v-model="searchQuery" @keydown.enter="search"/>
        <button v-if="searchQuery.length || searchResult || currentItem" @click="clearQuery">
          <icon>delete</icon>
        </button>
      </div>
      <specCreator v-if="currentItem" :currentItem="currentItem" :onSelect="onSelect"/>
      <renderTable v-else-if="searchResult" :searchResult="searchResult" @setCurrentItem="setCurrentItem"/>
    </div>
  `,
};

Vue.component("ktru-widget", KtruWidget);