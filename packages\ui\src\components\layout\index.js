export * from './group'
export * from './tab'

export var Layout = {
    name: "ui-layout",
    props: {
        fields: {
            type: Array,
            default: []
        },
        readonly: {
            //type: Boolean,
            required: false
        },
        actions: {
            type: Array,
        },
        root: {
            type: String,
            default: 'ui-layout-group'
        },
        groups: {
            type: Array,
            default: []
        },
        value: {

        },
        wait: {
            type: Boolean,
            default: false
        },
        hidden: {
            default: []
        }
    },
    computed: {
        items() {

            let root = {
                component: this.root,
                status: {}
            };

            this.fields.filter((field) => {

                if(this.hidden && this.hidden.includes(field.name))
                    return false;
                
                
                if (this.groups.length > 0) {
                    let group = field.group && field.group.split("/")[0];
                    if (!group)
                        return false
                    let group_name = group.replace(/[-!\}\{\\<\\>]+/gi, '').toLocaleLowerCase();
                    if (this.groups.indexOf(group_name) < 0)
                        return false;
                }

                if (field.type == 'setting')
                    return true;

                if (field.hidden && typeof field.hidden == 'function') {
                    return !field.hidden();
                }
                return !field.hidden;
            }).sort((a, b) => {
                return a.order - b.order
            }).forEach((field, key) => {
                if (this.readonly == true) {
                    field.readonly = true;
                }

                let current_group = root;
                let groups = field.group && field.group.split("/");
                if (groups && groups.length > 0) {
                    let { type: status_type } = field.status || {};
                    for (let key of groups) {
                        let name = key.replace(/[-!\}\{\\<\\>]+/gi, '');// key.match(/(\w+)/gi;);
                        let component = "ui-layout-group";
                        if (key.indexOf('{') >= 0)
                            component = "ui-layout-tab"
                        else if (key.indexOf('<') >= 0)
                            component = "ui-control-group"

                        if (name) {
                            current_group = current_group.child = current_group.child || {};
                            current_group = current_group[name] = current_group[name] || {
                                component: component,// key.indexOf('{') >= 0 ? "ui-layout-tab" : "ui-layout-group",
                                props: {
                                    //actions: this.actions,
                                    name: name.toString(),
                                    label: name.toString(),
                                    showLabel: key.indexOf('!') < 0,
                                    horizontal: key.indexOf('-') >= 0,
                                    //expanded: true,
                                }
                            }
                            
                            if (status_type) {
                                current_group.status = current_group.status || {};
                                current_group.status[status_type] = current_group.status[status_type] || 0;
                                current_group.status[status_type]++;
                            }else if (field.type == 'setting' && field.status) {
                                Object.keys(field.status).forEach((status_type)=>{
                                    current_group.status = current_group.status || {};
                                    current_group.status[status_type] = current_group.status[status_type] || 0;
                                    current_group.status[status_type] += field.status[status_type]
                                })
                            }

                        }

                        //if (component == "ui-control-group")
                        //    break;
                    }
                }

                if (field.type == 'setting') {
                    field.attr = field.attr || {}
                    current_group.props = current_group.props || {}
                    current_group.props.expanded = field.attr.expanded;
                    current_group.props.horizontal = field.attr.horizontal;
                    current_group.props.icon = field.attr.icon;
                    current_group.props.actions = field.actions;
                    current_group.props.wait = field.wait;
                    current_group.props.label = field.label || current_group.props.label;
                    current_group.props.hidden = field.hidden;
                    current_group.attr = field.attr;
                    return;
                }

                field.component = 'ui-field';
                field.type = field.type || "string"
                field.props = {
                    model: field
                }
                field.nativeOn = {

                }
                current_group = current_group.child = current_group.child || {};
                current_group[`Field:${key}:${field.name}`] = field
            });

            Object.keys(root.child || {}).forEach((name)=>{
                let status = root.child[name].status;
                if(!status)
                    return;
                if(status.type){
                    root.status[status.type] = root.status[status.type] || 0;
                    root.status[status.type]++;
                }else{
                    Object.keys(status).forEach((status_type)=>{
                        root.status = root.status || {};
                        root.status[status_type] = root.status[status_type] || 0;
                        root.status[status_type] += status[status_type]
                    })
                }

                
            })
            this.$emit("status",root.status)
            return root;
        }
    },
    render: function (c) {
        let $this = this;
        let setting = localStorage.getItem("develop_setting") || "{}";
        setting = JSON.parse(setting);

        let field_debug = setting.field_debug;

        function renderChilds(items, has_slot = false) {

            if (!items)
                return;

            return Object.keys(items).map((key) => {
                var item = items[key];
                let title;

                if (!item.type && item.props.hidden) {
                    return;
                }

                if (!item.type && !item.child)
                    return;

                let $child = renderChilds(item.child);
                if (!item.type && (!$child || $child.length<=0)){
                    return;
                }                
                if (has_slot && $this.$slots[item.props.name] && $child) {
                    $child.push($this.$slots[item.props.name]);
                }

                if (field_debug) {
                    title = JSON.stringify({
                        name: item.name,
                        label: item.label || item.name,
                        type: item.type,
                        group: item.group,
                        multiple: item.multiple,
                        order: item.order,
                        value: item.value,
                        description: item.description,
                        status: item.status,
                        required: item.required,
                        widget: item.widget,
                        dataBind: item.dataBind ? true : undefined,
                        ...(!item.type && item.props)
                    }, null, '\t');
                } else if (item.type !== 'info' && item.type !== 'action') {
                    let label = (item.attr?.placeholder || item.label || item.name);
                    
                    if (label && typeof label == 'string') {
                        label = label.replace(/^[\~\-\!]+/, '');
                        title =  $this.$t(label);
                    }
                }

                return c(item.component, {
                    key: `item_${key}`,
                    class: item.attr && item.attr.class,
                    domProps: {
                        title,
                        ...item.attr
                    },
                    model: item.model,
                    props: {status: item.status, ...item.props },
                    on: item.on,
                    nativeOn: item.nativeOn,
                }, [$child]);
            }).filter((item)=>{
                return item;
            })
        }
        return c(this.root, {
            class: ['ui-layout', this.wait && 'iac-wait'],
            domProps: {

            },
            props: {
                actions: this.actions
            },
            nativeOn: {

            }
        }, [renderChilds(this.items.child, true)]);
    }
}