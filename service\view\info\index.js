
import Model from "./_model";
import editPageDlg from './_dlg_edit_page'
import IacCalc from '../../components/calc';
import { Http } from "@iac/core";
import { Context } from '@iac/kernel'

export default {
    data: function () {
        return {
            context: Context,
            model: undefined,
            error: undefined
        }
    },
    components: {
        IacCalc,
    },
    computed: {
        page_id() {
            return this.$route.params.id;
        },
        imageStyle() {
            return {
                backgroundImage: `url(${this.model.image})`,
            }
        }
    },
    mounted: async function () {
        this.update_model();
    },
    watch: {
        $route(to, from) {
            this.update_model();
        }
    },
    methods: {
        async update_model() {

            await this.$wait(async () => {
                this.model = undefined;
                this.error = undefined;

                let { error, data: model } = await Model.get(this.page_id);
                this.model = model;
                this.error = error;
            })
        },
        async create() {
            let model = new Model({
                id: this.page_id
            })
            let page = await this.edit(model)
            if (page) {
                this.model = page;
                this.error = undefined;
            }
        },
        async edit(model) {
            if (!model && this.model)
                model = this.model;

            let page = await editPageDlg.Modal({ model: new Model({ ...model }), size: 'lg' });
            if (page) {
                model.image = page.image;
                model.title = page.title;
                model.description = page.description;
                model.content = page.content;
                return page;
            }
        },
        anticorruption_notification() {
            this.$wait(async () => {
                let { error, data } = await Http.api.rpc('anticorruption_notification', {
                    action: 'form'
                })

                if (error) {
                    Vue.Dialog.MessageBox.Error(error)
                }
                else if (data?.message) {
                    Vue.Dialog.MessageBox.Success(data.message, data.title)
                }
                return;
            }
            )
        },
        payment_form() {
            this.$wait(async () => {
                let { error, data } = await Http.api.rpc('payment_form', {})

                if (error != undefined) {
                    if (error.code != "AbortError")
                        await Vue.Dialog.MessageBox.Error(error);
                    return;
                }
                const linkSource = `data:application/pdf;base64,${data}`;
                const url = URL || webkitURL;
                const link = document.createElement('a');
                link.href = linkSource;// fileUrl;
                let current = new Date();
                let file_name = `Платежный документ от ${current.getFullYear()}-${current.getMonth() + 1}-${current.getDate()}.pdf`
                link.download = file_name;
                link.click();
                url.revokeObjectURL(data);
            })
        }
    },
    template: `
        <div class='static-page'>
            <iac-section v-if='error'>
                <ui-error :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
                <ui-btn v-if='$policy.system_page_edit || $policy["system_page_edit_"+page_id] || $policy["system_cm_info_edit"]' type='primary' v-on:click.native='create' >{{$t('create')}}</ui-btn>
            </iac-section>
            <div v-if='model'>
                <iac-section type='header'>
                    <ol class='breadcrumb'>
                        <li><router-link to='/'>{{$t('home')}}</router-link></li>
                        <li>{{model && model.title}}</li>
                    </ol>
                    <div class='image' v-if='model && model.image' :style='imageStyle'>

                    </div>

                    <div class='title'>
                        <h1>{{model.title}}</h1>
                        <ui-btn v-if='$policy.system_page_edit || $policy["system_page_edit_"+page_id] || $policy["system_cm_info_edit"]' type='primary' v-on:click.native='edit()'>{{$t('edit')}}</ui-btn>
                    </div>
                    <div class='description'>{{model.description}}</div>
                </iac-section>

                <iac-section>
                    <div v-html='model.content'/>
                </iac-section>
                <iac-section class="iac-calc-wrapper">
                    <template v-if='page_id === "tariffs"'>
                        <iac-calc  />
                        <ui-btn type='primary' :title="$t('payment_form_title')" v-on:click.native='payment_form'>{{$t('payment_form')}}</ui-btn>
                    </template>
                    <template v-else-if='page_id === "how_replenish"'>
                        <ui-btn type='primary' v-on:click.native='payment_form'>{{$t('payment_form')}}</ui-btn>
                    </template>
                    <template v-else-if='page_id === "anti_corruption" && context.User.id'>
                        <h1>{{$t("anti_corruption_title")}}</h1>
                        <div style="margin-bottom: 20px;">{{$t("anti_corruption_message")}} <a href="mailto:<EMAIL>"><EMAIL></a></div>
                        <ui-btn type='primary' v-on:click.native='anticorruption_notification'>{{$t('corruption_message')}}</ui-btn>
                    </template>
                </iac-section>
            </div>
        </div>
    `
}
