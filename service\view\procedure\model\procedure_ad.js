import Procedure from './procedure';

@Procedure.Registration("ad")
export default class Ad extends Procedure {

    constructor(context = {}) {

        /*context.props.timer = {
            group: 'test',
            type: "widget",
            label: '-timer',
            widget: {
                name: 'iac-timer',
                props: {
                    value: 12311
                }
            }
        }*/

        super(context);
    }

    static async init_context(context) {
        return {
        }
    }
}