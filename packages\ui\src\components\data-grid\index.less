.ui-data-view,
.ui-data-grid{
    .table-wrapper{
        //overflow-x: auto;
        //overflow-y: hidden;
        border: 1px solid #f3f3f3;
        table{
            width: 100%;
            min-width: 250px;
            border-collapse: collapse;
            background: #fff;
            th{
                padding: 16px 6px;
                font-size: 11px;
                font-weight: 500;
                color: #969595;
                line-height: 1.23;
                letter-spacing: 0.05em;
                text-transform: uppercase;
                border: 1px solid #f3f3f3;
            }
            tfoot td,
            tbody td{
                padding: 8px 6px;
                color: #201d1d;
                border: 1px solid #f3f3f3;
                font-size: 14px;
                line-height: 20px;
                vertical-align: top;
                a:hover{
                    text-decoration: underline;
                }
            }
            tfoot td{
                font-size: 13px;
                font-weight: 500;
                line-height: 1.23;
                letter-spacing: 0.05em;
                text-transform: uppercase;
                background: #f6f6f6;
            }
        }
    }
    &.on-item {
        tbody tr{
            cursor: pointer;
            &:hover {
               // background: #b9c1d1;
                box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.1)  !important;
                td{
                    color: #000 !important;
                }
            }
        } 
    }
}