import Vue from "vue"
import {Config} from '@iac/kernel'
var Component = {
    props: ["images"],
    data: function(){
        return {
            current: 0,
        }
    },
    methods:{
        select(index){
            this.current = index;
        },
        backGroud(index = this.current){
            let id = this.images[index];
            return `url("${Config.api_server}/file/${id}?index=${index}")`;
        }
    },
    template: `
        <div class='widget-image'>
            <div class='items' v-if='images && images.length > 1'>
                <div v-key='index' class='item' v-bind:class='{active: index == current}' 
                    v-bind:style='{backgroundImage: backGroud(index)}'
                    v-for='(image, index) in images' v-on:click='select(index)'>

                </div>
            </div>
            <div class='image-wrapper' v-bind:style='{backgroundImage: backGroud()}'>
                
            </div>
        </div>
    `
}

Vue.component("widget-image",Component);