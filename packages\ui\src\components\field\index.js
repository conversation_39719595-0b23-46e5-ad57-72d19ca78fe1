import Language from "../../../../core/src/language"
import './wrapper'

Vue.Fields = {

}

export var Field = {
    name: "ui-field",
    props: ["model"],
    inject: ['group'],
    data: function () {
        return {
            fields: Vue.Fields,

            placeHolder: undefined,
            label: undefined,
            label_s: undefined,
            description: undefined,
            horizontally: undefined,
            horizontally_fix: undefined,
            horizontally_fix2: undefined
        }
    },
    computed: {
        field() {
            return this.fields[this.model.type]
        },
        wrapper(){
            if(!this.model.wrapper || this.model.wrapper.disabled){
                return {}
            }

            let wrap = true
            let actions = this.model.actions;

            if(this.model.type == 'bool'){
                wrap = false;
            }

            if(this.model.type == 'file' || 
                this.model.type == 'action'){
                actions = undefined;
            }



            return {
                wrap: wrap,
                is:  `ui-field-wrapper-${this.model.wrapper.name || "default"}`,
                actions: actions,
                content: this.model.wrapper
            }
        },
        widget() {
            return this.model.widget || {
                name: "ui-alert",
                props: {
                    type: "warning"
                },
                content: Language.t('this_widget_is_not_configured')
            }
        },
        controlGroup() {
            return this.group && this.group == 'control'
        },
        show_field() {
            if (!this.model)
                return false;
            if (this.model.type == "hidden")
                return false;

            if (this.model.type == "action" && this.model.buttons){
                if((this.model.actions|| []).filter((action) => {
                    if (!action)
                        return false;
                    if (typeof action.hidden == 'function')
                        return !action.hidden();
                    return !action.hidden;
                }).length <= 0)
                return false
            }

            if (this.model.type != "entity")
                return true;
            if (this.model.dataSource)
                return true;
            return false;
        },
        show_status() {
            return !this.controlGroup && (!this.model.multiple || this.value && this.value.length <= 1) && this.status[0]
        },
        attr() {
            let readonly = typeof this.model.readonly == 'function' ? this.model.readonly() : this.model.readonly

            if(this.model.type == 'integer'){
                this.model.type = 'float'
                this.model.attr = this.model.attr || {};
                this.model.attr.predicate = "^(-)?(\\d+)?$";
            }

            return {
                name: this.model.name,
                icon: this.model.icon,// || "field",
                wait: this.model.wait,
                type: (this.model.type == 'password' || this.model.type == 'number' || this.model.type == 'float') && this.model.type,
                label: this.placeHolder,
                //status: this.model.status,
                readonly: readonly ? true : false,
                dataSource: this.model.dataSource,
                has_del: this.model.multiple ? true : this.model.has_del,
                meta: this.model.meta,
                source: this.model.source,
                ...this.model.attr,
                placeholder: undefined
                
                //required: this.model.required
            }
        },
        required() {
            let readonly = typeof this.model.readonly == 'function' ? this.model.readonly() : this.model.readonly
            if (readonly)
                return false;

            switch (this.model.type) {
                case 'static':
                case 'info':
                    return false;
            }

            if (typeof this.model.required == "function") {
                return this.model.required()
            }
            return this.model.required;
        },
        status() {
            if (!this.model.multiple || this.model.type == "model") {
                return {
                    0: this.model.status
                }
            }

            if (!this.model.status || !this.model.status.data) {
                return {
                    0: this.model.status
                }
            }

            return this.model.status.data.reduce((pre, curr) => {
                curr.type = this.model.status.type
                pre[curr.i] = curr
                return pre
            }, {})

        },
        value() {
            if (!this.model.multiple || this.model.type == "model") {
                return [this.model.value]
            }
            let value = Array.isArray(this.model.value) ? this.model.value : [this.model.value];
            value = value.filter((value) => {
                return value != undefined && value != '';
            })

            if (this.attr.readonly && this.model.multiple && this.model.type == 'file' && this.model.actions && this.model.actions.length >0){
               value.push(undefined);
            }

            if ( (!this.attr.readonly && this.model.type != "static" && this.model.type != "static")  || value.length <= 0)
                value.push(undefined);

            return value
        },
        classes() {
            return [
                {
                    inline: this.model.type == "action",
                    horizontally: this.horizontally_fix || this.horizontally_fix2 || this.horizontally,
                    horizontally_fix: this.horizontally_fix,
                    horizontally_fix2: this.horizontally_fix2,
                    "field-border": this.model.border, 
                },
                `field-type-${this.model.type}`,
                `status-${this.status[0]?.type}`,
            ]
        },
        details_fields() {

            return this.model.attr.details.map((name) => {
                return {
                    label: `-${name.label || name}`,
                    type: "static",
                    value: this.model.value[name.field || name]
                }
            })
        }
    },
    components: {
        fieldInfo: {
            props: ["title"],
            template: `
                <span title=''>?
                    <div v-html='$t(title)'/>
                </span>
            `
        },
        overflow: {
            props: ["value","source"],
            methods: {
                onClick(){
                    this.$emit('input', !this.value)
                }
            },
            computed: {
                show_scroll(){
                    let source_length = this.source?.items?.length;
                    return this.value != undefined && source_length > 10
                },
                classes() {
                    return [
                        {
                            overflow: this.show_scroll,
                            compact: this.value
                        }
                    ]
                },
            },
            template: `
                <div v-bind:class="classes">
                    <div class='field-content overflow-compact thin-scroll primary'><slot/></div>
                    <template v-if='show_scroll'>
                        <div class='action-background' />
                        <div class='action' v-on:click='onClick'><span>{{value ? "развернуть" : "свернуть"}}</span></div>
                    </template>
                    
                </div>
            `
        }
    },
    watch: {
        "model.description": {
            immediate: false,
            async handler(value, oldVal) {
                this.update_description();
            }
        },        
        "model.attr.placeholder": {
            immediate: false,
            async handler(value, oldVal) {
                this.update_placeHolder();
            }
        },
        "model.label": {
            immediate: false,
            async handler(value, oldVal) {
                this.update_label();
                this.update_placeHolder();
                this.update_horizontally();
            }
        }
    },
    mounted(){
        this.update_label();
        this.update_placeHolder();
        this.update_description(); 
        this.update_horizontally();
    },
    methods: {
        update_label(){
            switch (this.model.type) {
                case 'bool':
                case 'info':
                    return;
            }

            let label = this.model.label || this.model.name;
            if (this.model.type == "action" || this.model.type == "static") {
                label = this.model.label || '';
            }

            let index = label.indexOf('!')
            if (index >= 0 && index <= 1)
                return;

            if (Array.isArray(label)) {
                label = label.map((item) => {
                    return item.replace(/^[\~\-\!]+/, '');
                })
            } else {
                label = label.replace(/^[\~\-\!]+/, '');
            }
            this.label = label

            label = Language.t(this.label);
            let i = label.lastIndexOf(" ")

            this.label_s = {
                first: i < 0 ? label : label.substring(0, i),
                last: i > 0 ? label.substring(i) : ""
            } 

        },
        update_placeHolder(){
            let label = this.model?.attr?.placeholder ||  this.model.label || this.model.name;
            if (Array.isArray(label)) {
                label = label.map((item) => {
                    return item.replace(/^[\~\-\!]+/, '');
                })
            } else {
                label = label.replace(/^[\~\-\!]+/, '');
            }
            this.placeHolder = label;
        },
        update_description(){
            let type = undefined
            let message = this.model.description

            if (!message)
                return;
            if (message[0] == '^') {
                type = 'left'
                message = message.substring(1)
            } else if (message[0] == '$') {
                type = 'right'
                message = message.substring(1)
            } else if (message[0] == '_') {
                type = 'top'
                message = message.substring(1)
            } else if (message[0] == '-') {
                type = 'sub'
                message = message.substring(1)
            }
            this.description = {
                type, message
            }
        },
        update_horizontally(){
            let label = this.model.label || this.model.name;
            let match = /^[\~\-\!]+/gm.exec(label)
            if(!match || !match[0])
                return;
            let index = -1;
            
            index = match.indexOf('~');
            if (index >= 0 && index <= 1)
                this.horizontally = true;

            index = match.indexOf('-');
            if (index >= 0 && index <= 1)
                this.horizontally_fix = true;
            
            index = match.indexOf('--');
            if (index >= 0 && index <= 2)
                this.horizontally_fix2 = true;
        },
        updateValue(event, index) {
            if (!this.model.multiple || this.model.type == "model") {
                this.model.value = event
                this.$emit("change", this.model.value)
                return;
            }

            if (event !== undefined && event !== '') {
                /*let value = [...this.model.value]
                value[index] = event;
                this.model.value = value;*/
                //this.model._value = Array.isArray(this.model.value) ? this.model.value : [this.model.value];
                //let value = Array.isArray(this.model.value) ? this.model.value : [this.model.value];
                //Vue.set(this.model._value, index, event)
                //this.model.value = [...this.model._value]



                let value = [...(Array.isArray(this.model.value) ? this.model.value : [this.model.value])];
                value[index] = event;
                this.model.value = [...value]


            } else {
                if (!this.model.value) {
                    return;
                }
                let value = [...this.model.value]
                value.splice(index, 1)
                this.model.value = [...value]
            }
            this.$emit("change", this.model.value)

            //this.model.value = [...this.model._value]
            //this.model.value = [...this.model.value];


        },
        inputValue(event, index) {
            if (this.model.attr && this.model.attr.react) {
                this.updateValue(event, index);
            }
        }
    },
    template: `
<component :is='wrapper.wrap && wrapper.is || "div"' v-if='show_field' :key='model.key || (model.name+model.type)' class='ui-field' v-bind:class="classes" :content='wrapper.content' :actions='wrapper.actions'>
    <div class='field-content'>
        <div v-if_dev='label || model.type == "action"' class='field-label'>
            <div v-if='label' class='label-wrapper'>
                <label>
                    <fieldInfo class='description' v-if='description && description.type == "left"' :title='description.message' />
                    <span>{{label_s.first}}</span>
                    <span style='white-space: nowrap;'>{{label_s.last}} <span class='color-red' v-if='required'> *</span>:</span>
                    <fieldInfo class='description' v-if='description && description.type == "right"' :title='description.message' />
                </label>
                <div class='field-description' v-if='description && description.type == "sub"' v-html='$t(description.message)'/>
            </div>
        </div>
        <div class='field-container'>
            <div class='field-description' v-if='description && description.type == "top"' v-html='$t(description.message)'/>
            <div class='field-control'>
                <template :key='index' v-for='val,index in value'>
                    <ui-layout v-if='model.type == "model"' :fields='model.fields' v-bind='attr' />
                    <component v-else-if='model.type == "widget"'  :is='widget.name' v-bind='widget.props'>
                        {{widget.content}}
                    </component>

                    <component v-else-if='field' :is='field.is'
                        v-bind='attr'
                        v-bind:value="val"
                        v-on:change="updateValue($event,index)"
                        v-on:input="inputValue($event,index)"
                        v-bind:actions="!wrapper.actions && model.actions"
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                    />

                    <ui-markdown-view v-else-if='model.type == "markdown"'
                        v-bind='attr'
                        :content="val"
                    />

                    <ui-eimzo v-else-if='model.type == "eimzo"'
                        v-bind='attr'
                        v-bind:value="val"
                        v-on:input="updateValue($event,index)"
                        v-bind:actions="!wrapper.actions && model.actions"
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        /> 
                    <ui-link v-else-if='model.type == "link"'
                        v-bind='model.attr'
                        /> 
                    <ui-data-view v-else-if='model.type == "data-view"'
                        v-bind='attr'
                        v-bind:value="val"
                        v-bind:actions="!wrapper.actions && model.actions"
                        /> 
                    <ui-data-grid v-else-if='model.type == "data-grid"'
                        :class='model.wait && "iac-wait"'
                        v-bind='attr'
                        v-bind:value="val"
                        v-bind:actions="!wrapper.actions && model.actions"
                        /> 
                    <ui-entity v-else-if='model.type == "entity"'
                        v-bind='attr'
                        v-bind:value="val"
                        v-on:input="updateValue($event,index)"
                        v-bind:actions="!wrapper.actions && model.actions"
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        :prefix='model.prefix'
                        :suffix='model.suffix'
                        />  
                    <ui-date type='date-time' class='control' v-else-if='model.type == "date-time"'
                        type='date-time'
                        v-bind='attr'
                        v-bind:value="val"
                        v-on:input="updateValue($event,index)"
                        v-bind:actions="!wrapper.actions && model.actions"
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        :prefix='model.prefix'
                        :suffix='model.suffix'
                        />                    
                    <ui-date type='date' class='control' v-else-if='model.type == "date"'
                        type='date'
                        v-bind='attr'
                        v-bind:value="val"
                        v-on:input="updateValue($event,index)"
                        v-bind:actions="!wrapper.actions && model.actions"
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        :prefix='model.prefix'
                        :suffix='model.suffix'
                        />
                    <ui-date type='week' class='control' v-else-if='model.type == "week"'
                        type='week'
                        v-bind='attr'
                        v-bind:value="val"
                        v-on:input="updateValue($event,index)"
                        v-bind:actions="!wrapper.actions && model.actions"
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        :prefix='model.prefix'
                        :suffix='model.suffix'
                        />    
                    <ui-date type='time' class='control' v-else-if='model.type == "time"'
                        type='time'
                        v-bind='attr'
                        v-bind:value="val"
                        v-on:input="updateValue($event,index)"
                        v-bind:range="model.range"
                        v-bind:actions="!wrapper.actions && model.actions"
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        :prefix='model.prefix'
                        :suffix='model.suffix'
                        />
                    <ui-action class='control' v-else-if='model.type == "action"'
                        v-bind='attr'
                        v-bind:actions="!wrapper.actions && model.actions"
                        v-bind:buttons="model.buttons"
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        />             
                    <ui-enum v-else-if='model.type == "enum"'
                        v-bind='attr'
                        v-bind:value="val"
                        v-on:input="updateValue($event,index)"
                        v-bind:actions="!wrapper.actions && model.actions"
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        /> 
                    <ui-enum-tree v-else-if='model.type == "enum-tree"'
                        v-bind='attr'
                        v-bind:value="val"
                        v-on:input="updateValue($event,index)"
                        v-bind:actions="!wrapper.actions && model.actions"
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        />
                    <ui-tag v-else-if='model.type == "tag"'
                        v-bind='attr'
                        v-bind:value="val"
                        v-on:input="updateValue($event,index)"
                        v-bind:actions="!wrapper.actions && model.actions"
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        />
                    <ui-slider v-else-if='model.type == "range"'
                        v-bind='attr'
                        :min='model.min'
                        :max='model.max'
                        v-bind:value="val"
                        v-on:change="updateValue($event,index)"
                        v-on:input="inputValue($event,index)"
                        v-bind:actions="!wrapper.actions && model.actions"
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        />  
                    <template v-else-if='model.type == "bool"'> 
                        <component  v-if='wrapper.is' :is='wrapper.is' 
                            :checkbox='true'  
                            :content='model.wrapper' 
                            v-bind='attr'
                            v-bind:value="val"
                            v-on:input="updateValue($event,index)"
                            v-bind:actions="model.actions"
                            :description='model.description'
                            v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                            :required='(index || label) ? false : model.required'/>
                        <ui-checkbox class='field' v-else 
                            v-bind='attr'
                            v-bind:value="val"
                            v-on:input="updateValue($event,index)"
                            v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                            :required='(index || label) ? false : model.required'
                            />

                    </template>
                    <ui-file  v-else-if='model.type == "file"'

                        v-bind:show_comment='model.show_comment'
                        v-bind:show_photo='model.show_photo'
                        v-bind='attr'
                        :min='model.min'
                        :max='model.max'                    
                        v-bind:value="val"
                        v-on:input="updateValue($event,index)"
                        v-bind:actions="!wrapper.actions && model.actions"
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        />    

                    <ui-text  v-else-if='model.type == "text"'
                        v-bind='attr'
                        v-bind:value="val"
                        v-on:change="updateValue($event,index)"
                        v-on:input="inputValue($event,index)"
                        v-bind:actions="!wrapper.actions && model.actions"
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        />
                    <ui-html  v-else-if='model.type == "html"'
                        v-bind='attr'
                        v-bind:value="val"
                        v-on:input="updateValue($event,index)"
                        v-bind:actions="!wrapper.actions && model.actions"
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        />    
                    <ui-info  v-else-if='model.type == "info"'
                        v-bind='attr'
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        v-bind:value="val"
                        />  
                    <ui-static  v-else-if='model.type == "static"'
                        v-bind='attr'
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        v-bind:value="val"
                        />                      
                    <ui-input  v-else
                        :min='model.min'
                        :max='model.max'
                        v-bind='attr'
                        v-bind:value="val"
                        v-on:change="updateValue($event,index)"
                        v-on:input="inputValue($event,index)"
                        v-bind:actions="!wrapper.actions && model.actions"
                        v-bind:status="(show_status && status[index]) ? status[index].type : status[index]"
                        :required='(index || label) ? false : model.required'
                        :prefix='model.prefix'
                        :suffix='model.suffix'
                        /> 
                </template>
            </div>
            <div :class='"status "+status[0].type' v-if='show_status'>{{status[0].message}}</div> 
            <div class='field-description' v-if='description && !description.type' v-html='$t(description.message)'/>
        </div>
        <template v-if='model.attr && model.attr.details && value[0]'>
            <ui-layout :fields='details_fields' style='margin-top: 15px;'/>
        </template>
    </div> 
</div>
        `
}