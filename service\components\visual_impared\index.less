.visual-impared {
  position: relative;
  text-align: center;

  &__dropdown {
    margin: 0 5px;
    border: none;
    background-color: transparent;
    text-align: center;
    cursor: pointer;

    svg {
      fill: #ababab;;
    }

    &:hover,
    &:focus {
      outline: none;

      svg {
        fill: @brand-primary;
      }
    }
  }

  &__dropdown-menu {
    position: absolute;
    top: 107%;
    left: 50%;
    width: 180px;
    z-index: 9;
    transform: translateX(-50%);
  }

  &__overflow {
    border: 1px solid #ddd;
    border-radius: 4%;
    overflow: hidden;
  }

  &__header {
    background: #f7f7f7;
    border-bottom: 1px solid #eaeaea;
    padding: 7.5px 0;
    text-align: center;
  }

  &__colors {
    margin: 0;
    border-bottom: 1px solid #ccc;
    padding: 8px 6px;
    height: 100%;
    background-color: @white;
    border-radius: 4%;

    li {
      display: inline-block;
      margin: 0 2px;
    }

    button {
      display: flex;
      border: 1px solid #ccc;
      padding: 5px;
      font-size: inherit;
      font-family: inherit;
      background-color: #fff;
      cursor: pointer;
      align-items: center;

      &::after {
        content: "";
        margin-left: 5px;
        width: 16px;
        height: 16px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 576 512'%3E%3Cpath fill='currentColor' d='M288 144a110.94 110.94 0 00-31.24 5 55.4 55.4 0 017.24 27 56 56 0 01-56 56 55.4 55.4 0 01-27-7.24A111.71 111.71 0 10288 144zm284.52 97.4C518.29 135.59 410.93 64 288 64S57.68 135.64 3.48 241.41a32.35 32.35 0 000 29.19C57.71 376.41 165.07 448 288 448s230.32-71.64 284.52-177.41a32.35 32.35 0 000-29.19zM288 400c-98.65 0-189.09-55-237.93-144C98.91 167 189.34 112 288 112s189.09 55 237.93 144C477.1 345 386.66 400 288 400z'/%3E%3C/svg%3E");
      }
    }

    .gray {
      background: @gray;
    }

    .invert {
      background: @black;
      color: #fafafa;

      &::after {
        filter: invert(1);
      }
    }

  }
}

.theme-invert{
    filter: grayscale(100%) invert(100%);
    background-color: @white;
    img,
    .visual-impared__colors >li,
    //.brand-lockup,
    .image-wrapper,
    .iac-service-footer {
      filter: grayscale(100%) invert(100%);
    }
}

.theme-gray{
  filter: grayscale(100%);

}

.theme-gray1 {

  .iac-service-header,
  .iac-service-header~.iac-service-content,
  .iac-dialog_modal_box,
  .iac-service-nav,
  .iac-home-page,
  .iac-service-footer {
    filter: grayscale(100%);
  }

  .iac-nav-top {
    position: relative;
    z-index: 1;
  }
}

.theme-invert1 {
  background-color: @dark;

  .iac-service-header,
  .iac-service-header~.iac-service-content,
  .iac-dialog_modal_box,
  .iac-service-nav,
  .iac-home-page>*:not(.iac-service-footer) {
    filter: grayscale(100%) invert(100%);
    background-color: @white;
  }

  //===anti-invert fix===//
  .visual-impared__colors {
    .default {
      background: @black;
      color: #fafafa;

      &::after {
        filter: invert(1);
      }
    }

    .invert {
      background-color: transparent;
      color: black;

      &::after {
        filter: invert(0);
      }
    }
  }

  .iac-nav-top {
    position: relative;
    z-index: 1;
  }

  .image,
  img {
    filter: invert(100%);
  }
}

@media (max-width: 450px) {
  .visual-impared {

    &__dropdown {
      margin: 0;
      padding: 0 5px;
    }
  }
}
