
import { DataSource } from '@iac/data';
import { Language } from '@iac/core';

var Component = {
    props: ["limit"],
    data() {
        return {
            source: new DataSource({
                search: true,
                query: {
                    queryText: {
                        label: "!search.bkl.placeholder",
                        hidden: ()=>{
                            return this.limit;
                        }
                    }
                },
                store: {
                    ref: 'list_of_stock_exchange',
                    injectQuery: (params) => {
                        if(this.limit)
                            params.limit = this.limit;
                        return params;
                    },
                    context: (context)=>{
                        context.properties = []
                        for (let key in context.product_properties) {
                            context.properties.push({prop_name: context.product_properties[key].prop_name,value: context.product_properties[key].val_name})
                        }
                        return context;
                    }
                },
                columns: [
                    {label:"exchange.contract",field:"id"},
                    {label:"name_asset", field: "row_name",style:'text-align: left; width: 100%;'},
                    {label: "amount_in_lot",field: "amount_in_lot",style:'text-align: right;white-space: nowrap;'},
                    {label:"start_price_lot",field:"start_price",style:'text-align: right;white-space: nowrap;'},
                    {label: "storage_location", field:"storage",style: "text-align: left;white-space: nowrap;"}
                ]
            })
        }
    },   
    methods: {
        product_name(product_name){
            return product_name && (product_name[Language.local] || product_name["ru-RU"] || product_name)
        },
        company_link(company_id) {
            return `/company/${company_id}`;
        },
        show_properties(model) {
            console.log("222",model)
            Vue.Dialog({
                props: ['model'],
                methods: {
                    product_name(product_name){
                        return product_name && (product_name[Language.local] || product_name["ru-RU"] || product_name)
                    }
                },
                template: `
                  <div>
                    <header>{{product_name(model.product_name)}}</header>
                    <main>
                      <iac-layout-static :value='model.product_properties || model.properties' />
                    </main>
                    <footer>
                      <ui-btn type='secondary' v-on:click.native='Close'>{{$t('Close')}}</ui-btn>
                    </footer>
                  </div>
                `
            }).Modal({
                model: model
            })
        }
    },
    template: `
    <ui-data-grid :readonly='true' class='top_filter iac-view-financial_quotation' :dataSource='source' :columns='source.columns'>
        <template slot='row_name' slot-scope='props'>
            <div class='name' v-on:click.prevent.stop='show_properties(props.item)'>{{product_name(props.item.product_name)}}</div>
            <!-- <div>{{product_name(props.item.product_name)}}</div> -->
            <div class='props clamp_2'>
                <span :title='prop.prop_name' v-for='prop in props.item.properties'>{{prop.value}}</span>
            </div>
            <!-- <router-link style='color: #ccc;' :to='company_link(props.item.company_id)'>{{props.item.company_name}}</router-link> -->
        </template>
        <template slot='amount_in_lot' slot-scope='props'>
            <div>{{props.item.amount_in_lot}} &nbsp;<ui-ref source='ref_unit' :value='props.item.unit' /></div>
        </template>
        <template slot='start_price' slot-scope='props'>
            <iac-number :value='props.item.start_price'  delimiter=' ' part='2' />
        </template>
    </ui-data-grid>
    `
}

Vue.component("iac-view-bkl",Component)
