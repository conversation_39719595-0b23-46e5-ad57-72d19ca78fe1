import { Http } from '@iac/core'



export default Vue.Dialog("PublishProcedure",{
    props: ["proc_id"],
    data: function () {
        return {
            field_value: undefined,
            error: undefined
        }
    },
    methods: {
        async send() {

            await this.$wait(async () => {
                if (!this.field_value) {
                    return;
                }

                let formData = new FormData();
                formData.append('scope_tender_participant', this.proc_id);
                formData.append('data', this.field_value.file, this.field_value.name);
                let { data, error } = await Http.upload.form('tender/attach', formData);
                console.log("ERROR", data, error);
                if (error) {
                    this.error = error
                } else {
                    this.Close({
                        data: {
                            id: data.uuid,
                            name: data.meta.name,
                            //desc: context.description,
                            meta: {
                                "type": data.meta.type,
                                "content_type": data.meta.content_type,
                                "type_group": data.meta.group,
                                "size": data.meta.size
                            }
                        }
                    });
                }
            })
        }
    },
    template: `
        <div>
            <header>{{$t('action.publish_procedure')}}</header>
            <main class='error' v-if='error'>
                {{error.message}}
            </main>
            <main>
                <ui-file label='field.approvement' v-model='field_value'/>
                <ui-info  label='field.approvement_info'/>
            </main>
            <footer>
                <ui-btn type='primary' v-on:click.native='send'>{{$t('action.publish_procedure')}}</ui-btn>
            </footer>
        </div>
    `
})