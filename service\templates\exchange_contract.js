import Vue from "vue";
import { Context } from "@iac/kernel";
import { DataSource } from "@iac/data";
import { Http } from '@iac/core';

const Component = {
  props: ["model"],
  data: function () {
    return {
      user: Context.User,
       calculated_ref_status: this.model.ref_status || "exchange_contract_status",
    }
  },
  methods: {
    link(item) {
      return `/workspace/contract/${item.number}`
    },
    async open_file(e) {
      e.preventDefault();
      this.$wait(async () => {
        if (this.model.contract_body.custom || !this.model.contract_body.link) {
          await Vue.Dialog.editContractFileBody.Modal({
            size: 'full',
            readonly: true,
            value: this.model.contract_body,
            number: this.model.number
          })
          return;
        }
        if (Context.User.id) {
          await Context.User.refreshToken();
          window.location = `${this.model.contract_body.link}?token=${Context.User.access_token}`
        } else {
          window.location = `${this.model.contract_body.link}`
        }
      })
    }
  },
  template: `
    <ui-data-view-item :model='model'>
        <template slot='header'>
            <div>
              <!--  <iac-entity-edit v-if='model.number' :value='{id: model.id, type: "contract", title: "№"+model.number}' /> -->
              <iac-date :title='$t("exchange.deal_date")' v-if='model.contract_close_at' :date="model.contract_close_at" withoutTime />
            </div>
            <!-- <div><ui-ref :source='calculated_ref_status' :value='model.status' /></div> -->


        </template>
        
        <template slot='title'>
            <router-link v-if=0 :to='link(model)'>{{ model.contract_name || ($t('contract') + ' №' + model.number) }}</router-link>
            <div v-else>{{ model.contract_name || ($t('contract') + ' №' + model.number) }}</div>
        </template>

        <!--  <template slot='description'>
            <div class='clamp_2'>
                <label>{{ $t('contract.initiator') }}: </label>
                <span :title='model.org_company.company_title'>{{ model.org_company.company_title }}</span>
            </div>
            <div class='clamp_2' v-if='model.contragent_company.company_title'> 
                <label>{{ $t('contract.contragent') }}: </label>
                <span :title='model.contragent_company.company_title'>{{ model.contragent_company.company_title }}</span>
            </div>
        </template> -->

        <template slot='props'>
            <div v-if='model.parent_id'>
              <label>{{ $t('exchange.contract_number') }}:</label>
              <div>{{ model.parent_id }}</div>
            </div>
            
            <div v-if='model.good && model.good.unit'>
              <label>{{ $t('exchange.unit') }}:</label>
              <div><ui-ref source='ref_unit' :value='model.good.unit' /></div>
            </div>
            
            <div v-if='model.type_of_packing'>
              <label>{{ $t('exchange.packing_type') }}:</label>
              <div>{{ model.type_of_packing.name }}</div>
            </div>
            
            <div v-if='model.unit_of_packing'>
              <label>{{ $t('exchange.packing_unit') }}:</label>
              <div><ui-ref source='ref_unit' :value='model.unit_of_packing' /></div>
            </div>

            <div v-if='model.price'>
              <label>{{ $t('exchange.start_price') }}:</label>
              <div><iac-number :value='model.price' delimiter=' ' part='2' />&nbsp;<span :title="model.currency">{{ model.currency }}</span></div>
            </div>

            <div v-if='model.contract_type'>
              <label>{{ $t('exchange.contract_deal_type') }}:</label>
              <div>{{ model.contract_type }}</div>
            </div>

            <div v-if='model.price'>
              <label>{{ $t('exchange.deal_unit_price') }}:</label>
              <div><iac-number :value='model.price' delimiter=' ' part='2' />&nbsp;<span :title="model.currency">{{ model.currency }}</span></div>
            </div>

            <div v-if='model.total_price'>
              <label>{{ $t('exchange.total_price') }}:</label>
              <div><iac-number :value='model.total_price' delimiter=' ' part='2' />&nbsp;<span :title="model.currency">{{ model.currency }}</span></div>
            </div>

            <div v-if='model.contract_body' >
              <label>{{$t('contract.file')}}:</label>
              <div style='text-align: right'>
                <a v-if='model.contract_body.link && !model.contract_body.custom' v-bind:href='model.contract_body.link' v-on:click='open_file'>{{model.contract_body.meta.name}}</a>
                <a v-else href='javascript:void(0)' v-on:click='open_file'>{{$t('contract')}}</a>
              </div>
            </div>
        </template>
    </ui-data-view-item>
  `
}

Vue.component("template-exchange_contract", Component);
