import { Entity } from '@iac/data'
import { Http, Language } from '@iac/core'
import { Settings } from '@iac/kernel'
import { Seller as TenderSeller, Buyer as TenderBuyer } from './tender.model'
import { Seller as SelectionSeller, Buyer as SelectionBuyer } from './selection.model'
import { Seller as ShopSeller, Buyer as ShopBuyer } from './shop.model'
import { Seller as ReductionSeller, Buyer as ReductionBuyer } from './reduction.model'

class SelectModel extends Entity {
  constructor(onModelChange) {
    super()
    this.onModelChange = onModelChange
  }

  onChangeProperty(_data) {
    this.onModelChange(this)
  }

  props() {
    return {
      proc_type: {
        description:'$calc.proc_type_description',
        required: true,
        group: '<ololo>',
        type: 'entity',
        dataSource: [
          { id: 'tender', name: 'tender' },
          { id: 'selection', name: 'selection' },
          { id: 'shop', name: 'nav.electronic_shop' },
          { id: 'reduction', name: 'reduction' },
        ],
        has_del: true
      },
      role: {
        description:'$calc.role_description',
        required: true,
        group: '<ololo>',
        type: 'entity',
        dataSource: [
          { id: 'buyer', name: 'company.buyer' },
          { id: 'seller', name: 'company.seller' },
        ],
        has_del: true
      }
    }
  }
}

export default {
  data() {
    return {
      modelsMap: {
        'tender': { 'buyer': TenderBuyer, 'seller': TenderSeller },
        'selection': { 'buyer': SelectionBuyer, 'seller': SelectionSeller },
        'shop': { 'buyer': ShopBuyer, 'seller': ShopSeller },
        'reduction': { 'buyer': ReductionBuyer, 'seller': ReductionSeller },
      },
      selectModel: new SelectModel(this.selectCalcModel),
      calcModel: undefined,
      result: undefined,
      currentBRV: 1
    }
  },
  methods: {
    showResult(result) {
      this.result = result
    },
    selectCalcModel(selectModel) {
      const type = selectModel?.proc_type?.exp?.value
      const role = selectModel?.role?.exp?.value
      this.result = undefined
      this.calcModel = (role && type) ? new this.modelsMap[type][role](this.currentBRV, this.showResult) : undefined
    },
    async getCurrentBRV() {
      let { error, data } = await Http.api.rpc("common_ref", { ref: "last_base_calculated_value", op: "read" })
      this.currentBRV = (!error && data?.[0]?.value) ? data[0].value : 1
    },
    getSuffix(name) {
      const suffixMap = {
        '_percent': '%',
        'current_offers_amount': ''
      }
      for (let i = 0; i < Object.keys(suffixMap).length; i++) {
        if (name.indexOf(Object.keys(suffixMap)[i]) >= 0) {
          return suffixMap[Object.keys(suffixMap)[i]]
        }
      }
      return Settings._default_currency
    }
  },
  mounted() {
    this.getCurrentBRV()
  },
  template: `
    <div class='pt-20'>
      <h2>{{ $t('calculator') }}</h2>
      <ui-layout-group horizontal>
        <ui-layout-group style="max-width:400px;">
          <ui-layout :fields='selectModel.fields' />
          <ui-layout v-if='calcModel' :fields='calcModel.fields' />
        </ui-layout-group>
        <ui-layout-group class='table-wrap' v-if='result'>
          <h3 v-if='result.data'>{{$t('calc.data')}}</h3>
          <table v-if='result.data'>
            <tr>
              <td width='50%'>{{ $t('position') }}</td>
              <td width='50%'>
                <div>{{ $t('value') }}</div>
                <div v-if="result.title">{{ $t(result.title) }}</div>
              </td>
            </tr>
            <tr v-for="(value, name, index) in result.data" :key='index'>
              <td>{{ $t('calc.'+name) }}</td>
              <td>
                <iac-number :value='value' delimiter=' ' part='2' :round='true' />{{getSuffix(name)}}
              </td>
            </tr>            
          </table>
          <h3 v-if='result.computed'>{{$t('calc.result')}}</h3>
          <table v-if='result.computed'>
            <tr>
              <td width='50%'>{{ $t('position') }}</td>
              <td width='50%'>
                <div>{{ $t('value') }}</div>
                <div v-if="result.title">{{ $t(result.title) }}</div>
              </td>
            </tr>
            <tr v-for="(value, name, index) in result.computed" :key='index'>
              <td>{{ $t('calc.'+name) }}</td>
              <td>
                <iac-number :value='value' delimiter=' ' part='2' :round='true' />{{$settings._default_currency}}
              </td>
            </tr>
            <tr v-if='result.total'>
              <td>{{ $t('total') }}</td>
              <td>
                <iac-number :value='result.total' delimiter=' ' part='2' :round='true' />{{$settings._default_currency}}
              </td>
            </tr>
          </table>
        </ui-layout-group>
      </ui-layout-group>
    </div>
  `,
}
