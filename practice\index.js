const PracticeComponent = {
    props: ["page"],
    
    template: `
        <div>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('practice')}}</li>
                </ol>
                <div class='title'>
                    <h1>Практика</h1>
                </div>
            </iac-section>
            <iac-section>

            </iac-section>
        </div>
    `
}

Vue.component("practice-component", PracticeComponent);