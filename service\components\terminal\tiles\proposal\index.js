import ProposalSetting from './setting'
import ProposalModel from '../../model/proposal'
import Contract from '../../model/contract'

import { Http, Language } from '@iac/core'

let ProposalComponent = {
    props: ["model"],
    data: function () {
        return {
            contract: undefined,
            proposal: ProposalModel,
            proposal_setting: Vue.Tiling["proposal"].setting,
            general_setting: Vue.Tiling["settings"].general_setting,
        }
    },
    computed: {
        $proposal_setting() {
            return this.proposal_setting.params
        },
        $general_setting() {
            return this.general_setting.params
        },
        list() {
            return this.proposal.list.filter((item) => {
                if (this.model.contract && this.model.contract.id != item.contract_id)
                    return false;
                return true;
            })
        }
    },
    watch: {
        "model.contract": {
            immediate: true,
            async handler(contract, oldVal) {
                if (contract && this.contract && contract.id == this.contract.id)
                    return;
                if (this.contract) {
                    this.contract.unwatch(Contract.Event_General)
                    this.contract = undefined
                }
                if (contract && contract.id) {
                    this.contract = Contract.get(contract)
                    this.contract.watch(Contract.Event_General)
                }                
            },
        }
    },
    beforeDestroy() {
        if (this.contract)
            setTimeout(() => {
                this.contract.unwatch(Contract.Event_General);
            }, 200)
    },
    methods: {
        async delete_item(item) {
            if (await Vue.Dialog.MessageBox.Question(Language.t('proposal.delete_item.question')) == Vue.Dialog.MessageBox.Result.Yes) {
                //await Http.proc.rpc("delete_bet",{
                //    proposal_id: item.id
                //})

                this.$wait(async () => {
                    let { error, data } = await this.proposal.delete_bet(item.id)
                    if (error) {
                        Vue.Dialog.MessageBox.Error(error);
                    }
                })

            } else {
                return;
            }
        },
        async add_item() {
            this.$wait(async () => {
                await this.proposal.add_bet({
                    contract_id: this.model.contract && this.model.contract.id
                })
            })

        }
    },
    template: `
        <div class='iac-exchange-proposal-tile'>
        <div v-if='contract && (!contract.status || contract.status == "closed")' style='padding: 4px 8px;flex: 1 1 auto;background: #efefef; '>
            <span style='font-size: 16px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;'>{{contract.id}}: {{$t("contract_status.closed")}}</span>
        </div>
        <div v-else-if='contract && contract.status && contract.status == "commited"' style='padding: 4px 8px;flex: 1 1 auto;background: #efefef; '>
            <span style='font-size: 16px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;'>{{contract.id}}: {{$t("contract_status.commited")}}</span>
        </div>
        <div v-else class='content thin-scroll' style='overflow-y: scroll; flex: 1 1 100%;'>
            <table :class='[{"striped":$general_setting.striped}]' cellpadding=0 border=0 cellspacing=0  >
                <thead>
                    <tr>
                        <th class='id' v-if='!model.contract'>{{$t('exchange.contract')}}</th>
                        <th style='width: 100%;'>{{$t('nav.company')}}</th>
                        <th class='number'>{{$t('exchange.lot.count')}}</th>
                        <th class='number'>{{$t('price')}}</th>
                        <th style='min-width: 20px;'>
                            <ui-btn v-if='proposal.add_bet' type='sm empty primary' v-on:click.native='add_item'>{{$t('terminal.add_bet')}}</ui-btn>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for='item in list'>
                        <td class='id' v-if='!model.contract'>{{item.contract_id}}</td>
                        <td>
                            <span>{{item.company}}</span>
                            <span>{{item.company_name}}</span>
                        </td>
                        <td class='number'>
                            <iac-number :value='item.amount' delimiter=' ' part='0' />
                        </td>
                        <td class='number'>
                            <iac-number :value='item.price' delimiter=' ' part='2' />
                        </td>
                        <td>
                            <span style='cursor: pointer; color: #dc362e' v-on:click='delete_item(item)'><icon>trash</icon></span>
                        </td>
                    </tr>
                </tbody>
            </table>
            </div>
        </div>

    `
}

Vue.Tiling("proposal", {
    component: ProposalComponent,
    //setting: ProposalSetting,
    select_contract: true,
    select_group: true
})