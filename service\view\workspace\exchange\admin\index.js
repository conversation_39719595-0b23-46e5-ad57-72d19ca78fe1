import { Http,Language } from '@iac/core'
import { DataSource } from '@iac/data'

export default {
    data: function () {
        return {
            sourceTraders: new DataSource({
                store: {
                    method: "get_company_traders",
                    context: (context) => {
                        context.header = [`№${context.company_info?.id}`, `${this.$t("inn")}: ${context.company_info?.inn}`]
                        context.title = { text: context.company_info?.title }
                        context.sourceTraders = new DataSource({
                            ignore_listeners: true,
                            template: "template-user",
                            store: {
                                data: context.traders,
                                context: (trader) => {
                                    trader.actions = [
                                        {
                                            label: "delete",
                                            handler: async () => {

                                                let { error, data } = await Http.api.rpc("delete_trader_from_company",{
                                                    company_id: context.company_info?.id,
                                                    trader_id: trader.id
                                                })

                                                if (error && error.code != "AbortError") {
                                                    Vue.Dialog.MessageBox.Error(error)
                                                } else if (data) {
                                                    context.sourceTraders.store.delete(trader.id)
                                                    context.sourceTraders.reload();
                                                }
                                            }
                                        }
                                    ]
                                    return trader;
                                }
                            },
                            actions: [
                                {
                                    label: "add",
                                    handler: async () => {
                                        let { error, data } = await Http.api.rpc("add_trader_to_company",{
                                            company_id: context.company_info?.id
                                        })
                                        if (error && error.code != "AbortError") {
                                            Vue.Dialog.MessageBox.Error(error)
                                        } else if (data) {
                                            context.sourceTraders.store.set_item(data)
                                            context.sourceTraders.reload();
                                        }
                                    }
                                }
                            ]
                        })

                        Object.defineProperty(context, "sub_title", {
                            configurable : true,
                            get: function () {
                                return {
                                    text: `${context.sourceTraders?.items?.length} ${Language.t("trader", { count: context.sourceTraders?.items?.length })} `,
                                    link: () => {
                                        Vue.Dialog({
                                            props: ["source"],
                                            template: `
                                            <div>
                                                <main>
                                                    <ui-data-view :search='false' type='tile' :dataSource='source'/>
                                                </main>
                                                <footer>
                                                    <ui-btn type='secondary' v-on:click.native='Close'>{{$t('close')}}</ui-btn>
                                                </footer>
                                            </div>
                                        `
                                        }).Modal({
                                            source: context.sourceTraders,
                                            size: "right"
                                        });
                                    }
                                }
                            }
                        });
                        context.description = [
                            {
                                props: ["model"],
                                template: `<div v-if='model.meta'>
                                        <label>{{$t('license')}}</label>
                                        <span><iac-date :date="model.meta.accepted_at"  withoutTime withMonthName/></span>
                                    </div>`
                            }
                        ]
                        context.sourceTraders.load();

                        return context;
                    }
                }
            })
        }
    },
    template: `
    <iac-access :access='$policy.exchange_broker_admin_panel'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.exchange.admin')}}</li>
            </ol>
            <div class='title'>
                <h1>{{$t('nav.exchange.admin')}}</h1>
            </div>
        </iac-section>

        <iac-section>
            <ui-layout-tab>
                <ui-layout-group key='traders' label='Traders'>
                    <ui-data-view :dataSource='sourceTraders' :search='false'/>
                </ui-layout-group>
            </ui-layout-tab>
        </iac-section>
    </iac-access>
    `
}
