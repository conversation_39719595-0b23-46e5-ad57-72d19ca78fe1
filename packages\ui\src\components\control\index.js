export var Control = {
    name: "ui-control",
    props: {
        has_del: {
            type: <PERSON><PERSON><PERSON>,
            default: false
        },
        icon: String,
        opened: <PERSON><PERSON><PERSON>,
        disabled: <PERSON><PERSON><PERSON>,
        readonly: <PERSON><PERSON><PERSON>,
        dropdown: <PERSON><PERSON><PERSON>,
        required: <PERSON><PERSON><PERSON>,
        label: String,
        status: Object,
        actions: Array,
        wait: {
            type: <PERSON><PERSON><PERSON>,
            default: false
        }
    },
    data: function () {
        return {
            dropdownTop: false,
            dropdownRight: false,
        }
    },
    beforeDestroy(){
        window.removeEventListener('scroll',this.reposition)
    },
    watch: {
        dropdown: {
            immediate: true,
            async handler(val, oldVal) {
                if (val == true) {
                    this.reposition();
                    window.addEventListener('scroll', this.reposition);
                } else {
                    window.removeEventListener('scroll',this.reposition)
                }
            }
        }
    },
    computed: {
        component_status() {
            if (typeof this.status == "string") {
                return {
                    type: this.status,
                    message: undefined
                }
            }
            return this.status;
        },
        classes() {
            return [
                (() => {
                    return this.component_status ? ["status", this.component_status.type] : '';
                })(),
                {
                    "icon": this.icon,
                    "opened": this.opened,
                    "disabled": this.disabled,
                    "readonly": this.readonly,
                    "dropdown": this.dropdown,
                    "dropdown-top": this.dropdownTop,
                    "dropdown-right": this.dropdownRight.action,
                    "required": this.required,
                    "wait": this.wait
                }
            ]
        }
    },
    methods: {
        action(event) {
            this.$emit('action', event)
        },
        reposition() {
            const elClientRect = this.$el.getBoundingClientRect();
            let clCenter = {
                x: elClientRect.left + elClientRect.width / 2,
                y: elClientRect.top + elClientRect.height / 2
            }
            let htmlSize = {
                width: document.documentElement.clientWidth,
                height: document.documentElement.clientHeight,
            }

            this.dropdownRight = (clCenter.x > htmlSize.width / 2)
            this.dropdownTop = (clCenter.y > htmlSize.height / 2)
        }
    },
    template: `
        <div class='ui-control' v-bind:class="classes">
            <div class='icon' v-if='icon'><icon>{{icon}}</icon></div>

            <div class='prefix'>
                <slot name="prefix"/>
            </div>   

            <div class='container'>
                <label>{{$t(label)}}</label>
                <slot/>
            </div>

            <div class='suffix'>
                <slot name="suffix"/>
            </div>

            <div class='toolbar'>
                <div class='actions' @mousedown.prevent>
                    <div class='action delete' v-if='opened && has_del' v-on:click='action("clear")'><icon>delete</icon></div>
                    <div class='action select' v-if='$slots.dropdown' v-on:click='action("select")'><icon>arrow</icon></div>
                    <slot name="toolbar"/>
                    <div class='action status' v-if='component_status && component_status.message' :title='component_status.message' v-on:click='action(component_status)'><icon/></div>
                </div>
                <ui-action class='action list' :actions='actions' icon='action'/>
            </div>

            <div class='dropdown' v-if='dropdown'><slot name='dropdown'/></div>
        </div>
    `
}

export var ControlGroup = {
    name: "ui-control-group",
    provide:{
        group: 'control'
    },
    template: `<div class='ui-control-group'>
        <slot/>
    </div>`
}