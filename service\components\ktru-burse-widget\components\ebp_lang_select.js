export default {
    name: "ebplangSelect",
    model: {
        prop: 'langs',
        event: 'change'
    },
    props: {
        langs: {
            type: Array,
            required: true
        },
        label: String,
        disabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {}
    },
    methods: {
        onButtonClick(id) {
            if (id == 0 || this.disabled) {
                return
            }
            const newLangs = [...this.langs]
            newLangs[id].active = !newLangs[id].active
            this.change(newLangs)
        },
        change(newLangs) {
            this.$emit('change', newLangs)
        }
    },
    template: `
    <div class="iac--ebp-lang-select">
        <label>{{label}}</label>
        <button v-for="(lang, index) in langs" @click="onButtonClick(index)" :class="{'active': lang.active}">{{lang.title}}</button>
    </div>
    `
}