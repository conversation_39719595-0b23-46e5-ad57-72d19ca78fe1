.ui-checkbox{
    margin-block-start: 8px;
    margin-block-end: 8px;
    &.required {
      color: #D67777;
    }
    &.error{
        color: #DC1C00;
    }
    &.checked {
      color: inherit;
    }
    &.field{
        //margin-bottom: 30px;
    }
    &.readonly,
    &.disabled{
        color: #777;
        >label, >label >input{
            cursor: not-allowed;    
        }
    }
    >label{
        display: flex;
        line-height: 20px;
        font-size: 14px;
        >input{
            flex: 0 0 auto;
            margin-block-start: 0px;
            margin-block-end: 0px;
            margin-inline-start: 0px;
            margin-inline-end: 4px;
        }
    }

}