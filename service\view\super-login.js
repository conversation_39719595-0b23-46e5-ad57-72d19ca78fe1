import { Config, Context } from '@iac/kernel'
import { Language } from '@iac/core'

export default {
  data() {
    return {
      error: null
    }
  },
  async created() {
    const { id } = this.$route.params;
    if (!id)
      return;

    let error = await Context.User.authorization_god(id);
    if(!error){
      this.$router.push({ path: '/workspace' })
    }else{
      this.$router.push({ path: '/' })
    }


  },
  template: `
    <iac-section>
      <ui-error v-if='error' :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
    </iac-section>
  `
}