import { DataSource, RemoteStore, Query, RefStore } from '@iac/data';
import { Http, Language } from '@iac/core'
import { Context, Develop, Settings } from '@iac/kernel'


var ktru_product = new Query({
  product_id: {
    type: 'entity',
    group: 'choose_product',
    label: '!choose_product',
    has_del: true,
    dataSource: new DataSource({
      valueExp: 'product_id',
      displayExp: "product_name",
      search: true,
      store: new RefStore({
        ref: "ref_enkt_products",
        key:"product_id"
      })
    }),
    multiple: true,
    // hidden: () => {
    //   return !Settings.procedures?._filter_product
    // }
  },
});

var ad_query = new Query({
  min_price: {
    group: 'price_unit/<min-max>',
    type: 'float',
    label: '!from',
    has_delete: true,
    min: 0,
    bind: {
      status: 'price_error && {"type":"error"}'
    },
  },
  max_price: {
    group: 'price_unit/<min-max>',
    type: 'float',
    label: '!to',
    has_delete: true,
    min: 0,
    bind: {
      status: 'price_error && {"type":"error"}'
    },
  },
  price_error: {
    sync: false,
    group: 'price_unit',
    type: "model",
    label: "!",
    bind: {
      value: "min_price > max_price",
      status: (model) =>{
        return model.price_error && {"type":"error","message": Language.t("to_from_error")}
      }
    }
  },
  min_amount_gte: {
    group: 'delivery_amount_min/<min>',
    type: 'float',
    label: '!from',
    has_delete: true,
    min: 0,
    bind: {
      status: 'min_amount_error && {"type":"error"}'
    }
  },
  min_amount_lte: {
    group: 'delivery_amount_min/<min>',
    type: 'float',
    label: '!to',
    has_delete: true,
    min: 0,
    bind: {
      status: 'min_amount_error && {"type":"error"}'
    }
  },
  min_amount_error: {
    sync: false,
    group: 'delivery_amount_min',
    type: "model",
    label: "!",
    bind: {
      value: "min_amount_gte > min_amount_lte",
      status: (model) =>{
        return model.min_amount_error && {"type":"error","message": Language.t("to_from_error")}
      }
    }
  },
  max_amount_gte: {
    group: 'delivery_amount_max/<max>',
    type: 'float',
    label: '!from',
    has_delete: true,
    min: 0,
    bind: {
      status: 'max_amount_error && {"type":"error"}'
    }
  },
  max_amount_lte: {
    group: 'delivery_amount_max/<max>',
    type: 'float',
    label: '!to',
    has_delete: true,
    min: 0,
    bind: {
      status: 'max_amount_error && {"type":"error"}'
    }
  },
  max_amount_error: {
    sync: false,
    group: 'delivery_amount_max',
    type: "model",
    label: "!",
    bind: {
      value: "max_amount_gte > max_amount_lte",
      status: (model) =>{
        return model.max_amount_error && {"type":"error","message": Language.t("to_from_error")}
      }
    }
  },
});
var search_query = new Query({
  queryText: {
    icon: 'search',
    label: "!Search",
    hidden: true,
  },
});

var area_query = new Query({
    delivery_regions: {
    group: "area",
    label: "!area",
    type: "enum-tree",
    dataSource: "ref_uz_region_lv4",
    order: -1,
  }
});

var green_query = new Query({
  green: {
    type: "entity",
    label: "!green_procedures",
    group: "green_procedures",
    has_del: true,
    dataSource: [{id: true, name: "yes"}, {id: false, name: "no"}],
    hidden: () => !Settings.procedures?._green
  },
});

export default {
  props: ['type', 'query'],
  data() {
    return {

    businessDataSource: new DataSource({
              limit: 9,
              query: new Query({
                is_national: {
                  value: false,
                  hidden: true,
                  sync: false,
                },
              }, [ktru_product, ad_query, search_query, green_query, area_query]), 
              store: new RefStore({
                ref: 'ref_online_shop_public',
                injectQuery: (params) => {
                  params.fields = ["green","product","unit","debug_info","id","publicated_at","status","name","price","close_at","totalcost","currency", "amount","min_amount","images","owner_legal_area_id","product_name","remain_time"]
                  
                  params.filters.price_error = undefined;
                  params.filters.min_amount_error = undefined;
                  params.filters.max_amount_error = undefined; 
                  params.filters = params.filters || {}; 
                  params.filters.is_comm_shop = true;
                  return params;
                },
              }),
              template: 'template-shop'
            })

    };
  },
  methods: {
    addToCart(ids, proc_key) {

      this.$wait(async () => {
        let { error, data } = await Http.api.rpc("ref", {
          op: "create_many",
          ref: "ref_cart",
          data: {
            id: ids,
            proc_key: proc_key
          }
        });
        if (error)
          return Vue.Dialog.MessageBox.Error(error)

        this.source[proc_key].checkedItems = [];
        ids, this.source[proc_key].items.filter((item) => {
          return ids.includes(item.id)
        }).forEach(item => {
          item.cart_proc_keys = item.cart_proc_keys || [];
          item.cart_proc_keys.push(proc_key);
        })

        let message = data?.message

        if (await Vue.Dialog.MessageBox.Question(`${message || ''} ${Language.t('go_to_cart')}?`) == Vue.Dialog.MessageBox.Result.Yes) {

          this.$router.push({ path: '/workspace/cart' })
        }
      })
    }
  },
template: `
  <ui-data-view type='tile' :dataSource='businessDataSource'>
    <template v-slot:filter>
      <ui-filter :query='businessDataSource.query' />
    </template>

    <template v-slot:item="props">
      <widget-shop type='business' :props='props' :item='props.item' />
    </template>
  </ui-data-view>
`


};
