import Vue from "vue"

const Component = {
  props: ["model"],
  template: `
        <ui-data-view-item :model='model'>
            
            <template slot='header'>
              <div>
                <span v-if='$develop.content_debug'>№{{ model.id }}</span>
              </div>
            </template>
            
            <template slot='title'>
                <span>{{model.user_name}}</span>
            </template>

            <template slot='sub_title'>
            </template>

            <template slot='description'>
            </template>

            <template slot='props'>
              <div v-if='model.user_email'>
                <label>{{$t('email')}}:</label>
                <div><a :href="'mailto:' + model.user_email">{{model.user_email}}</a></div>
              </div>
            </template>
        
        </ui-data-view-item>
    `,
}

Vue.component("template-company_request", Component)
