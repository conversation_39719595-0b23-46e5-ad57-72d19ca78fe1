import { Http } from "@iac/core";

export default Vue.Dialog("ParticipantsDlg", {
    props: ["model"],
    data: function () {
        return {
            columns: [
                { field: "name", style: 'width:100%;' },
                { field: "proposal_availability" },
                { field: "winners", style: 'text-align: right;' },
            ]
        }
    },
    computed: {
        _get_participants() {
            return this.model.access("get_participants");
        },
        _select_winners() {
            return this.model.access("select_winners");
        },
        _set_winner() {
            return this.model.access("set_winner");
        }
    },
    methods: {
        async select_winners(item) {
            await this.wait(async () => {
                let { error, data } = await Http.proc.rpc("select_winners", {
                    proc_id: this.model.id,
                    part_id: [item.id],
                    winners: !item.winner
                })
                if (error) {
                    return await Vue.Dialog.MessageBox.Error(error);
                }
                item.winner = !item.winner;
            })
        },
        async set_winner(item, key) {
            await this.wait(async () => {
                if (key == 'winner' && item.winner == true)
                    key = undefined;

                if (key == 'reserve' && item.reserved_winner == true)
                    key = undefined;

                let { error, data } = await Http.proc.rpc("set_winner", {
                    proc_id: this.model.id,
                    part_id: item.id,
                    key: key
                })
                if (!error) {
                    item.winner = false;
                    item.reserved_winner = false;
                    if (key && key == "winner") {
                        item.winner = true;
                    } else if (key && key == "reserve") {
                        item.reserved_winner = true;
                    }
                } else {
                    return await Vue.Dialog.MessageBox.Error(error);
                }
            })
        },
    },
    template: `
        <div>
            <header>{{$t('participants')}}</header>
            <main>
                <ui-data-grid class='participants' raw :dataSource='model.participants' :columns='columns'>
                    
                <router-link v-if='props.item.proposal_available' v-on:click.native='Close' slot='name' slot-scope='props' :to='{path: "/procedure/"+model.id+":"+props.item.id+"/core", query: $route.query}'>{{props.item.name}}</router-link>
                <div v-else>{{props.item.name}}</div>
                    
                    <template slot='winners' slot-scope='props'>
                        <div style='text-align: right; white-space: nowrap;'>
                            <ui-btn-group>
                                <ui-btn v-if='_select_winners' :type='"sm "+(props.item.winner ? "primary" : "default")' @click.native='select_winners(props.item)'>{{$t("to_next_stage")}}</ui-btn>
                                <ui-btn v-if='_set_winner' :type='"sm "+(props.item.winner ? "primary" : "default")' @click.native='set_winner(props.item,"winner")'>{{$t("set_winner")}}</ui-btn>
                                <ui-btn v-if='_set_winner' :type='"sm "+(props.item.reserved_winner ? "primary" : "default")' @click.native='set_winner(props.item,"reserve")'>{{$t("set_reserved_winner")}}</ui-btn>
                            </ui-btn-group>
                        </div>
                    </template>                
                </ui-data-grid>
            </main>
            <footer>
                <ui-btn type='secondary' @click.native='Close()'>{{$t('close')}}</ui-btn>
            </footer>
        </div>
    `
});
