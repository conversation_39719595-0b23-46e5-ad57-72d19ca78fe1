var get_token = async (domain)=>{
    try{
        let response = await fetch(domain, {
            method: 'POST',
            body: JSON.stringify({  
                method: "token",
                params:{
                    client_id: "af36f6cbc",
                    grant_type: "debug",
                    user_id: 1        
                }
            }),
            headers: {
                'Content-Type': 'application/json;charset=utf-8',
            }, 
        });
    
        return await response.json()
    }catch(e){}
    return {result: {}}
}

var refresh_token = async (domain, token) =>{
    try{
        let response = await fetch(domain, {
            method: 'POST',
            body: JSON.stringify({  
                method: "token",
                params:{
                    client_id: "af36f6cbc",
                    grant_type: "refresh_token",
                    refresh_token: token        
                }
            }),
            headers: {
                'Content-Type': 'application/json;charset=utf-8',
            }, 
        });
    
        return await response.json()
    }catch(e){}
    return {result: {}}
}

var check_jwt = async (domain1, domain2)=>{

    domain1 = `https://api.${domain1}/auth`
    domain2 = `https://api.${domain2}/auth`

    let response;

    response = await get_token(domain1);
    if(!response?.result?.refresh_token){
        console.error("Ошибка получения токена:",domain1,response?.result?.refresh_token)
        return;
    }
 
    let {result } = await refresh_token(domain2, response?.result?.refresh_token);
    if(result?.refresh_token){
        console.log(domain2,"JWT token подошел");
    }else{
        console.log("OK:",domain2,"JWT token НЕ подошел");
    }    
}


var check = async (domain1)=>{
    console.log("Донор JWT токена:",domain1,'\n');
    ["kg-test.xt-xarid.uz","torg.kg","test.xt-xarid.uz:8443","xt-xarid.uz"].forEach((domain2)=>{
        if(domain1 != domain2)
        check_jwt(domain1, domain2)
    })
}

check("kg-test.xt-xarid.uz");