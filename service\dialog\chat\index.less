.iac-dialog {

  &_modal_box-content {

    &--tender-chat {
      height: 90vh;
      overflow: hidden;
    }
  }

  &-body {

    &--tender-chat {
      margin-top: 0 !important;
      height: 100%;
    }
  }
}

.tender-chat {
  display: flex;
  margin: 0 -24px;
  height: 100%;
  font-size: 14px;
  line-height: 1.45;
  
  &__messages {
    overflow: auto;
  }

  &__messages-block {
    position: relative;
    margin-top: 30px;
    border-top: 1px solid #f3f3f3;
  }

  &__messages-date {
    position: sticky;
    top: 30px;
    font-size: 13px;
    text-align: center;
    line-height: 1.3;
    text-transform: uppercase;
    z-index: 1;
    transform: translateY(-50%);

    .iac-date {
      display: inline-block;
      padding: 8px 12px;
      background-color: #fff;
      color: fade(#201D1D, 40%);
      box-shadow: 0px 0px 32px rgba(198, 198, 198, 0.25);
      border-radius: 100px;
    }
  }

  &__left {
    width: 30%;
    height: 100%;
    overflow: auto;
  }

  &__right {
    display: flex;
    left: 30%;
    width: 70%;
    height: 100%;
    flex-grow: 1;
    flex-direction: column;
  }

  &__form {
    margin-top: auto;
    padding: 20px 24px;
  }

  &__textarea {
    border: 1px solid #ededed;
    padding: 12px 38px 12px 16px;
    max-height: 266px;
    font-size: 14px;
    color: #201d1d;
    line-height: 1.45;
    border-radius: 4px;
    overflow: auto;

    &:empty::before {
      content: attr(data-placeholder);
      opacity: 0.6;
    }

    &:focus {
      border-color: @brand-primary;
      outline-color: @brand-primary;
    }
  }

  &__submit {
    position: absolute;
    right: 41px;
    bottom: 33px;
    border: none;
    width: 21px;
    background-color: transparent;
    background-image: url("data:image/svg+xml,%3Csvg width='21' height='18' viewBox='0 0 21 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.00999999 18L21 9L0.00999999 0L0 7L15 9L0 11L0.00999999 18Z' fill='%23009AB8'/%3E%3C/svg%3E");
    background-position: center left;
    background-repeat: no-repeat;
    text-indent: -9999px;
    cursor: pointer;
    overflow: hidden;
  }
}

.chat-groups {

  &__item {
    display: flex;
    border-bottom: 1px solid #ededed;
    padding: 16px 20px;
    color: #919191;
    cursor: pointer;
    justify-content: space-between;

    &--active {
      border-color: #009ab8;
      background-color: #009ab8;
      color: #fff;

      .chat-groups__title  {
        color: #fff;
      }
    }
  }

  &__title {
    margin: 0 0 4px;
    font-size: inherit;
    color: #201d1d;
  }

  &__message {
    line-height: 1.2;

    p {
      margin: 0;
    }
  }

  &__right {
    text-align: right;
  }

  &__unread {
    display: inline-block;
    padding: 4px;
    min-width: 24px;
    font-weight: 500;
    background-color: #2596CC;
    color: #fff;
    text-align: center;
    line-height: 1.2;
    border-radius: 50%;
  }

  &__date {
    display: block;
    margin-bottom: 12px;
  }
}

.message {
  width: 50%;
  margin-bottom: 16px;
  padding: 0 24px;

  &__author {
    margin: 0 0 4px;
    font-size: inherit;
    font-weight: 500;
    color: #201d1d;
  }

  &__text {
    margin: 0 0 4px;
    padding: 6px 12px;
    background-color: #f7f7f7;
    color: #201d1d;
    border-radius: 4px;

    p {
      margin: 0;
    }
  }

  &__time {
    line-height: 1.2;
    text-align: right;
  }

  &--own {
    margin-left: auto;

    .message__text {
      position: relative;
      background-color: #009ab8;
      color: #fff;

      &::after {
        content: '';
        position: absolute;
        bottom: -8px;
        right: 0;
        border: 6px solid transparent;
        border-top-color: #009ab8;
        border-right-color: #009ab8;
      }
    }

    .message__time {
      text-align: left;
    }
  }
}

.chat-header {
  padding: 12px 24px;
  border-bottom: 1px solid #ededed;
  background-color: #fcfcfc;

  &__title {
    margin-bottom: 4px;
    font-size: 18px;
    font-weight: 500;
    color: #201d1d;
    line-height: 1.35;
  }

  &__number {
    margin-bottom: 4px;
    color: #919191;
    line-height: 1.2;
  } 
}

.greeting {
  display: flex;
  color: #919191;
  align-items: center;
  justify-content: center;
  flex-grow: 1;

  &__text {
    padding-bottom: 53px;
    text-align: center;

    &::before {
      content: '';
      margin: 0 auto 32px;
      width: 104px;
      height: 104px;
      display: block;
      background-image: url("data:image/svg+xml,%3Csvg width='104' height='104' viewBox='0 0 104 104' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg opacity='0.15'%3E%3Cpath d='M78.1014 65.4929C77.2599 65.4929 76.578 66.1748 76.578 67.0163V84.1952C76.578 86.7151 74.5276 88.7655 72.0077 88.7655H25.8984C25.4944 88.7655 25.107 88.926 24.821 89.2115L15.2343 98.7986V90.2889C15.2343 89.4474 14.5525 88.7655 13.7109 88.7655H7.61717C5.09721 88.7655 3.04687 86.7151 3.04687 84.1952V39.5077C3.04687 36.9878 5.09721 34.9374 7.61717 34.9374H19.8047C20.6462 34.9374 21.3281 34.2555 21.3281 33.414C21.3281 32.5725 20.6462 31.8906 19.8047 31.8906H7.61717C3.41696 31.8906 0 35.3077 0 39.5077V84.1952C0 88.3954 3.41696 91.8124 7.61717 91.8124H12.1875V102.476C12.1875 103.092 12.5586 103.648 13.1279 103.884C13.3164 103.962 13.5143 104 13.7105 104C14.107 104 14.4966 103.845 14.7883 103.554L26.5293 91.8124H72.0077C76.2079 91.8124 79.6249 88.3954 79.6249 84.1952V67.0163C79.6249 66.1748 78.943 65.4929 78.1014 65.4929Z' fill='%23201D1D'/%3E%3Cpath d='M70.2811 31.4843C71.1225 31.4843 71.8046 30.8022 71.8046 29.9609C71.8046 29.1195 71.1225 28.4374 70.2811 28.4374C69.4398 28.4374 68.7577 29.1195 68.7577 29.9609C68.7577 30.8022 69.4398 31.4843 70.2811 31.4843Z' fill='%23201D1D'/%3E%3Cpath d='M64.1874 31.4843C65.0287 31.4843 65.7108 30.8022 65.7108 29.9609C65.7108 29.1195 65.0287 28.4374 64.1874 28.4374C63.346 28.4374 62.6639 29.1195 62.6639 29.9609C62.6639 30.8022 63.346 31.4843 64.1874 31.4843Z' fill='%23201D1D'/%3E%3Cpath d='M58.0936 31.4843C58.935 31.4843 59.6171 30.8022 59.6171 29.9609C59.6171 29.1195 58.935 28.4374 58.0936 28.4374C57.2523 28.4374 56.5702 29.1195 56.5702 29.9609C56.5702 30.8022 57.2523 31.4843 58.0936 31.4843Z' fill='%23201D1D'/%3E%3Cpath d='M96.3826 0H31.9921C27.7919 0 24.3749 3.41716 24.3749 7.61717V52.3046C24.3749 56.5048 27.7919 59.9217 31.9921 59.9217H77.4705L89.2117 71.6631C89.5034 71.9546 89.893 72.1092 90.2895 72.1092C90.4857 72.1092 90.6836 72.0712 90.8721 71.9932C91.4414 71.7576 91.8125 71.2019 91.8125 70.5858V59.9217H96.3828C100.583 59.9217 104 56.5048 104 52.3046V7.61717C104 3.41716 100.583 0 96.3826 0ZM100.953 52.3046C100.953 54.8245 98.9026 56.8749 96.3826 56.8749H90.2889C89.4473 56.8749 88.7654 57.5568 88.7654 58.3983V66.908L79.1788 57.3211C78.893 57.0355 78.5056 56.8751 78.1014 56.8751H31.9921C29.4721 56.8751 27.4218 54.8247 27.4218 52.3048V7.61737C27.4218 5.09741 29.4721 3.04707 31.9921 3.04707H96.3826C98.9026 3.04707 100.953 5.09741 100.953 7.61737V52.3046Z' fill='%23201D1D'/%3E%3C/g%3E%3C/svg%3E");
      background-position: center;
      background-repeat: no-repeat;
    }
  }
}