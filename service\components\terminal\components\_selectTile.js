export default Vue.Dialog({
    props: ["tiles"],
    data: function(){
        return {
            items: this.tiles.map((tile)=>{
                return {
                    ...tile,
                    checked: true,
                }
            })
        }
    },
    computed: {
        select_items(){
            return this.items.filter((item)=>{
                return item.checked
            });
        },
        enable_add(){
            return this.select_items.length>0;
        }
    },
    methods: {
        add(){
            this.Close(this.select_items)
        }
    },
    template: `
        <div>
            <main>
                <div v-for='item in items'>
                    <label>
                        <input v-model="item.checked" type='checkbox' /> {{item.tile}}
                    </label>
                </div>
            </main>
            <footer>
                <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('Close')}}</ui-btn>
                <ui-btn type='primary' :disabled='!enable_add' v-on:click.native='add'>{{$t("add")}}</ui-btn>
            </footer>
        </div>
    `
})