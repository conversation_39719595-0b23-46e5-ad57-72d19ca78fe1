import Vue from 'vue';
import { Settings } from '@iac/kernel'

export function addNull(num) {
  if (num < 10) {
    return `0${num}`;
  }
  return num;
}

var Component = {
  props: {
    date: String,
    alwaysHour: Boolean,
    forGroup: Boolean,
    full: Boolean,
    fullWithSeconds: Boolean,
    withoutTime: Boolean,
    withMonthName: Boolean,
    timeZone: {
      type: String,
      default: 'global',
    },
  },
  computed: {
    display() {

      if (!this.date) {
        return '';
      }
      
      const date = new Date(this.date);
      const today = new Date();
      if (this.timeZone === 'global') {
        let tz = 300;
        if(Settings._country == "KG"){
          tz = 360;
        }

        const offset = date.getTimezoneOffset() + tz;
        date.setMinutes(date.getMinutes() + offset);
        today.setMinutes(today.getMinutes() + offset);
      }

      if (this.fullWithSeconds) {
        return {
          date: `${addNull(date.getDate())}.${addNull(date.getMonth() + 1)}.${date.getFullYear()}`,
          time: `${addNull(date.getHours())}:${addNull(date.getMinutes())}:${addNull(date.getSeconds())}`,
        };
      }else if (this.full) {
        return {
          date: `${addNull(date.getDate())}.${addNull(date.getMonth() + 1)}.${date.getFullYear()}`,
          time: `${addNull(date.getHours())}:${addNull(date.getMinutes())}`,
        };
      } else if (this.withMonthName) {
        return {
          date: `${addNull(date.getDate())} ${this.$t('month_c_' + date.getMonth())} ${date.getFullYear()}`,
          time: undefined,
        };
      } else if (this.withoutTime) {
        return {
          date: `${addNull(date.getDate())}.${addNull(date.getMonth() + 1)}.${date.getFullYear()}`,
          time: undefined,
        };
      } else if (this.forGroup && date.getFullYear() === today.getFullYear()) {
        return {
          date: `${addNull(date.getDate())} ${this.$t('month_c_' + date.getMonth())}`,
          time: undefined,
        };
      }
      else if (this.alwaysHour || today.toDateString() === date.toDateString()) {
        return {
          date: undefined,
          time: `${addNull(date.getHours())}:${addNull(date.getMinutes())}`,
        };
      }
      return {
        date: `${addNull(date.getDate())}.${addNull(date.getMonth() + 1)}.${date.getFullYear()}`,
        time: undefined,
      };
    }
  },
  template: `
    <time class='iac-date'>
      <span class='date' v-if='display.date'><icon>date</icon>{{ display.date }}</span>
      <span class='time' v-if='display.time'><icon>time</icon>{{ display.time }}</span>
    </time>
  `,
};

Vue.component('iac-date', Component);
