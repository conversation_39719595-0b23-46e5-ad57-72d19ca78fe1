export default {
    name: "ebpInput",
    model: {
        prop: 'data',
        event: 'change'
    },
    props: {
        data: {
            type: Object,
            required: true
        },
        label: String,
        placeholder: String,
        disabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {}
    },
    methods: {
        onChangeInput(newValue) {
            this.change(newValue)
        },
        change(newValue) {
            this.$emit('change', newValue)
        }
    },
    template: `
    <div class="iac--ebp-input">
        <label>{{label}}</label>
        <input v-model="data"
            @change="e=>onChangeInput(event.target.value)"
            :placeholder="placeholder"
            :disabled="disabled"/>
    </div>
    `
}