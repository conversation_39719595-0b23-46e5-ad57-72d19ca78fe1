import {Entity} from '@iac/data'
import hljs from 'highlight.js';

let examples = [
    {
        title: "Горизонтальные",
        description: "В данном примере для каждой пары филдов используется отдельная группа с горизонтальной разметкой",
        code: {
            field_1: {
                group: "!-row_1",
                label: "Гос. номерный знак"
            },
            field_2: {
                group: "!-row_1",
                label: "Марка/модель"
            },
            field_3: {
                group: "!-row_2",
                label: "Гот выпуска"
            },
            field_4: {
                group: "!-row_2",
                label: "Тип кузова"
            },

            massa_1: {
                group: "!-row_3/<massa_1>",
                label: "Полная массса"
            },
            unit_1: {
                group: "!-row_3/<massa_1>",
                label: "!unit",
                type: "entity",
                dataSource: ["т","кг.","гр."]
            },

            massa_2: {
                group: "!-row_3/<massa_2>",
                label: "Масса без нагрузки"
            },
            unit_2: {
                group: "!-row_3/<massa_2>",
                label: "!unit",
                type: "entity",
                dataSource: ["т","кг.","гр."]
            }

        }
    },
    {
        title: "Горизонтальные через setting",
        description: `
Разметку из предыдущего примера можно реализовать через надстройку группы где мы указываем максимальное количество колонок.
Также в этом примере для групировок <> использует тип model. Данный подход позволяет указать label над всей группы
        `,
        code: {
            field_1: {
                group: "!top",
                label: "Гос. номерный знак"
            },
            field_2: {
                group: "top",
                label: "Марка/модель"
            },
            field_3: {
                group: "top",
                label: "Гот выпуска"
            },
            field_4: {
                group: "top",
                label: "Тип кузова"
            },

            field_5: {
                group: "top",
                label: "Полная массса",
                type: "model",
                fields: {
                    massa: {
                        group: "<massa>",
                        label: "!Масса"
                    },
                    unit: {
                        group: "<massa>",
                        label: "!unit",
                        type: "entity",
                        dataSource: ["т","кг.","гр."]
                    }
                },
            },
            field_6: {
                group: "top",
                type: "model",
                label: "Масса без нагрузки",
                fields: {
                    massa: {
                        group: "<massa>",
                        label: "!Масса"
                    },
                    unit: {
                        group: "<massa>",
                        label: "!unit",
                        type: "entity",
                        dataSource: ["т","кг.","гр."]
                    }
                }
            },   
            field_7: {
                group: "top",
                label: "№ свидетельство о рег. авто",
            },
            field_8: {
                group: "top",
                label: "Дата выдачи свидетельство о рег. авто",
                type: "date"
            },    
   
            
            field_11: {
                group: "!bottom",
                label: "Данные по GPS трекинг",
                readonly: true
            }, 

            field_12: {
                group: "bottom",
                label: "-Файлы транспорта",
                type: "file"
            },

            row_setting: {
                group: "top",
                type: "setting",
                attr: {
                    class: "horizontal horizontal_2"
                }
            }
        }
    },
    {
        description: `
В данном примере размер поля уменьшен через css что наглядно показывает как label модели отображается над дочерними элементами        
        `,
        code: {

            field_2: {
                group: "!core",
                label: "ПИНФЛ",
            },
            field_3: {
                group: "core",
                label: "Паспортные данные",
                type: "model",
                fields: {
                    serial: {
                        label: "!Серия",
                        group: "<pasport>",
                        attr: {
                            style: "max-width: 50px"
                        }
                    },
                    number: {
                        label: "!Номер",
                        group: "<pasport>",
                    }
                }

            },
            field_4: {
                group: "core",
                label: "ФИО",

            },
            field_5: {
                group: "core",
                label: "Дата рождения",
                type: "date",
            },
            field_6: {
                group: "core",
                label: "Эл. почта",
            },
            field_7: {
                group: "core",
                label: "Номер телефона",
                type: "phone"
            },

            field_8: {
                label: "-Фото паспорты",
                type: "file",
            },

            field_9: {
                label: "-Файлы транспорта",
                type: "file"
            },

            core_group_setting: {
                group: "core",
                type: "setting",
                attr: {
                    class: "horizontal horizontal_2"
                }
            }
        }
    },
]

examples.forEach((example)=>{
    example.model = new Entity({
        props: example.code
    })
})


export default {
    data: function () {
        return {
            examples: examples
        }
    },
    methods:{
        $h(code){
            return hljs.highlight(JSON.stringify(code, null, '  '), { language: "json" }).value;
        }
    },

    template: `
## Группировка

<ui-layout-group v-for='{title,description,code,model},index in examples'>

### {{title}}
> {{description}}

<ui-layout-tab :name='"example_"+index'>
<ui-layout-group label='Code'>

<pre><code v-html='$h(code)'/></pre>
</ui-layout-group>
<ui-layout-group label='Result'>
    <ui-layout :fields='model.fields'/>
</ui-layout-group>
</ui-layout-tab>
</ui-layout-group>


`
}