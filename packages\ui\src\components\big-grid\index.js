

export var BigGrid = {
    name: "ui-big-grid",
    props: {
        countItems: {}
    },
    data() {
        return {
            item_height: 40,
            items: Array(this.countItems).fill(null).map((item, index) => {
                return {
                    index: index,
                    id: index,
                    name: `Product #${index}`
                }
            }),
            activeItem: undefined,
            firstItem: 0,
            row: 0,

            fields: [
                {
                    name: "static1", label: "!",
                    type: 'layout-static',
                    value: `
[{tabitem}/group1]
Код товара : 16.10.21.119-00003 
Количество: 10 шт
Дополнительные свойства: свойство 1 
    свойство 2 
    свойство 3 
    свойство 4 

[{tabitem}/group2]
Срок гарантии на товар, работу или услуги: 100 д                   
Срок поставки товара, работы или услуги: 10 д      
Товар должен быть произведен не ранее: 2022 года               `
                }
            ]
        }
    },
    computed: {
        count() {
            return this.items.length;
        },
        content_styles() {

            let height = this.count * this.item_height

            return {
                "min-height": `${height}px`,
                position: "relative",
            }
        },
        hideitems_styles() {
            let item_height = this.item_height;

            /* let active = this.$refs.active;
             active = active && active[0] 
             if(active){
                 let active_coords = active && active.getBoundingClientRect();
                 if(active_coords.top < 0){
                     item_height += active_coords.top
                     if(item_height <0)
                     item_height = 0
                 }
             }*/


            return {
                height: `${this.hide_items * item_height}px`,
                border: "0px solid"
            }
        },
        view_items() {
            let items = this.items.slice(this.firstItem, this.firstItem + 30)
            if (this.activeItem < this.firstItem) {
                items.unshift(this.items[this.activeItem])
            }
            return items
        },
        hide_items() {
            if (this.activeItem < this.firstItem) {
                return this.firstItem - 1;
            }
            return this.firstItem;
        },
        details() {

            let items_count = this.items.length;

            let view_count = 30

            let items = this.items.slice(this.firstItem, this.firstItem + view_count)
            let details = [
                //100,
                //[this.items[1]],
                //200,
                items,
                //100
            ]

            if (this.activeItem == undefined) {
                if (this.firstItem)
                    details.unshift(this.firstItem)

                let end = items_count - this.firstItem - items.length;
                if (end > 0)
                    details.push(end);
            } else if (this.activeItem < this.firstItem) {
                if (this.firstItem - this.activeItem - 1)
                    details.unshift(this.firstItem - this.activeItem - 1)
                details.unshift([this.items[this.activeItem]])
                if (this.activeItem)
                    details.unshift(this.activeItem)

                let end = items_count - this.firstItem - items.length;
                if (end > 0)
                    details.push(end);
            } else if (this.activeItem > (this.firstItem + view_count - 1)) {
                details.unshift(this.firstItem)

                if (this.activeItem - (this.firstItem + view_count))
                    details.push(this.activeItem - (this.firstItem + view_count))
                details.push([this.items[this.activeItem]])

                let end = items_count - this.activeItem - 1;
                if (end > 0)
                    details.push(end);

            } else {
                details.unshift(this.firstItem)

                let end = items_count - this.firstItem - items.length;
                if (end > 0)
                    details.push(end);
            }


            return details;
        }
    },
    created() {
        window.addEventListener('scroll', this.recalc);
        window.addEventListener('resize', this.recalc);
        
    },
    mounted(){
        this.recalc();
    },
    destroyed: function () {
        window.removeEventListener('scroll', this.recalc);
        window.removeEventListener('resize', this.recalc);
    },
    methods: {
        recalc() {

            let active = this.$refs.active;
            active = active && active[0]
            let active_coords = active && active.getBoundingClientRect();
            let content = this.$refs.content
            let coords = content.getBoundingClientRect();

            if (active_coords && active_coords.top <= 0 && active_coords.bottom > 0 && this.activeItem >= this.firstItem) {
                this.firstItem = this.activeItem
                if(this.firstItem > (this.items.length-30))
                this.firstItem = this.items.length-30 > 0 ? this.items.length-30 : 0;
                return 
            }


            let top = coords.top
            //if(active_coords && active_coords.top <= 0){
            //    top = top+active_coords.height;
            //}

            if (this.activeItem < this.firstItem) {
                top = top + active_coords.height;

            }

            if (top <= 0) {
                this.firstItem = Math.floor((-top) / this.item_height);
                

            } else {
                this.firstItem = 0
            }
            if(this.firstItem > (this.items.length-30))
            this.firstItem = this.items.length-30 > 0 ? this.items.length-30 : 0;
        },
        recalc1() {

        }
    },
    template: `
        <div class='ui-big-grid'>
            <div>
                <table cellspacing="0" cellpadding="0" border="0" width='100%' style='position: absolute1; font-size: 14px; color: #333;'>
                    
                    <thead class='sticky' style='z-index: 100; background: #f6f6f6; position: sticky;'>
                        <tr style='height: 49px'>
                            <td colspan='2' style=' padding: 10px; border-bottom: 1px solid #ccc'>{{count}} {{ $t('product', { count: count }) }}</td>
                        </tr>
                        <tr>
                            <td style=' padding: 10px; border-bottom: 1px solid #ccc;'>name</td>
                            <td style=' padding: 10px; border-left: 1px solid #ccc; border-bottom: 1px solid #ccc;'>actions</td>
                        </tr>
                    </thead> 

                    <tbody ref='content' :key='firstItem'>
                    <template v-for='_items in details'>
                        <template v-if='_items.map'>
                            <template :key='_item.index' v-for='_item in _items'>
                                <tr>
                                    <td style='height: 40px; border-bottom: 1px solid #ddd; width: 100%; padding: 8px;' v-on:click='activeItem =  activeItem != _item.index  ? _item.index : undefined'>{{_item.name}}</td>
                                    <td style='height: 40px; border-left: 1px solid #ddd; border-bottom: 1px solid #ddd'></td>
                                </tr>
                                <tr ref='active' v-if='activeItem == _item.index'>
                                    <td style='padding: 20px; border-bottom: 1px solid #ddd;' colspan='2'>
                                        <ui-layout :fields='fields' />
                                    </td>
                                </tr>
                            </template>
                        </template>

                        <tr v-else>
                            <td :style='{"height": (_items*item_height)+"px"}'></td>
                        </tr>

                    </template>
                    </tbody>
                    <tbody style='position: sticky; bottom: 0px; z-index: 100; background: #f6f6f6;'>
                        <tr>
                            <td style=' padding: 10px; border-top: 1px solid #ccc;'>Count: {{count}}</td>
                            <td style=' padding: 10px; border-left: 1px solid #ccc; border-top: 1px solid #ccc;'>Total</td>
                        </tr>
                    </tbody> 
                </table>
            </div>
        </div>
    `,
}