import { Property, DataSource } from '@iac/data'
import { Entity } from '@iac/data'
import { Http, Event, Language, Action } from '@iac/core'
import { Config, Context } from '@iac/kernel'


class ModelProperty extends Property {
    constructor(context) {
        super(context)
    }

    async Upload(value){
        return await this.model.Upload(value);
    }

    get propertyModel() {
        return ModelProperty;
    }

    get meta() {
        if (this.type == 'file' && this.value) {
            return {
                ...this._meta,
                url: (value) => {
                    if (!value.id)
                        return;
                    return `${Config.api_server}/file/${value.id}`
                },
                url_mod: async (value) => {
                    if (!Context.User.access_token && this.attr?.access_unauthorized_denied) {
                        Vue.Dialog.MessageBox.Info(Language.t("view_file.unauthorized"))
                        return;
                    }
                    let url = this.meta.url(value);
                    if (!url)
                        return;

                    await Context.User.refreshToken();
                    return `${url}?token=${Context.User.access_token}`
                }
            }
        }
        return this._meta
    }
}

export default class ProcedureProperty extends ModelProperty {

    constructor(context) {
        super(context)

        if (this.type == "setting")
            return;

        //this._label = undefined;
        this.system = context.attributes.system;
        this.rights = context.attributes.rights_list

        let $this = this;
        this._actions = [
            {
                label: "delete_field",
                btn_type: "danger",
                hidden: () => {
                    if (this.system || !this.access("del_field"))
                        return true;
                },
                handler: async () => {
                    this.wait = true;
                    await this.model.delField(this);
                    this.wait = false;
                }
            },
            {
                label: "edit",
                type: "action",
                hidden: () => {
                    if (this.system || !context.attributes.offer_dub || !context.attributes.offer_required || !this.access("upd_field"))
                        return true;
                },
                handler: async () => {
                    await this.model.requirement({
                        size: "lg",
                        id: this.name,
                        method_marks: this.model.method_marks,
                        group: this.group,
                        order: this.order,
                        proc_id: this.model.id,
                        get category() {
                            let category = $this.group?.split("/")[0]
                            if ($this.model.procedure_actions) {
                                let procedure_actions = $this.model.procedure_actions;
                                if (procedure_actions)
                                    for (let item of procedure_actions) {
                                        if (item.group == category) {
                                            category = item.category || item.group
                                            break;
                                        }
                                    }
                            }
    
                            return category;
                        }
                    })
                }
            },

        ].concat(context.attributes.actions || [])
    }

    setAttributes(attributes) {
        if (attributes.type) {
            switch (attributes.type) {
                case "struct":
                    attributes.type = 'model'
                    break;
                case "ref":
                    attributes.type = 'entity'
                    attributes.dataSource = attributes.type_def;
                    delete attributes.type_def;
                    break;
                case "enum-tree":
                case "enum":
                    attributes.dataSource = attributes.dataSource || attributes.type_def;
                    delete attributes.type_def;
                    break;
                case "time":
                    if (attributes.type_def && Array.isArray(attributes.type_def)) {
                        attributes.range = `${attributes.type_def[0]}-${attributes.type_def[1]}`;
                    } else {
                        attributes.range = attributes.type_def;
                    }
                    delete attributes.type_def;
                    break;
                case "range":
                    if (attributes.type_def) {
                        attributes.min = attributes.type_def[0]
                        attributes.max = attributes.type_def[1]
                    }
                    delete attributes.type_def;
                    break;
                case "boolean":
                    attributes.type = "entity";
                    attributes.has_del = true;
                    attributes.dataSource = [
                        { id: false, name: "no" },
                        { id: true, name: "yes" }
                    ]
                    delete attributes.type_def;
                    break;
                case "action":

                    break;
            }
        }
        attributes.label = attributes.label || attributes.name;

        if(this.system != undefined)
            this.system = attributes.system;
        this.rights = attributes.rights_list || this.rights_list

        super.setAttributes(attributes);
    }

    get readonly() {

        if (["static", "info", "action"].indexOf(this.type) >= 0)
            return this._readonly;

        if (this.rights && this.rights.upd_value)
            if (!this.access("upd_value"))
                return true;
            else
                return false;
        if (!this.access("upd_field"))
            return true;

        return this._readonly;
    }
    set readonly(value) {
        this._readonly = value;
    }

    get actions() {

        return this._actions;
    }
    set actions(value) {

        this._actions = value;
    }

    access(name) {

        if (this.rights && this.rights[name])
            if (this.rights[name] == "granted")
                return true;
            else
                return false;

        return this.model.access(name);
    }

}