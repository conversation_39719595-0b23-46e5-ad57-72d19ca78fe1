import {RemoteStore, DataSource, Query} from '@iac/data';
import { Config, Context } from '@iac/kernel';
import InsertBcvDialog from './_dlg_insert_bcv';

export default {
  data() {
    return {
      bcvColumns: [
        'amount',
        'date',
        'description'
      ],
      bcvDetails: new DataSource({
        query: new Query({
          op: 'read',
          ref: 'get_base_calculated_values'
        }),
        store: new RemoteStore({
          method: 'common_ref'
        })
      }),
      user: Context.User
    }
  },
  methods: {
    async openInsertBcvDialog() {
      if (await InsertBcvDialog.Modal()) this.bcvDetails.reload();
    }
  },
  template: `
  <iac-access :access='$policy.common_bcv_list || $policy.common_bcv_create'>
    <iac-section>
      <ol class='breadcrumb'>
        <li><router-link to='/'>{{$t('home')}}</router-link></li>
        <li>{{$t('nav.bcv')}}</li>
      </ol>
      <div class='title'>
        <h2>{{$t('nav.bcv_full')}}</h2>
        <div v-if='$policy["common_bcv_create"]'><ui-btn type='primary' v-on:click.native='openInsertBcvDialog'>{{$t('insert_bcv')}}</ui-btn></div>
      </div>
    </iac-section>
    <iac-section>
      <ui-data-grid :dataSource='bcvDetails' :columns='bcvColumns'>
        <template slot='date' slot-scope='props'><div  style='text-align: center;'><iac-date :date='props.item.date' withoutTime/></div></template>
        <template slot='amount' slot-scope='props'><div style='text-align: right;'><iac-number :value='props.item.value' delimiter=' ' part='2'/></div></template>
      </ui-data-grid>
      
    </iac-section>
  </iac-access>
  `
}