export default {
  name: 'specCreator',
  props: {
    currentItem: {
      type: Object,
      required: true
    },
    onSelect:{
      type:Function,
      required:true
    }
  },
  data() {
    return {}
  },
  methods: {
    createSpecAndSendIt() {
      const spec = this.createBurseProductSpec()
      this.onSelect(spec)
    },
    createBurseProductSpec() {
      const { id, name, skp, tnved, groups, unit, master_unit, properties } = this.currentItem
      const raw = {
        id,
        name,
        skp,
        tnved,
        groups: groups.map(({ id, parent_id, name }) => ({ id, parent_id, name })),
        unit: { name: unit.name, ratio: unit.ratio },
        master_unit: { name: master_unit.name, ratio: master_unit.ratio },
        properties: properties.map(({ name, value }) => ({ name, value: value ? { name: value.name } : value }))
      }

      return {
        category: { code: "", id: "", uid: "", title: "" },
        product_id: id,
        product_uid: id,
        product_name: name["ru-RU"] || this.currentItem.temporary.currentName || name[Object.keys(name)[0]],
        type: 0,
        product_properties: properties.filter(prop=>prop.value).map(prop => ({
          val_numb: 0,
          val_name: prop.value.name["ru-RU"] || prop.value.temporary.currentName || prop.value.name[Object.keys(prop.value.name)[0]],
          value_id: 0,
          prop_numb: 0,
          prop_name: prop.name["ru-RU"] || prop.temporary.currentName || prop.name[Object.keys(prop.name)[0]],
          prop_id: 0
        })),
        unit: unit.name["ru-RU"] || unit.temporary.currentName || unit.name[Object.keys(unit.name)[0]],
        unit_ratio: unit.ratio,
        master_unit: master_unit.name["ru-RU"] || master_unit.temporary.currentName || master_unit.name[Object.keys(master_unit.name)[0]],
        master_unit_ratio: master_unit.ratio,
        skp,
        tnved,
        groups: groups.map(({ id, name, parent_id, temporary }) => ({ id, parent_id, name: name["ru-RU"] || temporary.currentName || name[Object.keys(name)[0]] })),
        raw
      }
    }
  },
  template: `
    <div class="iac--ktru__spec_creator" v-if="currentItem">
      <h3>{{$t('specification_creation_for_code_for').replace('_____', currentItem.temporary.currentName)}}</h3>
      <div class="iac--ktru__spec_creator-about">
        <div>{{$t('product_name')}}</div>
        <div v-for="(group,index) in currentItem.groups" :key="index">{{group.temporary.currentName+' / '}}</div>
        <div>{{currentItem.temporary.currentName}}</div>
      </div>
      <div class="iac--ktru__spec_creator-about">
        <div>{{$t('tnved')}}</div>
        <div>{{currentItem.tnved}}</div>
      </div>
      <div class="iac--ktru__spec_creator-about">
        <div>{{$t('skp')}}</div>
        <div>{{currentItem.skp}}</div>
      </div>
      <div class="iac--ktru__spec_creator-about">
        <div>{{$t('master_unit')}}</div>
        <div>{{currentItem.master_unit.temporary.currentName}} ({{currentItem.master_unit.ratio}})</div>
      </div>
      <div iac--ktru__spec_creator-selectors>
        <label><span>*</span>{{$t('unit')}}</label>
          <select v-model="currentItem.unit">
          <option v-for="unit in currentItem.units" :value="unit">{{unit.temporary.currentName}} ({{unit.ratio}})</option>
        </select>
      </div>
      <div class="iac--ktru__spec_creator-divider"/>
      <div class="iac--ktru__spec_creator-selectors">
        <div v-for="prop in currentItem.properties">
          <label>{{prop.temporary.currentName}}</label>
          <select v-model="prop.value">
            <option :value="undefined">{{$t('not_selected')}}</option>
            <option v-for="val in prop.values" :value="val">{{val.temporary.currentName}}</option>
          </select>
        </div>
      </div>
      <div class="iac--ktru__spec_creator-footer">
        <button @click="createSpecAndSendIt">{{$t('add')}}</button>
      </div>
    </div>
    `
}