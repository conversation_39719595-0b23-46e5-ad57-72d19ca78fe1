export var Button = {
    name: "ui-btn",
    props: {
        type: String,
        active: Boolean,
        title: {
            type: String,
        }
    },
    computed: {
        classes() {
            return [
                (() => {
                    if (this.type)
                        return this.type.split(' ').map((type) => {
                            return `ui-btn-${type}`
                        }).join(" ");
                })(),
                {
                    active: this.active
                }]
        },
        _title() {
            return this.title || this.$slots.default.map((item) => {
                return item.text
            }).filter((title) => {
                return title;
            }).toString();
        }
    },
    template: `<button :title='_title' class='ui-btn' v-bind:class="classes"><slot/></button>`
}