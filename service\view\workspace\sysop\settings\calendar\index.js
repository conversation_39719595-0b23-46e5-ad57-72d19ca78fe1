import { DataSource, ArrayStore } from '@iac/data'
import Model from './model'
import { Http } from '@iac/core'

var calendarMonth = {
    props: ["month"],
    methods: {
        async onDay(day) {
            this.$wait(async () => {
                await day.set({
                    is_work_day: !day.is_work_day
                })
            })

        },
    },
    template: `
        <div class='ui-ctrl-date'>
            <div class='month'>
                <span class='value'>{{$t("month_"+month.index)}}</span>
            </div>
            <div class="weeks">
                <span>{{$t('Mon')}}</span>
                <span>{{$t('Tue')}}</span>
                <span>{{$t('Wed')}}</span>
                <span>{{$t('Thu')}}</span>
                <span>{{$t('Fri')}}</span>
                <span>{{$t('Sat')}}</span>
                <span>{{$t('Sun')}}</span>
            </div>  
            <div class='days'>
                <span v-bind:class='{"red" : day && !day.is_work_day}' v-for='day in month.index_days'>
                    <span v-if='day' v-on:click='onDay(day)'>{{day.value || ''}}</span>
                </span>
            </div>             
        </div>
    `
}

export default {
    data: function () {
        return {
            year: 2025,
            model: undefined,
            error: undefined,
            dataSourceH: new DataSource({
                store: new ArrayStore({
                    data: Array(2100 - 2023).fill(null).map((year, index) => {
                        return {
                            id: 2023 + index,
                            name: 2023 + index
                        }
                    })
                })
            }),
        }
    },
    computed: {
        year_value() {
            return (this.year && this.year.id) || this.year
        }
    },
    watch: {
        year_value: {
            immediate: false,
            async handler(val, oldVal) {

                this.update_calendar()
            }
        }
    },
    mounted() {
        this.update_calendar()
    },
    methods: {
        update_calendar(year = this.year_value) {
            this.$wait(async () => {
                let { error, data } = await Model.get(year)
                this.error = error
                this.model = data
            })
        },
        offsetYear(offset) {
            this.year = this.year_value + offset;
            //this.update_calendar()
        },
        update_procedure_dates() {
            this.$wait(async () => {
                let { data, error } = await Http.api.rpc('update_procedure_dates');
                if (error) 
                    Vue.Dialog.MessageBox.Error(error);
                else if (data?.message)
                    Vue.Dialog.MessageBox.Success(data.message);
                else
                    Vue.Dialog.MessageBox.Success("Запрос отправлен");
            })
        }
    },
    components: {
        calendarMonth: calendarMonth
    },
    template: `
        <iac-access :access='$policy.system_page_edit'>
            <iac-section>
                <div style='display: flex;align-items: center;justify-content: space-between;'>
                <h2>{{ $t('nav.settings.calendar') }}:</h2>
                <ui-control-group>
                    <ui-btn type='primary' v-if='$policy.system_calendar_edit' v-on:click.native='update_procedure_dates'>
                        {{$t('Update_date_completion_procedures')}}
                    </ui-btn>
                    <ui-btn :disabled='year_value <= 2023' type='secondary' v-on:click.native='()=>{offsetYear(-1)}'>
                        <icon style='rotate: 90deg;'>arrow</icon>
                    </ui-btn>
                    
                    <ui-entity label='year' v-model='year' :dataSource='dataSourceH' />
                    <ui-btn :disabled='year_value >= 2099' type='secondary' v-on:click.native='()=>{offsetYear(1)}'>
                        <icon style='rotate: -90deg;'>arrow</icon>
                    </ui-btn>
                </ui-control-group>
                </div>
            </iac-section>
            <iac-section>
                <ui-error v-if='error' :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
                <div v-if='model' style='display: grid; grid-gap: 15px; grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));'>
                    <div style='background: #fff;width: 263px; border:1px solid #eee' :key='key' v-for='(month,key) in model.months'>
                        <calendar-month :month='month'/>
                    </div>
                </div>
            </iac-section>
        </iac-access>
    `,
}