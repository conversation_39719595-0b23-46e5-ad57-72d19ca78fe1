export var CheckBox = {
    name: "ui-checkbox",
    props: ["icon", "label", "status", "value", "readonly", "disabled", "name", "type", "required"],
    computed: {
        component_status() {
            if (typeof this.status == "string") {
                return {
                    type: this.status,
                    message: undefined
                }
            }
            return this.status;
        },
        classes() {
            return [
                (() => {
                    return this.component_status ? ["status", this.component_status.type] : '';
                })(),
                {
                    "disabled": this.disabled,
                    "readonly": this.readonly,
                    "wait": this.wait,
                    "required": this.required,
                    "checked": this.value,
                }
            ]
        },
    },
    methods: {
        update(event) {
            this.$emit('input', event.target.checked)
        }
    },
    template: `<div class='ui-checkbox' v-bind:class="classes">
        <label><input type='checkbox' :checked='value' @change="update"  
        :disabled="disabled || readonly"
        /><span v-html='$t(label)'/></label>
    </div>`
}
