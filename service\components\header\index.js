import {Context} from '@iac/kernel'
import NavTop from '../nav_top'

export default {
    data: function () {
        return {
            pageYOffset: undefined,
            user: Context.User,
        }
    },
    components: {
        NavTop:NavTop
    },
    mounted: function () {
        let body = document.getElementsByTagName('body')[0];
        if (!body)
            return;
        body.classList.add("header", "header_show")
        window.addEventListener('scroll', this.onBodyScroll);
    },
    destroyed: function () {
        let body = document.getElementsByTagName('body')[0];
        if (!body)
            return;
        window.removeEventListener('scroll', this.onBodyScroll)
        body.classList.remove("header", "header_show")
    },
    methods: {
        onBodyScroll(e) {
            let body = document.getElementsByTagName('body')[0];
            if (!body)
                return;

            if (this.pageYOffset != undefined)
                if (this.pageYOffset > window.pageYOffset) {
                    // Show header
                    body.classList.add("header_show")

                } else if (window.pageYOffset >= 72) {
                    // Hide header
                    body.classList.remove("header_show")
                }

            this.pageYOffset = window.pageYOffset;
        }
    },
    template: `
        <div class='iac-service-header'>
            <NavTop has_toggle />
        </div>
    `
}