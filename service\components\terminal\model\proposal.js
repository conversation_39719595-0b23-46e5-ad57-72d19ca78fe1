import { Http } from '@iac/core'
import { Entity, DataSource } from '@iac/data'
import { Settings,Context } from '@iac/kernel'
class BetModel extends Entity {
    constructor(context) {
        super(context)
        this.context = context;
        this.contract_id = context.contract_id
        this.company_id = context.company_id
        this.amount = context.amount
        this.price = context.price
    }
    props() {
        return {
            contract_id: {
                required: true,
                type: "number",
                label: "-exchange.contract",
                readonly: () => {
                    return this.context.contract_id
                },
                attr: {
                    react: true
                },
                onChange: () => {
                    this.company_id = undefined;
                },
                order: 1
            },
            company_id: {
                type: "entity",
                label: "-company",
                required: true,
                readonly: () => {
                    return !this.contract_id
                },
                dataSource: {
                    //query: {
                    //    proc_id: this.contract_id
                    //},
                    store: {
                        host: Http.proc,
                        method: "companies_for_proposal",
                        injectQuery: (params) => {
                            params.proc_id = Number(this.contract_id);
                            return params
                        }
                    }
                },
                order: 2
            },
            amount: {
                required: true,
                label: "-exchange.lot.count",
                type: "float",
                attr: {
                    react: true
                },
                order: 3
            },
            price: {
                type: "float",
                label: "-exchange.price.lot",
                suffix: Settings._default_currency,
                required: true,
                attr: {
                    react: true
                },
                order: 4
            }
        }
    }
}

// document.addEventListener('visibilitychange', () => {
//    console.log("document.visibilityState",document.visibilityState)
//});

class Proposal {
    constructor() {
        this.listeners_count = 0;
        this.channel = undefined;
        this._list = undefined;
        this.data = {};


    }

    get list() {
        if (!this._list) {
            this._list = Object.keys(this.data).map((key) => {
                return { ...this.data[key], id: key }
            }).sort((a, b) => {
                if (a.contract_id == b.contract_id) {
                    return b.price - a.price
                }
                return a.contract_id - b.contract_id
            })
        }
        return this._list;
    }

    set_data(data) {
        this.data = { ...this.data, ...data }
        this._list = undefined;
    }

    get add_bet() {
        if (!this.channel)
            return;
        if( !Context.Access.policy.exchange_exchange_exposition_bet)
            return;

        // exchange.trader.is_client_trader 
        // exchange.trader.is_broker_trader
        return async (context = {}) => {

            let model = new BetModel(context);
            Vue.Dialog.MessageBox.Form({
                fields: model.fields,
                onClose: (params) => {
                    return new Promise(async (resolve, reject) => {
                        this.channel.push("new_bet", [
                            params.contract_id,
                            params.company_id,
                            params.price,
                            params.amount
                        ])
                            .receive("ok", (data) => {
                                resolve({
                                    data: {
                                        ...params,

                                    }
                                })
                            })
                            .receive("error", async (response) => {
                                // response = [response]

                                let error;

                                if (!response) {
                                    error = {
                                        message: "Неизвестная ошибка"
                                    }
                                } else if (!Array.isArray(response)) {
                                    error = await DataSource.get("ref_terminal_errors").byKey(response)
                                } else if (Array.isArray(response)) {
                                    error = {
                                        code: ":bad_params",
                                        message: "Ошибка заполнения форм",
                                        data: []
                                    }
                                    for (let index in model.fields) {
                                        let field = model.fields[index];
                                        let error_info = await DataSource.get("ref_terminal_errors").byKey(response[index])
                                        if (error_info)
                                            error.data.push({
                                                name: field.name,
                                                message: error_info.message
                                            })
                                    }
                                }
                                if (!model.setError(error)) {
                                    resolve({
                                        error: error
                                    });
                                } else {
                                    resolve({});
                                }
                            })
                            .receive("timeout", (data) => {
                                resolve({
                                    error: {
                                        message: "timeout"
                                    }
                                });
                            })
                    })
                }
            })


        }
    }

    get delete_bet() {
        if (!this.channel)
            return;
        if( !Context.Access.policy.exchange_exchange_exposition_bet)
            return;
        return (id) => {
            return new Promise(async (resolve, reject) => {
                this.channel.push("drop_my_bet", id)
                    .receive("ok", (data) => {
                        resolve({

                        })
                    })
                    .receive("error", async (response) => {
                        resolve({
                            error: await DataSource.get("ref_terminal_errors").byKey(response)
                        })
                    })
            })
        }
    }

    join() {
        if (!this.channel && this.listeners_count && document.visibilityState == 'visible') {
            Http.proc.socket.join(`tp:exchange:proposal`, (channel) => {
                this.channel = channel;
                channel.on('add', (data = {}) => {
                    this.set_data(data);
                });
                channel.on('rm', (data = []) => {
                    data.forEach((key) => {
                        this.data[key] = undefined
                        delete this.data[key]
                    })
                    this._list = undefined;
                });

                channel.joinPush
                    .receive("ok", (data) => {
                        if (data) {
                            this.data = { ...data }
                            this._list = undefined;
                        }
                    })
                    .receive("error", (data) => {
                        this.channel = undefined
                    })
            },true)
        }
    }
    leave() {
        if (this.listeners_count <= 0 || document.visibilityState != 'visible') {
            if(this.channel)
                Http.proc.socket.leave_channel(this.channel);
            this.channel = undefined;
        }
    }

    watch() {
        this.listeners_count++;
        if (this.listeners_count) {
            document.addEventListener('visibilitychange', this.visibilitychange);
        }
        this.join()

    }
    unwatch() {
        this.listeners_count--;
        if (!this.listeners_count) {
            document.removeEventListener('visibilitychange', this.visibilitychange);
        }
        this.leave();
    }
    visibilitychange = (data) => {
        if (document.visibilityState == 'visible') {
            this.join();
        } else if (document.visibilityState == 'hidden') {
            this.leave();
        }
    }
}
export default new Proposal();