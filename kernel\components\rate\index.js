const RateComponent = {
    name: "iac-rate",
    props: {
        value: Number
    },
    data() {
        return {
            stars: Array(5).fill(0)
        }
    },
    template: `
    <div class='iac-rate' :title='value'>
        <span v-key='index' :class="{empty:index>=value}" v-for='star,index in stars' @click="$emit('change', index+1)">&#9733;</span>
    </div>
    `
}

Vue.component('iac-rate', RateComponent);

Vue.Fields.rate = { is: 'iac-rate' }