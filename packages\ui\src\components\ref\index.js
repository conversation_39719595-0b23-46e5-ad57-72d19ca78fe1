import DataSource from "../../../../data/src/data_source"

export var Ref = {
    name: "ui-ref",
    props: ["source", "value"],
    data: function () {
        return {
            dataSource: undefined,
            item: undefined
        }
    },
    watch: {
        value: {
            immediate: true,
            async handler(value) {
                if(!value) return;
                if (value && value.name) {
                    return this.item = {
                        exp: {
                            display: value.name
                        }
                    }
                }
                if(!this.dataSource){
                    this.dataSource = DataSource.get(this.source)
                }
                if (this.dataSource)
                    return this.item = await this.dataSource.byKey(value);
            }
        }
    },
    computed: {
        display() {
            return this.item && this.item.exp && this.item.exp.display || this.value
        }
    },
    template: `
        <span>{{display}}</span>
    `
}