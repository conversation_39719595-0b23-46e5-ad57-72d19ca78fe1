import Procedure from './procedure';
import { Develo<PERSON> } from '@iac/kernel'

@Procedure.Registration("cargo_procedure")
export default class Cargo_Procedure extends Procedure {

    constructor(context = {}) {
        super(context);
        this.hide_mark = true;
    }

    get procedure_actions(){
        return [
            { buttons: 2, group: 'tech_fields', order: 1000 },
        ]
    }

    static async init_context(context) {

        return {

        }
    }
}