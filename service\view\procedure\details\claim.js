import {DataSource,ArrayStore} from '@iac/data'

export default {
    props: ["model"],
    data: function(){
        return {
            dataSource: new DataSource({
                store: new ArrayStore({data:[
                    {
                        number: "№223",
                        theme: "Я не доволен площадка",
                        result: "Отказать по жалобе",
                        date: "",
                        status: ""
                    },
                    {
                        number: "№223",
                        theme: "Я не доволен площадка",
                        result: "Отказать по жалобе",
                        date: "",
                        status: ""
                    },
                    {
                        number: "№223",
                        theme: "Я не доволен площадка",
                        result: "Отказать по жалобе",
                        date: "",
                        status: ""
                    },
                    {
                        number: "№223",
                        theme: "Я не доволен площадка",
                        result: "Отказать по жалобе",
                        date: "",
                        status: ""
                    }
                ]})
            }),
            columns: [
                "number",
                { field: "theme", style: "width:100%" },
                "result",
                "date",
                "status"
            ]
        }
    },
    methods: {

    },
    template: `
        <div>
            <ui-data-grid :dataSource='dataSource' :columns='columns'>
                <template slot='result' slot-scope='props'>
                    <span style='white-space: nowrap;'>{{props.item.result}}</span>
                </template>
            </ui-data-grid>
        </div>
    `
}
