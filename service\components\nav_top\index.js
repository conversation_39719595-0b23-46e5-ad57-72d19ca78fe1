import Brand from './../brand'
import Account from './account'
import Language from './../language'
import Clock from './../clock'
import ServiceMenu from '../../components/navigation/menu';
import VisualImpared from '../visual_impared';
import NavLinks from '../nav_links';
import ActiveSession from './active_session'

export default {
    props: {
        has_toggle: {
            type: Boolean,
            default: false
        },
    },
    data: function () {
        return {
            menu: ServiceMenu,
        }
    },
    computed: {
        toggle_view() {
            if (!this.has_toggle || this.menu.items.length <= 0)
                return false;

            return true;
        }
    },
    components: {
        Brand,
        Account,
        Language,
        Clock,
        VisualImpared,
        NavLinks,
        ActiveSession
    },
    methods: {
        toggle() {
            let body = document.getElementsByTagName('body')[0];
            if (!body)
                return;
            body.classList.toggle('side_bar_show');
        },
        async show_develop() {
            await Vue.Dialog.DevelopSettingDlg.Modal({
                size: 'right'
            })
        }
    },
    template: `
        <div class='iac-navigation-top'>
            <div class='iac-navigation-top__top' style='align-items: center;'>
                <Clock style='flex: 1 1 auto' />
                <ActiveSession style='flex: 1 1 auto' />
                <visual-impared class='ml-auto' style='height: 17px;margin: 0 10px;border-right: 1px solid #ccc;'/>
                <Language row/>
            </div>
            
            <div class='iac-navigation-top__body'>
                <span v-if='toggle_view' class='toggle' v-on:click='toggle'>
                    <icon>toggle</icon>
                </span>
                <Brand/>
                <div class='nav' :class='(settings._main_page && !$develop.old_home_page) ? "calc_margin" : ""' style='min-width: 0;'>
                    <div style='flex: 1 1 50px; display: flex; min-width: 50px;'>
                        <nav-links />
                    </div>
                    <visual-impared class='ml-auto' v-if='0' />
                    <Account />
                </div>
            </div>
        </div>
    `
}