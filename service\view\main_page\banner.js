export default {
    props: ["model"],
    template: `
        <div class='banner'>          
            <div class='body'>
                <div class='title'><icon :style='"color: "+model.color_icon+";"'>hayot_fav</icon> <label>{{model.title}}</label></div>
                <div class='content'><ui-markdown-view :content='model.content'/></div>
                <a class='ui-btn ui-btn-primary' target='_blank' :href='model.link' v-if='model.link' type='primary'>{{model.link_title || $t('more_detailed')}}</a>
            </div>
            <div class='image'><img :style='model.img_margin ? ("margin:"+model.img_margin) : "" ' slot='image' :src='model.img' /></div>
        </div>
    `
}