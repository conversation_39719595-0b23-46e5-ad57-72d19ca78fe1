version: '2'

services:
  frontend:
    image: nginx:1.13-alpine
    restart: always
    networks:
      - web
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ./_build:/usr/share/nginx/_html
      
    labels:
      - "traefik.enable=true"
      - "traefik.frontend.rule=Host:${APP_SITE}"

networks:
  web:
    external:
      name: ${DCAPE_NET}