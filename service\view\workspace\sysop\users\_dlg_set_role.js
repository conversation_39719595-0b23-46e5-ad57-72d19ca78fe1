import { DataSource, RemoteStore } from '@iac/data'
import { Http } from '@iac/core'

export default Vue.Dialog({
    props: ['user_id','role'],
    data: function () {
        return {
            value: undefined,
            error: undefined,
            policies: new DataSource({
                limit: 10000,
                displayExp:  (item)=> {
                    if(this.role){
                        return item.name;
                    }
                    return `${item.id}: <span style='color: #999'>${item.name}</span>`;
                },
                store: new RemoteStore({
                    method: this.role ? 'get_all_roles' : 'get_policies',
                    context: (context) => {
                        if (typeof context != 'string')
                            return context;
                        return {
                            id: context,
                            name: context + " "
                        }
                    },
                    inject: (items) => {
                        items.sort((a, b) => {
                            return a.id > b.id
                        });
                        
                        return items;
                    }
                })
            })
        }
    },
    mounted() {
        this.$wait(async () => {
            let { error, data } = await Http.api.rpc(this.role ? "get_user_roles" : "get_policies", {
                limit: !this.role ? 10000 : undefined,
                user_id: this.user_id
            })
            if (!error) {
                this.value = data ? data.map((item) => {
                    if (typeof item == 'string')
                        return item;
                    return item.id
                }) : []
                
            } else {
                this.error = error
            }
        })


    },
    methods: {
        update(value) {

            if (this.value != value) {
                this.value = value

                this.$wait(async () => {
                    let { error, data } = await Http.api.rpc(this.role ? "set_user_role" : "set_policies", {
                        user_id: this.user_id,
                        [this.role ? "role_ids" : "policies"]: value
                    })
                    if (error) {
                        Vue.Dialog.MessageBox.Error(error)
                    }
                });

            }

        }
    },
    template: `
        <div>
            <header>{{$t(this.role ? 'role' : 'policies')}}</header>
            <main>
                <ui-enum v-if='value' :dataSource='policies' v-bind:value='value' v-on:input="update" />
                <ui-error v-if='error' :code='error.status' :message='error.message' :details='error.details'  :number='error.number' /> 
            </main>
            <footer></footer>
        </div>
    `
})