import { DataSource, RefStore } from '@iac/data'
import "@iac/service/templates/company"
import { Language } from '@iac/core';

const query = () => {
    return {
        plan_date_from_gte: {
            group: "issuing_schedule.plan_date_from",
            type: "date",
            label: "from",
            has_del: true,
            bind: {
                status: `plan_date_from_error && {"type":"error"}`
            }
        },
        plan_date_from_lte: {
            group: "issuing_schedule.plan_date_from",
            type: "date",
            label: "to",
            has_del: true,
            bind: {
                status: `plan_date_from_error && {"type":"error"}`
            }
        },
        plan_date_from_error:  {
            sync: false,
            group: "issuing_schedule.plan_date_from",
            type: "model",
            label: "!",
            bind: {
                value: "plan_date_from_gte > plan_date_from_lte",
                status: `plan_date_from_error && {"type":"error","message":"${Language.t('to_from_error')}"}`
            },
        },
        plan_date_to_gte: {
            group: "issuing_schedule.plan_date_to",
            type: "date",
            label: "from",
            has_del: true,
            bind: {
                status: `plan_date_to_error && {"type":"error"}`
            }
        },
        plan_date_to_lte: {
            group: "issuing_schedule.plan_date_to",
            type: "date",
            label: "to",
            has_del: true,
            bind: {
                status: `plan_date_to_error && {"type":"error"}`
            }
        },
        plan_date_to_error:  {
            sync: false,
            group: "issuing_schedule.plan_date_to",
            type: "model",
            label: "!",
            bind: {
                value: "plan_date_to_gte > plan_date_to_lte",
                status: `plan_date_to_error && {"type":"error","message":"${Language.t('to_from_error')}"}`
            },

        },
    }
}

export default {
    data() {
        return {
            IssuingScheduleDataSource: new DataSource({
                store: new RefStore({
                    method: "ref_schedule",
                    ref: "exc_schedule_pgv",
                    injectQuery: (params) => (params.filters = {
                        ...params.filters,
                        plan_date_to_error: undefined,
                        plan_date_from_error: undefined,
                        exc_person: 'exc_all'
                    }, params)
                }),
                query: query(),
                template: "template-issuing_schedule_public"
            })
        }
    },
    template: `
    <div>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.issuing_schedule')}}</li>
            </ol>
            <h1>{{$t('nav.issuing_schedule')}}</h1>
        </iac-section>
        <iac-section>
            <ui-data-view :dataSource='IssuingScheduleDataSource'/>
        </iac-section>
    </div>
    `
}