import { DataSource, RefStore } from '@iac/data'
import "@iac/service/templates/company"

export default {
    props: { },
    data() {
        return {
            source: new DataSource({

                store: {
                    method: "ref",
                    ref: "ref_listing_exchange_commission",
                    context: context => {
                        return {
                            ...context,
                            actions: (context?.actions || []).map(action => {
                                if (action.is_reload) {
                                    action.response = ({data}) => {
                                        data && this.source.reload();
                                    };
                                }

                                return action;
                            })
                        }
                    }
                },
                actions: [
                    {
                        label: "add_user_to_listing_commission",
                        type: "request",
                        method: "ref",
                        params: {
                            ref: "ref_listing_exchange_commission",
                            op: "add_user_to_listing_exchange_commission"
                        },
                        response: ({data}) => {
                            data && this.source.reload();
                        }
                    },
                    {
                        label: "confirm_composition_of_commission",
                        type: "request",
                        method: "ref",
                        params: {
                            ref: "ref_listing_exchange_commission",
                            op: "confirm_listing_exchange_commission"
                        },
                        response: ({data}) => {
                            data && this.source.reload();
                        }
                    }
                ]
            })
        }
    },
    template: `
    <iac-access :access='$policy.exchange_exchange_contract_moderate' key='commission_list'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.commission_list')}}</li>
            </ol>
            <div class='title'>
                <h1>{{$t('nav.commission_list')}}</h1>
            </div>
        </iac-section>

        <iac-section>
            <ui-layout-tab>
                <ui-layout-group key='commission_list' label='commission_list'>
                    <ui-layout-group>
                        <ui-data-view :dataSource='source'/>
                    </ui-layout-group>
                </ui-layout-group>
            </ui-layout-tab>
        </iac-section>
    </iac-access>
    `
}
