import CompanyModel from './model'

export default {
    data: function () {
        return {
            model: undefined,
            error: undefined
        }
    },
    mounted() {
        this.$wait(async () => {
            let { data, error } = await CompanyModel.get(this.$route.params.id)

            this.model = data;
            this.error = error
        });
    },
    template: `
    <div>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{model && model.title}}</li>
            </ol>
            <h1>{{model && model.title}}</h1>
        </iac-section>

        <iac-section>
            <template  v-if="model">
                <ui-layout :fields='model.fields' style='max-width: 800px'/>
            </template>
            <div v-else-if='error' style='margin-top: 30px'>
                    <ui-error :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
                </div>
        </iac-section>
    </div>
    `
}