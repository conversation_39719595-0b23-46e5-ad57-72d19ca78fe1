


import Model from './model'
import Details from './details';
import { DataSource, Entity } from '@iac/data';
import { Http } from '@iac/core';
import { Context } from '@iac/kernel';

let Documentation = {
    props: ['model'],
    components: {
        docItem: {
            props: ['item', 'levels', "index", 'has_child'],
            template: `
                <tr>
                    <td v-if='0' style='padding: 0;text-align: left; padding-right: 5px;'>
                        <div style='display: inline-table;height: 100%;'>
                            <div style='display: table-row'>
                                <span style='display: table-cell;' :class='"none "+"none_"+level' v-for='level in levels'/>
                                <span :class='"index "+(has_child ? "has_child": "")'>{{index}} </span>
                                <div style='padding-bottom: 10px;'>
                                    {{item.ru}}
                                </div>
                            </div>
                        </div>
                    </td>
                    <td>{{index}}</td>
                    <td>{{item.ru}}</td>
                    <td></td>
                </tr>
            `
        }
    },
    render: function (c) {

        let renderItems = (items, levels = [], key = 0) => {
            if (!items)
                return;
            return items.map((item, index) => {

                let number = key ? key + '.' + (index + 1) : (index + 1);

                return [c('doc-item', {
                    props: {
                        levels: [...levels, ((index + 1) == items.length) ? 3 : 2],//.slice(1),
                        item: item,
                        index: number,
                        has_child: item?.items?.length > 0 ? true : false
                    }
                }), renderItems(item.items, [...levels, ((index + 1) == items.length) ? 0 : 1], number)]
            })

        }

        return c('table', {
            attrs: {
                cellpadding: 0,
                border: 0,
                cellspacing: 0
            },
        }, [c('tbody', {}, [renderItems(this.model.items)])])
    }
}


class TypeEntity extends Entity {
    constructor(context = {}) {
        super(context)
    }
    props() {
        return {
            title: {
                required: true
            },
            comment: {
                type: "text"
            }
        }
    }
}

let TypeEntityDlg = Vue.Dialog({
    props: ['onAdd'],
    data: function () {
        return {
            model: new TypeEntity()
        }
    },
    methods: {
        add() {
            if (this.model.validate())
                return;

            let meta = {
                title: this.model.title,
                comment: this.model.comment
            }
            this.$wait(async () => {
                let { error, data } = await this.onAdd(meta);
                if (error) {
                    Vue.Dialog.MessageBox.Error(error)
                } else if (data) {
                    this.Close(data)
                    //this.$router.push({ path: `/workspace/documentation/${data}` })
                }
            })

            //this.Close({
            //    title: this.model.title,
            //    comment: this.model.comment
            //})
        }
    },
    template: `
    <div>
        <main>
            <ui-layout :fields='model.fields'/>
        </main>
        <footer>
            <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
            <ui-btn type='primary' v-on:click.native='add'>{{$t('add')}}</ui-btn>
        </footer>
    </div>
    `
})


let docList = {
    data: function () {
        return {
            columns: [
                "index", "id", { field: 'name', label: 'Наименование', style: 'width: 100%; text-align: left;' }, "status", "company_id"
            ],
            source: new DataSource({
                actions: [
                    {
                        label: 'add',
                        handler: async () => {

                            let id = await TypeEntityDlg.Modal({
                                size: 'lg',
                                onAdd: async (meta) => {
                                    return await Http.api.rpc("ref", {
                                        ref: "ref_documentation_pages",
                                        op: "add",
                                        data: {
                                            meta: meta
                                        }

                                    })
                                }
                            })

                            if (!id)
                                return

                            this.$router.push({ path: `/workspace/documentation/${id}` })
                        },
                        hidden: () => {
                            return !Context.Access.policy.company_buyer_admin_cm_dp_add
                        }
                    }
                ],
                store: {
                    ref: 'ref_documentation_pages',
                    context: (context) => {

                        context.name = context.meta?.title || context.type
                        context.comment = context.meta?.comment

                        /*context.actions = [
                            {
                                icon: 'trash',
                                btn_type: 'danger'
                            }
                        ]*/

                        return context
                    },
                    injectQuery: (params) => {
                        params.op = 'list_types'
                        return params
                    }
                },
            })
        }
    },
    template: `
    <iac-access :access='$policy.company_buyer_admin_cm_dp_list_types || $policy.company_buyer_admin_cm_dp_add' >
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>Закупочная документация</li>
            </ol>
            <div class='title'>
                <h1 style='margin: 0;'>Закупочная документация</h1>
            </div>
        </iac-section>
        <iac-section>
            <ui-data-grid :buttons='true' :dataSource='source' :columns='columns'>
                <template slot='name' slot-scope='props'>
                    <router-link :to='"/workspace/documentation/"+props.item.type'>{{props.item.name}}</router-link>
                    <div v-if='props.item.comment'>{{props.item.comment}}</div>
                </template>
                <template slot='status' slot-scope='props'>
                    <span>{{$t(props.item.status)}}</span>
                </template>
            </ui-data-grid>
        </iac-section>

    </iac-access>
    `
}


export default [
    {
        path: 'documentation',
        component: {
            template: `
            <router-view/>
            `
        },
        children: [
            { path: '/', component: docList },
            { path: ':id/:version?', component: Details }
        ]
    },
]