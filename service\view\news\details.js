import { Http } from '@iac/core';

export default {
  data() {
    return {
      model: undefined,
      error: undefined,
    };
  },
  mounted() {
    this.update(this.$route.params.id);
  },
  beforeRouteUpdate(to, from, next) {
    this.update(to.params.id);
    next();
  },
  methods: {
    async update(id) {
      const { data, error } = await Http.api.rpc('ref_get_news', {
        id,
        select: [
          'title',
          'news',
          'digest_pic',
          'category',
          'category_id',
          'created_at',
        ],
      });
      if (error !== undefined) {
        this.error = error;
        return;
      }
      this.model = data;
    },
  },
  template: `
    <div class='news-details'>
      <template v-if='model'>
        <iac-section type='header'>
          <ol class='breadcrumb'>
            <li><router-link to='/'>{{ $t('home') }}</router-link></li>
            <li><router-link to='/news'>{{ $t('link.news') }}</router-link></li>
            <li>{{ model.title }}</li>
          </ol>
          <div v-html='model.digest_pic' class='news-details__image news-details__wrap'></div>
        </iac-section>
        <iac-section>
          <div class='news-details__wrap'>
            <iac-date class='news-date news-details__date' :date='model.created_at' withoutTime />
            <h1 class='news-details__title'>{{ model.title }}</h1>
            <router-link v-if='model.category' :to='"/news?category_id=" + model.category_id'
              class='ui-tag-item news-details__tag'>{{ model.category }}</router-link>
            <div v-html='model.news' class='news-details__content'></div>
          </div>
        </iac-section>
      </template>
      <iac-section v-if='error'>
        <ui-error :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
      </iac-section>
    </div>
  `,
};
