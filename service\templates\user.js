import Vue from "vue";
import { Context } from '@iac/kernel'

const Component = {
  props: ["model"],
  computed: {
    link() {
      const model = this.model
      return `/workspace/user/${model.id}`
    },
    title() {
      const { model, $t } = this
      let name = model.first_name? model.name : `${model.surname || ""} ${model.name || ""} ${model.patronymic || ""}`
      return (!name || name.trim() == "") ? `${$t('member')} № ${model.id}` : name
    },
  },
  methods: {
    company_link(company_id) {
      return `/company/${company_id}`;
    },
    debug_login(e, id) {
      e.preventDefault();
      Context.User.authorization_god(id);
    }
  },
  template: `
        <ui-data-view-item :model='model'>

            <template slot='header'>
              <div>
                <iac-entity-edit v-if='$develop.content_debug && model.id' :value='{id: model.id, type: "user"}' />
                <a v-if='$develop.content_debug' title='Телепортироваться' v-on:click='e => debug_login(e, model.id)' href="#">🕳️ </a>
                <span v-if='model.inn'>{{ $t('inn') }}:{{model.inn}}</span>
              </div>
              <div v-if='model.status'>
                {{model.status}}
              </div>
            </template>

            <template slot='title'>
                <span>{{title}}</span>
            </template>

            <template slot='sub_title'>
            </template>

            <template slot='description'>
              <div v-if="model.face_meta && model.face_meta.force_request_change_password">
                <label class="text_danger">Требование на немедленную смену пароля отправлено</label>
              </div>
              <div v-if='model.roles && model.roles.length > 0'>
                <label>{{ $t('role') }}: </label>
                <span v-for="(role, index) in model.roles" :key='role.id'><i v-if="index != 0">&centerdot;</i> {{role.name}}</span>
              </div>
              <div v-else-if='model.roles' style='color: #A00'>
                <label>{{ $t('role') }}: </label>
                <span>{{ $t('user.rolerequired') }}</span>
              </div>
              <div v-if="(model.meta && model.meta.broker_info && model.meta.broker_info.company_id) || model.public_broker_id">
                <label class="text_danger">{{$t('broker_user_info')}}: </label>
                <span>{{model.meta.broker_info.company_id || model.public_broker_id}}</span>
              </div>
            </template>

            <template slot='props'>
              <div v-if='model.company_id'>
                <label>{{$t('company')}}:</label>
                <div>
                  <router-link v-if='$develop.content_debug' :to='company_link(model.company_id)'>{{ model.company_id }}</router-link>
                  <template v-else>{{ model.company_id }}</template>
                </div>
              </div>
              <div v-if='model.phone'>
                <label>{{$t('phone')}}:</label>
                <div>{{model.phone}}</div>
              </div>
              <div v-if='model.email'>
                <label>{{$t('email')}}:</label>
                <div><a :href="'mailto:' + model.email">{{model.email}}</a></div>
              </div>
              <div v-if='model.face_meta && model.face_meta.trader_test && model.face_meta.trader_test.status != null'>
                <label>{{$t('Статус аккредитации')}}:</label>
                <div>{{ model.face_meta.trader_test.status ? $t("active") : $t("inactive") }}</div>
              </div>
              <div title='Дата запроса на аккредитацию' v-if='model.face_meta && model.face_meta.trader_test && model.face_meta.trader_test.test_date'>
                <label>{{$t('Дата аккредитации')}}:</label>
                <div>
                  <iac-date :date='model.face_meta.trader_test.test_date' withoutTime />
                </div>
              </div>

              
              <div :title='model.face_meta.trader_test.reason' v-if='model.face_meta && model.face_meta.trader_test && model.face_meta.trader_test.rejected_at'>
                <label>{{$t('Дата отклонения')}}:</label>
                <div>
                  <iac-date :date='model.face_meta.trader_test.rejected_at' withoutTime />
                </div>
              </div>

              <div  v-if='model.face_meta && model.face_meta.trader_test && model.face_meta.trader_test.accepted_at'>
              <label>{{$t('Дата включения')}}:</label>
              <div>
                <iac-date :date='model.face_meta.trader_test.accepted_at' withoutTime />
              </div>
            </div>


              <div v-if='model.deleted_at'>
                <label>{{ $t('deleted_at') }}:</label>
                <div><iac-date :date='model.deleted_at' withoutTime /></div>
              </div>
            </template>

        </ui-data-view-item>
    `,
}

Vue.component("template-user", Component)
