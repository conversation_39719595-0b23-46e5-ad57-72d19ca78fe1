.iac-documentation {
    color: #333;

    p{
        margin: 0;
        padding: 5px 3px;
    }
    ul {
        padding-left: 40px;
        //padding-inline-start: 40px;
    }

    .flip-list-move {
        transition: transform 0.8s ease;
    }

    section {
        font-size: 14px;
        margin-bottom: 30px;

        >.title {
            font-size: 14px;
            font-weight: bold;
            display: flex;
            text-transform: uppercase;

            >div {
                padding: 0;// 5px 3px;

                text-align: center;
                >b {
                    display: inline-block;
                    width: 30px;
                    font-size: 14px;
                }

                flex: 1 1 50%;
            }
        }

        >.system,
        >.custom {
            .item {
                display: flex;

                >div {
                    padding: 5px 3px;

                    >b {
                        display: inline-block;
                        width: 30px;
                        font-size: 12px;
                    }

                    flex: 1 1 50%;
                }
            }
        }

        >.custom {
            .item {
                background: #fbf3e3;
                position: relative;
                cursor: pointer;
                border-bottom: 1px solid #0002;

                .drop-top {
                    position: absolute;
                    width: 100%;
                    top: 0;
                    bottom: 45%;

                    &.dragover {
                        border-top: 2px solid #000;
                    }
                }

                .drop-bottom {
                    position: absolute;
                    width: 100%;
                    top: 45%;
                    bottom: -1px;
                }

                &:last-child {
                    .drop-bottom.dragover {
                        border-bottom: 2px solid #000;
                    }
                }
            }

        }
        >.add_item{
            height: 30px;
            padding-left: 35px;
            >span{
                color: #18a3be;
                font-size: 12px;
                line-height: 30px;
                cursor: pointer;
                display: inline-block;
                &:hover{
                    text-decoration: underline;
                }
            }
        }
        &:hover{
            >.add_item >span{
                display: inline-block;
            }
        }
    }
}

.iac-documentation1 {
    font-size: 14px;
    color: #333;

    .flip-list-move {
        transition: transform 0.8s ease;
    }

    .system,
    .custom {
        flex-wrap: wrap;

        >div {
            min-width: 300px;
            padding: 5px 3px;

            &.shadow {
                box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.03);
                //padding-bottom: 20px;
            }
        }
    }

    .system {}

    .custom {
        background: #fbf3e3;
    }



    .drop-top {
        &.dragover {
            border-top: 1px solid;
        }
    }

    .drop-bottom {
        &.dragover {
            border-bottom: 1px solid;
        }
    }
}