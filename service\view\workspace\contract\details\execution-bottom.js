export default {
  props: ["model"],
  computed: {
    rights_publish() {
      const {
        execution_winner_publish,
        execution_org_publish,
        execution_coorp_publish,
      } = this.model.rights;
      return execution_winner_publish || execution_org_publish || execution_coorp_publish;
    },
    edit_execution() {
      const { rights } = this.model;
      return rights.edit_execution_org || rights.edit_execution_winner;
    }
  },
  methods: {
    async sign(part) {
      await this.$wait(async () => {
        if (!this.edit_execution || await this.model.edit_execution(part)) {
          await this.model.sign(part);
        }
      });
    },
    async execution_reject(){
      await this.$wait(async () => {
        await this.model.execution_reject();
      });
    },
    async reject_sign(part) {
      await this.$wait(async () => {
        await this.model.reject_sign(part);
      });
    },
    async coorp_publish() {
      const keys = [
        'execution_winner_publish',
        'execution_org_publish',
        'execution_coorp_publish',
      ];
      const method = keys.find(key => this.model.rights[key]);
      await this.$wait(async () => {
        await this.model.coorp_publish(method);
      });
    },
  },
  template: `
  <div class='requisites'>
    <div class='grid'>
      <div class='row'>
        <h2>{{ $t('company.buyer') }}</h2>
      </div>
      <div class='row'>
        <ui-btn v-if='model.rights.execution_org_sign' type='primary' @click.native='sign("execution_org")'>{{ $t('subscribe') }}</ui-btn>
        <ui-btn v-if='model.rights.execution_org_reject_sign' type='secondary' @click.native='reject_sign("execution_org")'>{{ $t('reject_sign') }}</ui-btn>
        <ui-btn v-if='model.rights.execution_org_sign && model.rights.execution_reject' type='secondary' @click.native='execution_reject'>{{ $t('reject') }}</ui-btn>
      </div>
    </div>
    <div class='grid'>
      <div class='row'>
        <h2>{{ $t('company.seller') }}</h2>
      </div>
      <div class='row'>
        <ui-btn v-if='model.rights.execution_winner_sign' type='primary' @click.native='sign("execution_winner")'>{{ $t('subscribe') }}</ui-btn>
        <ui-btn v-if='model.rights.execution_winner_reject_sign' type='secondary' @click.native='reject_sign("execution_winner")'>{{ $t('reject_sign') }}</ui-btn>
        <ui-btn v-if='model.rights.execution_winner_sign && model.rights.execution_reject' type='secondary' @click.native='execution_reject'>{{ $t('reject') }}</ui-btn>
      </div>
    </div>
    <div v-if='rights_publish' class='text-center w-100'>
      <ui-btn type='primary big' @click.native='coorp_publish'>{{ $t('execution.public') }}</ui-btn>
    </div>
  </div>   
  `
}