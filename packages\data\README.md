# Data

Подключение @iac/data

* IacObject
* Store

* [Property](#property)
* Model
* Entity
* DataSource
* Query
* RemoteStore
* RefStore
* ArrayStore
* BackEndStore

## Property
* attr
* wait
* label
* status
* group
* dataSource
* readonly
* hidden
* order
* actions
* has_del
* min
* max
* multiple
* required
* range
* widget
* show_comment
* show_photo
* sync
* dataBind
* description
* value
* icon
* meta
* buttons
* type
    * input - по умолчанию
    * float
    * number
    * text
    * static
    * info
    * html
    * file
    * photo
    * file_comment
    * bool (boolean)
    * range
    * tag
    * enum-tree
    * enum
    * action
    * time
    * date
    * date-time
    * entity
    * data-view
    * link
    * eimzo
    * widget
    * model