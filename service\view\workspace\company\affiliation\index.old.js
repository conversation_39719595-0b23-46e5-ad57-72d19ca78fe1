import { DataSource, Query, RemoteStore, ArrayStore } from '@iac/data'
import { Http, Language } from '@iac/core'
import AddCompanyDlg from './_dlg_add_company'

export default {
    data: function () {
        return {
            dataSource: new DataSource({
                store: new RemoteStore({
                    method: "get_affiliated_companies"
                })
            })
        }
    },
    methods: {
        async add_company() {
            if(await AddCompanyDlg.Modal({
                size: "lg"
            })){
                this.dataSource.reload();
            }
        },
        async delete_company(item){
            if(!item)
                return;
            if (await Vue.Dialog.MessageBox.Question(this.$t('question_delete_affiliated_company')) != Vue.Dialog.MessageBox.Result.Yes) {
                return;
            }
            await this.$wait(async () => {
                let { error, data } = await Http.api.rpc("delete_affiliated_company", {
                    inn: item.inn
                })
                if(error){
                    await Vue.Dialog.MessageBox.Error(error)
                }else{
                    if(data && data.message){
                        Vue.Dialog.MessageBox.Success(data.message)
                    }
                    this.dataSource.reload();
                }
            })
        }
    },
    template: `
        <div class='iac-affiliation-list'>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('nav.affiliation')}}</li>
                </ol>
                <div class='title'>
                    <h1>{{$t('nav.affiliation')}}</h1>
                    <div v-if='$policy.affiliation_create'><ui-btn type='primary' v-on:click.native='add_company'>{{$t('add')}}</ui-btn></div>
                </div>
            </iac-section>
            <iac-section>
                <ui-list :dataSource='dataSource'>
                    <template slot='template' slot-scope='props'>
                        <div style='white-space: normal; width: 100%;'>
                            <b style='color: #000; font-size: 16px;'>{{props.item.inn}}</b>
                            <span style='padding: 0 18px;'>{{props.item.comment}}</span>
                        </div>
                        <div>
                            <ui-btn type='danger' @click.native='delete_company(props.item)'>{{$t('delete')}}</ui-btn>
                        </div>
                    </template>
                </ui-list>
            </iac-section>
        </div>
    `
}