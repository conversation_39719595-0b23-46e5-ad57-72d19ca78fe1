import ebpLangSelect from '../components/ebp_lang_select'
import ebpMultilangInput from '../components/ebp_multilang_input'
import ebpInput from '../components/ebp_input'
import ebpMultilangTreeSelect from '../components/ebp_multilang_tree_select'
import ebpMultilangTable from '../components/ebp_multilang_table'
import ebpTextarea from '../components/ebp_textarea'

import BurseProductRequestCreateModel from '../models/burse_product_request.create.model'

export default Vue.Dialog({
  props: {
    currentItem: {
      type: Object,
      required: true
    }
  },
  components: {
    'ebpLangSelect': ebpLangSelect,
    'ebpMultilangInput': ebpMultilangInput,
    'ebpInput': ebpInput,
    'ebpMultilangTreeSelect': ebpMultilangTreeSelect,
    'ebpMultilangTable': ebpMultilangTable,
    'ebpTextarea': ebpTextarea
  },
  data() {
    return {
      model: new BurseProductRequestCreateModel(this.currentItem)
    }
  },
  methods: {
    async createProductWithChanges() {
      const modelForExport = this.model.export()
      this.onSelect?.(modelForExport)
      this.Close(modelForExport)
    }
  },
  template: `
  <div>
    <header>{{$t("ebp_specification_edit")}}</header>
    <main>
      <div v-if="model.motherland">{{$t('ebp_created_from')}} {{model.motherland.source}}({{model.motherland.id}})</div>
      <ebp-lang-select v-model="model.langs" :label="$t('ebp_select_languages')"/>
      <ebp-multilang-input v-model="model.name" :langs="model.langs" :label="$t('product_name')"
       :placeholder="$t('product_name')" :enableShowChanges="true"/>
      <div class="iac--ebp-row">
        <ebp-input v-model="model.skp" :label="$t('skp')" :placeholder="$t('skp')" :disabled="true"/>
        <ebp-input v-model="model.tnved" :label="$t('tnved')" :placeholder="$t('tnved')" :disabled="true"/>
      </div>
      <ebp-multilang-tree-select :data="model.groups" :langs="model.langs" :label="$t('group')"
       :placeholder="$t('group')" :request="model.getGroupsQuery"/>
      <ebpMultilangTable v-model="model.units" :columns="model.unitsColumnsWithoutMU"
       :addItem="model.addUnit" :updateItem="model.updateUnit" :deleteItem="model.deleteUnit" :langs="model.langs" :label="$t('units')"/>

      <h3>{{$t("properties")}}:</h3>
      <ebpMultilangTable v-for="(prop,index) in model.props" v-model="prop.values" :columns="model.propsColumns"
       :langs="model.langs" :label="prop.name" :updateLabel="newName=>model.updatePropName(index, newName)" :deleteTable="()=>model.deleteProp(index)"
       :addItem="()=>model.addPropVal(index)" :updateItem="(valIndex,newValue)=>model.updatePropVal(index,valIndex,newValue)" :deleteItem="(valIndex)=>model.deletePropVal(index,valIndex)"/>
       <ui-btn type='primary' @click.native="model.addProp">{{$t('add')}}</ui-btn>
       <ebpTextarea v-model="model.message" :label="$t('ebp_comment')"/>
    </main>
    <footer>
      <ui-btn :disabled="model.disabledFinal()" type='primary' @click.native="e=>createProductWithChanges()">{{$t('add')}}</ui-btn>
    </footer>
  </div>
    `
})