import { DataSource, BackEndStore, Entity } from '@iac/data'

let preview = {
    props: ['type', 'content'],
    template: `
        <ui-alert :type='type' style='font-size: 14px;'>
            <ui-markdown-view style='margin: -1em 0;' v-if='content' :content='content' />
            <div v-else>Отображаться не будет</div>
        </ui-alert>
    `
}

class Model extends Entity {
    constructor(context, source) {
        super(context)

        this.store = source._store

        context = context || {};

        this.id = context.id;
        this.date_start = context.date_start;
        this.date_end = context.date_end;

        this.position = context.position || "float";
        this.status_type = context.status_type || "info";

        this.url = context.url;
        this.ru_RU = context.message && context.message[0];
        this.uz_Cyrillic = context.message && context.message[1];
        this.uz_Latin = context.message && context.message[2];
        this.en_US = context.message && context.message[3];

        this.active = context.active;

    }
    props() {
        let $this = this;
        return {
            id: {
                type: 'hidden',
                label: '-id'
            },
            date_start: {
                label: '-from',
                type: 'date-time',
                group: 'Условия отображения/!date'
            },
            date_end: {
                label: '-to',
                type: 'date-time',
                group: 'Условия отображения/!date'
            },
            active: {
                label: '-active',
                type: 'bool',
                group: 'Условия отображения'
            },
            status_type: {
                label: '-type',
                type: "entity",
                dataSource: ["danger", "warning", "info", "success"],
                group: 'Вид отображения'
            },
            ru_RU: {
                type: 'text',
                label: '!message',
                group: 'Вид отображения/{tab}/ru_RU',
                attr: {
                    react: true
                }
            },
            preview_ru_RU: {
                type: 'widget',
                label: '!',
                group: 'Вид отображения/{tab}/ru_RU',
                widget: {
                    name: preview,
                    props: {
                        get type() {
                            return $this.status_type && $this.status_type.id
                        },
                        get content() {
                            return $this.ru_RU
                        }
                    }
                }
            },
            uz_Cyrillic: {
                type: 'text',
                label: '!message',
                group: 'Вид отображения/{tab}/uz_UZ@cyrillic',
                attr: {
                    react: true
                }
            },
            preview_uz_Cyrillic: {
                type: 'widget',
                label: '!',
                group: 'Вид отображения/{tab}/uz_UZ@cyrillic',
                widget: {
                    name: preview,
                    props: {
                        get type() {
                            return $this.status_type && $this.status_type.id
                        },
                        get content() {
                            return $this.uz_Cyrillic
                        }
                    }
                }
            },
            uz_Latin: {
                type: 'text',
                label: '!message',
                group: 'Вид отображения/{tab}/uz_UZ@latin',
                attr: {
                    react: true
                }
            },
            preview_uz_Latin: {
                type: 'widget',
                label: '!',
                group: 'Вид отображения/{tab}/uz_UZ@latin',
                widget: {
                    name: preview,
                    props: {
                        get type() {
                            return $this.status_type && $this.status_type.id
                        },
                        get content() {
                            return $this.uz_Latin
                        }
                    }
                }
            },
            en_US: {
                type: 'text',
                label: '!message',
                group: 'Вид отображения/{tab}/en_US',
                attr: {
                    react: true
                }
            },
            preview_en_US: {
                type: 'widget',
                label: '!',
                group: 'Вид отображения/{tab}/en_US',
                widget: {
                    name: preview,
                    props: {
                        get type() {
                            return $this.status_type && $this.status_type.id
                        },
                        get content() {
                            return $this.en_US
                        }
                    }
                }
            },
            position: {
                label: '-position',
                type: "entity",
                dataSource: ["header", "float"],
                group: 'Место отображения'
            },
            url: {
                label: '!url страниц',
                type: 'text',
            },

        }
    }

    async save() {

        let fields = this.fields.filter((field) => {
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).reduce((prev, curr) => {
            if (curr.readonly && curr.readonly())
                return prev
            prev[curr.name] = curr.value && curr.value.exp ? curr.value.exp.value : curr.value
            return prev
        }, {})

        fields.message = [
            fields.ru_RU,
            fields.uz_Cyrillic,
            fields.uz_Latin,
            fields.en_US,
        ]

        fields.date_start = fields.date_start ? new Date(fields.date_start).toISOString() : undefined
        fields.date_end = fields.date_end ? new Date(fields.date_end).toISOString() : undefined

        let { error, data } = await this.store.set_item(fields);
        if (error) {
            return Vue.Dialog.MessageBox.Error(error)
        }
        return data;
    }
}

var BannerDlg = Vue.Dialog({
    props: ["item", "source"],
    data: function () {
        return {
            model: new Model(this.item, this.source)
        }
    },
    methods: {
        async save() {
            this.$wait(async () => {
                let item = await this.model.save();
                if (item)
                    this.Close(item)
            })
        }
    },
    template: `
        <div>
            <header>Настройка баннера</header>
            <main>
                <ui-layout :fields='model.fields' />
            </main>
            <footer>
                <ui-btn v-on:click.native='Close()'>{{$t('cancel')}}</ui-btn>
                <ui-btn type='primary' v-on:click.native='save'>{{$t('save')}}</ui-btn>
            </footer>
        </div>
    `
})

export default {
    data: function () {
        return {
            source: new DataSource({
                store: new BackEndStore({
                    scope: "global",
                    storage_key: 'store_banners',
                    context: (context) => {
                        context.status_type = context.status_type || "info"
                        context.actions = [
                            {
                                label: 'edit',
                                handler: async () => {
                                    let item = await BannerDlg.Modal({
                                        item: context,
                                        source: this.source,
                                        size: 'right'
                                    })
                                    if (item) {
                                        await this.source.reload();
                                    }
                                }
                            },
                            {
                                label: 'delete',
                                handler: async () => {
                                    await this.source.store.delete(context.id)
                                    await this.source.reload();
                                }
                            }
                        ]
                        return context;
                    }
                }),
                actions: [
                    {
                        label: 'add',
                        handler: async () => {
                            let item = await BannerDlg.Modal({
                                source: this.source,
                                size: "right"
                            })
                            if (item) {
                                await this.source.reload();
                            }
                        }
                    },
                    {
                        label: 'clear',
                        handler: async () => {
                            await this.source.store.delete()
                            await this.source.reload();
                        }
                    }
                ],
                template: {
                    props: ["model"],
                    computed: {
                        labels() {
                            return [
                                "РУ",
                                "УЗ",
                                "UZ",
                                "EN"
                            ]
                        }
                    },
                    template: `
                        <ui-data-view-item :model='model'>
                            <template slot='header'>
                                <div>
                                    <span style='display: inline-block' v-if='model.date_start'> от <iac-date :date="model.date_start"  full/> </span>&nbsp;
                                    <span style='display: inline-block' v-if='model.date_end'> до <iac-date :date="model.date_end"  full/></span>
                                </div>
                                <div>{{model.active ? "active" : "hidden"}}</div>
                            </template>
                            <template slot='description'>
                                <div v-for='message,index in model.message'>
                                    <label>{{labels[index]}}: </label><span>{{message || "Отсутствует"}}</span>
                                </div>
                            </template>
                        </ui-data-view-item>
                    `
                }
            })
        }
    },
    template: `
        <iac-access :access='$policy.system_cm_banners_view'>
            <iac-section>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('nav.banners')}}</li>
                </ol>
                <div class='title'>
                    <h2>{{$t('nav.banners')}}</h2>
                </div>
            </iac-section>
            <iac-section>
                <ui-data-view :search='false' :dataSource='source' />
            </iac-section>
        </iac-access>
    `
}
