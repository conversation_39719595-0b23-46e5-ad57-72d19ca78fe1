import { DataSource } from '@iac/data';

export default {
  props: ['model'],
  created() {
    if (this.execution?.fine_currency?.load) {
      this.execution.fine_currency.load();
    }
  },
  data() {
    let proc_type;
    switch (this.model.proc_type) {
      case "selection":
       case "tender":
      case "reduction":
        proc_type = this.model.proc_type
        break;
      case 'nad':
      case 'ad':
        proc_type = 'ad'
        break;
      default:
        proc_type = "tender"
        break
    }

    const {
      edit_execution_org,
      edit_execution_winner,
      execution_winner_sign,
      execution_org_sign,
    } = this.model.rights;
    const edit_execution = edit_execution_org || edit_execution_winner;
    return {
      execution: {
        base: DataSource.get([
          {
            id: 1,
            name: 'termination',
          }, {
            id: 2,
            name: 'execution',
          },
        ]),
        termination_reason: DataSource.get({
          query: {
            proc_type: proc_type
          },
          store: {
            ref: "ref_refuse_reasons"
          }
        }),
        who_pays: DataSource.get([
          !edit_execution || execution_winner_sign ? {
            id: 1,
            name: 'company.buyer',
          } : undefined,
          !edit_execution || execution_org_sign ? {
            id: 2,
            name: 'contract.contragent',
          } : undefined,
        ]),
        fine_currency: DataSource.get('ref_currency'),
      },  
    }
  },
  computed: {
    proc_type() {
      //if(this.model.proc_type == "nad")
      //  return "ad"
      return "tender";// this.model.proc_type
    },
    edit_execution() {
      const { edit_execution_org, edit_execution_winner } = this.model.rights;
      return edit_execution_org || edit_execution_winner;
    },
    execution_base() {
      let execution_base = this.model.execution && this.model.execution.base && this.model.execution.base.value;
      return execution_base?.id || execution_base;
    },
    show_termination() {
      const { edit_execution_org, edit_execution_winner } = this.model.rights;


      //if(!execution_base)
      //  return false;

      // console.log(!edit_execution_org, execution_base == 2, (this.model.proc_type == 'request' || this.model.proc_type == 'reduction'))

      //if (!edit_execution_org && execution_base == 2 && (this.model.proc_type == 'request' || this.model.proc_type == 'reduction')) {
      //  return false;
      //}

      if (edit_execution_winner && this.execution_base == 2 && (this.model.proc_type == 'request' || this.model.proc_type == 'reduction')) {
        return false;
      }

      if (this.model.unblock_deposit_status != "for_execution" && this.model.execution.base?.value && (this.edit_execution || this.model.execution.who_pays?.value))
        return true
    },
    depositCurrency() {
      const { execution_winner_sign } = this.model.rights;
      // Если execution не определен или нет max_fine_sum, используем model.currency
      if (!this.model.execution || !this.model.max_fine_sum?.org_currency || !this.model.max_fine_sum?.contragent_currency) {
        return this.model.currency;
      }
      let currencyCode;
      // Если есть execution.who, используем логику на основе who
      if (this.model.execution.who) {
        currencyCode = this.model.execution.who === 'contragent' 
          ? this.model.max_fine_sum.contragent_currency.code
          : this.model.max_fine_sum.org_currency.code;
      } else {
        // Иначе используем логику на основе execution_winner_sign
        currencyCode = !execution_winner_sign
          ? this.model.max_fine_sum.contragent_currency.code
          : this.model.max_fine_sum.org_currency.code;
      }
      const currencies = this.execution.fine_currency.items;
      const currency = currencies.find(c => c.code === currencyCode);  
      return currency?.name || (!execution_winner_sign
        ? this.model.max_fine_sum.contragent_currency.name
        : this.model.max_fine_sum.org_currency.name);
    }
  },
  methods: {
    select(e, type) {
      if (type) {
        this.model.execution[type].value = e;
      }
    },
  },
  template: `
    <div v-if='model' class='grid'>
      <div class='row'>
        <div v-if='model.execution.who && !edit_execution'>
          <b style='font-size: 16px;'>{{ $t('contract.initiator') }}</b>
          <router-link v-if='model.execution.who != "org"' :to='"/company/" + model.initiator.company_details.id'>
            {{ model.initiator.company_details.title }}
          </router-link>
          <router-link v-else :to='"/company/" + model.contragent.company_details.id'>
            {{ model.contragent.company_details.title }}
          </router-link>
        </div>
        
        <div v-if='model.contract_close_at'>
          <label>{{ $t('contract.date_sign') }}:</label> 
          <iac-date :date='model.contract_close_at' withoutTime />
        </div>
        <div v-if='model.unblock_deposit_status != "for_execution" && (model.rights.edit_execution_org || model.rights.edit_execution_winner)'>
          <label>{{ $t('contract.deposit_sum') }}:</label>
          <iac-number :value='model.deposit_sum' delimiter=' ' part='2' /> {{ depositCurrency }}
        </div>
        
        <div v-if='model.execution.fine_amount.value && model.execution.fine_currency.value'>
          <label>{{ $t('contract.fine_amount') }}:</label>
          <iac-number :value='model.execution.fine_amount.value' delimiter=' ' part='2' /> {{ depositCurrency }}
        </div>
        <div v-if='model.execution.fine_reason.value'>
          <label>{{ $t('contract.fine_reason') }}:</label>
          <span>{{model.execution.fine_reason.value}}</span>
        </div>
      </div>
      <div class='row'>
        <label class='col-sm-3'>{{ $t('execution.base') }}:</label>
        <div class='col-sm-5'>
          <div class='ui-field'>
            <div class='field-container'>
              <div class='field-control'>
                <ui-entity  label='choose_base' required :status='model.execution.base.status && "error"'  :readonly='!edit_execution' :dataSource='execution.base' v-bind='model.execution.base' @input='select($event, "base")' ></ui-entity>
              </div>
              <div class='color-red' v-if='model.execution.base.status'>{{model.execution.base.status.message}}</div>
            </div>
          </div>
          <div class='ui-field' v-if='execution_base == 1'>
            <div class='field-container'>
              <div class='field-control'>
                <ui-entity  label='termination_reason' required :status='model.execution.termination_reason.status && "error"'  :readonly='!edit_execution' :dataSource='execution.termination_reason' v-bind='model.execution.termination_reason' @input='select($event, "termination_reason")' ></ui-entity>
              </div>
              <div class='color-red' v-if='model.execution.termination_reason.status'>{{model.execution.termination_reason.status.message}}</div>
            </div>
          </div>
        </div>
      </div>


      <div class='row' v-if='show_termination'>
        <label class='col-sm-3'>{{ $t('execution.penalties') }}:</label>
        <div class='col-sm-5'>
          
          <div class='ui-field' v-if='edit_execution || (model.execution.who_pays && model.execution.who_pays.value)'>
            <div class='field-container'>
              <div class='field-control'>
                <ui-entity label='who_pays' :readonly='!edit_execution' :status='model.execution.who_pays.status && "error"' :dataSource='execution.who_pays' v-bind='model.execution.who_pays' @input='select($event, "who_pays")' has_del></ui-entity>
              </div>
              <div class='color-red' v-if='model.execution.who_pays.status'>{{model.execution.who_pays.status.message}}</div>
            </div>
          </div>

          <div v-if='model.execution.who_pays && model.execution.who_pays.value'>
            
            <div class='ui-field' v-if='edit_execution  || model.execution.fine_amount.value'>
              <div class='field-container'>
                <div class='field-control'>
                <ui-input type="float" label='execution.fine_amount' :readonly='!edit_execution ' v-model.number='model.execution.fine_amount.value' :status='model.execution.fine_amount.status && "error"' has_del />
                </div>
                <div class='color-red' v-if='model.execution.fine_amount.status'>{{model.execution.fine_amount.status.message}}</div>
              </div>
            </div>

            <div class='ui-field' v-if='edit_execution || model.execution.fine_reason.value'>
              <div class='field-container'>
                <div class='field-control'>
                  <ui-text label='execution.penalties_reason' :readonly='!edit_execution ' v-model='model.execution.fine_reason.value' :status='model.execution.fine_reason.status && "error"' has_del />
                </div> 
                <div class='color-red' v-if='model.execution.fine_reason.status'>{{model.execution.fine_reason.status.message}}</div>
              </div>
            </div>

          </div>
        </div>

      </div>
    </div>
  `,
};
