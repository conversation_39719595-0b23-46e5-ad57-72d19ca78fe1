import { Http, Language } from '@iac/core'

export default class BurseProductRequestShowModel {
    constructor(product) {
        this.id = product.id
        this.motherland = product.motherland
        this.meta = product.meta
        this.message = product.message ?? ""
        this.tnved = product.tnved
        this.skp = product.skp
        this._name = product.name
        this._units = product.units ?? []
        this._properties = product.properties ?? []
        this._groups = []
        this.group_id = product.group_id
        this.getGroups()

        this.langs = [
            { name: "uz-UZ@cyrillic", active: true, title: "Уз" },
            { name: "uz-UZ@latin", active: false, title: 'Uz' },
            { name: "ru-RU", active: false, title: "Ру" },
            { name: "en-US", active: false, title: "En" }
        ]//брать из сеттингов и core language.js
        this.unitsColumns = [
            { name: 'master_unit', title: Language.t('master_unit'), type: 'radio' },
            { name: 'name', title: Language.t('name'), type: 'multilang_input' },
            { name: 'ratio', title: Language.t('ratio'), type: 'number' }
        ]
        this.unitsColumnsWithoutMU = [
            { name: 'name', title: Language.t('name'), type: 'multilang_input' },
            { name: 'ratio', title: Language.t('ratio'), type: 'number' }
        ]
        this.propsColumns = [
            { name: 'name', title: Language.t('name'), type: 'multilang_input' },
        ]

        this.prepareLangs()
    }

    get name() {
        return this._name
    }

    get units() {
        return this._units
    }

    get props() {
        return this._properties
    }

    get groups() {
        return this._groups
    }

    getGroups = async () => {
        const group_id = this.group_id
        let groups = []
        if (group_id) {
            while (!groups.length || groups[0].parent_id) {
                const { error, data } = await Http.api.rpc("ref", {
                    ref: "ref_enkt_burse_product_groups",
                    op: "read",
                    limit: 1,
                    offset: 0,
                    filters: { id: groups?.length ? groups[0].parent_id : group_id },
                    fields: ['id', 'parent_id', 'name']
                })
                groups = groups ?? []
                !error && data && data?.length && groups.unshift(data[0])
            }
            groups.forEach(item => delete item.__schema__)
        }
        this._groups = groups
    }

    prepareLangs = () => {
        this._units.forEach(unit => {
            this.langs.forEach(lang => unit.name[lang.name] && (lang.active = true))
        })
        this.langs[0].active = true
        this.langs = [...this.langs]
    }

    getGroupsQuery = async (parent_id) => {
        const { error: err, data: data } = await Http.api.rpc("ref", {
            ref: "ref_enkt_burse_product_groups",
            op: "read",
            "limit": 51,
            "offset": 0,
            filters: { parent_id },
            fields: ['id', 'parent_id', 'name', 'meta', 'has_children']
        })
        return data
    }
}