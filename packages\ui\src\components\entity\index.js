let SearchInput = {
    props: {
        text: String,
        timeout: {
            type: Number,
            default: 400
        }
    },
    data: function () {
        return {
            value: this.text,
            tid: undefined
        }
    },
    watch: {
        value: {
            immediate: true,
            async handler(val, oldVal) {
                if (val == oldVal)
                    return;

                if (val == '' || val == null)
                    val = undefined

                if (this.tid) {
                    clearTimeout(this.tid);
                }
                this.tid = setTimeout(async () => {
                    this.$emit("search", val)
                }, this.timeout);
            }
        }
    },
    template: `
        <input :placeholder='$t("search")' v-model='value' />
    `
}

export var Entity = {
    name: "ui-entity",
    props: ["search_timeout","not_found","icon", "label", "status", "value", "dataSource", "readonly", "disabled", "wait", "actions", "has_del", "required","prefix","suffix"],
    data: function () {
        return {
            dropdown: false
        }
    },
    computed: {
        slot_template() {
            return !!this.$slots.template;
        },
        controlIcon() {
            if (this.dataSource && this.value && this.value.exp) {
                return this.value.exp.icon || this.icon;
            }
            return this.value && this.value.icon ? this.value.icon : this.icon
        },
        controlName() {
            if (this.dataSource && this.value != undefined && this.value != null && this.value.exp) {
                return this.value.exp.display;
            }

            return this.value != undefined && this.value.name;
        },
        controlProps() {
            return {
                icon: this.controlIcon,
                wait: this.wait,
                actions: this.actions,
                label: this.label,
                status: this.status,
                readonly: this.readonly,
                disabled: this.disabled,
                opened: this.opened,
                dropdown: this.dropdown,
                class: this.classes,
                has_del: this.has_del
            }
        },
        classes() {
            return [

            ]
        },
        opened() {
            if (this.value || this.value === 0) {
                return true
            }
            return false;
        },
        searchControl() {
            if (this.dataSource && this.dataSource.search)
                return true;
            return false;
        },
        search_text() {
            if (typeof this.dataSource.search == 'string') {
                return this.dataSource.query.local[this.dataSource.search];
            } else if (typeof this.dataSource.search == 'boolean') {
                return this.dataSource.query.search_query;
            }
        }
    },
    watch: {
        value: {
            immediate: true,
            async handler(val, oldVal) {

                if (val == oldVal || !this.dataSource)
                    return;

                if(val && val.exp)
                    return;

                if (typeof val == 'object') {
                    this.value = this.dataSource.initExp(val)
                    //this.$emit("input", _object)
                    
                }else{
                    let _object = await this.dataSource.byKey(val)
                    this.$emit("input", _object)
                }
            }
        }
    },
    methods: {
        onItem(item) {
            this.dropdown = false;
            this.$emit("input", item)
        },
        action(event) {
            if (event == 'select' && !this.readonly && !this.disabled) {
                this.dropdown = !this.dropdown;
            }
            else if (event == 'clear') {
                this.value = undefined;
                this.dropdown = false;
                this.$emit('input', undefined)
            }
        },
        search(text) {
            if (this.search_text == text)
                return;
            if (this.dataSource && this.dataSource.query) {
                if (typeof this.dataSource.search == 'string') {
                    this.dataSource.query.set({ [this.dataSource.search]: text })
                } else if (typeof this.dataSource.search == 'boolean') {
                    this.dataSource.query.search(text);
                }
            }
        },
        actionSelect() {
            this.action("select");
        }
    },
    components: {
        SearchInput: SearchInput
    },
    template: `<ui-control class='ui-entity' v-bind="controlProps" v-on:action='action' :required='required'>
        <div class='control' @click='actionSelect'><div :title='$t(controlName)'>{{$t(controlName)}}&nbsp;</div></div>  
        <div class='entity-dropdown' slot='dropdown' v-on-clickaway='actionSelect'>
            <div v-if='searchControl' class='search'>
                <SearchInput :timeout='search_timeout' :text='search_text' v-on:search="search" />
            </div>

            <ui-list class='drop' :sync='false' select :dataSource='dataSource'  
                v-on:item='onItem' 
                v-on:select='onItem' 
                :not_found='not_found'
            >
                <template slot='template' slot-scope='props'>
                    <component class='display' v-if='dataSource.template' :is='dataSource.template' :model='props.item' style='margin: -14px -15px' />
                    <slot v-else name='template' :item='props.item'/>            
                </template>

                <template slot='not-found'>
                    <slot name='not-found' />           
                </template>
                
            </ui-list>
        </div>
        <div v-if='prefix' slot='prefix'>{{prefix}}</div>
        <div v-if='suffix' slot='suffix'>{{suffix}}</div>
    </ui-control>`
}

export * from './display'