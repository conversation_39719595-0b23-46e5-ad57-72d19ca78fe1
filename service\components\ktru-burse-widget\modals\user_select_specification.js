import Vue from "vue"
import ebpSearchTable from '../components/ebp_search_table'
import ebpSearchTree from '../components/ebp_search_tree'
import { Http } from '@iac/core'
import getUtils from '../utils'
import userCreateSpecification from './user_create_specification'

let searchTimeoutID = undefined

const SelectBurseProduct = {
  props: {
    onSelect: {
      type: Function,
      required: true
    }
  },
  components: {
    'ebpSearchTree': ebpSearchTree,
    'ebpSearchTable': ebpSearchTable
  },
  data() {
    return {
      searchQuery: "",
      searchResult: undefined,
      searchLoading: false,
      ...getUtils()
    }
  },
  watch: {
    searchQuery(_newVal, _oldVal) {
      clearTimeout(searchTimeoutID)
      searchTimeoutID = setTimeout(() => this.search(), 300)
    }
  },
  methods: {
    clearQuery() {
      this.searchQuery = ""
      this.searchResult = undefined
      this.searchLoading = false
    },
    async search() {
      const { searchQuery: query = "" } = this
      if (query.length < 3) {
        return
      }
      this.searchLoading = true
      const { error, data } = await Http.api.rpc("ref", {
        ref: "ref_enkt_burse_product",
        op: "read",
        limit: 15,
        offset: 0,
        query,
        fields: ['id', 'product', 'meta']
      })
      if (!error && data) {
        data.forEach(item => {
          delete item.__schema__
          item.product.id = item.id
          delete item.id
        })
        this.searchResult = data
      }

      this.searchLoading = false
    },
    async setCurrentItem(item = undefined) {
      const currentItem = item?.product
      if (currentItem) {
        const productSpecification = await userCreateSpecification.Modal({ size: "lg", currentItem, onSelect: this.onSelect })
        this.Close(productSpecification)
      } else {
        this.Close()
      }
    },
    redirectToBurseProductRequest() {
      this.$router.push({ path: '/workspace/exchange/burse_product_request' })
    }
  },
  mounted() {
    setTimeout(() => this.$refs?.search?.focus?.(), 300)
    window.addEventListener('message', this.onMessage)
  },
  beforeDestroy() {
    window.removeEventListener('message', this.onMessage);
  },
  template: `
  <div>
    <header>{{$t('classifier_of_goods_works_services_burse')}}</header>
    <main style='display: flex; padding: 0;'>
      <div class='iac--ktru-burse' @keydown.esc="e=>setCurrentItem()">
        <div class="iac--ktru-burse__search">
          <div>
            <icon v-if="searchLoading" class="iac--to-spin">spinner</icon>
            <icon v-else>search</icon>
          </div>
          <input ref="search" :placeholder="$t('search_for_product')" v-model="searchQuery" @keydown.enter="search"/>
          <button v-if="searchQuery.length || searchResult " @click="clearQuery">
            <icon>delete</icon>
          </button>
        </div>      
        <ebpSearchTable v-if="searchResult" :searchResult="searchResult" @setCurrentItem="setCurrentItem"/>
        <ebpSearchTree v-else @setCurrentItem="setCurrentItem" :emptyRootMode="true"/>
      </div>
    </main>
  </div>
  `
}

export default Vue.Dialog(SelectBurseProduct)
Vue.Dialog("SelectBurseProduct", SelectBurseProduct)