
import { Http } from "@iac/core";

export default class Model {
    constructor(context = {}) {
        this.date = new Date(context.date);
        this.entity_id = context.entity_id

        this.languages = context.template?.languages || {}
        this.template = context.template?.content || [];
        this.data = context.data || {};
        this.has_save = false

        for (let i in this.template) {
            if (this.template[i])
                this.template[i].wait = false;
        }

        for (let i in this.data) {
            this.data[i] = this.data[i].filter((item)=>{
                return item;
            })
        }

    }

    async set_entity(entity_id) {
        if (this.entity_id == entity_id)
            return;
        this.entity_id = entity_id
        let { error, data } = await Model.get_data(entity_id)
        if (data) {
            this.data = data[0]?.data || {};
        } else {
            this.data = {};
        }
        for (let i in this.data) {
            this.data[i] = this.data[i].filter((item)=>{
                return item;
            })
        }
    }

    async set_date(date, type = "procurement") {
        date = new Date(date);
        if (date.getTime() == this.date.getTime())
            return;
        this.date = date;
        let { error, data } = await Model.get_template(date)

        let template = data && data[0]?.data;

        this.languages = template?.languages || {}
        this.template = template?.content || [];

        for (let i in this.template) {
            if (this.template[i])
                this.template[i].wait = false;
        }

        

    }

    async save_item(section, index, params) {


        if (!this.entity_id)
            return;
        let key = [this.entity_id];
        if (section >= 0) {

            if (this.template[section])
                this.template[section].wait = true;

            key.push(section)
            if (index >= 0) {
                key.push(index)
            }
        }

        let { error, data } = await Http.api.rpc("ref", {
            ref: "ref_documentation_fields",
            op: "update_key",
            filters: {
            },
            data: {
                key: key.join("."),
                data: params
            },
        });

        if (section >= 0 && this.template[section])
            this.template[section].wait = false;

        return {
            error,
            data: data && params
        }

    }

    /*async save_item(section, item) {
        if (!this.entity_id)
            return;

        let key = [this.entity_id];
        let data = this.data;
        if (section >=0) {
            data = data[section] || {}
            key.push(section)
            if (item >=0) {
                data = data[item]
                key.push(item)
            }
        }

        console.log("save_item", key.join("."), data);
    }*/

    get save() {
        if (this.has_save && this.entity_id)
            return async () => {
                this.has_save = false;

                await Http.api.rpc("ref", {
                    ref: "ref_documentation_fields",
                    op: "update_key",
                    filters: {
                        //object_id: "1010555",

                    },
                    // "limit":6,
                    // "offset":0,
                    data: {
                        key: `${this.entity_id}`,
                        data: this.data
                    },
                });
            }
    }

    static async get_template(date, type = "procurement") {

        return await Http.api.rpc("ref", {
            ref: "ref_documentation_pages",
            op: "read",
            filters: {
                type: type,
                status: "public",
                updated_at_lte: date
            },
            limit: 1
        });
    }

    static async get_data(entity_id) {
        return await Http.api.rpc("ref", {
            ref: "ref_documentation_fields",
            op: "read",
            filters: {
                object_id: entity_id
            }
        });
    }

    static async get(content = {type: 'procurement'}) {

        let template = undefined;
        if (content.date) {
            // Получаем шаблон
            let { error, data } = await Model.get_template(content.date, content.type);
            if (data) {
                template = data[0]?.data;
            }
        }

        let model_data = undefined;
        if (content.entity_id) {
            // Получаем данные
            let { error, data } = await Model.get_data(content.entity_id);
            console.log(data);
            if (data) {
                model_data = data[0]?.data;
            }

        }







        console.log("get", template, model_data);

        return new Model({
            entity_id: content.entity_id,
            date: content.date,

            template: template,
            data: model_data
        });
    }
}