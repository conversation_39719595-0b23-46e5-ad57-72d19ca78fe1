import Vue from 'vue'
import { Language } from '@iac/core'
import { DataSource, RefStore } from '@iac/data'

const Component = {
    props: ['model'],
    computed: {
        procedure_url() {
            if(this.model.proc_type == 'custom_contract'){
                return `/workspace/contract/${this.model.proc_id}.0.0/core` 
            }
            return `/procedure/${this.model.proc_id}/core`
        },
        position_url() {
            return `/workspace/purchase/${this.model.id}`
        },
        month() {
            let { month_begin: begin, month_end: end } = this.model;
            if (begin < end) {
                return Language.t(`month_${begin}`) + ' - ' + Language.t(`month_${end}`)
            } else if (begin > end) {
                return Language.t(`month_${end}`) + ' - ' + Language.t(`month_${begin}`)
            } else {
                return Language.t(`month_${begin}`)
            }
        },
    },
    methods:{
        openProducts(){
            Vue.Dialog({
                props: ["position"],
                data: function(){
                    return {
                        source: new DataSource({
                            query: {
                                positions_id: this.position
                            },
                            store: new RefStore({
                                method: 'ref_public_schedule',
                                ref: 'schedule_goods',
                                context: (item)=>{
                                    return item
                                },
                                inject: (items) => {
                                    if (!items)
                                        return;
                
                                    let product_ids = {};
                                    items.forEach((item) => {
                
                                        if (item.product_name)
                                            return;
                                        //item.product_name = item.product_id
                                        product_ids[item.product_id] = product_ids[item.product_id] || {
                                            items: []
                                        };
                                        product_ids[item.product_id].items.push(item)
                                    })
                
                                    DataSource.get("ref_products").byKeys(Object.keys(product_ids)).then((products) => {
                                        products.forEach((product_item) => {
                                            let items = product_ids[product_item.id] && product_ids[product_item.id].items;
                                            if (items) {
                                                items.forEach((item) => {
                                                    item.product_name = product_item.name;
                                                })
                                            }
                                        })
                                    })
                
                                    return items;
                                }
                            }),
                        })
                    }
                },
                methods: {
                    properties(product){
                        Vue.Dialog({
                            props: ['model'],
                            template: `
                              <div>
                                <header>{{model.product_name || model.name}}</header>
                                <main>
                                  <iac-layout-static :value='model.product_properties' />
                                </main>
                                <footer>
                                  <ui-btn type='secondary' v-on:click.native='Close'>{{$t('Close')}}</ui-btn>
                                </footer>
                              </div>
                            `
                          }).Modal({
                            model: product
                          })
                    }
                },
                template: `
                    <div>
                        <header>{{$t('link.products')}}</header>
                        <main>
                            <ui-list :sync='false' :dataSource='source'>
                                <div  slot='item' slot-scope='props' :model='props.item'> 
                                    <a v-if='props.item.product_properties' href='' v-on:click.prevent='properties(props.item)'>{{props.item.product_name}}&nbsp;</a>
                                    <span v-else>{{props.item.product_name}}&nbsp;</span>
                                    <div style='color: #bbb; display: flex; font-size: 13px; border-bottom: 1px solid #eee; margin-bottom: 16px;'>
                                        <div style='flex: 1 1 auto;'><iac-number :value='props.item.amount' delimiter=' '  part='2' /> <ui-ref source='ref_unit' :value='props.item.unit' /> </div>
                                        <div><iac-number :value='props.item.price' delimiter=' '  part='2' />{{$settings._default_currency}}</div>
                                    </div>
                                </div>
                            </ui-list>
                        </main>
                    </div>
                `
            }).Modal({
                size: "right",
                position: this.model.id
            })
        }
    },
    template: `
        <ui-data-view-item :model='model'>
            <template slot='header'>
                <div>
                    <span>{{model.year}}</span>
                    <span>{{month}}</span>
                </div>
                <div v-if='model.proc_id && (model.proc_status!="draft" || model.proc_status==null)'>
                    <router-link :to='procedure_url'> {{ model.proc_type == 'custom_contract' ? $t("contract") :  $t(model.proc_type) }} № {{model.proc_id}}</router-link>
                </div>
                <div v-else-if='!model.public'>{{$t(model.status)}}</div>
            </template>
            <template slot='title'>
                <router-link :to='position_url' v-if='!model.public'>{{model.name}}</router-link>
                <div v-else>{{model.name}}</div>
            </template>

            <template slot='description' v-if='model.company'>
                <div><label>{{$t('nav.company')}}:</label> <span>{{model.company.inn}}, {{model.company.title}}</span></div>
            </template>

            <template slot='props'>

                <div>
                    <label>{{$t('quantity')}}</label>
                    <div v-if='model.count <= 0'>
                        <iac-number :value='model.count' delimiter=' ' part=''/>
                        {{ $t('product', { count: model.count }) }}
                    </div>
                    <div v-else>
                        <a @click.prevent='openProducts' href='#'><iac-number :value='model.count' delimiter=' ' part=''/>
                        {{ $t('product', { count: model.count }) }}</a>
                    </div>
                </div>

                <div>
                    <label>{{$t('total_sum')}}</label>
                    <div><iac-number :value='model.total_price' delimiter=' ' part='2'/> <span class='sub'>{{model.currency}}</span></div>
                </div>


            </template>
        </ui-data-view-item>
    `
}

Vue.component('template-purchase', Component);