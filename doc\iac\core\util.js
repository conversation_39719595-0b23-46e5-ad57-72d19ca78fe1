export default `
## Util

> Дополнительный набор методов

- [X] Number
- [ ] Date

### Number
    import {Util} from '@iac/core'

    /**********************************************
    * Переводит число в строку формата # ### ###.##
    * value: входящие значение
    * delimiter: Символ разделитель
    * part: знаков после запятой
    * round: округлять или нет
    ***********************************************/
    Util.Number(value, delimiter, part=0, round=false)



`