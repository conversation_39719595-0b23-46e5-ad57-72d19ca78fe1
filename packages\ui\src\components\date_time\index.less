.date-time {
    display: inline-flex;
}

.ui-ctrl-date {
    width: 260px;
    //height: 240px;
    display: block;

    display: flex;
    //align-items: center;
    //justify-content: center;
    color: #737373;
    cursor: default;
    display: flex;
    user-select: none;
    flex-direction: column;
    background: #fff;

    >.year >.value{
        cursor: pointer;
    }
    >.year,
    >.month {


        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        color: #737373;
        line-height: 32px;
        font-size: 14px;
        text-align: center;
        display: flex;
        flex: 0 0 32px;

        >span {
            flex: 0 0 32px;
            align-items: center;
            justify-content: center;
            padding: 0 16px;
            display: flex;

            &:not(.value) {
                cursor: pointer;
                border-radius: 5px;
                margin: 5px;

                &:hover {
                    background: #eee;
                }

                &:before {
                    content: " ";
                    display: block;
                    width: 9px;
                    height: 9px;
                    border: 2px solid #2597CC;
                    transform: rotateZ(45deg);
                    line-height: 32px;
                }
            }

            &:first-child:before {
                border-right: none;
                border-top: none;
            }

            &:last-child:before {
                border-left: none;
                border-bottom: none;
            }

            &.value {
                flex: 1 1 auto;
                position: relative;
                >.list {
                    display: inline-block;
                    position: absolute;
                    background: #fff;
                    border: 1px solid rgba(0, 0, 0, 0.137);
                    left: 0;
                    right: 0;
                    border-radius: 4px;
                    box-shadow: 0 0 5px 1px #0003;
                    z-index: 1;
                    >div{
                        &:not(:last-child) {
                            border-bottom: 1px solid #0002;
                        }
                        cursor: pointer;
                        &:hover{
                            background: #0001;
                        }
                        &.active{
                            background: #dbf4ff;
                        }
                    }
                }
            }
        }
    }

    >.weeks {
        flex: 0 0 27px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 0 5px;

        >span {
            font-size: 11px;
            line-height: 15px;
            color: #737373;
            font-weight: bold;
        }
    }

    >.days {
        flex: 1 1 auto;
        flex-wrap: wrap;
        display: flex;
        align-items: center;
        padding: 5px;

        >.week{
            border-radius: 5px;
            width: 100%; 
            display: flex;
            >span {
                flex: 0 0 14.28%;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                //border-radius: 50%;
                color: #737373;
                font-size: 12px;
                line-height: 15px;
                padding: 1px;
                cursor: pointer;
                >span {
                    width: 27px;
                    height: 27px;
                    line-height: 27px;
                    border-radius: 50%;
                }
                &.another >span{
                    opacity: 0.2;
                }
                &:hover {
                    &.another >span{
                        opacity: 0.8;
                    }
                    >span {
                        background: #E3E3E3; 
                    }
                }
            }

        }

        &:not(.select_week){
            >.week{
                >span {
                    &.value {
                        &.another >span{
                            opacity: 0.8;
                        }
                        >span {
                            background: rgb(180, 207, 250);
                            color: rgb(24, 90, 188);
                        }
                        &:hover>span {
                            background: rgb(164, 189, 230);
                            color: #fff;
                        }
                    }
        
                    &.current {
                        &.another >span{
                            opacity: 0.8;
                        }
                        >span {
                            background: rgb(26, 115, 232);
                            color: #fff;
                        }
                        &:hover>span {
                            background: rgb(19, 90, 182);
                            color: #fff;
                        }
                    }
                }
            }
        }

        &.select_week{
            >.week{
                >span {
                    >span {
                        border-radius: 5px;
                    }
                    
                    &:hover>span {
                        background: unset;
                    }
                }
 
                &.active{
                    >span {
                        background: rgb(180, 207, 250);
                        color: rgb(24, 90, 188);
                    }
                } 

                &:hover{
                    >span {
                        background: #f0f0f0;
                        &:nth-child(-n+5){
                            background: #DDD;
                            color: #000;                            
                        }
                    
                    }
                    &.active{ 
                        >span {
                            background: rgb(180, 207, 250);
                            &:nth-child(-n+5){
                                background: rgb(155, 181, 223);
                                color: #000;                            
                            }
                        
                        }
                    }
                }                
                
            }
        }

        >span {
            flex: 0 0 14.28%;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            //border-radius: 50%;
            color: #737373;
            font-size: 12px;
            line-height: 15px;
            padding: 1px;

            >span {
                width: 27px;
                height: 27px;
                line-height: 27px;
                border-radius: 50%;
                cursor: pointer;
            }

            &.value {
                >span {
                    background: rgb(180, 207, 250);
                    color: rgb(24, 90, 188);
                }
            }

            &.current {
                >span {
                    background: rgb(26, 115, 232);
                    color: #fff;
                }
            }

            &:hover>span {
                background: #E3E3E3;
            }

            &.red {
                >span {
                    background: #fde7e7;
                    color: #d24646;
                }

                &:hover>span {
                    background: #ffd4d4;
                }
            }
        }
    }
}

.ui-input-date {
    position: relative;

    >.value {
        padding: 5px;
        cursor: pointer;
        display: block;
        border-radius: 5px;

        &:hover {
            background: #eee;
        }
    }

    >.control {
        transition: transform 0.3s;
        top: 100%;
        z-index: 10;
        border: 1px solid #ccc;
        border-radius: 5px;
        display: block;
        box-sizing: border-box;
        position: absolute;
        //overflow: hidden;
        box-shadow: 0px 3px 7px rgba(0, 0, 0, 0.1);
    }

    &.dropdown-top {
        >.control {
            top: 0;
            transform: translateY(-100%);
            box-shadow: 0px -3px 7px rgba(0, 0, 0, 0.1);
        }
    }
}

.ui-input-time {
    >.value {
        display: inline-block;
        position: relative;

        >span {
            padding: 5px;
            cursor: pointer;
            display: inline-block;
            border-radius: 5px;

            &:hover {
                background: #eee;
            }
        }

        >.control {
            transition: transform 0.3s;
            top: 100%;
            position: absolute;
            z-index: 10;
            border: 1px solid #ccc;
            background: #fff;
            max-height: 200px;
            overflow: hidden;
            overflow-y: auto;
            min-width: 100px;
            border-radius: 5px;
            box-shadow: 0px 3px 7px rgba(0, 0, 0, 0.1);

            >div {
                margin-top: -1px;
                text-align: center;
                padding: 5px 10px;
                border-top: 1px solid #ccc;
                cursor: pointer;

                &:hover {
                    background: #eee;
                }
            }
        }
    }

    &.dropdown-top {
        >.value {
            >.control {
                top: 0;
                transform: translateY(-100%);
                box-shadow: 0px -3px 7px rgba(0, 0, 0, 0.1);
            }
        }
    }
}

.ui-date {
    >.container>.control {
        >* {
            display: inline-block;

            &:first-child {
                margin-left: -5px;
            }
        }
    }

    &.readonly,
    &.disabled {
        >.container>.control {

            >*>.value,
            >*>.value>span {
                cursor: default;

                &:hover {
                    background: unset;
                }
            }
        }
    }

}