# UI

* [Директивы](#директивы)
    * [clickaway](#clickaway)
* [Компоненты](#компоненты)
    * action
    * alert
    * button
    * button-group
    * checkbox
    * [control](#control)
    * data-grid
    * data-list - Устаревший компонент
    * data-view
    * date_time
    * eimzo
    * entity
    * enum
    * enum_tree
    * error
    * [field](#field)
    * file
    * html
    * info
    * input
    * layout
    * link
    * list
    * marked
    * ref
    * slider
    * static
    * tag
    * text
* Диалоговые окна
    * dialog
    * message_box
* less

## Директивы
---

## clickaway
Позволяет отследить клик за пределами элемента

Пример:
```
<div v-if='dropdown' v-on-clickaway='dropdown = false'></div>
```

## Компоненты
---

## control
Базовый компонент для элементов ввода

### входящие свойства:
* has_del - добавляет иконку для сброса значения
* icon - иконка
* opened - компонент в фокусе 
* disabled 
* readonly
* dropdown - отображает выпадающую область
* required - признак обязательности
* label - заголовок компонента
* status - подсвечивает цветом статуса
* actions - массив действий - отображается в виде выпадающего меню
* wait - временная блокировка компонента (используется при отправки данных на сервере)

### слоты
* dropdown
* stoolbar

### $emit
* action - параметры clear|select

## field
Компонент для отображения виджета с label(required), description, status
Данный компонент также отвечает за multiple

### входящие свойства:
* model - JSON или Объект класса [Property](./../data/README.md#property) из пакета [@iac/data](./../data/README.md)
