#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: \n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.0.6\n"


msgid "requirement.create"
msgstr "Создание требования"

msgid "requirement.name"
msgstr "Наименование требования"

msgid "requirement.description"
msgstr "Описание требования"

msgid "requirement.file_attach"
msgstr "Требуется ли загрузка файла от участника?"

msgid "requirement.file_attach_name"
msgstr "Наименование файла"

msgid "requirement.type_proposal"
msgstr "Что должен указать участник в своем ответе"

msgid "requirement.type_proposal_text"
msgstr "Участники введут произвольный текст"

msgid "requirement.type_proposal_text_desc"
msgstr "введенный текст будет оцениваться экспертно"

msgid "requirement.type_proposal_text_error"
msgstr "Данный критерий нельзя изменить так как участник больше не может в своем ответе вводить произвольный текст. Удалите пожалуйста данный критерий и создайте новый"

msgid "requirement.type_proposal_number"
msgstr "Участники будут вводить число"

msgid "requirement.type_proposal_number_desc"
msgstr "указанное число будет оцениваться по его соответствию требуемому интервалу допустимых значений"

msgid "requirement.type_proposal_boolean"
msgstr "Участники ответят Да или Нет"

msgid "requirement.type_proposal_boolean_desc"
msgstr "ответ от участника будет оцениваться по правилам, указанным далее в Настройках оценки"

msgid "requirement.type_proposal_boolean_0"
msgstr "Участники могут указать только Нет"

msgid "requirement.type_proposal_boolean_0_desc"
msgstr "участник не сможет подать предложение с ответом \"ДА\""

msgid "requirement.type_proposal_boolean_1"
msgstr "Участники могут указать только Да"

msgid "requirement.type_proposal_boolean_1_desc"
msgstr "участник не сможет подать предложение с ответом \"НЕТ\""

msgid "requirement.type_proposal_select"
msgstr "Участник будет выбирать из списка"

msgid "requirement.type_proposal_select_desc"
msgstr "Выбранный вариант будет оцениваться экспертно"

msgid "requirement.mark_auto"
msgstr "Кто оценивает ответ участника"

msgid "requirement.mark_auto_0"
msgstr "Система автоматически проставит баллы"

msgid "requirement.mark_auto_0_desc"
msgstr "при автоматическом выставлении оценки необходимо задать правило перевода числовых значений в баллы. Возможно указание обратно-пропорциональной оценки, когда бОльшее значение получит меньший балл. Получаемые баллы всегда будут находиться в диапазоне от 0 до 5 баллов"

msgid "requirement.mark_auto_1"
msgstr "Оценку выставляет эксперт"

msgid "requirement.mark_auto_1_desc"
msgstr "если оценку выставляет эксперт и это – оценка соответствия, то возможными значениями являются: 0 баллов (предложение не соответствует требованию), 3 балла (частично соответствует), 5 баллов (полностью соответствует)"

msgid "requirement.mark"
msgstr "Как будет оценивать эксперт?"

msgid "requirement.mark_number"
msgstr "Эксперт выставит баллы"

msgid "requirement.mark_boolean"
msgstr "Эксперт укажет уровень соответствия"


msgid "requirement.boolean_min_mark_label"
msgstr "За ответ Нет"

msgid "requirement.boolean_max_mark_label"
msgstr "За ответ Да"

msgid "requirement.mark_label"
msgstr "Участник получит"

msgid "requirement.weight"
msgstr "Установите относительное значение для данного требования"

msgid "requirement.min_label"
msgstr "Введите значение"

msgid "requirement.var_label"
msgstr "Если участник укажет"

msgid "requirement.var_eq"
msgstr "Значение равное"

msgid "requirement.var_lte"
msgstr "Меньше или равно"

msgid "requirement.var_min"
msgstr "Минимальное число"

msgid "requirement.var_gte"
msgstr "Больше или равно"

msgid "requirement.var_max"
msgstr "Максимальное число"


msgid "requirement.valid_1"
msgstr "Должно быть меньше"

msgid "requirement.valid_2"
msgstr "Должно быть больше"


msgid "requirement.name_info"
msgstr "<p>Примеры:</p>\n<ul>\n<li>Наличие лицензии</li>\n<li>Наличие складских помещений</li>\n<li>Количество сотрудников</li>\n<li>Наличие сервисного центра</li>\n</ul>"

msgid "requirement.description_info"
msgstr "<p>Примеры:</p>\n<ul>\n<li>Приложите файл с лицензией</li>\n<li>Опишите предлагаемые складские помещения</li>\n<li>Укажите количество сотрудников</li>\n<li>Подтвердите наличие авторизованного сервисного центра</li>\n</ul>"

msgid "requirement.weight_info"
msgstr "<p>Относительные значения по каждому требованию используются при расчете общей оценки предложения участника на данном этапе отбора.</p>\n<p>При этом будет учтена значимость для организатора каждого отдельного требования.</p>\n<ul>\n<li>0.1 – минимальная значимость требования</li>\n<li>10 – максимальная значимость требования</li>\n</ul>"





msgid "bad_params"
msgstr "Ошибка заполнения"

msgid "field_setting.warning"
msgstr "Внимание"

msgid "field_setting.request_params_mark"
msgstr "Параметры запроса"

msgid "field_setting.add_file"
msgstr "Прикрепите файл"

msgid "field_setting.info_value"
msgstr "Введите требования"

msgid "back"
msgstr "Назад"

msgid "next"
msgstr "Далее"

msgid "delete_field"
msgstr "Удалить поле"

msgid "field_setting.type_mark"
msgstr "Тип оценки"

msgid "field_setting.type_mark.edit"
msgstr "Выберите тип оценки"

msgid "field_setting.general"
msgstr "Создание информационного требования"


msgid "field_setting.general.mark"
msgstr "Создание критерия"

msgid "field_setting.mark"
msgstr "Настройка оценки"

msgid "field_setting.file_attach_group"
msgstr "Настройка файла"

msgid "field_setting.name"
msgstr "Введите наименование критерия"

msgid "field_setting.name_info"
msgstr "(Например: “Наличие лицензии” или “Количество сотрудников”)"

msgid "field_setting.value"
msgstr "Опишите требования"

msgid "field_setting.value_info"
msgstr "(Например: “Участник в рамках тендра должен предоставить лицензию на проектирование и эксплуатацию сетей передачи данных)"

msgid "field_setting.type_proposal"
msgstr "Выберите варианты/тип ответа участника"

msgid "field_setting.type_proposal_info"
msgstr ""
"«Да/Нет» - ответ от участника будет содержать «Да» или «Нет»"
"«Число» - ответ от участника будет содержать только цифровые значения"

msgid "field_setting.mark_auto"
msgstr "Способ оценки"

msgid "field_setting.mark_auto.edit"
msgstr "Выберите способ оценки"

msgid "field_setting.min"
msgstr "Минимальное"

msgid "field_setting.min.edit"
msgstr "Укажите минимальное значение"

msgid "field_setting.max"
msgstr "Максимальное"

msgid "field_setting.max.edit"
msgstr "Укажите максимальное значение"

msgid "field_setting.vi_min"
msgstr "Критичность"

msgid "field_setting.vi_max"
msgstr "Критичность"

msgid "field_setting.min_mark"
msgstr "Балл"

msgid "field_setting.max_mark"
msgstr "Балл"

msgid "field_setting.weight"
msgstr "Установите относительное значение для критерия"

msgid "field_setting.file_attach"
msgstr "Требуется ли загрузка файла для ответа от участника"

msgid "field_setting.file_attach_info"
msgstr ""
"«Да» - система будет обязательном порядке требовать загрузку файлаот участника"
"«Нет» - система не будет требовать в обязательном порядке загрузку файла от участника, но участник может загрузить файл по собственному желанию."

msgid "field_setting.f_label"
msgstr "Введите наименование подтверждающего файла, загружаемых участником"

msgid "field_setting.points"
msgstr "баллов"

msgid "automatic"
msgstr "Автоматическая"

msgid "expert"
msgstr "Экспертная"

msgid "template_save_caption"
msgstr "Сохранить шаблон?"

msgid "template_save_name"
msgstr "Наименование шаблона"

msgid "template_save_description"
msgstr "Внимание! Если шаблон с таким наименованием уже есть в базе, то он будет перезаписан."

msgid "field_setting.label_error"
msgstr "Наименование не должно начинаться со спец. символов (~!-)"


msgid "field_setting.points"
msgstr "баллов"

msgid "field_setting.point_count"
msgid_plural "field_setting.points_count"
msgstr[0] "{{count}} балл"
msgstr[1] "{{count}} балла"
msgstr[2] "{{count}} баллов"

msgid "field_setting.text_variants"
msgstr "Варианты ответов"

msgid "field_setting.text_variants.placeholder"
msgstr "Варианты ответов (на узбекском языке/на другом языке)"

msgid "field_setting.text_variants.description"
msgstr "первый и последний варианты обязательны для заполнения"

msgid "field_setting.criteria_type"
msgstr "Тип критерия"

msgid "field_setting.sample_file"
msgstr "Образец формы для участника"

msgid "field_setting.sample_file_attach"
msgstr "Образец формы для участника"

msgid "field_setting.other"
msgstr "Другой"

msgid "field_setting.extra_langs"
msgstr "на дополнительном языке"

msgid "field_setting.extra_langs.warning"
msgstr "Были изменения на основном языке"

msgid "field_setting.criteria_type_description"
msgstr "Пояснение требования от Министерства Финансов"

msgid "field.approvement_info_15"
msgstr "Загрузить можно файлы в формате .pdf. Размер не более 15 мб."

