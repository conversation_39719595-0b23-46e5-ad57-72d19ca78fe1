const fs = require('fs');
const path = require('path');

console.clear();

function fromDir(startPath, filter, callback) {
    if (!fs.existsSync(startPath)) {
        return;
    }

    var files = fs.readdirSync(startPath);
    for (var i = 0; i < files.length; i++) {
        var filename = path.join(startPath, files[i]);
        var stat = fs.lstatSync(filename);
        if (stat.isDirectory()) {
            fromDir(filename, filter, callback); //recurse
        }
        else if (filter.test(filename)) callback(filename);
    };
};

var dirs = ['kernel','service','packages']

dirs.forEach((dir)=>{
    let errors = []    
    dir = path.resolve(__dirname, "../",dir)

    fromDir(dir, /\.js$/, function (filePath) {
        
        
        let data = fs.readFileSync(filePath, { encoding: 'utf-8' });
        let templates = data.match(/template: ([`])(?:(?=(\\?))\2.)*?\1/gms);
        if(templates){

            templates = templates.map((template, index) => {
                template = template.substring(template.indexOf("`") + 1, template.length - 1);
                
                // получаем все теги
                let tags = template.match(/<(?:"[^"]*"['"]*|'[^']*'['"]*|[^'">])+>/gms);
                tags = tags.map((source, index) => {
                    let info = source.match(/([^\r\n\t\f\v= '"]+)(?:=(["'])?((?:.(?!\2?\s+(?:\S+)=|\2))+.)\2?)?/gm, '')
                    let attributes = (info.slice(1,info.length-1)) ;
                    let type = 'open'

                    
                    if(attributes.length <= 0){
                        if(/\/>/gm.test(info[0])){
                            type = 'open_close'
                        }else if(/<\//gm.test(info[0])){
                            type = 'close'
                        }
                        if(info.length > 0 && /\/>/gm.test(info[info.length-1])){
                            type = 'open_close'
                        }
                    }else if(attributes.length >0){
                        if(/\/>/gm.test(info[info.length-1])){
                            type = 'open_close'
                        }
                    }

                    let name = info[0].replace(/<\/?/gms,'')
                    name = name.replace('>','')

                  //  tag = tag.replace(/\s+/gm, '')
                    return {
                        name: name.trim(),
                        type: type,
                        source: source,
                        info: info,
                        attributes: attributes,
                        
                    };
                });

                let error = []
                let opens = []
                tags.forEach((tag, index)=>{
                    if(tag.type == 'open_close'){
                        return;
                    }
                    if(tag.type == 'open'){
                        opens.push(tag.name)
                    }else if(tag.type == 'close'){
                        if(opens[opens.length-1] == tag.name){
                            opens.pop()
                        }else{
                            error.push(`${tag.name} (${index+1}) закрылся раньше времени. Ожидаем ${opens[opens.length-1]}`)
                        }
                    }
                })

                if(opens.length>0){
                    error.push(`Нет закрывающихся тегов`)
                }

                let operators = template.match(/\?\./gms);
                if(operators && operators.length > 0){
                    error.push(`Обнаружен оператор опциональной последовательности: ${operators.length} шт `)
                }

                if(error.length>0){
                    errors.push({
                        file: filePath,
                        index: index+1,
                        errors: error
                    })
                }
            })
        }
    });
    
    console.log(dir, `Шаблонов с ошибками: ${errors.length}`)
    console.log("----------------------------------------------------------\n\n")
    errors.forEach((error)=>{
        console.log(`${error.file} (template_${error.index}):\n`,error.errors)
    })
})

