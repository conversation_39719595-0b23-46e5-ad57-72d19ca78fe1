import { Http } from '@iac/core'
import { DataSource, RemoteStore, ArrayStore, Query } from '@iac/data'

export default {
    data: function () {
        return {

            source: new DataSource({
                valueExp: 'report',
                displayExp: function (item) {
                    return item.report_name;
                },
                store: new RemoteStore({
                    method: "report",
                }),
                template: {
                    props: ["model"],
                    computed: {
                        url() {
                            return `/workspace/old_report/${this.model.report}`;
                        }
                    },
                    methods: {
                        async download(type) {
                            let params = {};
                            /*
                            params = await Vue.Dialog({
                                data: function(){
                                    return {
                                        fields: [
                                            {
                                                name: 'from',
                                                group: '!g-/<date>',
                                                type: "date",
                                                has_del: true, 
                                                value: undefined
                                            },
                                            {
                                                name: 'to',
                                                group: '!g-/<date>',
                                                type: "date",
                                                has_del: true, 
                                                value: undefined
                                            }
                                        ]
                                    }
                                },
                                methods: {
                                    download(){
                                        this.Close(this.fields.map((field)=>{
                                            return {
                                                name: field.name,
                                                value: field.value && new Date(field.value)
                                            }
                                        }).reduce((prev,curr)=>{
                                            prev[curr.name] = curr.value
                                            return prev
                                        },{}))
                                    }
                                },
                                template: `
                                    <div>
                                        <header>{{$t("report.page_title")}}</header>
                                        <main>
                                            <ui-layout :fields='fields' />
                                        </main>
                                        <footer>
                                            <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('cancel')}}</ui-btn>
                                            <ui-btn type='primary' v-on:click.native='download'>{{$t('download')}}</ui-btn>
                                        </footer>
                                    </div>
                                `
                            }).Modal({

                            })

                            if(!params)
                                return;
*/


                            this.$wait(async () => {
                                let ref = this.model.report.split(":tmpl")
                                let { error, data } = await Http.report.rpc("render_free_report", {
                                    ref: ref[0],
                                    template: ref[1],
                                    type: type,
                                    params: params,
                                }, {
                                    format: "blob"
                                });

                                if (error && error.code == "AbortError")
                                    return;

                                if (error) {
                                    Vue.Dialog.MessageBox.Error(error)
                                } else {
                                    // Сохраняем файл
                                    var a = document.createElement("a");
                                    document.body.appendChild(a);
                                    a.style = "display: none";
                                    let blob = new Blob([data], { type: "octet/stream" });
                                    let url = window.URL.createObjectURL(blob);
                                    a.href = url;
                                    a.download = `report.${type}`;
                                    a.click();
                                    window.URL.revokeObjectURL(url);
                                    a.remove();
                                }

                            });

                        }
                    },
                    template: `
                    <ui-data-view-item :model='model' style='background: #fff; border: 1px solid #eee; margin-bottom: -1px'>
                        <div slot='title'>
                            <span v-if='model.index'>{{model.index}}.</span> <router-link :to='url'> {{model.report_name}}</router-link>    
                        </div>
                        <div slot='props'>
                            
                            <ui-btn-group>
                                <ui-btn :key='format' type='info xs' v-for='format in model.operations' v-on:click.native='download(format)'>{{format}}</ui-btn>
                            </ui-btn-group>
                        </div>
                        <div v-if='model.description' slot='description' style='color: #777;'>
                        {{model.description}}
                        </div>
                    </ui-data-view-item>

                `
                }
            })
        }
    },
    template: `
        <div class='page-report'>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('nav.reports')}}</li>
                </ol>
                <div class='title'>
                    <h1 style='margin: 0;'>{{$t('nav.reports')}}</h1>
                </div>
            </iac-section>
            <iac-section>
                <ui-list :dataSource='source'>
                    <component  slot='item' slot-scope='props' v-if='source.template' :is='source.template' :model='props.item' />              
                </ui-list>
            </iac-section>
        </div>
    `
}
