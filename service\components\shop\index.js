import Vue from 'vue';
import { Config } from '@iac/kernel';
import RemainTime from './remain_time';

var Component = {
  props: ['item','type', 'key_id'],
  components: {
    RemainTime,
  },
  computed: {
    product_id(){
      return this.item.product && (this.item.product.product_id || "").replace('_','-');
    },
    imgUrl() {
      return Config.api_server + '/file/' + this.item.images[0];
    },
    id(){
      return  this.item[this.key_id || 'id']
    }
  },
  methods: {
    link(item) {
      return `/${this.type || "procedure"}/${item.id}/core`;
    },
    debug_info(info){
      
    }
  },
  template: `
    <div class='tile widget-shop' style='position: relative; height: 100%'>
      
      <span class='favourites' v-if='item.add_to_favourites' v-on:click='item.add_to_favourites'>&#9734;</span>
      <span class='favourites' style='color: #009ab8' v-else-if='item.remove_from_favourites' v-on:click='item.remove_from_favourites'>&#9733;</span>

      <ui-action v-if='item.actions' icon='action' :actions='item.actions' style='position: absolute; right: 0' />
      <div>{{item.mages}}</div>
      <div v-if='item.images && item.images.length' class='p-16'>
        <img :src='imgUrl' class='widget-shop__image' loading='lazy' alt='Фото'/>
      </div>
      <div class='widget-shop__body'>
        <div class='widget-shop__number'><iac-entity-edit  v-if='item.id' :value='{id: item.id, type: "ad"}' /> </div>
        <h2 class='subtitle subtitle--small widget-shop__title mb-8'>
          <div v-if='item.green && $settings.procedures && $settings.procedures._green' :title='$t("green_procedure")'  class='iac-marker green' style='float: right;margin-top: 7px;'/>
          <input style='margin: 0 5px 3px 0;' v-if='item.checkedItems'  type='checkbox' :id='id' :value='id' v-model="item.checkedItems" />
          <router-link :to='link(item)' class='color-inherit'>{{ item.product_name }}</router-link>
        </h2>

        <div class='widget-property content_debug' v-if='$develop.content_debug'>
          <pre>{{ item.debug_info }}</pre>
        </div>

        <div class='widget-property' v-if='product_id'>
          <span>{{ $t('code') }}:</span>
          <div class='widget-property__value'>{{product_id}}</div>
        </div>

        <div class='widget-property'>
          <span>{{ $t('quantity') }}:</span>
          <div class='widget-property__value'><iac-number :value='item.amount' delimiter=' ' part='2' />&nbsp;<ui-ref source='ref_unit' :value='item && item.unit'/></div>
        </div>
        <div class='widget-property'>
          <span>{{ $t('shop.min_amount') }}:</span>
          <div class='widget-property__value'><iac-number :value='item.min_amount' delimiter=' ' part='2' />&nbsp;<ui-ref source='ref_unit' :value='item && item.unit'/></div>
        </div>
        <div class='widget-property'>
          <span>{{ $t('price_unit') }}:</span>
          <span class='widget-property__value'><iac-number :value='+item.price' delimiter=' ' part='2'/> {{ item.currency }}</span>
        </div>
        <div v-if='item.close_at' class='widget-property'>
          <span>{{ $t('tender.close_at') }}:</span>
          <iac-date class='widget-property__value' :date='item.close_at' full />
        </div>
        <div class='widget-property' v-if='item.owner_registration_status'>
          <span>{{ $t('seller_company_status') }}:</span>
          <div class='widget-property__value'>{{ item.owner_registration_status }}</div>
        </div>
      </div>
      <remain-time v-if="!['draft', 'moderated', 'rejected', 'close'].includes(item.status)&&item.remain_time > 0" :value='item.remain_time' />
    </div>
  `,
};

Vue.component('widget-shop', Component);
