import { DataSource, RefStore, Query } from '@iac/data'
import EditorDlg from './_editor_dlg'
import { Http, Language } from '@iac/core'

export default {
    data: function () {
        return {
            dataSource: new DataSource({
                store: new RefStore({
                    method: "common_ref",
                    ref: "ref_affiliated_person",
                    context: (context) => {
                        context.actions = []
                        context.actions.push({
                            label: "edit",
                            handler: async () => {
                                this.onItem(context)
                            }
                        });
                        context.actions.push({
                            type: "sep"
                        });
                        context.actions.push({
                            icon: "delete",
                            label: "delete",
                            btn_type: "danger sm",
                            handler: async () => {
                                if (await Vue.Dialog.MessageBox.Question(Language.t("question_do_you_really_want_to_delete_this_record")) != Vue.Dialog.MessageBox.Result.Yes) {
                                    return;
                                }

                                let { error, data } = await Http.api.rpc("common_ref", {
                                    ref: "ref_affiliated_person",
                                    op: "delete",
                                    filters: {
                                        id: context.id
                                    }
                                })
                                if (!error)
                                    this.dataSource.reload();
                            }
                        });
                        return context;
                    }
                })
            })
        }
    },
    methods: {
        async onItem(item = {}) {
            let model = await EditorDlg.Modal({
                item: {...item},
                size: "lg",
            })

            if (model) {
                for (let field_name in model) {
                    item[field_name] = model[field_name];
                }
                return item;
            }
        },
        async createItem() {
            let item = await this.onItem();
            if (!item)
                return;
            this.dataSource.unshift_item(item)
        }
    },
    template: `
    <iac-access class='iac-affiliation-list' :access='$policy.affiliation_crud'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.affiliation')}}</li>
            </ol>
            <div class='title'>
                <h1>{{$t('nav.affiliation')}}</h1>
                <div v-if='$policy.affiliation_create'><ui-btn type='primary' v-on:click.native='createItem()'>{{$t('add')}}</ui-btn></div>
            </div>
        </iac-section>

        <iac-section>
        <ui-data-tile class='compact top_filter' :dataSource='dataSource' >
            <template slot='template' slot-scope='props'>
                <div class='tile'>
                    <div class=''>
                        <h2 class='subtitle subtitle--small'>
                        <a href='javascript:void(0)'  v-on:click='onItem(props.item)'>{{ props.item.fio }}</a>
                        </h2>
                    </div>
                    <div class='props'>
                        <div><label>{{$t('inn')}}:</label> <span>{{props.item.inn}}</span></div>

                        <div v-if='props.item.share_type && (props.item.share_percent || props.item.share_count)'><label>{{$t('share')}}:</label><span>
                            <template v-if='props.item.share_type == "share" && props.item.share_percent'>
                                <iac-number :value='props.item.share_percent' delimiter=' ' part='2' /> %
                            </template>
                            <template v-if='props.item.share_type == "stock" && props.item.share_count'><iac-number :value='props.item.share_count' delimiter=' ' part='' /> {{$t('stock',{count: props.item.share_count})}}</template>
                        </span></div>  

                        <div v-if='props.item.evidence_affiliate'><label>{{$t('reason')}}:</label> 
                        <span class='text-wrap'><ul v-if='props.item.evidence_affiliate.length > 1'>
                            <li v-for='evidence in props.item.evidence_affiliate'>{{evidence}}</li>
                        </ul><span v-else>{{props.item.evidence_affiliate[0]}}</span></span></div>
                    </div>
                </div>
            </template>
        </ui-data-tile>

        </iac-section>
    </iac-access>
    `
}