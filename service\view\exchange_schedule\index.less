.iac-exchange_schedule {
    >.details {
        display: grid;
        grid-template-columns: repeat(1, 90px 1fr 0);
        grid-auto-flow: column;
        font-size: 14px;
        background: #fff;

        >div {
            border: 1px solid #f3f3f3;
            margin: -1px -1px 0 0;
            padding: 6px;
            color: #201d1d;

            align-content: center;
            &.border{
                //margin-top: 0;
                border-bottom: 2px solid #f3f3f3;
            }
        }

        >.header {
            //background: #ccc;
            position: sticky;
            top: 30px;
            transition: top .3s cubic-bezier(.4, 0, .6, 1), opacity 0s .3s;
            align-content: center;

            padding: 16px 6px;
            font-size: 11px;
            font-weight: 500;
            color: #969595;
            line-height: 1.23;
            letter-spacing: 0.05em;
            text-transform: uppercase;
            border: 1px solid #f3f3f3;

            &.title {
                top: 0;
                line-height: 30px;
                padding: 0 6px;
                text-align: left;
                background: #fff;
            }
        }

        >.title {
            grid-column: 1 / span 3
        }

        >.time {
            grid-column: 1
        }

        >.groups {
            grid-column: 2
        }

        >.products {
            grid-column: 3
        }

    }
}

body {
    &.header_show {
        .iac-exchange_schedule {
            >.details>.header {
                transition: top .3s cubic-bezier(.4,0,.2,1) .3s,opacity 0s .3s;
                top: 143px;

                &.title {
                    top: 113px;
                }
            }

        }
    }
}

@media screen and (min-width: 1000px) {
    .iac-exchange_schedule {
        >.details {
            grid-template-columns: repeat(2, 90px 1fr 0);

            .session_1 {

                &.title,
                &.time {
                    grid-column-start: 4;
                }

                &.groups {
                    grid-column-start: 5;
                }

                &.products {
                    grid-column-start: 6;
                }
            }
        }
    }
}