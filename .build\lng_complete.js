const po2json = require('po2json');
const path = require('path');
const fs = require('fs');


let build_dir = path.resolve(__dirname, "../_build/lng");
let mkdirSync = (dir) => {
    if (fs.existsSync(dir)) {
        return;
    }
    mkdirSync(path.dirname(dir));
    fs.mkdirSync(dir);
}
mkdirSync(build_dir);

let language_dir = path.resolve(__dirname, '../language');
fs.readdirSync(language_dir).filter((name) => {
    return name.split('.').pop() === 'po'
}).map((name)=>{
    return name.split('.').shift();
}).forEach((name) => {
    let po_file = path.resolve(language_dir, `${name}.po`);
    let data = po2json.parseFileSync(po_file,{
        "format": 'jed'
    })

    data.locale_data.messages = Object.keys(data.locale_data.messages).reduce((prev,key)=>{
        let val = data.locale_data.messages[key];
        if(Array.isArray(val) && val.length == 1)
            val = val[0];
        prev[key.toLocaleLowerCase()] = val
        return prev
    },{})

    let language_file = path.resolve(build_dir, `${name}.json`);
    fs.writeFileSync(language_file, JSON.stringify(data))
    
});