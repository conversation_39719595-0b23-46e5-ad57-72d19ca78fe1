

let dir = undefined;

export default {
    data: function () {
        return {
            items: []
        }
    },
    async mounted() {
        if (!dir)
            return;
        for await (let handle of dir.values()) {
            this.items.push(handle);
        }
        this.items = this.items.sort((a, b) => {
            if (a.kind < b.kind)
                return -1;
            if (a.kind > b.kind)
                return 1;
            return 0;
        });
    },
    computed: {

    },
    methods: {
        async add_workspace() {


            const root = await navigator.storage.getDirectory();
            //const draftHandle = await root.getFileHandle("draft1.txt", { create: true });
            //const writable = await draftHandle.createWritable();
            //let write = await writable.write("BLABLA");
            //await writable.close();
           // console.log(write)

           this.items = [];
           for await (let handle of root.values()) {
                const fileData = await handle.getFile();
                console.log(await fileData.text());
               this.items.push(handle)
           }

           this.items = this.items.sort((a, b) => {
               if (a.kind < b.kind)
                   return -1;
               if (a.kind > b.kind)
                   return 1;
               return 0;
           });


           // const fileData = await draftHandle.getFile();
           // console.log(fileData);

            return;
            dir = await showDirectoryPicker();
            this.items = [];
            for await (let handle of dir.values()) {
                this.items.push(handle)
            }

            this.items = this.items.sort((a, b) => {
                if (a.kind < b.kind)
                    return -1;
                if (a.kind > b.kind)
                    return 1;
                return 0;
            });
        }
    },
    template: `
        <div style='padding: 8px 16px;'>
            <div style='padding: 16px 0;'><ui-btn v-on:click.native='add_workspace'>Add workspace</ui-btn></div>
            <div>
                <div v-for='handle in items' style='color: #666'>
                <icon v-if='handle.kind == "file"'>field</icon>
                <icon v-else>doc_group</icon>
                <span>{{handle.name}}</span>
                </div>
            </div>

        </div>
    `
}