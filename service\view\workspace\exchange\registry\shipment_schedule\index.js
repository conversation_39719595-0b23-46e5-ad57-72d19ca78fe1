import oldVersion from './old_version'
import {DataSource} from '@iac/data'
import Model from './model'

export default {
    data: function(){
        return {
            source: new DataSource({
                form: {
                    model: Model,
                    actions: {
                        create: "exc_pgo_booking_create",
                        update: "exc_pgo_booking_create",
                        delete: "exc_pgo_booking_create"
                    }
                },
                store: {
                    method: "ref_pgo",
                    ref: "pgo"
                }
            })
        }
    },
    components: {
        oldVersion,
    },
    template: `
        <old-version v-if='1' />
        <div v-else>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('nav.shipment_schedule')}}</li>
                </ol>
                <h1>{{$t('nav.shipment_schedule')}}</h1>
            </iac-section>
            <iac-section>
                <ui-data-view :dataSource='source'/>
            </iac-section>
        </div>
    `
};