.iac-map {

  &__svg {
    max-width: 100%;
    height: auto;

    path {
      cursor: pointer;
      fill: white;
      stroke: #009ab8;
      outline: none;
      
      &:hover,
      &.active {
        fill: fade(@brand-primary, 30%);
      }
    }
  }

  &__card {
    padding-top: 39px;
    padding-bottom: 39px;
  }

  &__reset {
    position: absolute;
    top: 26px;
    right: 13px;
    border: none;
    padding: 5px 6px;
    background-color: transparent;
    font-size: 12px;
    color: #868e96;
    cursor: pointer;

    &:hover,
    &:focus-visible {
      color: #666;
      background: #eee;
    }
  }
}

.statistics-table {
  width: 100%;
  font-size: 14px;
  line-height: 1.43;
  color: @gray;
  border-collapse: collapse;

  tr {
    border-bottom: 1px solid @light-gray;
  }

  td {
    padding: 16px 16px 16px 0;
    vertical-align: top;
  }

  p {
    margin: 0;
  }

  &__title {
    margin: 8px 0;
    font-size: 13px;
    font-weight: 500;
    line-height: 1.23;
  }

  &__count {
    display: block;
    font-size: 36px;
    font-weight: 500;
    color: @brand-primary;
    line-height: 1.333;
  }

  &__label {
    display: block;
    margin-top: -4px;
  }
}

@media (min-width: 421px) {
  .iac-map {

    &__reset {
      top: 25px;
      right: 6px;
    }
  }
}