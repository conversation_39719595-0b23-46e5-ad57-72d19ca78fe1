import {DataSource, Entity} from '@iac/data'
import {Guid} from '@iac/core'
class Model extends Entity {
    constructor(context){
        super(context)

        this.fields.forEach(field => {
            this[field.name] = context[field.name]
            field.attr = field.attr || {}
            field.attr.react = true;
        });
    }
    props(){
        return {

        }
    }

    get save(){
        if(this.validate(false))
            return;
        return async ()=>{

            let params = this.fields.filter((field) => {
                switch(field.type){
                    case "static":
                    case "action":
                    case "link":
                    case "widget":
                        return false;
                }

                if (field.hidden && typeof field.hidden == 'function') {
                    return !field.hidden();
                }
                return !field.hidden;
            }).map((field) => {
                let value = field.value;
                if (field.value && field.value.exp && field.value.exp.value != undefined) {
                    value = field.value.exp.value
                }

                if (Array.isArray(value) && value.length <= 0)
                    value = undefined;

                if(value == undefined || value == '')
                    value = null;

                return {
                    name: field.name,
                    value: value
                }
            }).reduce((prev, curr) => {
                prev[curr.name] = curr.value
                return prev;
            }, {})

            return {
                data: params
            }
        }
    }

}

let editModelDlg = Vue.Dialog({
    props: ["model"],
    methods: {
        save(){
            this.$wait(async ()=>{
                let {error, data} = await this.model.save();
                if(data){
                    this.Close(data);
                }
            })
        }
    },
    template: `
        <div>
            <main><ui-layout :fields='model.fields'/></main>
            <footer>
                <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
                <ui-btn type='primary' :disabled='!model.save' v-on:click.native='save'>{{model.id ? $t("edit") : $t('create')}}</ui-btn>
            </footer>
        </div>
    `
})


const GridComponent = {
    props: {
        value: Array,
        fields: Object,
        columns: Array,
        readonly: Boolean,
        has_del: Boolean,
        react: {
            type: Boolean,
            default: false
        }
    },
    watch: {
        value: {
            immediate: false,
            async handler(value, oldValue) {
                this.on_content_change = false;
                this.dataSource.store.setData({data: (value || []).map((item)=>{
                    return {...item} 
                })})
                this.dataSource.reload();
            }
        }
    },
    data: function(){

        return {
            on_content_change: false,
            dataSource: new DataSource({
                actions: [
                    {
                        label: "add",
                        handler: async ()=>{
                            if(!await this.edit())
                                return;
                            this.emit();
                        }
                    },
                    {
                        label: "apply",
                        btn_type: "success",
                        hidden: ()=>{
                            if(this.react)
                                return true

                            return !this.on_content_change;
                        },
                        handler: async ()=>{
                            this.emit();
                        }
                    },
                    {
                        label: "clear",
                        btn_type: "danger",
                        hidden: ()=>{
                            return !this.has_del || this.dataSource.items.length <= 0
                        },
                        handler: ()=>{
                            this.$emit("change",undefined)
                        }
                    }
                ],
                store: {
                    data: (this.value || []).map((item)=>{
                        return {...item}
                    }),
                    context: (context)=>{
                        context.raw = {...context}
                        !context.hasOwnProperty('bindClass') && Object.defineProperty(context, "bindClass", {
                            get: () => {
                                if(context.status == "new"){
                                    return "ui-alert ui-alert-success"
                                }
                                if(context.status == "edit"){
                                    return "ui-alert ui-alert-warning"
                                }
                                if(context.status == "delete"){
                                    return "ui-alert ui-alert-danger"
                                }
                            }
                        })
                        
                        context.actions = [
                            {
                                icon: 'edit',
                                btn_type: 'warning',
                                handler: async ()=>{
                                    if(!await this.edit(context))
                                        return;
                                    this.emit();
                                }
                            },
                            {
                                icon: 'delete',
                                btn_type: 'danger',
                                handler: ()=>{

                                    Vue.set(context,"status","delete")
                                    this.on_content_change = true;
                                    if(this.react)
                                        this.emit();
                                   /*
                                    let index = this.dataSource.items.indexOf(context)
                                    this.dataSource.items.splice(index,1)
                                    this.on_content_change = true;
                                    if(this.react)
                                        this.emit();*/
                                }
                            }
                        ]
                        return context;
                    }
                }
            })
        }
    },
    methods: {
         async edit(context){

            let model = new Model({
                ...context || {},
                props: this.fields,
                id: !!context
            })

            let result = await editModelDlg.Modal({
                model: model
            })

            if(!result)
                return false

            if(!context){
                let item = this.dataSource.init_item(this.dataSource._store.context(result));
                item.status = "new"
                this.dataSource.items.push(item);
            }else{
                Object.keys(result).forEach((key)=>{
                    context[key]  = context.raw[key]  =result[key]
                })
                Vue.set(context,"status","edit")
            }

            this.on_content_change = true;

            if(this.react)
                return true;
        },
        emit(){
            let items = this.dataSource.items.filter((item)=>{
                return item.status != 'delete'
            }).map((item)=>{
                Vue.set(item,"status",undefined)
                return item.raw;
            })
            
            this.$emit("change",items)
        }
    },
    template: `
    <ui-data-grid :readonly='readonly' :buttons='true' :dataSource='dataSource' :columns='columns'/>
`
}


Vue.component('iac-grid', GridComponent);
Vue.Fields.grid = { is: 'iac-grid' }
