.nav-links {
  margin-left: auto;
  font-size: 14px;
  line-height: 1.43;
  color: @black;
  flex-shrink: 0;
  width: 100%;

  &__list {
    //display: flex;
    padding: 0;
    list-style: none;
    height: 40px;
    overflow: hidden;
    margin-left: 50px;
  }

  &__item {
    padding: 0 16px;
    line-height: 40px;
    white-space: nowrap;
    display: inline-block;
  }

  &__link {
    display: block;
    color: inherit;
    text-decoration: none;

    &:hover,
    &:focus {
      color: @brand-primary;
    }
  }
}

@media (max-width: 800px) {
  .nav-links {
    display: none;
  }
}

.iac-nav-links{
  height: 40px; 
  width: 100%; 
  display: flex;    
  position: relative;
  // margin: 0px 8px;
  >.content{
    flex: 1 1 auto; 
    overflow: hidden;
    position: relative;
    >.frame{
      border: none;
      height: 0; 
      width: 100%;
    }
    >.items{
      display: block;
      >.item{
        display: inline-block;
        white-space: nowrap;
        line-height: 40px; 
        padding: 0 12px; 
        cursor: pointer;
        user-select: none;
        border-radius: 5px;

        &:hover{
          background: #eee;
          color: #000;
        }
        icon{
          font-size: 8px;
          margin-left: 8px;
          transition: all 0.3s;
          bottom: 1px;
          position: relative;
        }
        &.active{
          icon{
            transform: rotate(180deg);
          }
        }
      }
    }

  }

  >.dropdown{
    position: absolute;
    z-index: 1;
    bottom: -2px;
    .content{
      position: absolute; 
      top:0;
      left:0;
      border: 1px solid #e1e1e1;
      background: #fff;
      white-space: nowrap;
      box-shadow: 0 6px 18px 0 rgba(14, 21, 47, 0.13), 0 -2px 6px rgba(14, 21, 47, 0.03);
      
      padding: 8px 0;
      border-radius: 5px;

      &::before{
        content: " ";
        position: absolute;
        width: 10px;
        height: 10px;
        background: #fff;
        transform: rotateZ(45deg);

        border-left: 1px solid #e1e1e1;
        border-top: 1px solid #e1e1e1;

        top: -5px;
        left: 10px;
      }
      >.item{
        position: relative;
        z-index: 1;
        padding: 8px 13px 8px;
        color: #777;
        font-size: 14px;
        cursor: pointer;
        display: block;
        text-decoration: none;
        &:hover{
          background: #eee;
          color: #000;
        }
      }
    }
  }

}