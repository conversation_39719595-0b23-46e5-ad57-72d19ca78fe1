import IacProcedures from './../home_page/procedures';
import IacProceduresBirja from './procedures_birja';
import IacFooter from './../../components/footer';
import NavTop from '../../components/nav_top';
import Banners from './banners';
import LatestNews from './../home_page/latest_news';
import { Language } from '@iac/core';
import IacBusinessPage from './../home_page/business_page';

import { DataSource } from '@iac/data';
import { Settings } from '@iac/kernel';
import IacEcosystem from './ecosystem';

export default {
    data: function(){
        return {
            banner: 0,
            search_cargo_procedure: undefined,
            sourceCargoProcedure: new DataSource({
                store: {
                  ref: 'ref_cargo_procedure_public',
                  injectQuery: (params) => {
                    params.limit = 6;
                    return params;
                  },
                }
              })
        }
    },
    methods: {
        search_cargo(){
          if(this.search_cargo_procedure)
            this.$router.push({path: `cargo_procedure?queryText=${this.search_cargo_procedure}`})
          else
            this.$router.push({path: `cargo_procedure`})
        }
      },
    components:{
        IacProcedures,
        IacProceduresBirja,
        IacFooter,
        NavTop,
        Banners,
        LatestNews,
        IacEcosystem,
        IacBusinessPage
    },
    template: `
        <div class='iac-main-page'>
            <nav-top style='position: relative; z-index: 201; border-bottom: 1px solid #eee;' />
            
            <ui-layout-tab prefix='Наши сервисы' :button='1'  class='services' name='service'>
                
                <banners slot='header' style='margin-bottom: -30px;position: relative;'/>


                <ui-layout-group type='primary' icon='goszakupki' label='main_page.public_procurement' >
                    <section style='padding-top: 40px;'>
                        <div class='iac-container'>
                            <center><h1>{{ $t('title_public procurement') }}</h1></center>
                            <iac-procedures style='position: relative' />
                        </div>
                    </section>
                </ui-layout-group>
                <ui-layout-group type='warning' icon='birja' label='exchange_trading' v-if='$settings.exchange && !$settings.exchange._blocked'>
                    <section style='background: #f7fbfe;padding-top: 40px;'>
                        <div class='iac-container'>
                            <center><h1>{{ $t('stock_exchange_trading') }}</h1></center>
                            <iac-procedures-birja style='position: relative' />
                        </div>
                    </section>                
                </ui-layout-group>
                <ui-layout-group type='success' icon='gruz' label='logistics' v-if='$settings.exchange && !$settings.exchange._blocked && $settings.procedures && $settings.procedures.cargo_procedure'>
                    <section style='background: #f7fbfe;padding-top: 40px;'>
                        <div class='iac-container'>
                            <center><h1>{{ $t('electronic_logistics_portal') }}</h1></center>
                            <form  @submit.prevent='search_cargo' style='max-width: 700px; margin: 20px auto 0; width: 100%;'>
                                <ui-control-group>
                                <ui-input icon='search' label='search' v-model='search_cargo_procedure' />
                                <ui-btn type='primary'>{{$t('search')}}</ui-btn>
                                </ui-control-group>
                            </form>
                            <ui-data-view type='grid' :search='false' :toolbar='false' :dataSource='sourceCargoProcedure'/>
                            <div style='text-align:center; margin: 8px'><router-link class='ui-btn ui-btn-primary' to='/cargo_procedure'>{{ $t('hp.goto_cargo_procedures_page') }}</router-link></div>
                        </div>
                    </section>  
                </ui-layout-group>

                <ui-layout-group type='success' icon='ecosystem' label='main_page.ecosystem' v-if='$settings.ecosystem'>
                    <section style='padding-top: 40px;'>
                        <div class='iac-container'>
                            <center><h1>{{ $t('ecosystem_title') }}</h1></center>
                            <iac-ecosystem style='position: relative' />
                        </div>
                    </section>
                </ui-layout-group>

                <ui-layout-group type='secondary' icon='business' label='main_page.business' v-if='$settings.business'>
                    <section style='padding-top: 40px;'>
                        <div class='iac-container'>
                            <center><h1>{{ $t('business_title') }}</h1></center>
                            <iac-business-page style='position: relative' />
                        </div>
                    </section>
                </ui-layout-group>
                
            </ui-layout-tab>           
            <latest-news v-if='$settings.news' :style='"background: "+($settings.news._background || "#fbfbfb")' />
            <iac-footer />
        </div>
    `
}