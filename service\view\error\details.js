import { Http } from '@iac/core'

export default {
    data: function () {
        return {
            details: undefined
        }
    },
    mounted() {
        this.update();
    },
    watch: {
        $route(to, from) {
            this.update();
        }
    },
    methods: {
        update() {
            this.details = undefined;
            this.$wait(async () => {
                let { error, data } = await Http.api.rpc("get_error_by_id", {
                    id: this.$route.params.id
                })
                if (!error && data){
                    data = data+"";
                    data = data.replace(/\\"/ig,"\"")
                    data = data.replace(/\\n/ig,"\n")
                    this.details = data;
                }
            });
        }
    },
    template: `
        <div>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('error')}}</li>
                </ol>
                <div class='title'>
                    <h1>{{$route.params.id}}</h1>
                </div>
            </iac-section>
            <iac-section>
            <pre style='overflow: auto;border: 1px solid #ccc; border-radius: 3px;'><code>{{details}}</code></pre>
            </iac-section>
        </div>
    `
}