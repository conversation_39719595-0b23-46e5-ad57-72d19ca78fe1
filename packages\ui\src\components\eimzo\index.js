import DataSource from "../../../../data/src/data_source"

export var Eimzo = {
    props: ["label", "status", "value", "readonly", "disabled", "wait", "actions", "has_del", "required"],
    props: {
        label: String,
        status: Object,
        value: Object,
        readonly: <PERSON><PERSON><PERSON>,
        disabled: <PERSON><PERSON><PERSON>,
        wait: <PERSON><PERSON><PERSON>,
        actions: <PERSON><PERSON><PERSON>,
        has_del: <PERSON><PERSON><PERSON>,
        required: <PERSON><PERSON><PERSON>,
        createPkcs7: {
            type: <PERSON>olean,
            default: true
        }
    },
    name: "ui-e<PERSON><PERSON>",
    data: function () {
        return {
            ctrl_wait: false,
            dataSource: DataSource.get("eimzo")
        }
    },
    methods: {
        async onItem(item) {
            if (!item)
                return this.$emit("input", undefined);
                
            this.ctrl_wait = true;
            let EImzoStore = this.dataSource.store;

            if (!this.createPkcs7) {
                this.ctrl_wait = false;
                return this.$emit("input", {
                    name: item.text,
                    vo: item.vo,
                    exp: {
                        value: item.vo,
                        display: item.text
                    }
                })
            }

            let keyId = await EImzoStore.loadKey(item.vo);

            if (keyId) {
                let { error, data } = await EImzoStore.createPkcs7(keyId, "Жизнь хороша когда  пьешь не спеша");
                if (error) {
                    Vue.Dialog.MessageBox.Error(error)
                } else {
                    this.$emit("input", {
                        name: item.text,
                        pkcs7: data,
                        exp: {
                            value: data,
                            display: item.text
                        }
                    })
                }
            }
            this.ctrl_wait = false;
        }
    },
    template: `
        <ui-entity :label='label' :status='status' :value='value' :readonly='readonly' :disabled='disabled' :wait='ctrl_wait || wait' :actions='actions' :has_del='has_del' :required='required' :dataSource='dataSource' v-on:input="onItem"/>
    `
}