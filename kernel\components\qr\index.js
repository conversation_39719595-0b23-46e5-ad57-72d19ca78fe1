import Develop from './../../develop';
var QR = {
    name: "iac-qr",
    props: {
        value: {
            type: String,
            default: "https://hayotbirja.uz/"
        },
        size: {
            type: [Number, String],
            default: 200
        },
        //уровень коррекции ошибок
        level: {
            type: String,
            default: "H",
            validator: v => ["L", "M", "Q", "H"].includes(v)
        },
        icon: String,
        logo: String,

        //размер лого в процентах
        logoSize: {
            type: Number,
            default: 15,
            validator: v => v >= 5 && v <= 30
        },
        //margin вокруг логотипа в пикселях
        logoMargin: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            ready: false,
            qrcodeLib: null,
            svgContent: '' // Храним SVG как строку
        }
    },
    watch: {
        value() { this.updateQR(); },
        logo() { this.updateQR(); },
        icon() { this.updateQR(); },
        size() { this.updateQR(); }
    },
    async created(){
        await iac.Core.Assets.script("qrcode.js");
        this.qrcodeLib = window.qrcode;
        this.ready = true;
        this.updateQR();
    },
    methods: {
        updateQR() {
            if (!this.ready || !this.qrcodeLib) return;
            
            const qr = this.qrcodeLib(0, this.level); //Значение 0 - автоматический выбор версии QR-кода (можно лапками задать 1-40)
            qr.addData(this.value);
            qr.make();
            
            const modules = qr.getModuleCount();
            this.svgContent = this.createSVGContent(qr, modules);
        },
        
        createSVGContent(qr, modules) {
            const size = parseInt(this.size, 10); //парсим размер в число
            const logoMarginPx = parseInt(this.logoMargin, 10) || 0;
            
            // Коэффициент масштабирования для перевода пикселей в модули
            const scale = size / modules;
            // Отступ в модулях (переводим из пикселей)
            const logoMarginModules = logoMarginPx / scale;
            
            let svg = `<svg width="${size}" height="${size}" viewBox="0 0 ${modules} ${modules}" shape-rendering="crispEdges">`;
            
            // Создаем белый фон
            svg += `<rect width="100%" height="100%" fill="#FFFFFF"/>`;
            
            // Рисуем QR-код
            for (let y = 0; y < modules; y++) {
                for (let x = 0; x < modules; x++) {
                    if (qr.isDark(y, x)) {
                        svg += `<rect x="${x}" y="${y}" width="1" height="1" fill="#000000"/>`;
                    }
                }
            }
            
            // Встраиваем логотип или иконку в центр
            if (this.logo || this.icon) {
                // Размер логотипа с учетом процентов
                const logoSizeModules = Math.max(3, Math.floor(modules * this.logoSize / 100));
                // Размер логотипа с учетом отступа
                const logoSizeWithMargin = logoSizeModules + (logoMarginModules * 2);
                const center = modules / 2;
                
                // Позиция логотипа с учетом отступа
                const logoPos = center - logoSizeWithMargin / 2;
                
                // Вырезаем область под логотип/иконку с учетом отступа
                svg += `<rect x="${logoPos}" y="${logoPos}" width="${logoSizeWithMargin}" height="${logoSizeWithMargin}" fill="#FFFFFF"/>`;
                
                // Позиция самого логотипа внутри белой области
                const actualLogoPos = logoPos + logoMarginModules;
                
                if (this.icon) {
                    // Добавляем иконку с учетом отступа
                    const iconPos = center;
                    svg += `<text x="${iconPos}" y="${iconPos}" text-anchor="middle" dominant-baseline="central" font-family="iac-icon" font-size="${logoSizeModules}">${this.icon}</text>`;
                } else if (this.logo) {
                    // Добавляем логотип с учетом отступа
                    svg += `<image href="${this.logo}" x="${actualLogoPos}" y="${actualLogoPos}" width="${logoSizeModules}" height="${logoSizeModules}" preserveAspectRatio="xMidYMid meet"/>`;
                }
            }
            
            svg += '</svg>';
            return svg;
        },        
        copyToClipboard() {
            if (this.canCopy) {
                navigator.clipboard.writeText(this.value)
                    .then(() => {
                        Vue.Dialog.MessageBox.Info(`Значение скопировано в буфер обмена: <br> ${this.value}`);
                    });
            }
        },
        
    },
    computed: {
        qrSvg() {
            return this.svgContent;
        },
        canCopy() {
            return !this.isValidUrl && Develop.copy_qr_value && this.value;
        },
        isValidUrl() {
            return /^(https?:\/\/|www\.)[^\s$.?#].[^\s]*$/i.test(this.value);
        }
    },
    template: `
        <a v-if="isValidUrl && this.value" 
           :href="value" 
           target="_blank" 
           class="iac-qr" 
           v-html="qrSvg">
        </a>
        <div v-else 
             class="iac-qr" 
             v-html="qrSvg" 
             @click="copyToClipboard" 
             :class="{'iac-qr--copyable': canCopy}">
        </div>
    `
}
Vue.component('iac-qr', QR)

Vue.Fields.qr = { is: 'iac-qr' }