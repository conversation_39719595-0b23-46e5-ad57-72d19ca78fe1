Vue.Dialog("ImageDlg", {
    props: ["image","out_width", "circle"],
    data: function () {
        return {
            ord: undefined,
            scene_rect: undefined,
            offset: undefined,

            x: 50,
            y: 50,
            radius: 50,
            min_radius: 20,

            xDelta: 1,
            yDelta: 1,
        }
    },
    computed: {
        xRadius() {
            return this.radius * this.xDelta
        },
        yRadius() {
            return this.radius * this.yDelta
        },
        classes() {
            return [
                "image-dlg",
                {
                    handle: this.ord,
                    [`handle_${this.ord}`]: this.ord
                },
            ]
        },

        crop_style() {
            return {
                left: (this.x - this.xRadius) + "%",
                top: (this.y - this.yRadius) + "%",
                width: this.xRadius * 2 + "%",
                height: this.yRadius * 2 + "%",
            }
        },
        ords() {
            return ["nw", "sw", "ne", "se"]
        }
    },
    methods: {
        onLoad(e) {
            this.scene_rect = this.$refs.scene.getBoundingClientRect();
            this.min_radius =  (this.out_width*100/this.image.naturalWidth)/2
            if(this.min_radius < 10)
            this.min_radius = 10;
            

            this.diameter = this.scene_rect.width > this.scene_rect.height ? this.scene_rect.height : this.scene_rect.width;

            

            if (this.scene_rect.width < this.scene_rect.height) {
                this.xDelta = 1;
                this.yDelta = this.scene_rect.width / this.scene_rect.height
            } else {
                this.xDelta = this.scene_rect.height / this.scene_rect.width;
                this.yDelta = 1
            }
        },
        onMouseDown(e) {
            if (!e || !e.target)
                return;

            this.offset = {
                x: e.layerX,
                y: e.layerY,
            }
            this.ord = e.target.dataset.ord || "move";
            this.scene_rect = this.$refs.scene.getBoundingClientRect();

            window.addEventListener("mouseup", this.onMouseUp);
        },
        onMouseUp(event) {
            this.ord = undefined;
            window.removeEventListener('mouseup', this.onMouseUp);
        },
        onMouseMove(event) {
            if (!this.ord)
                return;
            let scene_rect = this.scene_rect;
            let x = event.x - scene_rect.x;
            let y = event.y - scene_rect.y;
            x -= this.offset.x
            y -= this.offset.y

            if (this.ord == 'move') {

                this.x = x * 100 / scene_rect.width + this.xRadius;
                this.y = y * 100 / scene_rect.height + this.yRadius;

                if ((this.x - this.xRadius) < 0) {
                    this.x = this.xRadius
                }
                if ((this.y - this.yRadius) < 0) {
                    this.y = this.yRadius
                }
                if ((this.x + this.xRadius) > 100)
                    this.x = 100 - this.xRadius;
                if ((this.y + this.yRadius) > 100)
                    this.y = 100 - this.yRadius;
                return;
            }

            let left = this.x - this.xRadius
            let top = this.y - this.yRadius
            let right = this.x + this.xRadius
            let bottom = this.y + this.yRadius

            let diametr = right - (x * 100 / scene_rect.width);
            if (this.ord == 'se' || this.ord == 'ne') {
                x += 18
                diametr = (x * 100 / scene_rect.width) - left;
            }
            this.radius = diametr / 2 / this.xDelta
            if (this.radius < this.min_radius)
                this.radius = this.min_radius;

            if (this.ord == 'se') {// right_bottom
                if ((left + this.xRadius * 2) > 100)
                    this.radius = (100 - left) / 2 / this.xDelta
                if ((top + this.yRadius * 2) > 100)
                    this.radius = (100 - top) / 2 / this.yDelta
                this.x = left + this.xRadius
                this.y = top + this.yRadius
            } else if (this.ord == 'nw') { // left_top
                if ((right - this.xRadius * 2) < 0)
                    this.radius = right / 2 / this.xDelta
                if ((bottom - this.yRadius * 2) < 0)
                    this.radius = bottom / 2 / this.yDelta
                this.x = right - this.xRadius
                this.y = bottom - this.yRadius
            } else if (this.ord == 'ne') {// right_top
                if ((left + this.xRadius * 2) > 100)
                    this.radius = (100 - left) / 2 / this.xDelta
                if ((bottom - this.yRadius * 2) < 0)
                    this.radius = bottom / 2 / this.yDelta
                this.x = left + this.xRadius
                this.y = bottom - this.yRadius
            } else if (this.ord == 'sw') {// left_bottom
                if ((right - this.xRadius * 2) < 0)
                    this.radius = right / 2 / this.xDelta
                if ((top + this.yRadius * 2) > 100)
                    this.radius = (100 - top) / 2 / this.yDelta
                this.x = right - this.xRadius
                this.y = top + this.yRadius
            }

        },
        save() {
            const inputWidth = this.image.naturalWidth;
            const inputHeight = this.image.naturalHeight;

            let outputImage = document.createElement('canvas'); 
            const ctx = outputImage.getContext('2d');
            outputImage.width = this.out_width
            outputImage.height = this.out_width  
            
            let left = this.x - this.xRadius
            let top = this.y - this.yRadius
            let right = this.x + this.xRadius
            let bottom = this.y + this.yRadius

            let x = left*inputWidth/100;
            let y = top*inputHeight/100;
            
            let width = right*inputWidth/100 - x;
            let height = bottom*inputHeight/100 - y;
            
            
            ctx.drawImage(this.image,x,y,width,height,0,0,this.out_width,this.out_width);

            this.Close(outputImage.toDataURL());
        }
    },
    created() {
        window.addEventListener("mousemove", this.onMouseMove);
    },
    beforeDestroy() {
        window.removeEventListener('mousemove', this.onMouseMove);
    },
    template: `
        <div v-bind:class="classes">
            <div>
                <div class='scene' ref='scene' @mousedown.prevent>
                    <img ref='image' width='100%' :src='image.src || image' style='display: block;' @load='onLoad' @mousedown.prevent />
                    <div class='react-crop' v-bind:style='crop_style' @mousedown="onMouseDown">
                        <div class='mask' :style='{borderRadius: this.circle ? "50%" : "unset"}'>
                            <div class='grid horizontal'/>
                            <div class='grid vertical'/>
                        </div>
                        <div :key='ord' v-for='ord in ords' :data-ord="ord"/>
                    </div>
                </div>
            </div>
            <footer>
                <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
                <ui-btn type='primary' v-on:click.native='save'>{{$t('select')}}</ui-btn>
            </footer>
        </div>
    `
});