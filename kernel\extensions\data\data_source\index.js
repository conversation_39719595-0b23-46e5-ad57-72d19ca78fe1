import { DataSource, Entity } from '@iac/data'
import Context from './../../../context'
import EntityModel from './model'

DataSource.prototype.onCreate = function (options) {

    this._form = options.form || {};
    
    for (let action in this._form.actions) {
        let _action = this._form.actions[action];

        if (typeof _action == "string")
            _action = {
                policy: _action
            }

        let { policy,question, label = action,reload=false, method=this._store.method,params = {
            ref: this._store.ref,
            op: action
        } } = _action

        this._form.actions[action] = {
            question: question,
            label: label,
            method: method,
            params: params,
            reload: reload,
            hidden: () => {
                return !Context.Access.policy[policy]
            }
        }
    }

    let _context = this._store.context
    this._store.context = (context) => {
        context = _context(context)

        try{
            context.actions = context.actions || []
        }catch(e){

        }

        if(!context?.actions ){
            return context;
        }
            //return context;

        if (this._form.actions?.update)
            context.actions.push({
                ...this._form.actions.update,
                icon: "edit",
                handler: async () => {
                    let action = this._form.actions.update
                    let item = await this.updateItem(context);
                    if(!item)
                        return;

                    if(action.reload)
                        return this.reload();

                    Object.keys(item).forEach((prop_name)=>{
                        Vue.set(context,prop_name,item[prop_name])
                    })

                    Vue.set(context,"status_type","info")
                    setTimeout(()=>{
                        Vue.set(context,"status_type",undefined)
                    },500)
                }
            });
        
        if(this._form.actions?.delete && context.actions.length > 0){
            context.actions.push({
                type: "sep"
            })
        }

        if (this._form.actions?.delete)
            context.actions.push({
                ...this._form.actions.delete,
                icon: "trash",
                btn_type: "danger",
                handler: async () => {

                    let action = this._form.actions.delete
                    let { error, data } = await this._store.host.rpc(action.method, {
                        ...action.params,
                        data: {
                            id: context.id
                        }
                    })

                    if (error) {
                        Vue.Dialog.MessageBox.Error(error)
                    } else {

                        if(action.reload)
                            return this.reload();

                        let index = this.items.indexOf(context);
                        this.items.splice(index, 1)

                        if ((this.state & DataSource.STATE_EOF) == 0)
                            this.next(1);
                    }
                }
            });

        return context;
    }

    if (this._form.actions?.create)
    this._actions.push({
        ...this._form?.actions?.create,
        handler: async () => {
            let action = this._form.actions.create
            let item = await this.updateItem();
            if(!item)
                return;

            if(action.reload)
                return this.reload();

            this.unshift_item(item)
            
            Vue.set(item,"status_type","success")
            setTimeout(()=>{
                Vue.set(item,"status_type",undefined)
            },500)
        }
    });
}

DataSource.prototype.updateItem = async function (context) {
    let _form = this._form;
    let _host = this._store.host

    let modelInterface = _form.model || EntityModel
    return await Vue.Dialog({
        props: ["context"],
        data: function () {
            return {
                model: new modelInterface({ ...this.context, props: _form.fields })
            }
        },
        computed: {
            action(){
                return this.context ? _form.actions.update : _form.actions.create;
            }
        },
        methods: {
            save() {
                this.$wait(async ()=>{
                    let params = await this.model.get_params();  
                    if(!params)
                        return;
                    params.id = this.context?.id;

                    let { error, data } = await _host.rpc(this.action.method, {...this.action.params, data: params});
                    if(error){
                        if (!this.model.setError(error) && error.code != "AbortError") {
                            Vue.Dialog.MessageBox.Error(error);
                        }
                    }else{
                        this.Close({...params,...data})
                    }
                    
                })
                
            }
        },
        template: `
        <div>
            <main>
                <ui-layout :fields='model.fields' />
            </main>
            <footer>
                <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
                <ui-btn type='primary' :disabled='!model.get_params' v-on:click.native='save'>{{$t(action.label)}}</ui-btn>     
            </footer>
        </div>
        `
    }).Modal({
        context: context
    })

    
}