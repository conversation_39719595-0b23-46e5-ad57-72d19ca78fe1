import Requisites from './requisites'
import Settings from './settings'
import Users from './users'
import Affiliation from './affiliation'
import Billing from './billing'
import PermissiveDocuments from './permissive_documents'
import Drivers from './drivers'

export default [
    { path: 'company_requisites', component: Requisites },
    { path: 'user', component: Users },
    { path: 'affiliation', component: Affiliation },
    { path: 'billing', component: Billing },
    { path: 'drivers', component: Drivers },
    { path: 'permissive_documents', component: PermissiveDocuments, props: { access: 'private' } },
    { path: 'company_settings', component: Settings },
]