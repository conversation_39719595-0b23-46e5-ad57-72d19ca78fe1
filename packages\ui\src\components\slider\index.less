.ui-slider{
    color: #B9B8B8;
    font-size: 14px;
    line-height: 20px;
    position: relative;
    .control{
        width: 100%;
        display: flex;
        align-items: flex-start;
        input{
            flex: 1 1 auto;
            width: calc(100% + 20px);
            margin: 0 -10px;
            &::-moz-focus-outer{
                border: 0;
            }
            &:focus {
                outline: none;
            }
        }
    }
    &.disabled {
      .control{
          opacity: 30%;
      }
    }
    &.readonly {
      .control{
        input[type=range] {

          &::-webkit-slider-runnable-track {
            cursor: default;
            background: #cbcbcb;
          }
          &::-webkit-slider-thumb {

            background: #cbcbcb;
            cursor: default;
          }
          &:focus::-webkit-slider-runnable-track {
            background: #cbcbcb;
          }
          &::-moz-range-track {
            cursor: default;
            background: #cbcbcb;
          }
          &::-moz-range-thumb {
            background: #cbcbcb;
            cursor: default;
          }
          &::-ms-track {
            cursor: default;
          }
          &::-ms-fill-lower {
            background: #cbcbcb;
          }
          &::-ms-fill-upper {
            background: #cbcbcb;
          }
          &::-ms-thumb {
            background: #cbcbcb;
            cursor: default;
          }
          &:focus::-ms-fill-lower {
            background: #cbcbcb;
          }
          &:focus::-ms-fill-upper {
            background: #cbcbcb;
          }
        }
      }
    }
    &.non_value{
      color: @control-error-border;
        .control{
            opacity: 30%;
        }
        .range{
          height: 20px;
          padding-bottom: 0;
        }
    }
    .range{
        position: relative;
        flex: 1 1 auto;
        margin: 0 18px;
        box-sizing: border-box;
        padding-bottom: 14px;
        .value{
            bottom: 0;
            color: #000000;
            position: absolute;
            transform: translateX(-50%);
        }        
    }
    &.error{
      input[type=range] {
        &::-webkit-slider-runnable-track{
          background: @control-error-border;
        }
        &::-webkit-slider-thumb{
          background: @control-error-border;
        }
        &:focus::-webkit-slider-runnable-track{
          background: @control-error-border;
        }
        &::-moz-range-track{
          background: @control-error-border;
        }
        &::-moz-range-thumb{
          background: @control-error-border;
        }
        &::-ms-fill-lower{
          background: @control-error-border;
        }
        &::-ms-fill-upper{
          background: @control-error-border;
        }
        &::-ms-thumb{
          background: @control-error-border;
        }
        &:focus::-ms-fill-lower{
          background: @control-error-border;
        }
        &:focus::-ms-fill-upper{
          background: @control-error-border;
        }
      }
    }  
}

input[type=range] {
  height: 20px;
  -webkit-appearance: none;
  width: 100%;

  &:focus {
    outline: none;
  }
  &::-webkit-slider-runnable-track {
    width: 100%;
    height: 2px;
    cursor: pointer;
    animate: 0.2s;
    background: #009AB8;
  }
  &::-webkit-slider-thumb {
    border: 2px solid #fff;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: #009AB8;
    cursor: pointer;
    -webkit-appearance: none;
    margin-top: -8px;
  }
  &:focus::-webkit-slider-runnable-track {
    background: #009AB8;
  }
  &::-moz-range-track {
    width: 100%;
    height: 2px;
    cursor: pointer;
    animate: 0.2s;
    background: #009AB8;
  }
  &::-moz-range-thumb {
    border: 2px solid #fff;
    height: 16px;
    width: 16px;
    border-radius: 25px;
    background: #009AB8;
    cursor: pointer;
  }
  &::-ms-track {
    width: 100%;
    height: 2px;
    cursor: pointer;
    animate: 0.2s;
    background: transparent;
    border-color: transparent;
    color: transparent;
  }
  &::-ms-fill-lower {
    background: #009AB8;
    border-radius: 2px;
  }
  &::-ms-fill-upper {
    background: #009AB8;
    border-radius: 2px;
  }
  &::-ms-thumb {
    margin-top: 1px;
    border: 2px solid #fff;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: #009AB8;
    cursor: pointer;
  }
  &:focus::-ms-fill-lower {
    background: #009AB8;
  }
  &:focus::-ms-fill-upper {
    background: #009AB8;
  }
}