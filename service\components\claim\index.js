import Vue from 'vue'

const Component = {
    props: ['item'],
    computed: {
        proc_url(){
            
            if(!this.item || !this.item.proc_id)
                return;
            return `/procedure/${this.item.proc_id}/core`;
        }
    },
    template: `
    <div class='tile widget-claim'>
        <div class='fill'>
            <a class='' href='' v-on:click.prevent='item.actions[0].handler'>№ {{item.id}}</a>
        </div>
        <div class='props'>
            <div><label>{{$t('proc_type')}}</label>
                <router-link v-if='proc_url' :to='proc_url'>{{$t('proc_type_' + item.proc_type)}} № {{item.proc_id}}</router-link>
                <span v-else>{{$t('proc_type_' + item.proc_type)}}</span>
            </div>
            <div><label>{{$t('contract_type')}}</label><span>{{$t('doc_type_' + item.doc_type)}}</span></div>
            <div><label>{{$t('delivery_time')}}</label><span>{{item.delivery_days}}&nbsp;{{$t('days')}}</span></div>
            <div><label>{{$t('total_sum')}}</label><span><iac-number :value='item.total_price' delimiter=' ' part='2'/>&nbsp;{{item.currency}}</span></div>
        </div>
    </div>
    `
}

Vue.component('widget-claim', Component);


