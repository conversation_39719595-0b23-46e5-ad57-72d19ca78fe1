.ui-data-tile{
    .ui-layout-group .content .data-list-list .items{
        
        // display: grid;
        // grid-template-columns: repeat(auto-fit , minmax(450px, 1fr) );
        // gap: 20px;
        
        //display: flex;
        //flex-wrap: wrap;
        //margin: -10px -10px 0;

        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        grid-gap: 15px;

        > .ui-list-item {
            /*flex: 1 1 400px;
            //flex-basis: calc(50% - 20px);
            min-width: 250px;
            margin: 10px;*/
            >.ui-action{
                align-self: baseline;
            }
        }

        > .ui-list-item.more {
            //display: inline-block;
        }
        
        > .ui-list-item:not(.more) {
            white-space: unset;
            box-shadow: 0px 0px 24px rgba(219, 219, 219, 0.25);
            border-radius: 4px;
            padding: 20px 24px;
            background: #fff;
            display: flex;
            color: #919191;
            font-size: 14px;
            line-height: 20px;
            >.tile{
                width: 100%;
                display: flex;
                flex-direction: column;
                >.fill{
                    flex: 1 1 auto;
                }
                >.props{
                    display: table;
                    flex: 0 0 auto;
                    width: 100%;
                    >*{
                        display: table-row;
                        >*{
                            display: table-cell;
                            padding: 4px 0;
                        }
                        >label{
                            padding-right: 5px;
                            white-space: nowrap;
                        }
                        >span{
                            max-width: 0;
                            color: #201D1D;
                            width: 100%;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }
                    }
                    @media (max-width: 900px) {
                        >*{
                            display: block;
                            >*{
                                display: block;
                            }
                            >span{
                                max-width: unset;
                            }
                        }
                    }                    

                }

            }
        }
    }

}