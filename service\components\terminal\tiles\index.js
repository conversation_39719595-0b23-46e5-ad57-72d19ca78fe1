import './exchange_contract'
import './bkl_exchange_contract'
import './order_book'
import './history'
import './proposal'
import './timer'

Vue.Tiling("note", {

    component: {
        props: ["model"],
        computed: {
            details() {

                // let item = {...this.model,space: undefined};
                // item.space = undefined;//item.space && item.space.key
                return JSON.stringify(this.model, (k, v) => {
                    if (k == 'context')
                        return;
                    if (k == 'space')
                        return v && v.label
                    if (k == 'node')
                        return {
                            id: v && v.id,
                            space: v.space
                        }
                    return v
                }, '\t')
            }
        },
        template: "<div style='overflow: auto;'><pre>{{details}}</pre></div>"
    },
    //setting: OrderBookSetting,
    select_contract: true,
    select_group: true
})