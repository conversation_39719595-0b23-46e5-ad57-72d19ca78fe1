.ui-file {
    >.file{
        display: flex;
        margin-bottom: 10px;
        >.icon{
            flex: 0 0 44px;
            height: 44px;
            background: #F4F4F4;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #969595;
            font-size: 20px;
        }
        >.content{
            padding: 0 8px;
            font-size: 14px;
            line-height: 20px;
            width: 100%;
            >.name{
                color: #666;
                display: flex
;
                a{
                    color: @primary-link;   
                    word-break: break-word;
                    //flex: 1 1 auto;
                }
                >.close{
                    flex: 0 0 auto;
                    color: #B9B8B8;
                    padding: 4px;
                    font-size: 13px;
                    cursor: pointer;
                    margin-left: 10px;
                }
            }
            >.size{
                color: #969595;
            }
            >.ui-text{
                margin-bottom: 0;
            }
        }
        
    }
    >.add{
        margin-top: 10px;
    }
    &.wait{
        opacity: 0.5;
    }
    &.show_photo{
        >.file >.icon{
            border: 1px solid #dadada;
            background-size: cover;
            background-repeat: no-repeat;
            >icon{
                display: none;
            }  
        }
    }
    &.error {
        >.file >.content {
            >.status_message,
            >.name a {
                color: #cc3c3c
            }
            >.size{
                color: #f49e9e;
            }
        }
    }
}