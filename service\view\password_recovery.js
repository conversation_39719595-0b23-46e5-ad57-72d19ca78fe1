import { Http, Language } from "@iac/core";
import { Entity } from '@iac/data'

const alphabetRegExpsStrings = [
  "a-zA-Z",
  "0-9",
  ".,:;?!*+%\\-<>@\\[\\]\\{\\}\\/\\\\_\\$\\#"
]
const allAlphabetRegExps = alphabetRegExpsStrings.map(string => new RegExp(`[${string}]`))//all groups needed
allAlphabetRegExps.push(new RegExp(`^[${alphabetRegExpsStrings.join('')}]+$`)) //only this symbols available

class PasswordRecovery extends Entity {
  props() {
    return {
      new_password: {
        label: 'password',
        type: 'password',
        required: true,
        attr: {
          autocomplete: 'new-password',
          react: true
        },
        onChange(val = "") {
          let conditions = []
          const valLength = val?.length ?? 0
          if (8 <= valLength && valLength <= 30) {
            conditions.push('length')
          }

          if (/^(?=.*[a-zA-Z])(?=.*[0-9])(?=.*[\.\,\:\;\?\!\+\%\-\<\>\@\[\]\{\}\/\_\$\#])[0-9a-zA-Z\.\,\:\;\?\!\+\%\-\<\>\@\[\]\{\}\/\_\$\#]+$/g.test(val)) {
            conditions.push('alphabet')
          }

          //if (allAlphabetRegExps.every(re => re.test(val))) {
          //  conditions.push('alphabet')
          //}

          if (val.toUpperCase() !== val && val.toLowerCase() !== val) {
            conditions.push('registr')
          }

          this.model.conditions = conditions
          if (conditions.length === 3) {
            this.status = { type: 'success' }
          }
        }
      },
      conditions: {
        type: "enum",
        label: "!",
        readonly: true,
        dataSource: [
          { id: 'length', name: Language.t('the_length_of_password_should_not_exceed_30_characters_or_be_less_than_8_characters') },
          { id: "alphabet", name: Language.t('password_must_consist_of_latin_letters_arabic_numerals_and_special_characters') },
          { id: "registr", name: Language.t('literal_part_of_the_password_must_contain_both_lowercase_and_uppercase_capital_letters') }
        ]
      },
    }
  }

  async save(query) {
    let { type, value, code } = query;

    let { new_password } = this;
    let { error, data } = await Http.api.rpc('set_password_by_code', {
      type,
      value,
      code,
      new_password
    });
    if (error !== undefined) {
      if (!this.setError(error)) {
        await Vue.Dialog.MessageBox.Error(error);
      }
    }
    return { error, data };
  }
}

export default {
  data: function () {
    return {
      error: undefined,
      success: undefined,
      password: undefined,
      model: new PasswordRecovery()
    }
  },
  computed: {
    resetDisabled() {
      const { conditions = [], new_password = "" } = this.model
      return conditions.length !== 3 || !new_password
    }
  },
  methods: {
    async send_request() {
      await this.$wait(async () => {
        let { error, data } = await this.model.save(this.$route.query)
        if (error === undefined) {
          if (data.message) {
            await Vue.Dialog.MessageBox.Success(data.message);
            this.$router.push({
              path: '/'
            });
          }
        } else {
          await Vue.Dialog.MessageBox.Error(error);
        }
      })
    }
  },
  template: `
        <iac-section>
            <div v-if='!success'>
                <h1>{{ $t('password_recovery') }}</h1>
                <ui-error v-if='error' :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
                <form v-on:submit.prevent='send_request'>
                    <div>
                        <h3>{{$route.query.value}}</h3>
                        <ui-layout-group horizontal>
                          <ui-layout class="iac-reset-fields" :fields='model.fields'/>
                          <div></div>
                        </ui-layout-group>
                        <ui-btn type='primary big' :disabled="resetDisabled">{{$t('password_reset')}}</ui-btn>
                    </div>
                </form>
            </div>
            <div v-else-if='success'>
                <h1>{{success.message}}</h1>
                <p>{{ $t('sign_with_new_pass') }}</p>
            </div>
        </iac-section>
    `
}