import { DataSource, RemoteStore, Query, RefStore } from '@iac/data';
import { Http, Language } from '@iac/core'
import { Context, Develop, Settings } from '@iac/kernel'

// Спёр query из search_procedure.js
var ktru_product = new Query({
  product_id: {
    type: 'entity',
    group: 'choose_product',
    label: '!choose_product',
    has_del: true,
    dataSource: new DataSource({
      valueExp: 'product_id',
      displayExp: "product_name",
      search: true,
      store: new RefStore({
        ref: "ref_enkt_products",
        key:"product_id"
      })
    }),
    multiple: true,
    // hidden: () => {
    //   return !Settings.procedures?._filter_product
    // }
  },
});

var ad_query = new Query({
  min_price: {
    group: 'price_unit/<min-max>',
    type: 'float',
    label: '!from',
    has_delete: true,
    min: 0,
    bind: {
      status: 'price_error && {"type":"error"}'
    },
  },
  max_price: {
    group: 'price_unit/<min-max>',
    type: 'float',
    label: '!to',
    has_delete: true,
    min: 0,
    bind: {
      status: 'price_error && {"type":"error"}'
    },
  },
  price_error: {
    sync: false,
    group: 'price_unit',
    type: "model",
    label: "!",
    bind: {
      value: "min_price > max_price",
      status: (model) =>{
        return model.price_error && {"type":"error","message": Language.t("to_from_error")}
      }
    }
  },
  min_amount_gte: {
    group: 'delivery_amount_min/<min>',
    type: 'float',
    label: '!from',
    has_delete: true,
    min: 0,
    bind: {
      status: 'min_amount_error && {"type":"error"}'
    }
  },
  min_amount_lte: {
    group: 'delivery_amount_min/<min>',
    type: 'float',
    label: '!to',
    has_delete: true,
    min: 0,
    bind: {
      status: 'min_amount_error && {"type":"error"}'
    }
  },
  min_amount_error: {
    sync: false,
    group: 'delivery_amount_min',
    type: "model",
    label: "!",
    bind: {
      value: "min_amount_gte > min_amount_lte",
      status: (model) =>{
        return model.min_amount_error && {"type":"error","message": Language.t("to_from_error")}
      }
    }
  },
  max_amount_gte: {
    group: 'delivery_amount_max/<max>',
    type: 'float',
    label: '!from',
    has_delete: true,
    min: 0,
    bind: {
      status: 'max_amount_error && {"type":"error"}'
    }
  },
  max_amount_lte: {
    group: 'delivery_amount_max/<max>',
    type: 'float',
    label: '!to',
    has_delete: true,
    min: 0,
    bind: {
      status: 'max_amount_error && {"type":"error"}'
    }
  },
  max_amount_error: {
    sync: false,
    group: 'delivery_amount_max',
    type: "model",
    label: "!",
    bind: {
      value: "max_amount_gte > max_amount_lte",
      status: (model) =>{
        return model.max_amount_error && {"type":"error","message": Language.t("to_from_error")}
      }
    }
  },
});
var search_query = new Query({
  queryText: {
    icon: 'search',
    label: "!Search",
    hidden: true,
  },
});

var area_query = new Query({
    delivery_regions: {
    group: "area",
    label: "!area",
    type: "enum-tree",
    dataSource: "ref_uz_region_lv4",
    order: -1,
  }
});

var green_query = new Query({
  green: {
    type: "entity",
    label: "!green_procedures",
    group: "green_procedures",
    has_del: true,
    dataSource: [{id: true, name: "yes"}, {id: false, name: "no"}],
    hidden: () => !Settings.procedures?._green
  },
});

export default {
  data: function() {
    return {
      localManufacturersDataSource: new DataSource({
        query: new Query({
          queryText: {
            icon: "search",
            label: "!Search",
            hidden: true
          }
        }, [ktru_product]),
        store: {
          method: "company_ref",
          ref: "ecosystem_producers",
          injectQuery: (params) => {
            params.fields = ["company_name","id","products_count","certificate_id","valid_from","expire_at","inn","localization_degree"]         
            return params
          },
          context:(context)=>{
            return {
              ...context,
              header: [` `, 
              {
                component: {
                  props: ["products","products_count","company_id"],
                  computed:{
                    count(){
                      return this.products_count;
                    }
                  },
                  methods: {
                    onclick(){
                      Vue.Dialog({
                        props: ["id"],
                        data: function() {
                          return {
                            source: new DataSource({
                              columns: [{field: 'enkt',label:'code', style:"white-space: nowrap;"},{field: "name",label: "direct_contract_comp_name", style:"width: 100%;"}],
                              store: {
                                data: async () =>{
                                  let {error,data} = await Http.api.rpc("company_ref",{
                                    ref: "ecosystem_producers",
                                    op: "read",
                                    filters: {
                                      id: this.id
                                    }
                                  })
                                  return data?.[0].data?.products || []
                                }
                              }
                            }),
                          }
                        },
                        template: `
                          <div>
                            <header>{{$t('link.products')}}</header>
                            <main>
                              <ui-data-grid :dataSource='source' :columns='source.columns' :buttons="true"/>
                            </main>
                            <footer>
                              <ui-btn type='secondary' v-on:click.native='Close'>{{$t('close')}}</ui-btn>
                            </footer>
                            </div>
                      `
                      }).Modal({
                        id: context.id,
                        size: "lg"
                      });
                    }
                  },
                  template: `
                  <div v-if='!count'>{{count}} {{$t("product",{ count: count })}}</div>
                  <a v-else href='javascript:void(0)' v-on:click='onclick'>{{count}} {{$t("product",{ count: count })}}</a>
                  `
                },
                props: {
                  products: context?.data?.products,
                  products_count: context.products_count,
                  company_id: context.id
                },
              }],
              title: {text: context.company_name},
              props: [{
                label: Language.t("inn"),text: context.inn
              },
              {
                label: Language.t("registry.certificate_number"),text: context.certificate_id
              },{
                props: ['model'],
                template: `
                    <div>
                        <label>{{$t("document_expire")}}</label>
                        <div>
                            <div>{{$t('from')}} <iac-date :date='model.valid_from' :withoutTime='true'/></div>
                            <div>{{$t('to')}} <iac-date :date='model.expire_at' :withoutTime='true'/></div>
                        </div>
                    </div>
                    `
              },
              {
                label: Language.t("localization"),text: Math.round(context.localization_degree)+'%'
              }]
            }
          }
        }
      }),
      ecosystemShopDataSource: new DataSource({
        query: new Query({         
        }, [ktru_product, ad_query, search_query, green_query, area_query]), 
        store: new RefStore({
          ref: 'ref_online_shop_public',
          injectQuery: (params) => {
            params.fields = ["green","product","unit","debug_info","id","publicated_at","status","name","price","close_at","totalcost","currency", "amount","min_amount","images","owner_legal_area_id","product_name","remain_time"]            
            params.filters.price_error = undefined;
            params.filters.min_amount_error = undefined;
            params.filters.max_amount_error = undefined;
            params.filters.ecosystem_producer = true
            return params;
          },
        }),
        template: 'template-shop'
      })
    }
  },
  template: `
    <div>
      <iac-section type='header'>
        <ol class='breadcrumb'>
          <li><router-link to='/'>{{$t('home')}}</router-link></li>
          <li>{{$t("reestr_local_manufacturers")}}</li>
        </ol>
        <h1>{{$t("reestr_local_manufacturers")}}</h1>
      </iac-section>
      <iac-section>
        <ui-layout-tab>
          <ui-layout-group label='local_manufacturers'>
            <ui-data-view :dataSource='localManufacturersDataSource' >
              <template slot='filter'>
                <ui-filter :query='localManufacturersDataSource.query' />
              </template>
            </ui-data-view>
          </ui-layout-group>
          
          <ui-layout-group label='offers' v-if='$settings.procedures && $settings.ecosystem && $settings.procedures.e_shop'>
            <ui-data-view  type='tile' :dataSource='ecosystemShopDataSource'>
              <template slot='filter'>
                <ui-filter :query='ecosystemShopDataSource.query' />
              </template>
              <widget-shop type='ecosystem' slot='item' slot-scope='props' :props='props' :item='props.item' />
            </ui-data-view>
          </ui-layout-group>
        </ui-layout-tab>
      </iac-section>
    </div>
  `
}
