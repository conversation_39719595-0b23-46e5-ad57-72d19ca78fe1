
import { RefStore, DataSource, Query } from '@iac/data';
import { Http, Language } from '@iac/core';

var Description = {
  props: ["text", "proc_id", "contract_number"],
  data: function () {
    return {
      textSplit: undefined
    }
  },
  mounted() {
    let reg = RegExp(`(${this.contract_number}|${this.proc_id})`)
    this.textSplit = this.text.split(reg);
  },
  template: `
    <span>
      <span v-for='sp in textSplit'>
        <router-link v-if='sp == contract_number' :to='"/workspace/contract/"+contract_number'>{{sp}}</router-link>
        <router-link v-else-if='sp == proc_id' :to='"/procedure/"+proc_id'>{{sp}}</router-link>
        <span v-else>{{sp}}</span>
      </span>
    </span>
  `
}

export default {
  data() {
    let trade_type_query = new Query({
      queryText: null,
      trade_type: {
        type: "entity",
        label: "!deal_type",
        has_del: "true",
        dataSource: [
          { id: "public", name: Language.t("public_procurement") },
          { id: "exchange", name: Language.t("event_exchange") }
        ],
        onChange: (value) => {
          if (value && trade_type_query.trade_type == value)
            return;
          this.update_company_balance();
        },
        hidden: true,
      }
    })

    return {
      trade_type_query: trade_type_query,
      company_balance: [{}],
      balanceColumns: [
        'num',
        'debit',
        'credit',
        'date',
        { field: "description", style: "width:100%" },
      ],
      withdrawColumns: [
        'num',
        'date',
        'amount',
        'account',
        { field: "comment", style: "width:100%; word-break: break-word;" },
        'status'
      ],
      activeBlocksColumns: [
        'procedure',
        'summa',
      ],
      debtRegistryColumns: [
          {label: "procedure",field: "procedure",style:'width: 100%;text-align: left;'},
          {label: "summa",field: "summa",style:'text-align: right;'},
      ],
      transferColumns: [
        { field: 'id', label: "№" },
        'date',
        { field: 'amount', label: "funds_transfer_summa", style: "text-align: right" },
        { field: 'source', label: "funds_transfer_sender" },
        { field: 'target', label: "funds_transfer_receiver" },
        { field: 'comment', label: "funds_transfer_description", style: 'width: 100%' },
        'status'
      ],
      updatedDate: null,
      balanceDetails: new DataSource({
        query: new Query({
          queryText: {
            label: "Search",
            group: '!filter-',
            description: "$query_billing_desc",
          },
          date_begin: {
            group: '!filter-/!child/<date>',
            type: 'date',
            label: 'from',
            has_del: true,
            bind: {
              status: `date_error && {"type":"error"}`,
            }
          },
          date_end: {
            group: '!filter-/!child/<date>',
            type: 'date',
            label: 'to',
            has_del: true,
            bind: {
              status: `date_error && {"type":"error"}`,
            }
          },
          date_error : {
            group: '!filter-/!child/',
            type: 'model',
            label: '!',
            sync: false,
            bind: {
              value: "date_begin > date_end",
              status: `date_error && {"type":"error","message":"${Language.t('to_from_error')}"}`,
            },
          },
          op_type: {
            group: '!filter-',
            type: 'entity',
            dataSource: new DataSource({
              store: new RefStore({
                ref: 'ref_billing_operations'
              })
            }),
          },
          account: {
            group: '!filter-',
            type: 'entity',
            label: '!choose_account',
            dataSource: 'get_company_bank_accounts_billing',
            hidden: true,
            has_del: true,
            onChange: async (value) => {
              await this.update_company_balance(value);
            },
          }
        }, [trade_type_query]),
        store: new RefStore({
          method: 'billing_ref',
          ref: "get_account_info",
          injectQuery: (params) => {
            params.filters = params.filters || {};
            params.filters.date_error = undefined;
            return params;
          },
        })
      }),
      withdrawDetails: new DataSource({
        query: new Query({
          date_begin: {
            group: '!filter-/!child/<date>',
            type: 'date',
            label: 'from',
            has_del: true,
            bind: {
              status: `date_error && {"type":"error"}`,
            }
          },
          date_end: {
            group: '!filter-/!child/<date>',
            type: 'date',
            label: 'to',
            has_del: true,
            bind: {
              status: `date_error && {"type":"error"}`,
            }
          },
          date_error : {
            group: '!filter-/!child/',
            type: 'model',
            label: '!',
            sync: false,
            bind: {
              value: "date_begin > date_end",
              status: `date_error && {"type":"error","message":"${Language.t('to_from_error')}"}`,
            },
          },
          op_status: {
            group: '!filter-',
            type: 'entity',
            dataSource: new DataSource({
              store: new RefStore({
                ref: 'ref_billing_status'
              })
            }),
          }
        }, [trade_type_query]),
        store: new RefStore({
          method: 'billing_ref',
          ref: "get_billing_exports",
          context: (context)=>{
            context.actions = [
              context.cancellable && {
                label: "cancel_export",
                handler: () => {
                  this.cancel_export(context.id);
                },
                policy: "balance_export_cancel",
                question: Language.t("question_cancel_export"),
              }
            ]
            return context
          },
          injectQuery: (params) => {
            params.filters = params.filters || {};
            params.filters.date_error = undefined;
            return params;
          },
        })
      }),
      transferDetails: new DataSource({
        query: new Query({
          date_begin: {
            group: '!filter-/!child/<date>',
            type: 'date',
            label: 'from',
            has_del: true,
            bind: {
              status: `date_error && {"type":"error"}`,
            }
          },
          date_end: {
            group: '!filter-/!child/<date>',
            type: 'date',
            label: 'to',
            has_del: true,
            bind: {
              status: `date_error && {"type":"error"}`,
            }
          },
          date_error : {
            group: '!filter-/!child/',
            type: 'model',
            label: '!',
            sync: false,
            bind: {
              value: "date_begin > date_end",
              status: `date_error && {"type":"error","message":"${Language.t('to_from_error')}"}`,
            },
          },
          status: {
            group: '!filter-',
            type: 'entity',
            has_del: true,
            dataSource: 'ref_transfer_status',
          }
        }, [trade_type_query]),
        store: {
          method: 'get_transfer_info',
          injectQuery: (params) => {
            params.filters = params.filters || {};
            params.filters.date_error = undefined;
            return params;
          },
        }
      }),
      activeBlocksDetails: new DataSource({
        query: new Query({
          queryText: {
            label: "ID",
            group: '!filter-',
            //description: "$query_billing_desc",
          },
          proc_type: {
            group: '!filter-',
            type: 'entity',
            dataSource: [
              "request", "reduction", "selection", "tender",
            ],
            has_del: true,
            hidden: ()=>{
              let trade_type = this.trade_type_query.trade_type?.id || this.trade_type_query.trade_type;
              return trade_type == 'exchange'
            }
          }
        }, [trade_type_query]),
        store: new RefStore({
          method: 'billing_ref',
          ref: "get_active_blocks",
          injectQuery: (params)=>{
            if(params.filters?.trade_type == 'exchange'){
              params.filters.proc_type = 'schedule_exchange_contract';
            }
            return params;
          },
          context: (context)=>{
            context.proc_id = context.proc_id || context.entity_id || ""
            if(typeof context.proc_id == 'string' && context.proc_id.indexOf('.') >0){
              context.label = [`contract.${context.proc_type}`,"contract"]
              context.link = `/workspace/contract/${context.proc_id}/core`
            }else{
              context.label = context.proc_type
              context.link = `/procedure/${context.proc_id}/core`
            }
            // type =  entity | exchange

            return context
          }
        })
      }),
      debtRegistryDetails: new DataSource({
        query: new Query({
          queryText: {
            label: "ID",
            group: '!filter-',
            //description: "$query_billing_desc",
          },
          status: {
            group: '!filter-',
            type: "entity",
            has_del: true,
            dataSource: [
              {id: "created", name: "debt_registry.created"},
              {id: "completed", name: "debt_registry.completed"}

            ]
          }
        }, [trade_type_query]),
        store: new RefStore({
          method: 'billing_ref',
          ref: "get_debt_registry",
          context: (context)=>{

            context.proc_id = context.proc_id || context.entity_id || ""
            if(typeof context.proc_id == 'string' && context.proc_id.indexOf('.') >0){
              context.label = [`contract.${context.proc_type}`,"contract"]
              context.link = `/workspace/contract/${context.proc_id}/core`
            }else{
              context.label = context.proc_type
              context.link = `/procedure/${context.proc_id}/core`
            }

            Object.defineProperty(context, "bindClass", {
              configurable: true,
              enumerable: true,
              get: () => {

                if (context.status == "completed")
                  return "ui-alert ui-alert-success";

                //if ((context.status & 1) != 0)
                //  return "ui-alert ui-alert-success";

                //if ((context.status & 2) != 0)
                //  return "ui-alert ui-alert-warning";

              },
            });

            context.actions = [
              {
                label: "debt_registry.create",
                question: this.$t("question.call.action"),
                hidden: ()=>{
                  return context.status == "completed"
                },
                handler: () => {
                  this.$wait(async ()=>{
                    let { error, data } = await Http.api.rpc("create_debt_payment", {
                      debt_id: context.id,
                    })
                    if (error) {
                      Vue.Dialog.MessageBox.Error(error);
                    } else if (data) {
                      context.status = 'completed'
                      await this.update_company_balance();
                    }
                  })

                }
              }
            ]
            return context
          }
        })
      })
    }
  },
  mounted() {
    this.update_company_balance();
  },
  methods: {

    async blockForExchangeTrading() {
      this.$wait(async () => {
          const { error, data } = await Http.api.rpc('create_exchange_deposit_payment', {
              operation_type: 'income'
          });
          
          if (error) {
              Vue.Dialog.MessageBox.Error(error);
          } else if (data) {
              Vue.Dialog.MessageBox.Success(data.message);
              await this.update_company_balance();
          }
      });
  },

  async unblockExchangeTrading() {
      this.$wait(async () => {
          const { error, data } = await Http.api.rpc('create_exchange_deposit_payment', {
              operation_type: 'withdraw'
          });
          
          if (error) {
              Vue.Dialog.MessageBox.Error(error);
          } else if (data) {
              Vue.Dialog.MessageBox.Success(data.message);
              await this.update_company_balance();
          }
      });
  },


    async transfer_funds() {
      var { error, data } = await Http.api.rpc('transfer_funds', {

      });
      if (error) {
        if (error.code != "AbortError") {
          Vue.Dialog.MessageBox.Error(error);
        }
      } else {
        if (data && data.message) {
          Vue.Dialog.MessageBox.Success(data.message);
          this.transferDetails.reload();
        }
      }
    },
    async update_company_balance(value) {

      value = this.balanceDetails?.query?.account?.id || this.balanceDetails?.query?.account;
      let trade_type = this.trade_type_query.trade_type?.id || this.trade_type_query.trade_type;

      if (trade_type == 'exchange')
        value = undefined;

      this.$wait(async () => {
        var { data } = await Http.api.rpc('get_company_balance', {
          account: value,
          trade_type: trade_type
        });
        if (!data)
          data = {}
        if (!Array.isArray(data))
          data = [data]

        this.company_balance = data;
        //if(!this.company_balance[0].has_accounts){
        //this.balanceDetails.query.account = undefined;
        //}
        this.updatedDate = new Date();
      });
    },
    async withdraw() {
      var { error, data } = await Http.api.rpc('export_funds', {

      });
      if (error) {
        if (error.code != "AbortError") {
          Vue.Dialog.MessageBox.Error(error);
        }
      } else {
        if (data && data.message) {
          Vue.Dialog.MessageBox.Success(data.message);
          this.withdrawDetails.reload();
        }
      }
    },
    async cancel_export(id) {
      var { error, data } = await Http.api.rpc('cancel_export', {
        id: id  
      });
      if (error) {
        Vue.Dialog.MessageBox.Error(error);
      } else {
        if (data && data.message) {
          Vue.Dialog.MessageBox.Success(data.message);
          this.withdrawDetails.reload();
        }
      }
    },
    async downloadBalanceDetails(type) {
      Vue.Dialog({
        props: ['type'],
        data() {
          return {
            fields: [
              {
                name: 'from',
                group: '!g-/<date>',
                type: "date",
                has_del: true,
                value: undefined,
                status: undefined
              },
              {
                name: 'to',
                group: '!g-/<date>',
                type: "date",
                has_del: true,
                value: undefined,
                status: undefined
              }
            ]
          }
        },
        methods: {
          async requestReport(from, to) {
            const requestData = { ref: "history_act_sverki", template: "history_act_sverki_base", type: this.type, params: { from, to } }
            let { error, data } = await Http.report.rpc("render_free_report", requestData, { format: "blob" });
            if (error && error.code == "AbortError")
              return;
            if (error) {
              Vue.Dialog.MessageBox.Error(error)
            } else {
              const a = document.createElement("a");
              document.body.appendChild(a);
              a.style = "display: none";
              a.href = window.URL.createObjectURL(new Blob([data], { type: "octet/stream" }));;
              a.download = `report.${type}`;
              a.click();
              window.URL.revokeObjectURL(a.href);
              a.remove();
            }
          },
          submit() {
            const fromField = this.fields.find(item => item.name === 'from')
            const toField = this.fields.find(item => item.name === 'to')
            fromField.status = undefined
            toField.status = undefined

            if (toField && fromField) {
              const from = new Date(fromField.value)
              const to = new Date(toField.value)
              if (from > to) {
                const status = { type: 'error', message: Language.t('end_date_of_the_period_cannot_be_earlier_than_starting_date') }
                fromField.status = status
                toField.status = status
              } else if (to > new Date()) {
                toField.status = { type: 'error', message: Language.t('end_date_of_the_period_cannot_be_later_than_current_date') }
              } else {
                this.requestReport(from, to)
                this.Close()
                Vue.Dialog.MessageBox.Success(this.$t('billing_download_success'))
              }
            }
          }
        },
        template: `
            <div>
              <header>{{$t('download_balance_sheet_details_for_the_period')}}</header>
              <main>
                <ui-layout :fields='fields' />
              </main>
              <footer>
                <ui-btn type='secondary' v-on:click.native='Close'>{{$t('cancel')}}</ui-btn>
                <ui-btn type='primary' v-on:click.native='submit'>{{$t('download')}}</ui-btn>
              </footer>
            </div>
          `
      }).Modal({ type })
    }
  },
  components: {
    description: Description,
  },
  template: `
  <iac-access :access='$policy.balance_view || $policy.balance_view_exchange'>
    <iac-section type='header'>
      <ol class='breadcrumb'>
        <li><router-link to='/'>{{$t('home')}}</router-link></li>
        <li>{{$t('nav.billing')}}</li>
      </ol>
      <div class='title'>
        <div><h1>{{$t('nav.billing')}}</h1></div>
        <ui-field v-if='$settings.exchange' :model='trade_type_query.properties.trade_type'/>
      </div>
    </iac-section>
    <iac-section>
      <ui-layout-tab>
        <ui-layout-group key='balance' label='balance'>
          <div class='billing_container'>
            <div class='billing-card'>
              <div class='billing-card__header'>    
                <div class='iac-col-lg-8'>
                  <h2 class='billing-card__title'>{{ $t('balance') }}</h2>  
                  <div class='billing-card__desc'>
                    <div v-if='updatedDate' class='balance-updated'>
                      <span>{{ $t('updated') }}</span>:
                      <iac-date :date='updatedDate' withMonthName /> {{ $t('billing.on') }} <iac-date :date='updatedDate' onlyTime />
                    </div>
                    <a href='#' class='refresh' @click.prevent='update_company_balance(balanceDetails.query.account && balanceDetails.query.account.id)'>{{ $t('balance.refresh') }}</a>
                  </div>
                </div>
                <div v-if="$policy.balance_exchange_deposit_create && trade_type_query.trade_type?.id === 'exchange' ">
                    <ui-btn type='primary' @click.native='blockForExchangeTrading'>{{$t('balance.block_for_exchange_trading')}}</ui-btn>
                    <ui-btn type='primary' @click.native='unblockExchangeTrading'>{{$t('balance.unblock')}}</ui-btn>
                </div>
                <div v-if='company_balance[0].has_accounts' class='iac-col-lg-4'>
                  <ui-field :model='balanceDetails.query.properties.account' has_del='true' />
                </div>
              </div>


              <ui-layout-tab name='currency' class='billing-card__body'>
                <ui-layout-group :label='balance.currency' class='table-wrap' v-for='balance in company_balance'>
                  <table class='table-collapsed billing-card__balance'>
                    <tr>
                      <td width='288'>{{ $t('balance.total_funds') }}</td>
                      <td><iac-number class='mr-12' :value='balance.total_funds' delimiter=' ' part='2'/> <router-link to='/info/how_replenish'>{{ $t('how_replenish') }}</router-link></td>
                    </tr>
                    <tr>
                      <td width='288'>{{ $t('balance.free_funds') }}</td>
                      <td><iac-number :value='balance.free_funds' delimiter=' ' part='2'/></td>
                    </tr>
                    <tr>
                      <td width='288'>{{ $t('balance.blocked_funds') }}</td>
                      <td><iac-number class1='color-red' style='color: #ff7733;' :value='balance.blocked_funds || 0' delimiter=' ' part='2'/></td>
                    </tr>
                    <tr v-if='$policy.balance_exchange_deposit_view && trade_type_query.trade_type?.id === "exchange"'>
                      <td width='288'>{{ $t('balance.blocked_for_exchange') }}:</td>
                      <td><iac-number class1='color-red' style='color: #ff7733;' :value='balance.exchange_deposit|| 0' delimiter=' ' part='2'/></td>
                    </tr>
                    <tr>
                      <td width='288'>{{ $t('balance.export_funds') }}</td>
                      <td><iac-number :value='balance.export_funds' delimiter=' ' part='2'/></td>
                    </tr>
                    <tr v-if='$settings.exchange && $policy.balance_debt_view'>
                      <td width='288'>{{ $t('debt_amount_short') }}</td>
                      <td><iac-number class='color-red' :value='balance.debt_amount || 0' delimiter=' ' part='2'/></td>
                    </tr>
                    <tr>
                      <td width='288'>{{ $t('currency') }}</td>
                      <td>{{balance.currency}}</td>
                    </tr>
                  </table>
                </ui-layout-group>
              </ui-layout-tab>

              
            </div>
            <ui-layout-group class='billing-card'>
              <div class='billing-card__header'>
                <h2 class='billing-card__title'>{{ $t('balance_details') }}</h2>
                <div class="ui-btn-group">
                  <button class='ui-btn ui-btn-info ui-btn-xs' @click='e=>downloadBalanceDetails("pdf")'>pdf</button>
                  <button class='ui-btn ui-btn-info ui-btn-xs' @click='e=>downloadBalanceDetails("xls")'>xls</button>
                  <button class='ui-btn ui-btn-info ui-btn-xs' @click='e=>downloadBalanceDetails("html")'>html</button>
                  <button class='ui-btn ui-btn-info ui-btn-xs' @click='e=>downloadBalanceDetails("json")'>json</button>
                </div>
              </div>
              <div class='billing-card__body1'>
                <ui-data-grid class='top_filter' :dataSource='balanceDetails' :columns='balanceColumns'>
                  <template slot='num' slot-scope='props'><div>{{ props.item.id }}</div></template>
                  <template slot='debit' slot-scope='props'><div v-if='props.item.debit' style='white-space: nowrap; text-align: right;'>
                    <iac-number :value='props.item.debit' delimiter=' ' part='2'/>
                    <span v-if='props.item.currency'>&nbsp;{{props.item.currency}}</span>
                  </div></template>
                  <template slot='credit' slot-scope='props'><div v-if='props.item.credit' style='white-space: nowrap; text-align: right;'>
                    <iac-number :value='props.item.credit' delimiter=' ' part='2'/>
                    <span v-if='props.item.currency'>&nbsp;{{props.item.currency}}</span>
                  </div></template>
                  <template slot='date' slot-scope='props'><div> <iac-date :date='props.item.date' full/></div></template>
                  <template slot='description' slot-scope='props'>
                      <div v-if='props.item.description'><description :proc_id='props.item.proc_id' :contract_number='props.item.contract_number' :text="props.item.description" /></div>
                      <div v-if='props.item.doc_number' style='color: #555'>{{$t('reason')}}: №{{props.item.doc_number}}, <description :proc_id='props.item.proc_id' :contract_number='props.item.contract_number' :text="props.item.doc_desc" /></div>
                  </template>
                </ui-data-grid>
              </div>
            </ui-layout-group>
          </div>
        </ui-layout-group>
        <ui-layout-group v-if='$policy.balance_export_view' key='withdraw' label='withdraw'>
          <div class='billing_container'>
            <ui-layout-group class='billing-card'>
              <div class='billing-card__header billing-card__header--with-action'>
                <h2 class='billing-card__title'>{{ $t('withdrawal_details') }}</h2>
                <ui-btn v-if='$policy.company_edit' type='primary' @click.native='withdraw'>{{$t('withdraw_action')}}</ui-btn>
              </div>
              <div class='billing-card__body1'>
                <ui-data-grid class='top_filter' :dataSource='withdrawDetails' :columns='withdrawColumns' :buttons='true'>
                  <template slot='num' slot-scope='props'><div>{{ props.item.number }}</div></template>
                  <template slot='date' slot-scope='props'><div><iac-date :date='props.item.date' withoutTime/></div></template>
                  <template slot='amount' slot-scope='props'><div v-if='props.item.amount' style='white-space: nowrap; text-align: right;'>
                    <iac-number :value='props.item.amount' delimiter=' ' part='2'/>
                    <span v-if='props.item.currency'>&nbsp;{{props.item.currency}}</span>
                  
                  </div></template>
                  <template slot='account' slot-scope='props'><div>{{props.item.account}}</div></template>
                  <template slot='status' slot-scope='props'><ui-ref style='white-space: nowrap' source='ref_billing_status' :value='props.item && props.item.status' class='{ "color-red": props.item && props.item.status == 3 }'/></template>
                </ui-data-grid>
              </div>
            </ui-layout-group>
          </div>
        </ui-layout-group>

        <ui-layout-group v-if='$policy.company_transfer_funds' key='funds_transfer' label='funds_transfer'>
        <div class='billing_container'>
          <ui-layout-group class='billing-card'>
            <div class='billing-card__header billing-card__header--with-action'>
              <h2 class='billing-card__title'>{{$t('funds_transfer')}}</h2>
              <ui-btn type='primary' @click.native='transfer_funds'>{{$t('funds_transfer_action')}}</ui-btn>
            </div>
          

          <ui-data-grid class='top_filter' :dataSource='transferDetails' :columns='transferColumns'>
            <template slot='date' slot-scope='props'><div> <iac-date :date='props.item.date' full/></div></template>

            <template slot='amount' slot-scope='props'><div v-if='props.item.amount' style='white-space: nowrap; text-align: right;'>
              <iac-number :value='props.item.amount' delimiter=' ' part='2'/>
              <span v-if='props.item.currency'>&nbsp;{{props.item.currency}}</span>
                  
            </div></template>
            
            <template slot='source' slot-scope='props'>
              <div :title='props.item.src_company_name'>{{props.item.src_company_code  || 'ЦО'}}</div>
            </template>

            <template slot='target' slot-scope='props'>
              <div :title='props.item.target_company_name'>{{props.item.target_company_code || 'ЦО'}}</div>
            </template>

            <template slot='status' slot-scope='props'><ui-ref style='white-space: nowrap' source='ref_transfer_status' :value='props.item && props.item.status' class='{ "color-red": props.item && props.item.status == 3 }'/></template>
          </ui-data-grid>
          
          </ui-layout-group>
          </div>
        </ui-layout-group>

        <ui-layout-group key='active_blocks' label='active_blocks'>
          <ui-data-grid class='top_filter' :dataSource='activeBlocksDetails' :columns='activeBlocksColumns'>
            <template slot='procedure' slot-scope='props'>
            <span v-if='props.item.type == "exchange"'>{{$t('session_blocked_funds')}}</span>
            <router-link v-else :to='props.item.link'> {{ $t(props.item.label) }} № {{props.item.proc_id}}</router-link>
          </template>
            <template slot='summa' slot-scope='props'><div v-if='props.item.amount'  style='white-space: nowrap; text-align: right;'>
              <iac-number :value='props.item.amount' delimiter=' ' part='2'/>
              <span v-if='props.item.currency'>&nbsp;{{props.item.currency}}</span>
            </div></template>
          </ui-data-grid> 
        </ui-layout-group>

        <ui-layout-group key='debt_registry' label='debt_registry' v-if='$policy.balance_debt_view' >
          <ui-data-grid :readonly='!$policy.balance_debt_create_payment' buttons class='top_filter' :dataSource='debtRegistryDetails' :columns='debtRegistryColumns'>
          <template slot='procedure' slot-scope='props'>
            <router-link :to='props.item.link'> {{ $t(props.item.label) }} № {{props.item.proc_id}}</router-link>
          </template>
          <template slot='summa' slot-scope='props'><div v-if='props.item.amount'  style='white-space: nowrap; text-align: right;'>
            <iac-number :value='props.item.amount' delimiter=' ' part='2'/>
            <span v-if='props.item.currency'>&nbsp;{{props.item.currency}}</span>
          </div></template>
          </ui-data-grid> 

        </ui-layout-group>

      </ui-layout-tab>
    </iac-section>
  </iac-access>
  `
}
