import { Query } from '@iac/data'
import { Context } from '@iac/kernel'

export default {
    data: function () {
        return {
            user: Context.User,
            query: {
                green: new Query({
                    green: {
                        type: "entity",
                        label: "!green_procedures",
                        group: "green_procedures",
                        has_del: true,
                        dataSource: [{id: true,name: "yes"},{id: false,name: "no"}],
                        hidden: ()=>!this.$settings?.procedures?._green
                    }                  
                }),
                search: new Query({
                    queryText: {
                        icon: 'search',
                        label: "!Search",
                        hidden: true,
                    },
                }),
                
                area: new Query({
                    area_path: {
                        group: "area",
                        label: "!area",
                        type: "enum-tree",
                        dataSource: "ref_uz_region_lv4",
                        //multiple: true,
                        //hidden: true,
                        order: -1,
                        onChange:(value)=>{
                            let area_path = value || [];
                            area_path = area_path.filter((val)=>{
                                if(val.indexOf('.') < 0)
                                    return false
                                return true;
                            })
                            this.query.area.set({area_path: area_path})
                        }
                    }
                }),
            }
        }
    },
    template: `
        <div>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('hp.registries.link5')}}</li>
                </ol>
                <h1>{{$t('hp.registries.link5')}}</h1>

                <div class='links'>
                    <router-link :title="$t('tender')" v-if='$settings.procedures && $settings.procedures.tender' to='/procedure/tender'>{{$t('tender')}}</router-link>
                    <router-link :title="$t('selection')" to='/procedure/selection'>{{$t('selection')}}</router-link>
                    <router-link :title="$t('reduction')" v-if='$settings.procedures && $settings.procedures.auction' to='/procedure/reduction'>{{$t('reduction')}}</router-link>
                    <router-link :title="$t('nav.electronic_shop')" v-if='$settings.procedures && $settings.procedures.e_shop' to='/procedure/ad'>{{$t('nav.electronic_shop')}}</router-link>
                    <router-link :title="$t('nav.national_shop')" v-if='$settings.procedures && $settings.procedures.e_shop && $settings.procedures.e_shop.national_shop' to='/procedure/nad'>{{$t('nav.national_shop')}}</router-link>
                    <router-link :title="$t('master_agreement')" v-if='$settings.procedures && $settings.procedures.master_agreement' to='/procedure/master_agreement'>{{$t('master_agreement')}}</router-link>
                    <router-link :title="$t('contest')" v-if='$settings.procedures && $settings.procedures.contest' to='/procedure/contest'>{{$t('contest')}}</router-link>
                </div> 

            </iac-section>

            <iac-section>
                <router-view :query='query' />
            </iac-section>
        </div>
    `
}