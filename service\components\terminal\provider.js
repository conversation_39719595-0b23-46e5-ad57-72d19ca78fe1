

Vue.Tiling = function (name, {component, setting,select_group=false,select_contract=false,required_contract=false,params={}}) {
    if (setting) {
        setting = new setting({
            id: name,
        });
        var mixin = {
            data: function () {
                return {
                    setting_model: setting
                }
            },
            computed: {
                $setting() {
                    return this.setting_model.params
                }
            }
        }

        component.mixins = component.mixins || [];
        component.mixins.push(mixin);
    }

    Vue.Tiling[name] = { 
        component: component, 
        setting: setting,
        select_group,
        select_contract,
        required_contract,
        params: params
    }
    return component
}


