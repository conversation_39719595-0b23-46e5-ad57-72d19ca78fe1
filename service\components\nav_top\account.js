import { Context, Config } from '@iac/kernel'
import { Http, Language } from '@iac/core'
import { Model, DataSource, RemoteStore } from '@iac/data'

import CreateCompanyDlg from './create_company'
import CreateExternalCompanyDlg from './create_external_company'

function updateQueryStringParameter(uri, key, value) {
    var re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
    var separator = uri.indexOf('?') !== -1 ? "&" : "?";
    if (uri.match(re)) {
        return uri.replace(re, '$1' + key + "=" + value + '$2');
    }
    else {
        return uri + separator + key + "=" + value;
    }
}

class AddToGos extends Model {
    constructor(context) {
        super(context)
        this.error = undefined;
        this.page = 0;
        this.wait = false
    }
    props() {
        return {
            pkcs7B64: {
                type: 'ecp',
                label: 'reg_company.ecp',
                has_del: true,
                required: true,
                value: undefined,
                description: 'activate_state_company_e_signature',
                attr: {
                    createPkcs7: true
                },
                onChange: async (value) => {

                    this.properties.company.dataSource = undefined;
                    this.company = undefined

                    if (!value) {
                        return;
                    }

                    this.wait = true;

                    let { error, data } = await Http.api.rpc("my_gos_companies", {
                        pkcs7B64: value
                    })
                    if (error) {
                        await Vue.Dialog.MessageBox.Error(error)

                    } else if (data.length > 0) {


                        this.properties.company.dataSource = DataSource.get(data.map((item) => {
                            return {
                                id: item.id,
                                name: item.title,
                                desc: item.anno,
                                has_admin: item.has_admin
                            }
                        }))

                    }
                    this.wait = false;
                }
            },
            banner_1: {
                type: 'widget',
                label: "!",
                widget: {
                    name: "ui-alert",
                    props: {
                        type: "warning",
                    },
                    content: Language.t('there_are_no_companies_with_the_specified_esignature_please_contact_system_administrator')
                },
                hidden: () => {
                    if (this.pkcs7B64 && !this.properties.company.dataSource && !this.wait)
                        return false;
                    return true;
                }
            },
            company: {
                type: 'entity',
                has_del: true,
                required: true,
            },
            banner_2: {
                type: 'widget',
                label: "!",
                widget: {
                    name: "ui-alert",
                    props: {
                        type: "warning",
                    },
                    content: Language.t('this_company_already_has_an_administrator_to_join_the_company_please_contact_its_administrator')
                },
                hidden: () => {

                    if (this.company && this.company.has_admin && !this.wait)
                        return false;
                    return true;
                }
            }
        }

    }

    get send() {
        if (!this.company || this.company.has_admin || this.wait) {
            return;
        }
        return async () => {
            let params = this.fields.filter((field) => {
                if (field.hidden && typeof field.hidden == 'function') {
                    return !field.hidden();
                }
                return !field.hidden;
            }).map((field) => {
                let value = field.value;
                if (field.value && field.value.exp && field.value.exp.value != undefined) {
                    value = field.value.exp.value
                }

                if (Array.isArray(value) && value.length <= 0)
                    value = undefined;

                return {
                    name: field.name,
                    value: value
                }
            }).filter((field) => {
                return field.value != undefined
            }).reduce((prev, curr) => {
                prev[curr.name] = curr.value
                return prev;
            }, {})


            return Http.api.rpc("inject_in_gos_company", params)
        }
    }


}

var inviteCount = {
    data: function () {
        return {
            count
        }
    },
    mounted() {
        Http.api.rpc("get_invites_count").then(({ error, data }) => {
            if (!error)
                this.count = data
        })
    },
    template: `
        <div>{{count}}</div>
    `
}

export default {
    data: function () {
        return {
            dropdown: false,
            user: Context.User,
            access: Context.Access,
            invite_count: undefined,
        }
    },

    computed: {
        face_photo() {
            if (!this.user.photo)
                return;
            return `${Config.api_server}/file/${this.user.photo}`
        }
    },

    methods: {
        async develop_tool() {
            this.dropdown = false;
            await Vue.Dialog.DevelopSettingDlg.Modal({
                size: 'right'
            })
        },
        open() {
            this.invite_count = 0;
            this.dropdown = true;
            Http.api.rpc("get_invites_count").then(({ error, data }) => {
                if (!error)
                    this.invite_count = data
            })
        },
        close() {
            this.dropdown = false;
        },
        profile() {
            this.dropdown = false;
            Vue.Dialog.MemberDlg.Modal({ id: "profile", size: "right" })
        },
        notify(){
            this.dropdown = false;
            Vue.Dialog({
                data: function(){
                    return {
                        value: undefined,
                        source: new DataSource({
                            store: {
                                method: "possible_subscribes",
                                context: (context)=>{
                                    context.name = context.name || context.id;
                                    context.desc = context.description;
                                    return context;
                                }
                            }
                        })
                    }
                },
                methods: {
                    async save(){
                        this.$wait(async ()=>{
                            let {error,data} = await Http.api.rpc("set_notify_subscribes",{
                                subscribes: this.value
                            })
                            if(error)
                            await Vue.Dialog.MessageBox.Error(error);
                        });
                    }
                },
                mounted(){
                    this.$wait(async ()=>{
                        let {error,data} = await Http.api.rpc("get_notify_subscribes")
                        if(error)
                            await Vue.Dialog.MessageBox.Error(error);
                        else{
                            this.value = data || []
                        }
                    })        
                },
                template: `
                    <div>
                        <header>{{$t("nav.notify_subscribes")}}</header>
                        <main v-if='value'>
                            <ui-enum-tree v-model='value' :dataSource='source' />
                        </main>
                        <footer>
                            <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
                            <ui-btn type='primary' v-on:click.native='save()'>{{$t('save')}}</ui-btn>                       
                        </footer>
                    </div>
                `
            }).Modal({

            });
            
        },
        invites() {
            this.dropdown = false;
            Vue.Dialog({
                props: ["change_user"],
                data: function () {
                    return {
                        source: new DataSource({
                            store: new RemoteStore({
                                method: "get_invites",
                                context: (context) => {
                                    context.actions = [
                                        {
                                            label: "accept",
                                            handler: async () => {
                                                let { error, data } = await Http.api.rpc("check_invite", {
                                                    company_id: context.company_id,
                                                    code: context.code,
                                                    accept: true
                                                })
                                                if (error) {
                                                    Vue.Dialog.MessageBox.Error(error);
                                                } else if (data) {
                                                    if (Array.isArray(data)) {
                                                        data = data[0];
                                                    }
                                                    if (data && data.id) {
                                                        if (await Vue.Dialog.MessageBox.Question(Language.t('go_to_the_workspace_of_this_company')) == Vue.Dialog.MessageBox.Result.Yes) {
                                                            this.change_user(data.id)
                                                            return this.Close();
                                                        }
                                                    }
                                                    this.source.reload();
                                                }
                                            }

                                        },
                                        {
                                            label: "deny",
                                            btn_type: "danger",
                                            handler: async () => {
                                                let { error, data } = await Http.api.rpc("check_invite", {
                                                    company_id: context.company_id,
                                                    code: context.code,
                                                    accept: false
                                                })
                                                if (error) {
                                                    Vue.Dialog.MessageBox.Error(error);
                                                } else {
                                                    if (data && data.message) {
                                                        Vue.Dialog.MessageBox.Success(data.message);
                                                    }
                                                    this.source.reload();
                                                }
                                            }
                                        }

                                    ]
                                    return context;
                                }
                            }),
                            columns: [
                                {
                                    field: "company", label: "company", style: 'width: 100%;', display: (value) => {
                                        return value.fulltitle
                                    }
                                }
                            ]
                        })
                    }
                },
                template: `
                    <div>
                        <header>{{$t('invites')}}</header>
                        <main>
                            <ui-data-grid :dataSource='source' :columns='source.columns' :buttons="true"/>
                        </main>
                        <footer>
                            <ui-btn type='secondary' v-on:click.native='Close'>{{$t('close')}}</ui-btn>
                        </footer>
                    </div>
                `
            }).Modal({
                size: "lg",
                change_user: this.change_user
            })
        },
        async change_user(user_id) {
            this.dropdown = false;
            let error = await this.user.change_user(user_id)
            if (error) {
                Vue.Dialog.MessageBox.Error(error);
            }
            //location.href = updateQueryStringParameter(location.href,"god",user_id)

        },
        async logOut() {
            this.dropdown = false;
            if(!await this.user.logOut(true))
                return;
            this.$router.push({ path: '/' })
        },
        async signIn() {
            this.dropdown = false;
            if (await Vue.Dialog.SignIn.Modal()) {
                if (this.$route.path == '/')
                    this.$router.push({ path: '/workspace' })
            }
        },
        async go_to_tasia() {
            let { error, data: url } = await Http.api.rpc("get_telegram_subscribe_url")
            if (error || !url) {
                return
            }
            window.open(url, '_blank').focus()
        },
        async create_company() {
            this.dropdown = false;

            let CreateDlg = this.$settings.external_company ? CreateExternalCompanyDlg : CreateCompanyDlg

            await CreateDlg.Modal({
                size: "lg",
                change_user: this.change_user
            })

        },
        async add_to_gos() {
            this.dropdown = false;
            await Vue.Dialog({
                props: ["change_user"],
                data: function () {
                    return {
                        model: new AddToGos()
                    }
                },
                methods: {
                    async send() {
                        this.$wait(async () => {
                            let { error, data } = await this.model.send();
                            if (error) {
                                Vue.Dialog.MessageBox.Error(error)
                            } else {
                                this.change_user(data.id)
                                this.Close();
                                //await this.user.refreshToken();
                                this.$router.push('/workspace')
                            }
                        })
                    }
                },
                template: `
                <div :class='model && model.wait && "iac-wait"'>
                    <header>{{ $t('activate_state_company')}}</header>
                    <main>
                        <ui-layout :fields='model.fields' />
                    </main>
                    <footer>
                        <ui-btn type='secondary' v-on:click.native='Close'>{{$t('close')}}</ui-btn>
                        <ui-btn type='primary' :disabled='!model || !model.send' v-on:click.native='send'>{{$t('send')}}</ui-btn>
                    </footer>
                </div>               
                `
            }).Modal({
                change_user: this.change_user
            })
        }
    },
    components: {
        inviteCount,
    },
    template: `
        <div class='user'>
            <template v-if='!user.face_id'>
                <ui-btn :title='$t("desc.signIn")' type='primary' v-on:click.native='signIn'>{{$t('signIn')}}</ui-btn>
            </template>
            <template v-else>
                <div>
                    <div class='user_account'> 
                        <div class='info'>
                            <router-link to='/workspace' class='name'>{{user.name}}</router-link>
                            <router-link class='team_name clamp_2' to='/workspace/company_requisites' v-if='$policy.company_edit || $policy.company_view_own_requisites'>{{user.team_name}}</router-link>
                            <div v-else class='team_name clamp_2'>{{user.team_name}}</div>
                        </div>
                        <span class='icon-wrapper' @click='open'>
                            <div v-if='face_photo' class='image'><img :src='face_photo'/></div>
                            <span v-else class='icon'>
                                <span>{{user.name[0]}}</span>
                            </span>
                        </span>
                    </div>
                    <div class='user_menu' style='min-width: 280px;' v-if='dropdown' v-on-clickaway='close'>
                        <div class='block'>
                            
                            <router-link to='/workspace' class='item hover nowrap' style='text-decoration: none;'>
                                <div>
                                    <div class='icon-wrapper'>
                                        <div v-if='face_photo' class='image'><img :src='face_photo'/></div>
                                        <div v-else class='icon'>
                                            <span>{{user.name[0]}}</span>
                                        </div>
                                    </div>
                                </div>
                                <div style='color: #000'>{{user.name}}</div>
                            </router-link>

                            <div class='item hover nowrap' v-on:click='profile'>
                                <div><icon>userinfo</icon></div><div>{{$t('account_profile')}}</div>
                            </div> 
                            <div v-if='$settings.notification && $settings.notification._subscribes' class='item hover nowrap' v-on:click='notify'>
                                <div><icon>bulb</icon></div><div>{{$t('nav.notify_subscribes')}}</div>
                            </div> 
                            <div class='item hover nowrap' v-on:click='invites' v-if='invite_count'>
                                <div>{{invite_count}}</div><div>{{$t('invites_count',{count: invite_count})}}</div>
                            </div> 
                            <div class='item hover nowrap' v-on:click='develop_tool' v-if='$user_develop.develop_tools'>
                                <div><icon>settings</icon></div><div>{{$t('developer_settings')}}</div>
                            </div> 
                            <div class='item hover' v-on:click='logOut'>
                                <div><icon>logout</icon></div><div>{{$t('account_exit')}}</div>
                            </div> 
                        </div>
                        <div class='block' style='max-height: 308px; overflow-x: hidden; overflow-y: auto;' v-if='user && user.users && user.users.length > 0'>
                            <div class='item hover' v-for='user in user.users' v-on:click='change_user(user.id)'>
                                <div><icon v-if='user.active'>ok</icon></div>
                                <div>{{user.company.title}}</div>
                            </div>
                        </div>

                        <div class='block'>
                            <div class='item hover nowrap'  v-on:click='create_company'>
                                <div>
                                    <div class='icon-wrapper' style='border: none;'>
                                        <div style='width: 36px; height: 36px;' class='icon'>
                                            <span>+</span>
                                        </div>
                                    </div>
                                </div>
                                <div>{{$t('account_register_a_company')}}</div>
                            </div>
                            <div v-if='$settings.ecp' class='item hover nowrap'  v-on:click='add_to_gos'>
                                <div>
                                    
                                </div>
                                <div>{{ $t('activate_state_company')}}</div>
                            </div>
                        </div>
                    </div>


                </div>
            </template>
        </div>
    `
}

