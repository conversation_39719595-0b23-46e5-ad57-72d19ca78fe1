import { Language } from '@iac/core';
import { Settings } from '@iac/kernel';

export default {
  props: {
    row: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      list: Settings._languages || ['ru-RU','uz-UZ@cyrillic','ky-KG','en-US'],
      Provider: Language,
      dropdown: false,
    }
  },
  computed:{
    languages() {
      return this.list.map((key)=>{
        return {
          id: key,
          label: Language.list[key] ? Language.list[key].title : key
        }
      })
    },
  },
  methods:{
    set(key){
      this.Provider.set(key);
    },
    close() {
      this.dropdown = false;
    }
  },
  template: `
    <div :title='$t("desc.language")' class='iac-language' v-if='!row'>
      <button class='iac-language__toggle button-inherit' @click='dropdown = true'>
        <span class='iac-language__title'>{{ list[Provider.local] }}</span>
        <icon class='iac-language__icon'>arrow</icon>
      </button>
      <ul v-if='dropdown' v-on-clickaway='close' class='iac-language__content list'>
        <li :key='lng.key' v-for='lng in languages'>
          <button @click='set(lng.id)' class='button-inherit item'>{{ lng.label }}</button>
        </li>
      </ul>
    </div>   
    <div :title='$t("desc.language")' v-else class='iac-language row'>
      <span :key='lng.key' v-for='lng in languages' @click='set(lng.id)' :class='lng.id == Provider.local && "active"'>{{ lng.label }}</span>
    </div> 
  `,
};
