

import Search from './search'
import SearchProcedure from './search_procedure'

import Layout from './details'
import Core from './details/core'
import Claim from './details/claim'
import Logs from './details/logs'
import Reports from './details/reports'

let params = {
    component: Layout,
    redirect: to => {
        return `${to.path}/core`;
    },
    children: [
        { path: 'core', component: Core },
        { path: 'claim', component: Claim },
        { path: 'logs', component: Logs },
        { path: 'reports', component: Reports }
    ]
}

export default [
    {
        path: '/procedure',
        redirect: to => {
            return `${to.path}/tender`;
        },
        component: Search,
        children: [
            { path: 'tender', component: SearchProcedure, props: { type: "tender" } },
            { path: 'contest', component: SearchProcedure, props: { type: "contest" } },
            { path: 'selection', component: SearchProcedure, props: { type: "selection" } },
            { path: 'ad', component: SearchProcedure, props: { type: "ad" } },
            { path: 'nad', component: SearchProcedure, props: { type: "nad" } },
            { path: 'reduction', component: SearchProcedure, props: { type: "reduction" } },
            { path: 'master_agreement', component: SearchProcedure, props: { type: "master_agreement" } }
        ]
    },
    {
        path: 'procedure/:id',
        ...params
    },
    {
        path: 'ad/:id',
        ...params,
        props: {
            type: "ad"
        }
    },
    {
        path: 'nad/:id',
        ...params,
        props: {
            type: "nad"
        }
    },
    {
        path: 'ecosystem/:id',
        ...params,
        props: {
            type: "ecosystem"
        }
    },
]