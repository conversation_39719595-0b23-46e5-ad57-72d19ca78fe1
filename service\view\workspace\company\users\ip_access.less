.ip_access {
    >.users {
        >.user {
            padding-bottom: 20px;
            position: relative;

            &::before {
                content: "";
                position: absolute;
                border-left: 1px dotted #666;
                bottom: 0;
                top: 0;
            }

            &:last-child {
                &::before {
                    bottom: unset;
                    height: 10px;
                }
            }

            >.title {
                display: flex;
                cursor: pointer;
                align-items: center;

                &::before {
                    content: " ";
                    flex: 0 0 7px;
                    height: 1px;
                    border-top: 1px dotted #666;
                    margin-right: 2px;
                }

                >icon {
                    font-size: 20px;
                    margin-right: 4px;
                    color: #CCC;
                }

                &.active {
                    >icon {
                        color: rgb(0 154 184);
                    }
                }
            }

            >.content {
                margin-left: 17px;
                padding-top: 10px;
                position: relative;

                &::before {
                    content: "";
                    position: absolute;
                    border-left: 1px dotted #666;
                    bottom: 11px;
                    top: 0;
                }

                >.desc {
                    padding: 0 0 10px 10px;
                    color: #777;
                    font-size: 14px;
                }

                >.ip {
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    padding: 3px 0;

                    &::before {
                        content: "";
                        flex: 0 0 7px;
                        height: 1px;
                        border-top: 1px dotted #666;
                        margin-right: 3px;
                    }
                    &:hover {
                        background: aliceblue;

                    }
                }
                >.add {
                    display: flex; align-items: center;
                    &:before{
                        content: "";
                        flex: 0 0 7px; height: 1px; border-top: 1px dotted #666; margin-right: 3px;
                    }
                    >icon {
                        line-height: 22px;
                        color: #009abfc4;
                        margin-left: 2px;
                    }

                    >.input {
                        line-height: 22px;
                        margin-left: 8px;
                        border: 1px solid #8ddef1;
                        min-width: 200px;
                        border-radius: 4px;
                        display: inline-block;
                        background: #eaf7fb;
                        font-size: 14px;
                        padding-left: 5px;
                        &:empty::before{
                            content: attr(placeholder);
                            color: #6d97a1;
                            pointer-events: none;
                        }
                        &:empty:focus{
                            &::before {
                                content: unset;
                            }
                        }
                    }
                }
            }
        }
    }
}