import * as components from './components'
import * as HttpExtensions from './extensions/http'
import Config from './config'
import Settings from './settings'
import Develop from './develop'
import UserDevelop from './user_develop'
import Context from './context'
import './templates'
import './_global'
import { Http, Language } from '@iac/core'
import Ref from './ref'
import Static from './static'
import Tick from './tick';
import ecp from './ecp';
import ecpManger from './ECPManager';
//import frameStorage from './frame-storage'

import { default as Data } from './data'

import Core from '@iac/core'
import './extensions/data'

class Kernel {
    static install = (Vue, args) => {
        // Регистрация компонентов
        for (const key in components) {
            const component = components[key]
            Vue.component(component.name || `iac-${key}`, component)
        }

        Vue.mixin({
            data: function () {
                return {
                    access: Context.Access,
                    settings: Settings,
                    config: Config,
                    develop: <PERSON>elo<PERSON>,
                }
            },
            computed: {
                $develop() {
                    return this.develop;
                },
                $settings() {
                    return this.settings;
                },
                //$feature() {
                //    return this.config.feature;
                //},
                $policy() {
                    return this.access.policy;
                },
                $user_develop() {
                    return Context.Access.develop;
                }
            }
        })


        // Ожидаем запуска сервиса
        this.onRun().then(async () => {
            console.log("Ядро запущено")
            //await Context.Access.updateData();
        });
    }

    static onRun() {
        if (!this._onRunPromise) {
            this._onRunPromise = new Promise((onRunResolve) => {
                this._onRunResolve = onRunResolve;
            })
        }


        return this._onRunPromise
    }

    static async Run(config) {
        Config.set(config);
        
        Http.Complete();
        
        // Прелоадер
        new Vue({
            data: function () {
                return {
                    blackout: Kernel.Blackout
                }
            },
            template: `
                <div>
                    <img v-if='$settings._logotype' style="max-width: 320px;margin: 50px auto; display: block;" :src="$settings._logotype" alt="logo" class="logo"/>            
                    <div v-if='blackout.active' class='blackout'>
                        <div class='content'><span v-if='blackout.code'>{{blackout.code}}:</span>{{$t(blackout.message || "blackout")}}</div>
                    </div>
                </div>
            `
        }).$mount("#preloader-info")       

        let response = await Http.api.rpc("get_settings", {});
        if(response.error?.status == 401){
            response = await Http.api.rpc("get_settings", {});
        }

        if(!response.data){
            Kernel.Blackout.active = true;
            Kernel.Blackout.code = response.error?.status;
            Kernel.Blackout.message = `Ошибка загрузки данных. Попробуйте обновить страницу.`;
            return;
        }
        
        let settings = response.data || {};

        Settings.set(settings)
        //  await frameStorage.init();

        let storeLanguage = localStorage.getItem("language");
        if(storeLanguage && Settings._languages && !Settings._languages.includes(storeLanguage)){
            storeLanguage = undefined; 
        }
        
        await Language.set(storeLanguage || (Settings._languages && Settings._languages[0]) ||  'ru-RU');




        Http.api.socket.join("system", (channel) => {
            channel.on("update_settings", (payload) => {
                Settings.set(payload.data)
                console.log("UPDATE Settings",Settings);
            })
        })

        this._onRunResolve();
        return true;
    }
}
Kernel.Config = Config;
Kernel.Settings = Settings;
Kernel.Ref = Ref;
Kernel.Static = Static;
Kernel.Tick = Tick;
Kernel.Context = Context;
Kernel.Develop = Develop;
Kernel.UserDevelop = UserDevelop;
Kernel.HttpExtensions = HttpExtensions;
Kernel.Data = Data;
Kernel.ecp = ecp;
Kernel.ecpManger = ecpManger

Kernel.Blackout = {
    code: undefined,
    active: false,
    message: "blackout"
};

Core.Context = Kernel.Context;

export default Kernel;

if (typeof window !== 'undefined' && window.Vue) {
    window.Vue.use(Kernel)
}