import { Context, Config } from '@iac/kernel'
import { Entity, DataSource, ArrayStore } from '@iac/data'
import { Http, Language } from '@iac/core'

let black_list_array = [
    { id: 101, type: 1, name: "national_terrorists" },
    { id: 102, type: 1, name: "international_terrorists" },

    { id: 201, type: 2, name: "black.nation_block_comp" },
    { id: 202, type: 2, name: "black.internation_block_comp" },

    { id: 302, type: 3, name: "black.group" },
]

let group_black_face_array = black_list_array.filter((item) => {
    return (item.type & 1) != 0
})

let group_black_company_array = black_list_array.filter((item) => {
    return (item.type & 2) != 0
})


let documents = undefined;

let black_list_document = {
    data: function () {
        return {
            error: undefined,
            model: {
                ...black_list_array.reduce((acc, current) => {
                    acc[current.id] = {
                        id: current.id,
                        name: current.name,
                        items: [],
                        actions: [
                            {
                                label: "add",
                                hidden: () => {
                                    if (Context.Access.policy['system_page_edit'])
                                        return false;
                                    return true;
                                },
                                handler: async () => {
                                    let result = await Vue.Dialog.MessageBox.Form({
                                        fields: [
                                            {
                                                type: "string",
                                                value: undefined,
                                                name: "title",
                                                required: true,
                                                label: "title",
                                                status: undefined
                                            },
                                            {
                                                type: "text",
                                                value: undefined,
                                                name: "anno",
                                                label: "anno",
                                                status: undefined
                                            },
                                            {
                                                label: "group",
                                                name: "group_id",
                                                type: "entity",
                                                dataSource: DataSource.get(black_list_array),
                                                value: current.id,
                                                status: undefined

                                            },
                                            {
                                                type: "file",
                                                value: undefined,
                                                name: "file",
                                                required: true,
                                                label: "-file",
                                                status: undefined
                                            }
                                        ],
                                        onClose: async (params) => {
                                            params.meta = {
                                                title: params.title,
                                                anno: params.anno,
                                            }

                                            let file = params.file.file
                                            let formData = new FormData();
                                            formData.append('data', file, file.name);

                                            let { data, error } = await Http.upload.form('tender/attach', formData);
                                            if (error) {
                                                return { error };
                                            }

                                            return {
                                                data: {
                                                    meta: params.meta,
                                                    group_id: params.group_id,
                                                    file: data
                                                }
                                            }
                                        }
                                    })

                                    if (!result)
                                        return;
                                    result.meta = result.meta || {};
                                    let { error, data } = await Http.api.rpc("ref", {
                                        ref: "ref_black_list_documents",
                                        data: result,
                                        op: "create"
                                    })

                                    if (error) {
                                        return Vue.Dialog.MessageBox.Error(error);
                                    }
                                    documents.unshift(data);
                                    this.model[data.group_id].items.unshift(data)
                                }
                            }
                        ]
                    }
                    return acc
                }, {}), 999999: { id: 999999, name: Language.t("without_group"), items: [] }
            }
        }
    },
    mounted() {
        this.$wait(async () => {
            if (!documents) {
                let { error, data } = await Http.api.rpc("ref", {
                    ref: "ref_black_list_documents",
                    op: "read"
                })
                this.error = error;
                if (data) {
                    documents = data

                }
            }
            if (documents)
                documents.forEach(document => {
                    let group_id = document.group_id
                    if (!this.model[group_id]) {
                        group_id = 999999;
                    }
                    this.model[group_id].items.push(document)
                });
        })
    },
    methods: {
        file_url(id) {
            return `${Config.api_server}/file/${id}`
        }
    },
    template: `
        <div>
            <ui-layout-group :actions_buttons='true' v-if='$policy.system_page_edit || group.items.length>0' :actions='group.actions' class='ui-data-grid' :label='group.name' v-for='group in model'>
            <div class='table-wrapper'>
                <table>
                    <thead v-if='0'>
                        <tr>
                            <th>дата добавления</th>
                            <th style='width: 100%; text-align: left;'>Информация о файле</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for='item in group.items'>
                            <td style='white-space: nowrap;'><iac-date :date='item.inserted_at' full icon></iac-date></td>
                            <td style='width: 100%;'>
                                <div  v-if='item.meta && item.meta.title'>{{item.meta.title}}</div>
                                <div v-if='item.meta && item.meta.anno'  style='font-size: 12px; opacity: 0.8;'>{{item.meta.anno}}</div>
                            </td>
                            <td style='text-align: right;'>
                                <a :href='file_url(item.file.uuid)'>{{$t("download")}}</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            </ui-layout-group>
        </div>
    `
}





class BlackItem extends Entity {
    constructor(context) {
        super(context);
        this.context = context;
        this.type = context.type;
        this.group_id = context.group_id
        this.meta = context.meta ? { ...context.meta } : {};
        this.date_inclusion = context.date_inclusion
        this.date_exclusion = context.date_exclusion
    }

    props() {
        return {
            type: {
                type: 'hidden'
            },
            group_id: {
                label: "-group",
                type: "entity",
                required: true,
                dataSource: DataSource.get(this.context.group_array)
            },
            meta: {},
            date_inclusion: {
                label: "-date_inclusion",
                type: "date",
                has_del: true,
                required: true,
            },
            date_exclusion: {
                label: "-date_exclusion",
                type: "date",
                has_del: true,
            }
        }
    }
}


class BlackItemFace extends BlackItem {
    constructor(context) {
        context.group_array = BlackItemFace.group_array
        context.type = BlackItemFace.type
        super(context)
    }
    static type = 'face';
    static group_array = group_black_face_array
    static metaTemplate = {
        props: ['item'],
        data: function () {
            return {
                source_group: black_list_array.map(item => ({ ...item, name: Language.t(item.name) }))
            }
        },
        computed: {
            title() {
                let title = [
                    this.item.meta?.fio?.last_name,
                    this.item.meta?.fio?.first_name,
                    this.item.meta?.fio?.patronymic
                ];
                if (this.item.meta?.addon_name) {
                    title = title.concat(this.item.meta?.addon_name)
                }
                return title.filter((item) => {
                    return item
                }).join(" ")
            },
            notes() {
                return this.item.meta?.notes
            }
        },
        template: `
            <div>
                <div>{{title}}</div>
                <ui-ref :source='source_group' :value='item && item.group_id'/>
                <div style='font-size: 12px; opacity: 0.8;'>{{notes}}</div>
            </div>
        `
    }
    props() {
        return {
            meta: {
                type: "model",
                label: "!",
                fields: {
                    fio: {
                        label: "-fio",
                        type: "model",
                        fields: {
                            last_name: {
                                group: "<full_name>",
                                label: "!surname"
                            },
                            first_name: {
                                group: "<full_name>",
                                label: "!name"
                            },
                            patronymic: {
                                group: "<full_name>",
                                label: "!patronymic"
                            },
                        }
                    },
                    addon_name: {
                        label: "-black.addon_name",
                        "multiple": true,
                    },
                    birthday: {
                        label: "-birthday",
                        type: "model",
                        fields: {
                            day: {
                                label: "!day",
                                group: "<birthday>",
                                type: "number",
                                max: 31,
                                min: 1,
                            },
                            month: {
                                label: "!month",
                                group: "<birthday>",
                                type: "entity",
                                has_del: true,
                                dataSource: "ref_months",
                            },
                            year: {
                                label: "!year",
                                group: "<birthday>",
                                type: "number",
                                min: 1,
                                max: new Date().getFullYear()
                            }
                        }
                    },
                    pinfl: {
                        label: "-pinfl",
                        type: "number",
                    },
                    notes: {
                        label: "-notes",
                        type: "text"
                    }
                }
            }

        }
    }
}
class BlackItemCompany extends BlackItem {
    constructor(context) {
        context.group_array = BlackItemCompany.group_array
        context.type = BlackItemCompany.type
        super(context)
    }
    static type = 'company';
    static group_array = group_black_company_array
    static metaTemplate = {
        props: ['item'],
        data: function () {
            return {
                source_group: black_list_array.map(item => ({ ...item, name: Language.t(item.name) }))
            }
        },
        computed: {
            title() {
                let title = [
                    this.item.meta?.company_name
                ];
                if (this.item.meta?.addon_name) {
                    title = title.concat(this.item.meta?.addon_name)
                }
                return title.filter((item) => {
                    return item
                }).join(", ")
            },
            notes() {
                return this.item.meta?.notes
            }
        },
        template: `
            <div>

                <router-link  v-if='item.criminal_id' :to='"/company/"+item.criminal_id'>{{title}}</router-link>
                <div v-else>{{title}}</div>

                <ui-ref :source='source_group' :value='item && item.group_id'/>
                <div style='font-size: 12px; opacity: 0.8;'>{{notes}}</div>
            </div>
        `
    }
    props() {
        return {
            meta: {
                type: "model",
                label: "!",
                fields: {
                    company_name: {
                        label: "-ecp.company",
                    },
                    addon_name: {
                        label: "-additional_id_name",
                        "multiple": true,
                    },
                    reg_date: {
                        label: "-reg_date",
                        type: "date",
                        has_del: true,
                    },
                    inn: {
                        label: "-inn",
                    },
                    notes: {
                        label: "-notes",
                        type: "text"
                    }
                }
            }
        }
    }
}

class Model extends Entity {
    constructor(context) {
        super(context)

        this.ref = context.ref;
        this.editorClass = context.editorClass;
        this.columns = [
            { field: "id", component: { props: ["item"], template: `<div>{{item.id}}</div>` } },
            { field: "criminal_id", style: 'font-size: 20px; color: #f00', label: " ", component: { props: ["item"], template: `<icon title='${Language.t("is_coincidence")}' v-if='item.criminal_id'>check2</icon><span v-else></span>` } },
            { field: "date_inclusion", component: { props: ["item"], template: "<iac-date :date='item.date_inclusion' withoutTime />" } },
            { field: "meta", label: "black.data", style: "width: 100%;", component: this.editorClass.metaTemplate },
            { field: "date_exclusion", component: { props: ["item"], template: "<iac-date :date='item.date_exclusion' withoutTime />" } },
        ];
        this.save = context.save;
        this.edit_black_items = {}
    }
    get edit() {
        return Context.Access.policy['system_page_edit'];
    }
    props() {

        return {

            new_black_items: {
                hidden: !this.edit,
                type: "data-grid",
                label: "!",
                dataSource: {
                    store: {
                        data: [],
                        context: (context) => {
                            context._status = context._status || 0;
                            Object.defineProperty(context, "bindClass", {
                                configurable: true,
                                enumerable: true,
                                get: () => {

                                    if ((context._status & 4) != 0)
                                        return "ui-alert ui-alert-danger";

                                    if ((context._status & 1) != 0)
                                        return "ui-alert ui-alert-success";

                                    if ((context._status & 2) != 0)
                                        return "ui-alert ui-alert-warning";

                                },
                            });

                            context.actions = [
                                {
                                    icon: "edit",
                                    btn_type: "warning",
                                    hidden: () => {
                                        return ((context._status & 4) != 0)
                                    },
                                    handler: async () => {
                                        let item = await this.edit_item(context);
                                        if (!item || item == 2)
                                            return;
                                        context.type = item.type
                                        context.group_id = item.group_id
                                        context.meta = item.meta
                                        context.date_inclusion = item.date_inclusion;
                                        context.date_exclusion = item.date_exclusion
                                        context._status |= 2;
                                    }
                                },
                                {
                                    icon: "trash",
                                    btn_type: "danger",
                                    hidden: () => {
                                        return ((context._status & 4) != 0)
                                    },
                                    handler: () => {
                                        context._status |= 4;
                                    }
                                },
                                {
                                    label: "Восстановить",
                                    btn_type: "danger",
                                    hidden: () => {
                                        return !((context._status & 4) != 0)
                                    },
                                    handler: () => {
                                        context._status &= ~4;
                                    }
                                }
                            ]

                            return context;
                        }
                    },

                },
                attr: {
                    buttons: true,
                    columns: this.columns
                },
            },
            actions: {
                type: "action",
                label: "!",
                buttons: true,
                hidden: !this.edit,
                actions: [
                    {
                        label: "add",
                        btn_type: "success",
                        handler: async () => {
                            let item = await this.edit_item();
                            if (!item || item == 2)
                                return;

                            //this.properties.new_black_items.dataSource.unshift_item({
                            //    _status: 1,
                            //    meta: item.meta
                            //source: item.source,
                            //summa: item.summa,
                            //currency: item.currency,
                            //})

                            this.properties.new_black_items.dataSource.store._array.unshift({
                                _status: 1,
                                type: item.type,
                                group_id: item.group_id,
                                meta: item.meta,
                                date_inclusion: item.date_inclusion,
                                date_exclusion: item.date_exclusion
                            })
                            this.properties.new_black_items.dataSource.reload();

                        }
                    },
                    {
                        label: "save",
                        handler: async () => {
                            let params = {
                                create: this.properties.new_black_items.dataSource.store._array.filter((item) => {
                                    if ((item._status & 4) != 0)
                                        return false
                                    return true
                                }).map((item) => {
                                    return {
                                        type: item.type,
                                        group_id: item.group_id,
                                        meta: item.meta,
                                        date_inclusion: item.date_inclusion,
                                        date_exclusion: item.date_exclusion
                                    }
                                }),
                                update: Object.keys(this.edit_black_items).map((key) => {
                                    return this.edit_black_items[key]
                                }).map((item) => {
                                    return {
                                        id: item.id,
                                        type: item.type,
                                        group_id: item.group_id,
                                        meta: item.meta,
                                        date_inclusion: item.date_inclusion,
                                        date_exclusion: item.date_exclusion
                                    }
                                })
                            }

                            /*if (params.create && params.create.length <= 0)
                                params.create = undefined
                            if (params.update && params.update.length <= 0)
                                params.update = undefined
*/
                            let { error, data } = await this.save(params);
                            if (error) {
                                Vue.Dialog.MessageBox.Error(error);
                            } else if (!data) {
                                Vue.Dialog.MessageBox.Error("Нет ответа от сервера");
                            } else {
                                this.edit_black_items = {}
                                this.properties.new_black_items.dataSource.store._array = []
                                this.properties.new_black_items.dataSource.reload();
                                this.properties.black_list.dataSource.reload();
                            }
                        }
                    },
                    {
                        label: "check_all",
                        btn_type: "warning",
                        handler: async () => {

                            let { error, data } = await Http.api.rpc("ref", {
                                ref: "ref_black_list",
                                op: "check_all",
                                filters: {
                                    type: this.editorClass.type
                                }
                            })

                            if (error) {
                                Vue.Dialog.MessageBox.Error(error);
                            } else if (data) {
                                if (data.message)
                                    Vue.Dialog.MessageBox.Error(data.message);
                                this.properties.black_list.dataSource.reload();
                            }
                        }
                    }
                ],
                attr: {
                    class: "sticky",
                    style: "text-align: right; bottom: 0;"
                }
            },
            black_list: {
                type: "data-grid",
                label: "!",
                dataSource: {
                    query: {
                        // queryText: {
                        //     label: "!search",
                        //     group: "-!filter"
                        // },
                        group_id: {
                            type: "entity",
                            label: "!group",
                            group: "group",
                            has_del: true,
                            dataSource: DataSource.get(this.editorClass.group_array)
                        },
                        inclusion_at_gte: {
                            type: "date",
                            label: "!inclusion_at_gte",
                            group: "date_inclusion",
                            has_del: true,
                            prefix: Language.t("from"),
                            bind: {
                                status: `inclusion_at_error && {"type":"error"}`
                            }

                        },
                        inclusion_at_lte: {
                            type: "date",
                            label: "!inclusion_at_lte",
                            group: "date_inclusion",
                            has_del: true,
                            prefix: Language.t("to"),
                            bind: {
                                status: `inclusion_at_error && {"type":"error"}`
                            }
                        },
                        inclusion_at_error: {
                            sync: false,
                            group: "date_inclusion",
                            type: "model",
                            label: "!",
                            bind: {
                                value: "inclusion_at_gte > inclusion_at_lte",
                                status: `inclusion_at_error && {"type":"error","message":"${Language.t('to_from_error')}"}`
                            },
                        },
                        exclusion_at_gte: {
                            type: "date",
                            label: "!group",
                            group: "date_exclusion",
                            has_del: true,
                            prefix: Language.t("from"),
                            bind: {
                                status: `exclusion_at_error && {"type":"error"}`
                            }

                        },
                        exclusion_at_lte: {
                            type: "date",
                            label: "!group",
                            group: "date_exclusion",
                            has_del: true,
                            prefix: Language.t("to"),
                            bind: {
                                status: `exclusion_at_error && {"type":"error"}`
                            }

                        },

                        exclusion_at_error: {
                            sync: false,
                            group: "date_exclusion",
                            type: "model",
                            label: "!",
                            bind: {
                                value: "exclusion_at_gte > exclusion_at_lte",
                                status: `exclusion_at_error && {"type":"error","message":"${Language.t('to_from_error')}"}`
                            },
                        },
                        is_criminal: {
                            type: "bool",
                            label: "is_coincidence"
                        },
                        is_active: {
                            type: "entity",
                            label: "active",
                            group: "group",
                            has_del: true,
                            dataSource: DataSource.get([{ id: true, name: "active" }, { id: false, name: "inactive" }])
                        },
                    },
                    store: {
                        ref: this.ref,
                        context: (context) => {
                            context._status = context._status || 0;
                            if (this.edit_black_items[context.id]) {
                                context = this.edit_black_items[context.id];
                            }

                            Object.defineProperty(context, "bindClass", {
                                configurable: true,
                                enumerable: true,
                                get: () => {

                                    if ((context._status & 4) != 0)
                                        return "ui-alert ui-alert-danger";

                                    if ((context._status & 1) != 0)
                                        return "ui-alert ui-alert-success";

                                    if ((context._status & 2) != 0)
                                        return "ui-alert ui-alert-warning";

                                },
                            });

                            if (this.edit)
                                context.actions = [
                                    {
                                        icon: "edit",
                                        btn_type: "warning",
                                        hidden: () => {
                                            return ((context._status & 4) != 0)
                                        },
                                        handler: async () => {
                                            let item = await this.edit_item(context);
                                            if (!item || item == 2)
                                                return;

                                            context.type = item.type
                                            context.group_id = item.group_id
                                            context.meta = item.meta
                                            context.date_inclusion = item.date_inclusion
                                            context.date_exclusion = item.date_exclusion
                                            context._status |= 2;

                                            this.edit_black_items[context.id] = context

                                        }
                                    },
                                    {
                                        icon: "trash",
                                        btn_type: "danger",
                                        hidden: () => {
                                            return true
                                            return ((context._status & 4) != 0)
                                        },
                                        handler: () => {
                                            context._status |= 4;
                                            this.edit_black_items[context.id] = context
                                        }
                                    },
                                    {
                                        label: "Восстановить",
                                        btn_type: "danger",
                                        hidden: () => {
                                            return true
                                            return !((context._status & 4) != 0)
                                        },
                                        handler: () => {
                                            context._status &= ~4;
                                            if (context._status == 0) {
                                                this.edit_black_items[context.id] = undefined;
                                                delete this.edit_black_items[context.id];
                                            }
                                        }
                                    }
                                ]
                            return context
                        },
                        injectQuery: (params) => {
                            params.filters.inclusion_at_error = undefined;
                            params.filters.exclusion_at_error = undefined;
                            params.filters.type = this.editorClass.type;
                            params.filters.is_criminal = params.filters.is_criminal ? true : undefined;
                            return params;
                        }
                    },
                    /*actions: [
                        {
                            label: "reload", handler: () => {
                                this.properties.black_list.dataSource.reload();
                            }
                        }
                    ]*/
                },
                attr: {
                    buttons: true,
                    columns: this.columns,
                    search: "queryText",
                    //_class: "top_filter"
                },
            }
        }
    }

    async edit_item(item = {}) {
        return Vue.Dialog.MessageBox.Form({
            size: "lg",
            fields: (new this.editorClass({ ...item })).fields,
            onClose: async (params) => {
                if (params.date_exclusion <= params.date_inclusion) {
                    let format_date_time = new Intl.DateTimeFormat("ru-RU", {
                        dateStyle: 'short',
                    })
                    let inclusion = format_date_time.format(new Date(params.date_inclusion));
                    return {
                        error: {
                            data: [
                                { name: "date_exclusion", message: `${Language.t("requirement.valid_2")} ${inclusion}` }
                            ]
                        }
                    }
                }
                return {
                    data: params
                }
            }
        })
    }
}


export default {
    data: function () {
        return {
            face: new Model({
                ref: "ref_black_list",
                editorClass: BlackItemFace,
                save: async (data) => {
                    return await Http.api.rpc("ref", {
                        ref: "ref_black_list",
                        op: "commit",
                        data: data
                    })
                }
            }),
            company: new Model({
                ref: "ref_black_list",
                editorClass: BlackItemCompany,
                save: async (data) => {
                    return await Http.api.rpc("ref", {
                        ref: "ref_black_list",
                        op: "commit",
                        data: data
                    })
                }
            }),
        }
    },
    async beforeRouteLeave(to, from, next) {
        if (await Vue.Dialog.MessageBox.Question(Language.t("black.warning_question"), "black.warning") == Vue.Dialog.MessageBox.Result.Yes)
            next()
        else next(false)
    },
    components: {
        black_list_document
    },
    template: `
        <iac-access :access='$policy.blacklist_read'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.black_list')}}</li>
            </ol>
            <div class='title'>
                <h1>{{$t('nav.black_list')}}</h1>
            </div>
        </iac-section>

        <iac-section>
            <ui-layout-tab>
                <ui-layout-group label='black.individuals'>
                    <ui-layout :fields='face.fields' />
                </ui-layout-group>
                <ui-layout-group label='black.entities'>
                    <ui-layout :fields='company.fields' />
                </ui-layout-group>

                <ui-layout-group label='black.reason_files'>
                    <black_list_document />
                </ui-layout-group>

            </ui-layout-tab>
        </iac-section>
        </iac-access>
    `
}

