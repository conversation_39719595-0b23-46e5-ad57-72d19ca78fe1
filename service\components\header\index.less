.iac-service-header{
    opacity: 0;
    position: fixed;
    top: 0;
    transform: translateY(-100%);
    transition: transform .3s cubic-bezier(.4,0,.6,1),opacity 0s .3s;
    width: 100%;
    background-color:   #233040;
    //box-shadow: 0 2px 4px    rgba(0,0,0,.5);
    display: flex;
    border-bottom: 1px solid #F3F3F3;

    justify-content: space-between;
    z-index: 301;    
}

body{
    &.header{
       padding-top: 112px; 
    }
    
    &.header_show{
        .iac-service-header{
            opacity: 1;
            transform: translateY(0);
            transition: transform .3s cubic-bezier(.4,0,.2,1) .3s,opacity 0s .3s;
        }

        .sticky {
            top: 113px;
            transition: top .3s cubic-bezier(.4,0,.2,1) .3s,opacity 0s .3s;
        }
    }
}

.sticky {
    z-index: 1;
    position: sticky;
    top: 0;
    transition: top .3s cubic-bezier(.4,0,.6,1),opacity 0s .3s;
}