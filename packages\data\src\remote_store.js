
import Store from './store'
import Http from '../../core/src/http';

export default class RemoteStore extends Store {
    constructor(options) {
        super(options);
        this.host = options.host || Http.default;
        this.method = options.method
    }

    async by<PERSON>eys(keys) {
        if (keys == undefined)
            return;

        if (this.method) {
            let key = Array.isArray(this.key) ? this.key[0] : this.key
            let { error, data } = await this.host.rpc(this.method, {
                [key]: keys
            });
            return data
        }
        return keys;
    }

    async by<PERSON>ey(key) {
        if (key == undefined)
            return;

        let data = await this.byKeys(key);
        
        return Array.isArray(data) ? this.context(data[0]) : this.context(data)

        if (this.method) {
            let { error, data } = await this.host.rpc(this.method, {
                [this.key]: key
            });
            if (!error) {

                data = Array.isArray(data) ? data[0] : data
            }
            return data
        }
        return key;
    }

    async queryByOptions(options) {
        options = options || {};
        let { take = 10, skip = 0 } = options || {};

        let {error, data} = await this.host.rpc(this.method, {...await this.injectQuery({
            limit: take,
            offset: skip,
            ...options.query,
            
        }),queryText: undefined}, {
            signal: options.signal
        });
        
        if (!error && data) {
            data = data.map((context) => {
                return this.context(context);
            })
        }

        return {error, data}
    }
}
