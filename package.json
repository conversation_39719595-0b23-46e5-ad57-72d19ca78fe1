{"name": "iac", "version": "1.13.0", "description": "", "main": "index.js", "scripts": {"lng-gen": "node ./.build/lng_gen.js", "build": "cross-env NODE_ENV=production npm-run-all glyph css js lng-complete ", "css": "npm-run-all css-default css-valkyrie", "css-default": "npm-run-all theme-default css-prefix-default", "theme-default": "lessc themes/default/theme.less _build/css/theme.default.css", "css-prefix-default": "postcss --config .build/postcss.config.js -r \"_build/css/theme.default.css\"", "css-valkyrie": "npm-run-all theme-valkyrie css-prefix-valkyrie", "theme-valkyrie": "lessc themes/valkyrie/theme.less _build/css/theme.valkyrie.css", "css-prefix-valkyrie": "postcss --config .build/postcss.config.js -r \"_build/css/theme.valkyrie.css\"", "js": "rollup -c --config .build/rollup.config.js", "lng-complete": "node ./.build/lng_complete.js", "glyph": "node ./.build/glyph.js", "dev": "cross-env NODE_ENV=dev npm run develop", "bun-dev": "cross-env NODE_ENV=dev bun run develop", "ddev": "cross-env NODE_ENV=ddev npm run develop", "develop": "npm-run-all js-develop", "js-develop": "rollup -c --watch --config .build/rollup.config.js", "static": "node .build/static.js", "validate": "node .build/validate.js"}, "author": "", "license": "ISC", "devDependencies": {"@babel/core": "7.9.0", "@babel/plugin-proposal-class-properties": "7.8.3", "@babel/plugin-proposal-decorators": "7.8.3", "@babel/preset-env": "7.9.5", "@open-wc/rollup-plugin-html": "1.0.3", "@rollup/plugin-image": "3.0.1", "autoprefixer": "9.7.6", "cross-env": "^7.0.3", "less": "3.11.1", "nodemon": "1.19.4", "npm-run-all": "4.1.5", "po2json": "1.0.0-beta-2", "postcss": "7.0.27", "postcss-cli": "7.1.0", "rollup": "1.32.1", "rollup-plugin-babel": "4.4.0", "rollup-plugin-commonjs": "10.1.0", "rollup-plugin-copy": "3.3.0", "rollup-plugin-livereload": "^2.0.5", "rollup-plugin-modify": "3.0.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-postcss": "2.5.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-serve": "^2.0.2", "rollup-plugin-terser": "5.3.0", "rollup-plugin-vue": "5.1.6", "svg2ttf": "4.3.0", "svgicons2svgfont": "9.1.1", "ttf2woff": "2.0.1", "vue-template-compiler": "2.6.11"}, "dependencies": {"highlight.js": "^11.7.0", "marked-highlight": "^1.0.0", "phoenix_js": "0.0.3", "regenerator-runtime": "0.13.5"}}