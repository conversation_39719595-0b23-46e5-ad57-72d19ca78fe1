/* Firefox */
  .thin-scroll {
    scrollbar-width: thin;
    scrollbar-color: #888888 #ececec;
    cursor: auto;
    &.primary{
      scrollbar-color: #009ab8 #fff;
    }
  }
  
  /* Chrome, Edge and Safari */
  .thin-scroll::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  .thin-scroll::-webkit-scrollbar-track {
    border-radius: 3px;
    background-color: #ececec;
  }
  
  .thin-scroll::-webkit-scrollbar-track:hover {
    background-color: #c5c5c5;
  }
  
  .thin-scroll::-webkit-scrollbar-track:active {
    background-color: #c4c4c4;
  }
  
  .thin-scroll::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #888888;
  }
  
  .thin-scroll::-webkit-scrollbar-thumb:hover {
    background-color: #8a8a8a;
  }
  
  .thin-scroll::-webkit-scrollbar-thumb:active {
    background-color: #8d8d8d;
  }
  