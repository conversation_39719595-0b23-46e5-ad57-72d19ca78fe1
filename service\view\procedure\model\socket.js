import Phoenix from "phoenix_js"
import { Config, Context } from '@iac/kernel'
import { Language as LngProvider, Http } from '@iac/core'

var channelParam = {
    //token: Context.User.access_token,

}

export default class TenderSocket {
    static async socket() {
        let host =  Config.pp_server.replace('http://','ws://')    
            host = host.replace('https://','wss://')  

        if (!this._socket) {
            this._socket = new Phoenix.Socket(`${host}/socket`, {
                transport: Phoenix.WebSocket, params: {

                }
            })
            this._socket.connect();
        }
        return this._socket;
    }

    static async channel(id) {
        let socket = Http.HttpSocket.connect(Config.pp_server);// await TenderSocket.socket();

        channelParam.token = Context.User.access_token;
        channelParam.lng = LngProvider._local_socket



        let channel = socket.channel(`tp:procs:${id}`, channelParam)
        channel.onError((event) => { console.log("there was an error!", event) })
        channel.onClose((event) => console.log("the channel has gone away gracefully", event))

        return channel;
    }

    static async join(id, callback) {
        let channel = await TenderSocket.channel(id);
        channel.on("event", (event) => {
            callback({
                type: "event",
                name: event.event_name,
                data: event.data,
                object_type: event.object_type,
                object: event.object
            })
        })

        channel.join()
            .receive("ok", (messages) => {
                
            })
            .receive("error", async ({response={}, reason }) => {
                if (response.code === "access_denied" || reason == "unauthorized") {
                    await Context.User.refreshToken();
                    channelParam.token = Context.User.access_token
                    channelParam.lng = LngProvider._local_socket

                }
            })
            .receive("timeout", () => console.log("Networking issue. Still waiting..."))
        return channel;
    }

    static async leave_channel(channel) {

        return new Promise((resolve,reject)=>{
            channel.leave()
                .receive("ok", ({ messages }) => {console.log("catching leave", messages); resolve(true)})
                .receive("error", ({ reason }) => {console.log("failed leave", reason); resolve(false)})
                .receive("timeout", () => {console.log("Networking issue. Still waiting..."); resolve(false)})
        })


    }

    static async leave(id) {
        let channel = await TenderSocket.channel(id);
        return await TenderSocket.leave_channel(channel)
    }
}