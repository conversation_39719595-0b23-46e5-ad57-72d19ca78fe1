import Vue from 'vue';

const Component = {
  props: ['item'],
  template: `
    <div class='iac-card p-20 mb-24 card-stat'>
      <div class='section-title mb-0 text-black'>{{ item.total_count }}</div>
      <p class='just-text just-text--sm m-0'>{{ item.name }}</p>
      <div class='card-stat__period-count'>{{ item.period_count === 0 ? item.period_count : '+' + item.period_count }}</div>
    </div>
  `,
};

Vue.component('widget-card-stat', Component);
