import { Entity } from '@iac/data'

export default class TileSetting extends Entity {
    constructor(context) {
        super(context)
        this.id = context.id

        this.updateContext(context);

        window.addEventListener('storage', async (e) => {
            if(e.key == `tileSetting_${this.id}`){
                this.updateContext();
            }
        })
    }

    updateContext(context){

        try {
            let setting = localStorage.getItem(`tileSetting_${this.id}`) || "{}";
            setting = JSON.parse(setting);
            context = {
                ...context,
                ...setting
            }

        } catch (e) {
            return;
        }


        this.lock = true;
        this.fields.forEach(field => {
            field.attr = field.attr || {};
            field.attr.react = true
            //if (context[field.name] != undefined) {
                switch (field.type) {
                    case "bool":
                        this[field.name] = Boolean(context[field.name]);
                        break;
                    case "range":
                    case "float":
                    case "number":
                        this[field.name] = Number(context[field.name]);
                        break;
                    default:
                        this[field.name] = context[field.name]
                }
            //}
        });
        this.lock = false;
    }

    onChangeProperty(data) {
        if (!this.lock)
            localStorage.setItem(`tileSetting_${this.id}`, JSON.stringify(this.params))
    }

    get params() {
        return this.fields.filter((field) => {
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).map((field) => {
            let value = field.value;
            if (field.value && field.value.exp && field.value.exp.value != undefined) {
                value = field.value.exp.value
            }

            if (Array.isArray(value) && value.length <= 0)
                value = undefined;

            return {
                name: field.name,
                value: value
            }
        }).filter((field) => {
            return field.value != undefined
        }).reduce((prev, curr) => {
            prev[curr.name] = curr.value
            return prev;
        }, {})
    }

}

class GeneralSetting extends TileSetting {
    constructor(context = {}) {
        context.id = "general"
        super(context)

    }
    props() {
        return {
            show_maximize: {
                type: "bool",
                label: "Отображать иконку maximize",
            },
            show_settings: {
                type: "bool",
                label: "Отображать иконку настроек",
            },
            striped: {
                label: "Отображать таблицы в виде зебры",
                type: "bool"
            },
        }
    }
}

let SettingsComponent = {
    name: "setting",
    props: {
        active: {
            default: "general"
        }
    },
    data: function () {
        return {
            tile_settings: {
                general: Vue.Tiling["settings"].general_setting,
                order_book: Vue.Tiling["order_book"].setting,
                bkl_exchange_contract: Vue.Tiling["exchange_contract"].setting,
               // proposal: Vue.Tiling["proposal"].setting
            },
            active_tile: this.active
            /* [
                {label: "general", settings: Vue.Tiling["settings"].general_setting},
                {label: "order_book",settings: Vue.Tiling["order_book"].setting}
            ],*/
        }
    },

    watch: {
        active: {
            immediate: true,
            async handler(value, oldValue) {
                if (value == 'exchange_contract')
                    this.active_tile = 'bkl_exchange_contract'
                else
                this.active_tile = value;
                
            }
        }
    },
    methods: {
        onExpanded(show, index) {
            if (show) {
                this.active = index
                this.$emit("params", { active: this.active })
            }
        }
    },
    template: `
        <div :key='active' class='iac-tile-settings' style='height: 100%;'>
            <div class='content thin-scroll' style='height: 100%;overflow-y: scroll;overflow-x: hidden;'>   
                <ui-layout-group :label='"tile."+key' :expanded='key == active_tile' v-for='item,key in tile_settings' v-on:expanded='e=>onExpanded(e,key)'>
                    <ui-layout :fields='item.fields' />
                </ui-layout-group>
            </div>
        </div>
    `
}

Vue.Tiling("settings", { 
    component: SettingsComponent,
    params: {active: "general"}
})
Vue.Tiling["settings"].general_setting = new GeneralSetting()