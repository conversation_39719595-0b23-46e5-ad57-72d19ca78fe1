import Settings from './../settings'
import './component'
import eimzo from './../eimzo'
import { Http } from '@iac/core'
import Develop from '../develop'

import ecpManager from '../ECPManager'

class Provider {
    constructor(name) {
        this.name = name;
    }

    async loadKey(...args) {
        return await eimzo.loadKey(...args);
    }

    async createPkcs7(...args) {
        return await eimzo.createPkcs7(...args);
    }

    async subscribe(...args) {
        let response = await eimzo.subscribe(...args);
        if(!response)
            return;
        let { error, data } = response;
        if (data && Settings.ecp && Settings.ecp._timestamp) {
            response = await Http.api.rpc(Settings.ecp._timestamp, {
                pkcs7: data
            })
            if (!response.error)
                data = response.data || data;
        }
        return { error, data };
    }
}


export default class ecp {

    static get provider() {
        if (!ecp._provider && Settings.ecp && Settings.ecp._provider) {
            ecp._provider = new Provider(Settings.ecp._provider);
        }
        return ecp._provider;
    }

    static get loadKey() {
        return ecp.provider && ecp.provider.loadKey
    }

    static get createPkcs7() {
        return ecp.provider && ecp.provider.createPkcs7
    }

    static get subscribe() {
        if(Develop.new_ecp_develop)
            return ecpManager.subscribe;
        if (!ecp.provider) {
            return async () => {
                return {
                    data: "ignore_eimzo"
                }
            }
        }
        return ecp.provider && ecp.provider.subscribe
    }
}