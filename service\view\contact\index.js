import { Http, Language } from "@iac/core"
import { Settings } from "@iac/kernel"
import '../../components/contact'
import '@iac/kernel/components/qr'

export default [
    {
        path: '/contact',
        component: {
            data: function () {
                return {
                    pageData: undefined,
                    error: undefined,
                    qrCodeUrls: {
                        googlePlay: "https://play.google.com/store/apps/details?id=uz.tenderpro.hayot",
                        appStore: "https://apps.apple.com/us/app/hayot-birja-xt-xarid/id6503948155"
                    }
                }
            },
            mounted() {
                this.$wait(async () => {
                    await this.fetchPageData();
                })
            },
            watch: {
                'pageData.googlePlayQrText': function(newVal) {
                    if (newVal) {
                        this.qrCodeUrls.googlePlay = newVal;
                    }
                },
                'pageData.appStoreQrText': function(newVal) {
                    if (newVal) {
                        this.qrCodeUrls.appStore = newVal;
                    }
                }
            },
            methods: {
                async fetchPageData() {
                    let response = await Http.default.rpc("get_page", {
                        id: 'contact'
                    });
                    
                    if (response.error) {
                        this.error = response.error;
                    } else if (response.data && response.data.data) {
                        this.pageData = response.data.data;
                    }
                    
                    return response;
                },
                async edit() {
                    // Используем уже загруженные pageData или загружаем, его их нет
                    let pageContent = '';
                    let googlePlayQrText = '';
                    let appStoreQrText = '';
                    if (!this.pageData) {
                        await this.fetchPageData();
                    }
                    
                    if (this.pageData) {
                        pageContent = this.pageData.content;
                        googlePlayQrText = this.pageData.googlePlayQrText || '';
                        appStoreQrText = this.pageData.appStoreQrText || '';
                    }
                    
                    let dialogResult = await Vue.Dialog({
                        data: function () {
                            let $this = this;
                            return {
                                fields: [
                                    {
                                        type: "text",
                                        label: "content",
                                        value: pageContent,
                                        attr: {
                                            react: true,
                                            rows: 15
                                        }
                                    },
                                    {

                                        label: "qr_code_google_play",
                                        value: googlePlayQrText,
                                    },
                                    {

                                        label: "qr_code_app_store",
                                        value: appStoreQrText,
                                    },
                                    {
                                        type: "widget",
                                        label: 'preview',
                                        widget: {
                                            name: 'iac-layout-static',
                                            props: {
                                                get value() {
                                                    return $this.fields[$this.$route.query.tabid_language || 0].value
                                                }
                                            }
                                        }
                                    }
                                ]
                            }
                        },
                        methods: {
                            save() {
                                this.$wait(async () => {
                                    let response = await Http.default.rpc("set_page", {
                                        id: 'contact',
                                        data: {
                                            content: this.fields[0].value,
                                            googlePlayQrText: this.fields[1].value,
                                            appStoreQrText: this.fields[2].value
                                        }
                                    });
                                    
                                    let error = response.error;
                                    if (error) {
                                        Vue.Dialog.MessageBox.error(error);
                                    } else {
                                        // Возвращаем обновленный контент и тексты QR-кодов
                                        this.Close({
                                            content: this.fields[0].value,
                                            googlePlayQrText: this.fields[1].value,
                                            appStoreQrText: this.fields[2].value
                                        })
                                    }
                                    return;
                                });
                            }
                        },
                        
                        template: `
                            <div>
                                <main>
                                    <ui-layout :fields='fields' />
                                </main>
                                <footer>
                                    <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('cancel')}}</ui-btn>
                                    <ui-btn type='primary' v-on:click.native='save'>{{$t('save')}}</ui-btn>
                                </footer>
                            </div>
                        `
                    }).Modal({
                        size: "lg",
                    });

                    if (dialogResult){
                        // обновляем content и тексты QR-кодов в pageData
                        this.pageData = {
                            ...this.pageData,
                            content: dialogResult.content,
                            googlePlayQrText: dialogResult.googlePlayQrText,
                            appStoreQrText: dialogResult.appStoreQrText
                        };
                    }
                }
            },
            template: `
                <div>
                    <iac-section type='header'>
                        <ol class='breadcrumb'>
                            <li><router-link to='/'>{{$t('home')}}</router-link></li>
                            <li>{{$t('footer_contact.title')}}</li>
                        </ol>
                        <div class='title'><h1>{{$t('footer_contact.title')}}</h1>
                        <ui-btn type='primary' v-if='$policy.system_cm_info_edit || $policy.system_page_edit_contact' v-on:click.native='edit'>{{$t('edit')}}</ui-btn></div>
                    </iac-section>  
                    <iac-section style='background: #fff'>   
                        <iac-layout-static v-if='pageData && pageData.content' :value='pageData.content' />
                      
                        <ui-layout-group  v-if="pageData" label='social.title'>
                            <div>
                                <iac-social-icons class="contact-icon"/>
                            </div>
                        </ui-layout-group>

                        <ui-layout-group v-if="pageData" label='mobile_app.title'>
                            <div style="display: flex; flex-direction: row; gap: 32px; align-items: flex-start;">
                                <div style="display: flex; flex-direction: column; align-items: center; width: 160px;">
                                    <div style= "margin-bottom: 8px; color: rgb(115, 115, 115); font-size: 14px; height: 32px; display: flex; align-items: center; text-align: center;">{{$t('google_play_title')}}</div>
                                    <iac-qr :value="pageData.googlePlayQrText || qrCodeUrls.googlePlay" :size="160" level="L"></iac-qr>
                                </div>
                                <div style="display: flex; flex-direction: column; align-items: center; width: 160px;">
                                    <div style= "margin-bottom: 8px; color: rgb(115, 115, 115); font-size: 14px; height: 32px; display: flex; align-items: center; text-align: center; white-space: pre-line;">{{$t('app_store_title')}}</div>
                                    <iac-qr :value="pageData.appStoreQrText || qrCodeUrls.appStore" :size="160" level="L"></iac-qr>
                                </div>
                            </div>
                        </ui-layout-group>

                        <ui-error v-if='error' :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
                    </iac-section>               
                </div>
            `
        },
    },
]