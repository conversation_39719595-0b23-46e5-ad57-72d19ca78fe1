const Component = {
    props: ['model'],
    computed: {
        url() {
            return `/procedure/${this.model.id}/core`
        }
    },
    methods: {
        openProducts() {
            const { lotNum, model: { good_count, meta } } = this;
            
            Vue.Dialog.products.Modal({
                size: 'right',
                model: {
                    title: `${this.$t(this.model.type)} №: ${this.model.id}`,
                    count: good_count,
                    items: meta.good_maps,
                }
            })
        }
    },
    template: `
        <ui-data-view-item :model='model'>
            
            <template slot='header'>
                <div><iac-entity-edit  v-if='model.id' :value='{id: model.id, type: model.type}' /> &nbsp;&nbsp; <iac-date v-if='model.publicated_at' :date="model.publicated_at"  withoutTime withMonthName/></div>
                <div>
                    <ui-ref source='status_tender' :value='model && model.status'/>
                </div>
            </template>

            <template slot='title'>
                <router-link :to='url' class='title'>{{model.name}}</router-link>
            </template>
            <template slot='sub_title'>
                <a @click.prevent='openProducts' href='#' >{{model.good_count}} {{ $t('product',{count: model.good_count}) }}</a>
            </template> 

            <template slot='props'>
                <div>
                    <label>{{$t('tender.close_at')}}:</label>
                    <div><iac-date v-if='model.close_at' :date="model.close_at"  withoutTime withMonthName/></div>
                </div>
                <div>
                    <label>{{$t('tender.max_price')}}:</label> 
                    <div><iac-number :value='model.totalcost' delimiter=' ' part='2'/>&nbsp;{{model.currency}}</div>
                </div>
                <div>
                    <label>{{$t('tender.language')}}:</label>
                    <div><ui-ref source='ref_locale' :value='model && model.lang'/></div>
                </div>
                <div>
                    <label>{{$t('tender.offers')}}:</label> 
                    <div>{{model.part_count}}</div>
                </div>
            </template>

        
        </ui-data-view-item>
    `
}

Vue.component('template-procedure', Component);