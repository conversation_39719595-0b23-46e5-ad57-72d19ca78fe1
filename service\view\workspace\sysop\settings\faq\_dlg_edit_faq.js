export default Vue.Dialog({
    props: ["model"],
    methods: {
        async save() {
            await this.wait(async () => {
                let { error, data } = await this.model.save();
                if (!error)
                    this.Close(this.model)
            })
        }
    },
    template: `
      <div>
          <header>{{$t('hp.faq')}}</header>
          <main>
              <ui-layout :fields='model.fields'/>
          </main>
          <footer>
            <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('cancel')}}</ui-btn>
            <ui-btn type='primary' v-on:click.native='save'>{{$t('save')}}</ui-btn>
          </footer>
      </div>
  `
})