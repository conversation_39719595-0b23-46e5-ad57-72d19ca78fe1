export var Error = {
    name: "ui-error",
    props: ["code","message", "details", "number"],
    methods: {
        show_details(){
            let code = this?.details?.code;

            let stacktrace = this?.details?.stacktrace;
            if (stacktrace) {
                stacktrace = stacktrace.split('\n').map(x => x.trim()).join('\n');
            };

            let details = JSON.stringify(this.details, null, '\t')
            details = details.replace(/\\"/ig,"\"")
            details = details.replace(/\\n/ig,"\n")

            Vue.Dialog({
                props: ["message","details", "number", "stacktrace", "code"],
                template: `
                    <div>
                        <header>{{message}}</header>
                        <main>
                            <router-link v-if='number' :to='"/error/"+number' target= '_blank'>{{number}}</router-link>
                            <template v-if="stacktrace">
                                <p>Stacktrace: </p>
                                <pre style="
                                    overflow: auto;
                                    border: 1px solid #ccc;
                                    border-radius: 3px;
                                    word-wrap: break-word;
                                    white-space: pre-wrap;
                                ">{{stacktrace}}</pre>
                            </template>
                            <template v-if="code">
                                <p>Message: </p>
                                <pre style="
                                    overflow: auto;
                                    border: 1px solid #ccc;
                                    border-radius: 3px;
                                    word-wrap: break-word;
                                    white-space: pre-wrap;
                                ">{{code}}</pre>
                            </template>
                            <p>Other data: </p>
                            <details>
                            <pre style='overflow: auto;border: 1px solid #ccc; border-radius: 3px;'><code style="word-wrap: break-word; white-space: pre-wrap;">{{details}}</code></pre>
                            </details>
                        </main>
                        <footer>
                            <ui-btn type='secondary' @click.native='Close()'>{{$t('close')}}</ui-btn>
                        </footer>
                    </div>
                `
            }).Modal({
                message: this.message,
                details: details,
                number: this.number,
                size: "lg",
                stacktrace,
                code,
            })
        }
    },
    template: `<div class='ui-error'>
        <h1 v-if='code'>{{code}}</h1>
        <div>
            <span>{{message}}</span>
            <div v-if='details && $develop.content_debug'><a href='javascript:void(0)' v-on:click='show_details'>{{$t('detailing')}}</a></div>
        </div>
    </div>`
}
