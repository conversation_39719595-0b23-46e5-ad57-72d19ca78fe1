import {Config} from '@iac/kernel'

const Component = {
    props: ['model'],
    methods: {
        getFileUrl(id) {
          return `${Config.api_server}/file/${id}`
        }
      },
    template: `
        <ui-data-view-item :model='model'>
            <template slot='header'>
                <div>№{{model.id}}</div>
                <div>
                  <span :title='$t("from")'><iac-date :date='model.plan_date_from' withoutTime /></span>&nbsp;
                  <span :title='$t("to")'><iac-date :date='model.plan_date_to' withoutTime /></span>
                </div>
            </template>

            <template slot='title'>
                <div class='clamp_2'>{{model.title}}</div>
            </template>

            <template slot='description'>
                <div>
                    <label>{{ $t('company') }}:</label>
                    <span>{{model.company_name}}</span>
                </div>
                <div>
                    <label>{{ $t('shipment_creator') }}:</label>
                    <span>{{model.face_fullname}}</span>
                </div>
            </template>

            <template slot='description'>
                <div>
                    <label>{{$t('file')}}:</label>
                    <span>
                        <div v-for="(file,index) in model.files" style='padding: 0 0 5px'>
                            <a v-if='file.id' :href='getFileUrl(file.id)'>{{ file.name }}</a>
                            <span title='недопустимый формат файла' style='color: #900' v-else>{{ file.name }}</span>
                        </div>
                    </span>
                </div>
            </template>

        </ui-data-view-item>
    `
}


Vue.component('template-pgz', Component);