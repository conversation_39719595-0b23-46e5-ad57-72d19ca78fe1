import Vue from "vue"

const Component = {
  props: ["model"],
  template: `
        <ui-data-view-item :model='model'>
            
            <template slot='header'>
            </template>
            
            <template slot='title'>
                <div>{{model.name}}</div>
            </template>

            <template slot='sub_title'>
            </template>

            <template slot='description'>
            </template>

            <template slot='props'>
              <div v-if='model.email'>
                <label>{{$t('email')}}:</label>
                <div><a :href="'mailto:' + model.user_email">{{model.email}}</a></div>
              </div>
              <div v-if='model.created_at'>
                <label>{{$t('send_date')}}:</label>
                <div><iac-date :date='model.created_at' full></iac-date></div>
              </div>
            </template>
        </ui-data-view-item>
    `,
}

Vue.component("template-company_invite", Component)
