import { Entity } from '@iac/data';
import { Util, Language } from '@iac/core';
import { Settings } from '@iac/kernel';

export default class ExchangeContractBody extends Entity {
  constructor(context = {}, contract) {
    super(context);

    this.contract = contract;
    this.wait = false;

    this.signed_at = context.signed_at
    this.partial_paid_at = context.partial_paid_at

    this.good = context.good || {};
    this.country_of_production = context.country_of_production;
    this.type_of_packing = context.type_of_packing;
    this.unit_of_packing = context.unit_of_packing;
    this.type_of_packing_description = context.type_of_packing_description;
    this.status = contract.status;
    this.complaint_number = context.complaint_number;
    this.base_for_create_additional_contract = context.base_for_create_additional_contract;
    this.fine_currency = Settings._default_currency;

    this.parent_id = context.parent_id;
    this.proc_id = context.proc_id;

    this.period_of_delivery = context.period_of_delivery && `${context.period_of_delivery} ${Language.t("day", { count: context.period_of_delivery, plural: true })}`;
    this.period_of_paying = context.period_of_paying && `${context.period_of_paying} ${Language.t("day", { count: context.period_of_paying, plural: true })}`;

    this.incoterms = context.incoterms;
    this.type_of_vehicle = context.type_of_vehicle;
    this.stock_address = context.stock_address && context.stock_address.stock_name;
    this.procent_of_nedogruz = `${context.procent_of_nedogruz || 0} %`;

    this.price = context.price && `${Util.Number(context.price, ' ')} ${Settings._default_currency}`;
    this.amount_of_bought_lots = context.amount_of_bought_lots;
    this.amount_of_good_in_lot = context.amount_of_good_in_lot && `${context.amount_of_good_in_lot} ${context.unit_of_good}`;
    this.vat = `${context.vat || 0} %`;


    if (context.rkp_fine) {
      context.rkp_fine.fine_amount = context.rkp_fine.fine_amount ? `${Util.Number(context.rkp_fine.fine_amount, ' ', 2)} ${Settings._default_currency}` : undefined;
      context.rkp_fine.who_pay = context.rkp_fine.who_pay?.name || context.rkp_fine.who_pay
    }

    this.rkp_fine = context.rkp_fine;
    this.execution = context.execution;

    let timers = Object.keys(context.timers || {}).map((key) => {
      let timer = context.timers[key];
      if (timer.fire) return;

      return {
        name: key,
        type: "widget",
        label: "--timer." + key,
        description: "$timer." + key + ".desc",
        widget: {
          name: "iac-timer",
          props: {
            date: timer.at
          }
        }
      }
    }).filter(timer => timer).reduce((acc, curr) => {
      acc[curr.name] = curr
      return acc
    }, {})

    this.properties.timers.setAttributes({
      fields: {
        ...timers,
      }
    })
  }

  props() {
    return {
      status: {
        group: "contract.page_title",
        label: "--status",
        type: "widget",
        widget: {
          name: 'ui-ref',
          props: {
            source: "exchange_contract_status",
            value: this.status
          }
        }
      },
      signed_at: {
        type: 'widget',
        label: '--contract.date_sign',
        widget: {
          name: 'iac-date',
          props: {
            date: this.signed_at,
            full: true
          }
        },
        hidden: () => !this.signed_at
      },
      partial_paid_at: {
        type: 'widget',
        label: '--full_funds_blocking_date',
        widget: {
          name: 'iac-date',
          props: {
            date: this.partial_paid_at,
            full: true
          }
        },
        hidden: () => !this.partial_paid_at
      },
      complaint_number: {
        label: "--arbitration_complaint",
        type: "link",
        attr: {
          text: `№ ${this.complaint_number}`,
          to: `/workspace/exchange/arbitration_complaint_list/${this.complaint_number}`
        },
        hidden: () => !this.complaint_number
      },
    
      timers: {
        type: "model",
        label: "!"
      },
      parent_id: {
        type: "link",
        label: "--exchange.contract",
        attr: {
          text: `№ ${this.parent_id}`,
          to: `/procedure/${this.parent_id}`
        }
      },
      proc_id: {
        type: "link",
        label: "--session",
        attr: {
          text: `№ ${this.proc_id}`,
          to: `/procedure/${this.proc_id}`
        }
      },
      base_for_create_additional_contract: {
        label: "--additional_agreement_basis",
        type: 'static',
        hidden: () => typeof this.base_for_create_additional_contract != "string"
      },

      amount_of_good_in_lot: {
        group: "lot_information",
        label: "--goods_amount_in_lot",
        type: 'static'
      },
      amount_of_bought_lots: {
        group: "lot_information",
        label: "--lots_by_contract",
        type: 'static'
      },
      vat: {
        group: "lot_information",
        type: "static",
        label: "--vat_percent_value"
      },
      price: {
        group: "lot_information",
        label: "--exchange.price.lot",
        type: 'static'
      },
      period_of_paying: {
        group: "lot_information",
        label: "--timer.wait_lock_sum",
        type: "static",
      },
      good: {
        group: "product_information",
        type: "product",
        readonly: true,
        label: "!",
        attr: {
          eye: true,
          source: "bk"
        }
      },
      country_of_production: {
        label: "--country_of_origin",
        group: "product_information",
        type: "static",
        dataSource: "ref_country",
      },
      type_of_packing: {
        label: "--exchange.packing_type",
        group: "product_information/!-info/packaging",
        type: "static",
        dataSource: {
          store: { ref: "ref_packing_types" }
        }
      },
      unit_of_packing: {
        label: "--exchange.packing_unit",
        group: "product_information/!-info/packaging",
        type: "static",
        dataSource: "ref_unit",

      },
      type_of_packing_description: {
        label: "!",
        group: "product_information/!-info/packaging",
        readonly: true,
        hidden: () => !this.type_of_packing_description
      },

      period_of_delivery: {
        group: "delivery_conditions",
        label: "--delivery_time",
        type: "static",
      },
      incoterms: {
        group: "delivery_conditions",
        label: "--delivery_basis",
        type: "static",
        dataSource: {
          store: {
            method: "ref_incoterms"
          }
        }
      },
      type_of_vehicle: {
        group: "delivery_conditions",
        label: "--transport_type",
        type: "static",
        dataSource: {
          store: {
            ref: "ref_delivery_condition"
          }
        }
      },
      stock_address: {
        group: "delivery_conditions",
        label: "--warehouse_address",
        type: "static",
      },
      procent_of_nedogruz: {
        group: "delivery_conditions",
        label: "--underload_percentage",
        type: "static",
      },
      rkp_fine: {
        group: "fine_information",
        label: "!",
        type: "model",
        readonly: true,
        hidden: !this.rkp_fine,
        fields: {
          base: {
            label: "--reason",
            type: "static"
          },
          who_pay: {
            label: "--who_pays",
            type: "static",
            attr: {
              lng: true
            }
          },
          fine_amount: {
            label: "--execution.fine_amount",
            type: "static",
          },
          fine_at: {
            label: "--execution.fine_at",
            type: this.rkp_fine?.fine_at ? "widget" : "static",
            widget: {
              name: "iac-date",
              props: {
                fullWithSeconds: true,
                date: this.rkp_fine?.fine_at,
              },
            },
          }
        }
      }
    }
  }
}
