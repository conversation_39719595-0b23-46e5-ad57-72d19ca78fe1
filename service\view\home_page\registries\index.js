import { DataSource, ArrayStore } from '@iac/data';

export default {
  props: {
    reg: {
      type: Boolean,
      default: true,
    },
    lib:{
      type: Boolean,
      default: true,
    },
  },
  components: {
    registries: {
      props: ["title", "items"],
      template: `
        <ui-layout-group>
          <h2 class='section-title'>{{ $t(title) }}</h2>
          <div class="ui-data-list ui-data-tile">
            <div class="ui-layout-group data-list_content horizontal_false">
              <div class="content">
                <div class="ui-list data-list-list">
                  <div class="items">
                    <div class="ui-list-item" v-for='item in items'>
                      <widget-registry :item='item' />
                    </div>
                  </div>
                </div> 
              </div>
            </div>
          </div>
        </ui-layout-group>
      `
    }
  },
  template: `
    <iac-section class='section-block' v-if='$content.registries'>
          <registries title='for_users' :items='$content.registries.library' v-if='lib && $content.registries.library' style='margin-bottom: 80px;' />
          <registries title='hp.registries.title' :items='$content.registries.list' v-if='reg && $content.registries.list' />
    </iac-section>
  `,
};
