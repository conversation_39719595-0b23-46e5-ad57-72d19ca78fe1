import Model from './../model/index'
import { Context } from '@iac/kernel'

import './_procedure_dlg'

export default {
    props: ["type"],
    data: function () {
        return {
            coreStatus: {},
            model: undefined,
            error: undefined,
            reload: false,
            updateKey: undefined,
            user: Context.User,
        }
    },
    mounted: async function () {
        await this.update_model()
    },
    updated: async function () {
        if (this.model && this.model.raw_id != this.model_id) {
            await this.update_model();
        }
    },
    beforeDestroy(){
        if(this.model){
            this.model.onReload.unbind(this.onReload)
            this.model.unwatch();
        }
    },
    methods: {
        async onReload(){
            //this.reload = true;
            this.$wait(async ()=>{
                await this.model.refresh()    
            })
            
            //await this.update_model() 
            //this.reload = false;
        },
        async chat(){
            await Vue.Dialog.Chat.Modal({
                size: "full",
                object: {
                    type: this.model.procedure,
                    id: this.model.id
                }
            })
        },
        async update_model() {
            await this.$wait(async ()=>{
                if(this.model){
                    this.model.onReload.unbind(this.onReload)
                    this.model.unwatch();
                }
                //this.model = undefined;
                let { error, data } = await Model.get(this.model_id, this.type);
                this.error = error;
                this.model = data;
                if(this.model){
                    this.model.onReload.bind(this.onReload)
                    this.model.watch();
                }
                this.updateKey = new Date();
            });
        },
        onCoreStatus(status){
            this.coreStatus = status || {}
        }
    },
    computed: {
        classes() {
            return [
                {
                    //"iac-wait":this.reload || (this.model && this.model.raw_id != this.model_id)
                    
                }
            ]
        },
        model_id() {
            return this.$route.params.id;
        },
        coreLink() {
            return `/${this.type || "procedure"}/${this.$route.params.id}/core`
        },
        claimLink() {
            return `/${this.type || "procedure"}/${this.$route.params.id}/claim`
        },
        chatLink() {
            return `/${this.type || "procedure"}/${this.$route.params.id}/chat`
        },
        reportsLink() {
            return `/${this.type || "procedure"}/${this.$route.params.id}/reports`
        },
        logsLink() {
            return `/${this.type || "procedure"}/${this.$route.params.id}/logs`
        }
    },
    template: `
        <div class='iac-tender-details'>
            <template v-if='model'>
                <iac-section type='header'>
                    <ol class='breadcrumb'>
                        <li><router-link to='/'>{{$t('home')}}</router-link></li>
                        <li v-if='type == "ecosystem"'><router-link to='/registry/local_manufacturers?tabid_tab=1'>{{$t('local_manufacturers_offers')}}</router-link></li>
                        <li v-else-if="type == 'business'"><router-link to='/business/ad'>{{$t('reestr_business')}}</router-link></li>
                        <li v-else><router-link to='/procedure'>{{$t('hp.registries.link5')}}</router-link></li>
                        <li>{{$t(model.procedure)}}</li>
                    </ol>
                    <div class='title'>
                        <h1>{{$t(model.procedure)}} № {{model.id}}</h1>
                    </div>
                    <div class='status'>
                        <span class='label'>{{$t('status')}}:</span>
                        <ui-ref class='value' :source='"status_"+model.procedure || "tender"' :value='model.status'/>
                    </div>


                    <div class='part' v-if='model && model.title_part'>
                        <b>{{$t('participant')}} {{model.title_part}}</b>
                        <router-link :to='{path: "/procedure/"+model.id+"/core", query: $route.query}'><icon>delete</icon></router-link>
                    </div>

                    <div class='links'>
                        <router-link :to='coreLink'>{{$t('procedure')}} <ui-badge :model='coreStatus'/></router-link>
                        <router-link v-if='model.access("get_reports")' :to='reportsLink'>{{$t('reports')}}</router-link>
                        <router-link v-if='model.access("get_logs")' :to='logsLink'>{{$t('logs')}}</router-link>
                    </div>  
                </iac-section>
                <iac-section>
                    <div class='tender-layout' v-bind:class='classes'>
                        <router-view :key='updateKey' class='content' :model='model' v-on:coreStatus='onCoreStatus'/>
                    </div>
                </iac-section>
            </template>
            <template v-if='error'>
                <iac-section>
                    <ui-error :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
                </iac-section>            
            </template>
        </div>

    `
}