import Model from './model';
import { Http, Event, Language } from '@iac/core';
import contractLog from './../../contract/details/logs';

export default {
  data() {
    return {
      model: undefined,
      error: undefined,
      channel: undefined,
    };
  },
  watch: {
    "$route.params.id": {
      immediate: true,
      async handler(val, oldVal) {
        await this.update(val);
      }
    }
  },
  beforeDestroy() {
    if (this.channel) {
      // При смене id закрывает канал по текущей сущности
      Http.api.socket.leave_channel(this.channel);
      this.channel = undefined;
    }
  },
  methods: {
    update(id) {
      this.$wait(async () => {
        let { error, data } = await Model.get(id);
        this.model = data;
        this.error = error;

        if (this.channel) {
          // При смене id закрывает канал по текущей сущности
          Http.api.socket.leave_channel(this.channel);
          this.channel = undefined;
        }

        this.channel = Http.api.socket.join(`contract-topic:${id}`, (channel) => {
          channel.on('update', (data = {}) => {
            // Вызываем метод обновления данных
            // this.user_statistics.count_votes_user = data.count_votes_user
          });
        });
      });
    },
    async modelDevelop(method, show_dlg = false) {
      await this.$wait(async () => {
        if (await this.model.develop(method, show_dlg) && method == 'delete_contract')
          this.$router.push({ path: `/workspace/contract` });
      });
    },
  },
  computed: {
    rights() {
      return this.model && this.model.rights && Object.keys(this.model.rights).length;
    },
    classes() {
      return [ { "iac-wait": !this.model || !this.rights || this.model.wait } ];
    },
    showLogs() {
      return this.$policy.system_contract_logs_read || 
             this.model?.rights?.show_log === 'grant';
    }
  },
  components: {
    contractLog: contractLog
  },
  template: `
    <div v-bind:class="classes" class='iac-tender-details'>
      <iac-section type='header'>
        <ol class='breadcrumb'>
          <li><router-link to='/'>{{ $t('home') }}</router-link></li>
          <li><router-link to='/workspace/exchange/arbitration_complaint_list'>{{$t('arbitration_complaints')}}</router-link></li>
          <li v-if='model'>{{ model.number }}</li>
        </ol>
        <div class='title'>
          <h1>{{$t("arbitration_complaint")}} <span v-if='model'>№ {{ model.number }}</span></h1>
        </div>

        <div v-if='model' class='status'>
          <span class='label'>{{$t('status')}}</span>
          <span class='value'>
            <ui-ref source='arbitration_complaint_status' :value='model && model.status'/>
          </span>
        </div>
      </iac-section>

      <iac-section v-if='model && rights'>
        <div class='page-contract' style='background: #fff; padding: 16px; border-radius: 8px;'>
          <ui-btn-group style='padding-bottom: 20px; display: grid; grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));' v-if='$develop.content_debug'>
            <ui-btn type='warning' v-key='action' v-for='action in model.actions' @click.native='modelDevelop(action.method)' style='border-radius: 0px; margin: -1px 0 0 -1px;'>{{action.name}}</ui-btn>
          </ui-btn-group>

          <ui-layout-tab>
            <ui-layout-group label='contract.data'>
              <ui-layout :fields='model.fields'/>
            </ui-layout-group>

            <ui-layout-group label='logs' v-if='$policy.system_contract_logs_read || (model && model.rights && model.rights.show_log === "grant")'>
              <contract-log :model='model'/>
            </ui-layout-group>

          </ui-layout-tab>
        </div>
      </iac-section>

      <iac-section v-if='error'>
        <ui-error style='margin: 10px' :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
      </iac-section>
    </div>
  `
}
