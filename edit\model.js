import { Data } from '@iac/kernel'


class Property extends Data.Property {
    constructor(context) {
        super(context)
        this.border = false;
    }
}

export default class Model extends Data.Entity {
    constructor(context = {}) {
        super(context)
    }
    get propertyModel() {
        return Property;
    }
    props() {
        return {
            t1: {
                group: "test"
            },
            t2: {
                group: "test/22"
            },
            t3: {

            },
            t4_model: {
                type: "model",
                label: "TEST",
                fields: {
                    a_1: {
                        label: "TEST",
                    }
                }
            },

            static: {
                type: "static",
                label: "-static",
                group: "test/33",
                value: "awdawdawd"
            },

            setting22: {
                type: "setting",
                group: "test/22"
            }
        }
    }
}