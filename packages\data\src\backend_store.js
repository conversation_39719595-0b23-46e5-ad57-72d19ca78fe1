import Store from './store'
import ArrayStore from './array_store'
import Http from '../../core/src/http';
import Guid from '../../core/src/guid';

export default class BackendStore extends ArrayStore {
    constructor(options) {

        super(options);



    }
    /*async queryByOptions(options) {
        return await this.host.rpc("backend_storage_get", {
            scope: this.scope,
            key: this.storage_key
        });
    }*/
    async setData(context) {
        if(!context.storage_key || !context.scope)
            return;

        this.scope = context.scope
        this.storage_key = context.storage_key

        let { error, data } = await Http.default.rpc("backend_storage_get", {
            scope: this.scope,
            key: this.storage_key
        });

        if (data == null && this.scope != 'global') {
            let { error: deleteError } = await this.delete();
            if (deleteError) {
                return;
            }
            data = {};
        }


        context.data = Object.keys(data).map((key) => {
            data[key].id = key;
            return data[key]
        });
        super.setData(context)
    }
    async set_item(item) {
        let id = item.id || Guid.newGuid().toString();
        let key = `${this.storage_key}.${id}`
        let data = {
            ...item,
            id: id,
        }


        let { error } = await Http.default.rpc("backend_storage_set", {
            scope: this.scope,
            key: key,
            data: data
        });
        if (!error) {
            await super.set_item(data);
        }


        return { error, data }
    }
    async delete(id) {

        let key = id ? `${this.storage_key}.${id}` : this.storage_key;
        let data = id ? null : {};

        let { error } = await Http.default.rpc("backend_storage_set", {
            scope: this.scope,
            key: key,
            data: data
        });

        if (!error) {
            super.delete(id)
        }

        return { error, data }
    }
}