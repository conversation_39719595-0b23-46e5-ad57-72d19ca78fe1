export default {
  name: 'specCreator',
  props: {
    currentItem: {
      type: Object,
      required: true
    },
    onSelect: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      disableFinalButton: true
    }
  },
  methods: {
    getSpecTitle(currentItem) {
      return `${currentItem.code} - ${currentItem.currentName.VALUE}`
    },
    checkAddingAbility() {
      this.disableFinalButton = Boolean(this.currentItem?.properties?.some(prop => prop.required && !prop.currentValue))
    },
    createSpecAndSendIt() {
      const spec = this.createOldVersionSpec(this.currentItem)
      this.onSelect(spec)
    },
    createOldVersionSpec(data) {
      const spec = {
        category: {
          code: data.category.code,
          id: data.category.id,
          uid: data.category.id,
          title: data.category.currentName?.VALUE ?? ''
        },
        product_id: data.code,
        product_uid: data.id,
        product_name: data.currentName.VALUE,
        type: data.type ? Number(data.type) : data.type,
        product_properties: data.properties.filter(item => Boolean(item.currentValue)).map(({ id, currentName, currentValue, number }) => ({
          val_numb: currentValue.number,
          val_name: currentValue.currentName.VALUE,
          value_id: currentValue.id,
          prop_numb: number,
          prop_name: currentName.VALUE,
          prop_id: id
        })).filter(field => Boolean(field.val_name)),
        unit: undefined,
        version: data.version
      }
      //Костыль из-за получения кривых данных по Единицам Измерения
      const findMarkers = ["Единица измерения", "O'lchov birligi", "Ўлчов бирлиги", "Бирлик", "Birlik", "Unit"].map((item) => item.toLowerCase());
      spec.unit = spec.product_properties.find(({ val_name, prop_name }) => val_name && findMarkers.includes((prop_name ?? "").toLowerCase()))?.val_name ?? "n/a";
      //конец костыля
      return spec
    },
  },
  mounted() {
    this.checkAddingAbility()
  },
  beforeUpdate() {
    this.checkAddingAbility()
  },
  template: `
    <div class="iac--ktru__spec_creator" v-if="currentItem">
      <h3>{{$t('specification_creation_for_code_for').replace('_____', getSpecTitle(currentItem))}}</h3>
      <div class="iac--ktru__spec_creator-about">
        <div>{{$t('item_id')}}</div>
        <div>{{currentItem.code}}</div>
      </div>
      <div class="iac--ktru__spec_creator-about">
        <div>{{$t('product_name')}}</div>
        <div>{{currentItem.currentName.VALUE}}</div>
      </div>
      <div class="iac--ktru__spec_creator-divider"/>
      <div class="iac--ktru__spec_creator-selectors">
        <div v-for="prop in currentItem.properties">
          <label><span v-if="prop.required">*</span>{{prop.currentName.VALUE}}</label>
          <select v-model="prop.currentValue" @change="checkAddingAbility">
            <option v-if="!prop.required" :value="undefined">{{$t('not_selected')}}</option>
            <option v-for="val in prop.values" :value="val">{{val.currentName.VALUE}}</option>
          </select>
        </div>
      </div>
      <div class="iac--ktru__spec_creator-footer">
        <button :disabled="disableFinalButton" @click="createSpecAndSendIt">{{$t('add')}}</button>
      </div>
    </div>
    `
}