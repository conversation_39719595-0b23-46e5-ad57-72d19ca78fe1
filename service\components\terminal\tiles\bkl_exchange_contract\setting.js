import TileSetting from '../setting'

export default class ContractSetting extends TileSetting {
    constructor(context) {
        super(context)

    }

    props() {
        return {
            group_by: {
                label: "Группировка контрактов",
                type: "entity",
                dataSource: [{
                    id: "products", name: "По продуктам"
                }, {
                    id: "companies", name: "По компаниям"
                }],
                has_del: true
            },
            expanded: {
                label: "Расширенная информация по объемам",
                type: "bool"
            }
        }
    }
}