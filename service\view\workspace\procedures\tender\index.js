import { Context, <PERSON>elop, Settings } from "@iac/kernel";
import { Action } from "@iac/core";
import { DataSource, RemoteStore, RefStore, Query } from "@iac/data";

let ktru_product = new Query({
  product_id: {
    type: "entity",
    group: "choose_product",
    label: "!choose_product",
    has_del: true,
    dataSource: new DataSource({
      valueExp: "product_id",
      displayExp: "product_name",
      search: true,
      store: new RefStore({
        ref: "ref_enkt_products",
        key: "product_id",
      }),
    }),
    multiple: true,
    hidden: () => {
      return !Settings.procedures?._filter_product
    }
  },
});

let query = new Query(
  {
    green: {
      type: "entity",
      label: "!green_procedures",
      group: "green_procedures",
      has_del: true,
      dataSource: [
        { id: true, name: "yes" },
        { id: false, name: "no" },
      ],
      hidden: () => !Settings?.procedures?._green,
    },
  },
//   [ktru_product]
);

export default {
  data: function() {
    return {
      user: Context.User,
      query: query,
      ktru_product: ktru_product,
      tender_own: new DataSource({
        query: new Query(
          {
            status: {
              type: "enum",
              dataSource: "status_tender",
              value: [],
            },
            my_procedures: {
              type: "entity",
              dataSource: [
                { id: false, name: "no" },
                { id: true, name: "yes" },
              ],
              has_del: true,
              hidden: () => {
                return !Develop.filter_develop;
              },
            },
            multilot: {
              type: "entity",
              label: "!multipos_procedures",
              group: "multipos_procedures",
              has_del: true,
              dataSource: [
                { id: true, name: "yes" },
                { id: false, name: "no" },
              ],
            },
            is_new_multilot: {
              type: "entity",
              label: "!multilot_procedures", 
              group: "multilot_procedures",
              has_del: true,
              hidden: !Settings.procedures?._multilot,
              dataSource: [
                { id: true, name: "yes" },
                { id: false, name: "no" },
              ],
            },
            relation: {
              value: "owner",
              type: "hidden",
              sync: false,
            },
          },
          [ktru_product, query]
        ),
        store: new RefStore({
          ref: "ref_tender_private",
          injectQuery: (params) => {
            params.fields = [
              "green",
              "id",
              "publicated_at",
              "status",
              "name",
              "good_count",
              "close_at",
              "totalcost",
              "currency",
              "lang",
              "part_count",
              "meta",
              "remain_time",
              "lot_count",
            ];

            params.filters.delivery_regions = params.filters.area_path;
            params.filters.area_path = undefined;

            return params;
          },
        }),
        template: "template-tender",
      }),
      team_tender_party_own: new DataSource({
        query: new Query(
          {
            status: {
              type: "enum",
              dataSource: "participant_status_tender",
              value: [],
            },
            relation: {
              value: "participant",
              type: "hidden",
              sync: false,
            },
            multilot: {
              type: "entity",
              label: "!multipos_procedures",
              group: "multipos_procedures",
              has_del: true,
              dataSource: [
                { id: true, name: "yes" },
                { id: false, name: "no" },
              ],
            },
            is_new_multilot: {
              type: "entity",
              label: "!multilot_procedures", 
              group: "multilot_procedures",
              has_del: true,
              hidden: !Settings.procedures?._multilot,
              dataSource: [
                { id: true, name: "yes" },
                { id: false, name: "no" },
              ],
            },
            company_id: {
              type: "entity",
              label: "!company",
              group: "company",
              has_del: true,
              dataSource: {
                search: true,
                displayExp: "title",
                store: {
                  method: "company_ref",
                  ref: "companies",
                },
              },
            },
          },
          [ktru_product, query]
        ),
        store: new RefStore({
          ref: "ref_tender_private",
          injectQuery: (params) => {
            params.fields = [
              "green",
              "id",
              "publicated_at",
              "status",
              "name",
              "good_count",
              "close_at",
              "totalcost",
              "currency",
              "lang",
              "part_count",
              "meta",
              "remain_time",
              "lot_count",
            ];

            params.filters.delivery_regions = params.filters.area_path;
            params.filters.area_path = undefined;

            return params;
          },
        }),
        template: "template-tender",
      }),
    };
  },
  methods: {
    async create() {
      let response = await Action["procedure.create"]();

      if (!response) return;
      let { error, data } = response;
      if (!error) {
        this.$router.push({ path: `/procedure/${data[0].proc_id}/core` });
      }
    },
  },
  template: `
    <iac-access :access='$policy.tender_list_own || $policy.tender_list_participant || $policy.tender_create || $policy.tender_offer_create' class='iac-tender-list'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('tenders')}}</li>
            </ol>
            <div class='title'>
                <h1>{{$t('tenders')}}</h1>
                <div v-if='0 && $policy.tender_create'><ui-btn type='primary' v-on:click.native='create'>{{$t('create_tender')}}</ui-btn></div>
            </div>
        </iac-section>
        <iac-section>
            <ui-layout-tab :clear='true'>
                
                <ui-layout-group key='Organize' v-if='$policy.tender_list_own' label='Organize'>
                    <ui-data-view  :dataSource='tender_own'/>
                </ui-layout-group> 

                <ui-layout-group key='Participate' v-if='$policy.tender_list_participant' label='Participate'>
                    <ui-data-view  :dataSource='team_tender_party_own'/>
                </ui-layout-group>  

            </ui-layout-tab>
        </iac-section>
    </iac-access>
    `,
};
