import { DataSource, RemoteStore, RefStore, Query } from '@iac/data'
import { Language } from '@iac/core'
import { Settings } from '@iac/kernel'

let ktru_product = new Query({
    product_id: {
      type: "entity",
      group: "choose_product",
      label: "!choose_product",
      has_del: true,
      dataSource: new DataSource({
        valueExp: "product_id",
        displayExp: "product_name",
        search: true,
        store: new RefStore({
          ref: "ref_enkt_products",
          key: "product_id",
        }),
      }),
      multiple: true,
      hidden: () => {
        return !Settings.procedures?._filter_product
      }
    },
  });

var query = new Query({

})

export default {
    data: function () {
        return {
            ktru_product: ktru_product,
            master_agreement_own: new DataSource({
                query: new Query({
                    status: {
                        type: 'enum',
                        dataSource: "ref_master_agreement_status_private",
                        value: [],
                    },
                    relation: {
                        value: "owner",
                        type: "hidden",
                        sync: false,
                    },
                }, [ktru_product, query]),
                store: new RefStore({
                    ref: "ref_master_agreement_private",
                    injectQuery: (params) => {
                        params.fields = ["green","id", "publicated_at", "status", "name", "good_count", "close_at", "totalcost", "currency", "lang", "part_count", "meta"]
                        return params;
                    },
                }),
                template: 'template-master_agreement'
            }),
            master_agreement_party: new DataSource({
                query: new Query({
                    status: {
                        type: 'enum',
                        dataSource: "ref_master_agreement_status_participant",
                        value: [],
                    },
                    relation: {
                        value: "participant",
                        type: "hidden",
                        sync: false,
                    },
                }, [ktru_product, query]),
                store: new RefStore({
                    ref: "ref_master_agreement_private",
                    injectQuery: (params) => {
                        params.fields = ["green","id", "publicated_at", "status", "name", "good_count", "close_at", "totalcost", "currency", "lang", "part_count", "meta"]
                        return params;
                    },
                }),
                template: 'template-master_agreement'
            })
        }
    },
    template: `
        <iac-access :access='$policy.master_agreement_list_own || $policy.master_agreement_list_participant'>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('master_agreement')}}</li>
                </ol>
                <div class='title'>
                    <h1>{{$t('master_agreement')}}</h1>
                </div>
            </iac-section>   
            <iac-section>
                <ui-layout-tab :clear='true'>

                    <ui-layout-group v-if='$settings.procedures && $settings.procedures.master_agreement && $policy.master_agreement_list_own' key='Organize' label='Organize'>
                        <ui-data-view :dataSource='master_agreement_own' />
                    </ui-layout-group>
                    
                    <ui-layout-group v-if='$settings.procedures && $settings.procedures.master_agreement && $policy.master_agreement_list_participant' key='Participate' label='Participate'>
                        <ui-data-view :dataSource='master_agreement_party' />
                    </ui-layout-group>
                        
                </ui-layout-tab>
            </iac-section>  
        </iac-access>
    `
}