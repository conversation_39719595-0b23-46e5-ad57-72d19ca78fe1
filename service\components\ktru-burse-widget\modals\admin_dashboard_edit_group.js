import ebpLangSelect from '../components/ebp_lang_select'
import ebpMultilangInput from '../components/ebp_multilang_input'
import ebpInput from '../components/ebp_input'
import ebpMultilangTreeSelect from '../components/ebp_multilang_tree_select'
import ebpMultilangTable from '../components/ebp_multilang_table'
import getUtils from '../utils'

export default Vue.Dialog({
  props: {
    currentItem: {
      type: Object,
      required: true
    }
  },
  components: {
    'ebpLangSelect': ebpLangSelect,
    'ebpMultilangInput': ebpMultilangInput,
    'ebpInput': ebpInput,
    'ebpMultilangTreeSelect': ebpMultilangTreeSelect,
    'ebpMultilangTable': ebpMultilangTable
  },
  data() {
    return {
      ...getUtils()
    }
  },
  methods: {
    async saveGroup() {
      const group_id = this.currentItem.groups?.length ? this.currentItem.groups[this.currentItem.groups.length - 1].id : null
      delete this.currentItem.name.is_new
      this.Close({ op: "save", id: this.currentItem.id, name: this.currentItem.name, parent_id: group_id, meta: this.currentItem.meta })
    },
    close() {
      this.Close()
    },
    async deleteGroup() {
      this.Close({ op: "delete", id: this.currentItem.id })
    },
    updateGroup(groups) {
      if (groups?.length) {
        this.currentItem.groups = [...groups]
        this.currentItem.group_id = groups[groups.length - 1].id
        this.currentItem = { ...this.currentItem }
      }
    },
    clearGroup() {
      this.currentItem.groups = []
      this.currentItem.parent_id = null
      this.currentItem = { ...this.currentItem }
    },
    async loadGroups() {
      this.currentItem.groups = this.currentItem.parent_id ? await this.getGroups(this.currentItem.parent_id) : []
      this.currentItem = { ...this.currentItem }
    },
    disabledSave() {
      return this.langs.some(lang => lang.active && !this.currentItem.name[lang.name])
    }
  },
  async created() {
    this.langs.forEach(item => item.active = true)
    this.currentItem.groups = []
    this.loadGroups()
  },
  template: `
  <div>
    <header>{{$t("ebp_group_edit")}}</header>
    <main>
      <ebp-lang-select v-model="langs" :label="$t('ebp_select_languages')"/>
      <ebp-multilang-input v-model="currentItem.name" :langs="langs" :label="$t('group_name')"
       :placeholder="$t('group_name')" :enableShowChanges="true"/>
      <ebp-multilang-tree-select :data="currentItem.groups" :langs="langs" :label="$t('group')"
       :placeholder="$t('group')" :request="getGroupsQuery" :hiddenGroupsIds="[currentItem.id]" :clear="clearGroup" :update="updateGroup"/>
    </main>
    <footer>
      <ui-btn type='danger' v-if="currentItem.id && !currentItem.has_children && !currentItem.has_children_prod" @click.native="deleteGroup">{{$t('delete')}}</ui-btn>  
      <ui-btn type='primary' :disabled="disabledSave()" @click.native="saveGroup">{{$t('save')}}</ui-btn>
      <ui-btn type='primary' @click.native="close">{{$t('cancel')}}</ui-btn>
    </footer>
  </div>
    `
})