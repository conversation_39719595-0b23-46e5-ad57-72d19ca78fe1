import ebpMultilangInput from './ebp_multilang_input'

export default {
    name: "ebpMultilangTable",
    model: {
        prop: 'data',
        event: 'change'
    },
    components: {
        'ebpMultilangInput': ebpMultilangInput
    },
    props: {
        langs: {
            type: Array,
            required: true
        },
        columns: {
            type: Array,
            required: true
        },
        data: {
            type: Array,
            required: true
        },
        label: {
            type: [Object, String],
            required: true
        },
        disabled: {
            type: Boolean,
            default: false
        },
        deleteTable: {
            type: Function
        },
        updateLabel: {
            type: Function
        },
        addItem: {
            type: Function
        },
        updateItem: {
            type: Function
        },
        deleteItem: {
            type: Function
        }
    },
    data() {
        return {}
    },
    methods: {
        onChangeHandlerLabel() {
            this.updateLabel?.(this.label)
        },
        addNewRow() {
            this.addItem?.()
        },
        updateRow(index) {
            this.updateItem?.(index, { ...this.data[index] })
        },
        deleteRow(index) {
            this.deleteItem?.(index)
        }
    },
    computed: {
        disabled() {
            return !(this.addItem || this.updateItem || this.deleteItem)
        }
    },
    template: `
    <div class="iac--ebp-multilang-table">
        <button v-if="deleteTable" @click="deleteTable">x</button>
        <label v-if="typeof label == 'string'">{{label}}</label>
        <ebp-multilang-input class="label" v-else v-model="label" @change="e=>onChangeHandlerLabel(e)" :langs="langs"
         :disabled="!updateLabel" :enableShowChanges="true"/>
        <div class="data">
            <div v-for="(column,colIndex) in columns">
                <div>{{column.title}}</div>
                <div v-for="(item, index) in data">
                    <b v-if="colIndex==0 && item.is_new">!</b>
                    <ebp-multilang-input v-if="column.type=='multilang_input'" v-model="item[column.name]"
                     @change="e=>updateRow(index)" :langs="langs" :placeholder="column.title" :disabled="disabled"/>
                    <input v-else-if="column.type=='boolean'" type="checkbox" v-model="item[column.name]"
                     @change="e=>updateRow(index)" :disabled="disabled" />
                    <input v-else-if="column.type=='radio'" type="checkbox" v-model="item[column.name]"
                     @change="e=>updateRow(index)" :disabled="item[column.name] || disabled"/>
                    <input v-else-if="column.type=='number'" type="number" v-model="item[column.name]"
                     @change="e=>updateRow(index)" :disabled="disabled"/>
                    <div v-else >{{item[column.name]}}</div>
                    <button v-if="colIndex==columns.length-1 && !disabled" @click="deleteRow(index)">x</button>
                </div>
            </div>            
        </div>
        <div v-if="!disabled" class="actions">
            <ui-btn type='primary' @click.native="addNewRow">{{$t('add')}}</ui-btn>
        </div>
    </div>
    `
}