import { Entity, DataSource, ArrayStore, RemoteStore, Query } from '@iac/data'

export default {
    data:function(){
        return {
            dataSource: new DataSource({
                store: new RemoteStore({
                    method: "get_company_users"
                })
            })
        }
    },
    template: `
        <div>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('registry.participants')}}</li>
                </ol>
                <h1>{{$t('registry.participants')}}</h1>
            </iac-section>
            <iac-section>
                <ui-data-list :dataSource='dataSource'>
                </ui-data-list>
            </iac-section>
        </div>
    `
}