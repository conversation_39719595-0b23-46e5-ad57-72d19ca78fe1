export default {
  props: ['model'],
  computed: {
    procedure_url(){
      return `/procedure/${this.model.proc_id}/core`
    }
  },
  methods: {
    async save() {
      await this.$wait(async () => {
        await this.model.edit_information();
      });
    },
    async user_repeat_isugf_call() {
      await this.$wait(async () => {
        await this.model.user_repeat_isugf_call();
      });
    },
  },
  template: `
    <div class='grid'>
      <div class='row'>
        <div class='col-sm-3'>
          <h2>{{ $t('contract.page_title') }}</h2>
          <label>{{ $t('status') }}:</label>
          <ui-ref source='contract_status' :value='model && model.status'/>
        </div>
        <div class='col-sm-9'>
          <div class='row' style="display: flex; flex-direction: column;">
          <ui-alert style='margin: 0; flex: auto;' v-if='model.isugf_reply_positive == false && model.isugf_reply.ERRMSG' type='danger'>
            <div style='margin-bottom: 20px'>{{model.isugf_reply.ERRMSG}}</div>
            <div v-if="model.rights.user_repeat_isugf_call"><ui-btn type='xs danger' v-on:click.native='user_repeat_isugf_call'>{{$t('isugf.repeat_request')}}</ui-btn></div>
          </ui-alert>
          </div>
        </div>
      </div>

      <div class='row' v-if='model.lot_id'>
        <label class='col-sm-3'>{{ $t('contract.lot_id') }}:</label>
        <div class='col-sm-5'>
          <div>{{ model.lot_id }}</div>
        </div>
      </div>
      
      
      <div class='row'>
        <label class='col-sm-3'>{{ $t('contract.contract_name') }}:</label>
        <div class='col-sm-5'>
          <ui-input v-if='model.rights.edit_information && !model.additional_contract' label='contract.enter_contract_name' v-model='model.contract_name' has_del />
          <div v-else>{{ model.contract_name }}</div>
        </div>
      </div>

      <div v-if='model.version_number' class='row'>
        <label class='col-sm-3'>{{ $t('contract.extra_number') }}:</label>
        <div class='col-sm-5'>
          <div>{{ model.version_number }}</div>
        </div>
      </div>
      
      <div class='row'>
        <label class='col-sm-3'>{{$t('company.buyer')}}:</label>
        <div class='col-sm-5'>
          <router-link :to='"/company/" + model.initiator.company_details.id'>
            {{ model.initiator.company_details.title }}
          </router-link>
        </div>
      </div>

      <div class='row'>
        <label class='col-sm-3'>{{ $t('contract.org_company_address') }}:</label>
        <div class='col-sm-5'>
          {{ model.org_company_address.postal_area_id }}, {{ model.org_company_address.postal_address }}
        </div>
      </div>

      <div class='row'>
        <label class='col-sm-3'>{{ $t('set_winner') }}:</label>
        <div class='col-sm-5'>
          <router-link :to='"/company/" + model.contragent.company_id'>
            {{ model.contragent.company_details.title }}
          </router-link>
        </div>
      </div>
 
      <div v-if='model.finances && model.finances.length' class='row'>
        <label class='col-sm-3'>{{$t('contract.finance')}}:</label>
        <div class='col-sm-5 finance'>
          <table>
            <tr v-for='(finance, index) in model.finances'>
              <td>{{ index + 1 }}.</td>
              <td>{{ finance.name }}</td>
              <td><iac-number :value='finance.sum' delimiter=' ' part='2' /> {{finance.currency || model.base_currency}}</td>
            </tr>
          </table>
        </div>
      </div>
      <div class='row'>
        <label class='col-sm-3'>{{ $t('contract.proc_type') }}:</label>
        <div class='col-sm-5' v-if='model.proc_id'><router-link :to='procedure_url'> {{ $t(model.proc_type) }} № {{model.proc_id}}</router-link></div>
        <div class='col-sm-5' v-else>{{ $t(model.proc_type) }}</div>
      </div>
      <div class='row'>
        <label class='col-sm-3'>{{ $t('contract.tender_close_at') }}:</label>
        <div class='col-sm-5'><iac-date :date='model.tender_close_at' withoutTime /></div>
      </div>
      <div class='row'>
        <label class='col-sm-3'>{{ $t('contract.protocol') }}:</label>
        <div class='col-sm-5'><span v-if='model.protocol.nam'>{{ model.protocol.name }}:</span> №{{ model.protocol.number }}</div>
      </div>
      
      <div class='row'>
        <label class='col-sm-3'>{{ $t('contract.contract_close_at') }}:</label>
        <div class='col-sm-5' v-if='model && model.contract_close_at'><iac-date :date='model.contract_close_at' withoutTime /></div>
      </div>

      <div class='row' v-if='model && model.confirm_income_date'>
        <label class='col-sm-3'>{{ $t('contract.confirm_income_date') }}:</label>
        <div class='col-sm-5' v-if='model && model.confirm_income_date'><iac-date :date='model.confirm_income_date' withoutTime /></div>
      </div>

      <div class='row' v-if='model && model.confirm_income_date'>
        <label class='col-sm-3'>{{ $t('contract.delivery_date') }}:</label>
        <div class='col-sm-5' v-if='model && model.delivery_date'><iac-date :date='model.delivery_date' withoutTime /></div>
      </div>

      <div v-if='model.execution_date' class='row'>
        <label class='col-sm-3'>{{ $t('contract.execution_date') }}:</label>
        <div class='col-sm-5'>
          <iac-date :date='model.execution_date' withoutTime></iac-date>
        </div>
      </div>
      <div class='row'>
        <label class='col-sm-3'>{{ $t('contract.execution_days') }}:</label>
        <div class='col-sm-5'>
          <ui-input type='number' min='0' :readonly='!model.rights.edit_information' :label='$t("contract.edit_execution_days")'
            v-model.number='model.execution_days.value' :status='model.execution_days.status' required has_del />
        </div>
      </div>
      <div class='row' v-if='model && model.timers && model.timers.global_reject_timer && model.timers.global_reject_timer.meta && model.timers.global_reject_timer.meta.at'>
        <label class='col-sm-3'>{{ $t('contract.sign_time') }}:</label>
        <div class='col-sm-5'>
          <iac-date :date='model.timers.global_reject_timer.meta.at' full />
        </div>
      </div>

      <div class='row' v-if='model.ready_for_delivery && model.ready_for_delivery.datetime'>
        <label class='col-sm-3'>{{ $t('contract.ready_for_delivery') }}:</label>
        <div class='col-sm-5'>
          <iac-date :date='model.ready_for_delivery.datetime' full />
        </div>
      </div>   

      <div v-if='model.rights.edit_information' class='row'>
        <div class='text-center'>
          <ui-btn type='primary' @click.native='save'>{{ $t('save') }}</ui-btn>
        </div>
      </div>
    </div>
  `,
};
