import { Language } from '@iac/core';
import Model from './model/procedure'

let EditModel = {
    props: ["model"],
    data: function () {
        return {
            comment: undefined,
        }
    },
    computed: {
        yes() {
            if ((this.model.rights & 1) == 0)
                return;
            return () => {
                this.$wait(async () => {
                    const { comment} = this;
                    let result = await this.model.vote({
                        comment,
                        vote: true
                    })
                    if (!result)
                        return;
                    if (result.error) {
                        await Vue.Dialog.MessageBox.Error(result.error);
                    } else {
                        if (result.data && result.data.message)
                            await Vue.Dialog.MessageBox.Success(result.data.message)
                        else
                            await Vue.Dialog.MessageBox.Success(this.$t('vote_accepted'))
                        if ((this.model.rights & 4) == 0)
                            this.$emit('reload')
                        else
                            this.$router.push({
                                path: '/workspace/agreement/procedure'
                            });
                    }
                })
            }
        },
        no() {
            if ((this.model.rights & 2) == 0)
                return;
            return () => {
                this.$wait(async () => {
                    const { comment } = this;
                    if (!comment || comment == '') {
                        return Vue.Dialog.MessageBox.Error(Language.t('required_comment'));
                    }
                    let result = await this.model.vote({
                        comment,
                        vote: false
                    })
                    if (!result)
                        return;
                    if (result.error) {
                        await Vue.Dialog.MessageBox.Error(result.error);
                    } else {
                        if (result.data && result.data.message)
                            await Vue.Dialog.MessageBox.Success(result.data.message)
                        else
                            await Vue.Dialog.MessageBox.Success(this.$t('vote_accepted'))

                        if ((this.model.rights & 4) == 0)
                            this.$emit('reload')
                        else
                            this.$router.push({
                                path: '/workspace/agreement/procedure'
                            });
                    }
                })
            }
        },
        abstained() {
            if ((this.model.rights & 4) == 0)
                return;
            return () => {
                this.$wait(async () => {
                    const { comment } = this;

                    if (!comment || comment == '') {
                        return Vue.Dialog.MessageBox.Error(Language.t('required_comment'));
                    }

                    let result = await this.model.vote({
                        comment,
                        vote: null
                    })
                    if (!result)
                        return;
                    if (result.error) {
                        await Vue.Dialog.MessageBox.Error(result.error);
                    } else {
                        if (result.data && result.data.message)
                            await Vue.Dialog.MessageBox.Success(result.data.message)
                        else
                            await Vue.Dialog.MessageBox.Success(this.$t('vote_accepted'))

                        this.$router.push({
                            path: '/workspace/agreement/procedure'
                        });
                    }
                })
            }
        },
        close_procedure() {
            if ((this.model.rights & 8) == 0)
                return;
            return () => {
                this.$wait(async () => {
                    if (await Vue.Dialog.MessageBox.Question(Language.t("vote_close_procedure_question")) != Vue.Dialog.MessageBox.Result.Yes) {
                        return;
                    }

                    let result = await this.model.close_procedure()
                    if (result.error) {
                        await Vue.Dialog.MessageBox.Error(result.error);
                    } else {
                        this.$emit('reload')
                    }
                })
            }
        }
    },
    methods: {
        async subscribe() {
            await this.$wait(async () => {
                let result = await this.model.subscribe();
                if (!result)
                    return;
                if (result.error) {
                    await Vue.Dialog.MessageBox.Error(result.error);
                } else {
                    if (result.data && result.data.message)
                        await Vue.Dialog.MessageBox.Success(result.data.message)
                    else
                        await Vue.Dialog.MessageBox.Success(this.$t('document_signed'))

                    this.$router.push({
                        path: '/workspace/agreement/procedure'
                    });
                }
            })

        }
    },
    template: `
        <ui-layout-group horizontal=2>
            <ui-layout-group>    
                <div style='margin-bottom: 20px'>
                    <router-link class='object' :title='model.object_title' :to='"/procedure/"+model.object_id+"/core"'>{{$t(model.object_type)}} №{{model.object_id}}</router-link>
                </div>
                <ui-layout-group horizontal=2>
                    <div v-if='model.start_at' style='margin-bottom: 10px'>
                    {{$t('agreement.date_from')}}: <iac-date :date='model.start_at' full ></iac-date>
                    </div>
                    <div v-if='model.end_at' style='margin-bottom: 10px'>
                    {{$t('agreement.date_to')}}: <iac-date :date='model.end_at' full></iac-date>
                    </div>
                </ui-layout-group> 

                <ui-layout :fields='model.fields' :readonly='true'  />
                <template v-if='model.status != "close"'>
                  <ui-text v-if='(model.rights & 7) != 0' v-model='comment' label='comment'/>
                  <div v-if='(model.rights & 7) != 0' style='font-size: 12px; margin-bottom: 15px; margin-top: -12px;color: #888;'>{{$t('comment_to_protocol')}}</div>
                  <ui-layout-group horizontal=2>
                      <div>
                        <ui-btn type='primary' v-if='yes' v-on:click.native='yes'>{{ $t('behind') }}</ui-btn>
                        <ui-btn type='primary' v-if='no' v-on:click.native='no'>{{ $t('against') }}</ui-btn>
                        <ui-btn type='primary' v-if='abstained' v-on:click.native='abstained'>{{ $t('abstained') }}</ui-btn>
                        <ui-btn type='primary' v-if='close_procedure' v-on:click.native='close_procedure'>{{ $t('close_procedure') }}</ui-btn>
                      </div>
                  </ui-layout-group>
                </template>
                <div v-else>
                    <ui-btn type='primary' v-on:click.native='subscribe'>{{$t('subscribe')}}</ui-btn>
                </div>

            </ui-layout-group>
        </ui-layout-group>
    `
}

export default {
    data: function () {
        return {
            model: undefined,
            error: undefined
        }
    },
    mounted: async function () {
        this.update_model();

    },
    components: {
        EditModel
    },
    methods: {
        update_model() {
            this.$wait(async () => {
                let { error, data } = await Model.get(this.$route.params.id);
                this.model = data;
                this.error = error;
            });
        }
    },
    template: `
        <div>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li><router-link to='/workspace/agreement/procedure'>{{$t('nav.agreements_procedures')}}</router-link></li>
                    <li>{{$t('nav.agreements')}}</li>
                </ol>
                <h1>{{$t('nav.agreements')}}: <ui-ref source='ref_status_agreement_procedure' :value='model && model.status'/> </h1>
            </iac-section>
            <iac-section>
                <div v-if='error'>{{error}}</div>
                <EditModel v-if='model' :model='model' v-on:reload='update_model' />
            </iac-section>
        </div>
    `
}