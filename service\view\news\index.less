.news-list {
  padding: 20px 0 6px;

  .ui-data-list {
    margin: 0 -12px;
  }

  .data-list_content {
    margin: 0 12px;
  }

  .items {
    display: flex;
    margin: 0 -12px;
    flex-wrap: wrap;
  }

}

.ui-tag1 {
  padding: 24px 9px 20px;

  &.opened {

    > .container {

      > label {
        display: block;
        position: static;
        margin-bottom: 12px;
        font-size: 13px;
        font-weight: 500;
        border-color: @light-gray;
        color: @gray;
        letter-spacing: 0.05em;
        text-transform: uppercase;
      }
    }
  }
}

.news-details {
  padding: 12px 0 6px;

  &__wrap {
    max-width: 809px;
  }

  &__title {
    margin: 0 0 16px;
    font-size: 44px;
    line-height: 1.18;
  }

  &__tag {
    margin: 0 0 20px;
  }

  &__image {
    margin-top: 20px;

    p {
      position: relative;
      padding-top: 41.534%;
      margin: 0;
      line-height: 0;
    }

    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100% !important;
      height: 100%;
      object-fit: cover;
    }
  }

  &__date {
    display: block;
    margin-bottom: 8px;
  }

  &__content {
    font-size: 16px;
    color: @black;
    line-height: 1.5;

    h2 {
      font-size: 20px;
      color: @dark;
      line-height: 1.2;
    }

    p {
      margin: 0 0 20px;
    }

    ol {
      padding-left: 16px;
      
      strong:first-child {
        display: block;
      }
    }

    ul {
      padding: 0;
      list-style: none;

      li {
        position: relative;
        margin-bottom: 16px;
        padding-left: 16px;
  
        &::before {
          position: absolute;
          top: 8px;
          left: 0;
          width: 8px;
          height: 8px;
          background-color: @brand-primary;
          border-radius: 50%;
          content: '';
        }
      }
    }
  }
}

.mb-24 {
  margin-bottom: 24px;
}

.pr-12 {
  padding-right: 12px;
}

.pl-12 {
  padding-left: 12px;
}