.fade-enter-active,
.fade-leave-active {
    transition: opacity .2s;
    overflow: hidden;
}

.fade-leave-to,
.fade-enter {
    opacity: 0;
}

.ui-layout-group {
    white-space: normal;
    &.compact {
        .ui-field.horizontally>.field-content>.field-label {
            flex: 0 0 140px;
        }
    }


    min-width: 250px;
    //border: 1px solid transparent;

    h1 {
        font-style: normal;
        font-weight: 500;
        font-size: 44px;
        line-height: 52px;
        margin: 0;
        padding: 0;
    }

    ul {
        line-height: 20px;
        font-size: 14px;
        padding-left: 16px;
        margin: 0;

        li {

            //list-style-type: none;
            &::marker {
                //content: '\2023';
                color: @primary-link;
            }
        }
    }

    >.label {
        margin: 0 0 16px;
        border-top: 1px solid #F3F3F3;
        display: flex;
        align-items: baseline;

        >.title {
            flex: 1 1 auto;
            font-weight: 500;
            font-size: 18px;
            line-height: 24px;
            //line-height: 60px;
            //white-space: nowrap;
            color: #201D1D;
            padding-top: 20px;
        }
    }

    &:first-child {
        >.label {
            border: none;
            margin-top: -20px;
        }
    }

    >.content {
        display: flex;
        flex-direction: column;
        flex: 1 1 100%;
        min-width: 0;

        >* {
            flex: 0 0 auto;
        }

        >*:last-child:first-child:not(.ui-layout-group):not(.ui-data-list):not(.ui-list) {
            flex: 1 1 100%;
        }
    }

    &.horizontal {
        >.content {
            flex-direction: row;
            flex-wrap: wrap;
            margin: 0 -8px;
            align-items: start;

            >* {
                &.ui-layout-group {
                    >.label {
                        border: none;
                        margin-top: -20px;
                    }
                }

                flex: 1 1 0;
                margin-left: 8px;
                margin-right: 8px;
            }
        }

        &_2 {
            >.content>* {
                flex-basis: calc(50% - 16px);
            }
        }

        &_3>.content>* {
            flex-basis: calc(33% - 16px);
        }

        &_4>.content>* {
            flex-basis: calc(25% - 16px);
        }

        &_5>.content>* {
            flex-basis: calc(20% - 16px);
        }
    }

    &.collapsed {
        >.label {
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &::after {
                display: inline-block;
                font: normal normal normal 10px/1 "iac-icon";
                text-rendering: auto;
                font-feature-settings: "liga" 1;
                text-transform: none !important;
                content: "expanded";
                //width: 20px;
                text-align: center;
                color: #8D8D8D;
                transition: transform 0.5s;
            }

            &:hover {}
        }

        &.expanded {
            >.label::after {
                transform: rotateZ(180deg)
            }
        }
    }

    &.panel {
        box-shadow: 0px 10px 25px rgba(0, 0, 0, 0.1);
        border-radius: 5px;

        >.label {
            border: none;
            background: #F9F9F9;
            padding: 0 20px;
        }

        >.content {
            padding: 0 20px;
        }

        .ui-layout-group {
            min-width: unset;
        }

    }
}

.ui-layout-tab {
    .tab-nav {
        margin-bottom: 15px;
        border-bottom: 1px solid #E5E5E5;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        z-index: 200;
        align-items: center;
        &.tab-header {
            >.tab-nav-action-group{
                >.close {
                    display: flex;
                    align-items: center;
                    font-size: 10px;
                    padding: 3px 6px;
                    color: #868e96;
                    cursor: pointer;
                    &:hover{
                        color: #666;
                        background: #0001;
                    }
                }    
            }
            >.prefix {
                align-self: normal;
                vertical-align: middle;
                display: flex;
                align-items: center;
            }
            >.tab-nav-title {
                flex: 1 1 0;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                padding: 0 6px;
                font-size: 13px;
                color: #666;

                align-self: normal;
                vertical-align: middle;
                display: flex;
                align-items: center;


                &::before {
                    content: " ";
                    position: absolute;
                }
            }
            
        }

        &.sticky {
            background: #fff;
        }

        >.tab-nav-items {
            >.prefix{
                margin-right: 6px;  
                color: #737373;
            }
            >.ui-btn {
                margin-right: 10px;
                icon{
                    margin-right: 6px;  
                }
            }
            >.item {
                display: inline-block;
                cursor: pointer;
                padding: 0 16px;
                line-height: 48px;
                font-size: 13px;
                border-bottom: 1px solid transparent;
                margin-bottom: -1px;
                text-transform: uppercase;
                color: #969595;
                font-weight: 500;
                position: relative;
                >.close{
                    padding: 3px;
                    display: none;
                    position: absolute;
                    font-size: 8px;
                    line-height: 10px;
                    border-radius: 3px;
                    top: 50%;
                    transform: translateY(-50%);
                    right: 0;
                    &:hover{
                        background: #0001;
                    }
                }

                &.active {
                    border-color: @primary-link;
                    color: @primary-link;
                    //font-weight: bold;
                    >.close{
                        display: inline-table;
                    }
                }

                &:hover{
                    >.close{
                        display: inline-table;
                    } 
                }
                
            }
        }

        >.tab-nav-action-group {
            //padding: 0 24px;
            >.tab-nav-action {
                >.toggle {
                    line-height: 58px;
                    margin-right: -8px;
                    >icon{
                        font-size: 20px
                    }
                }
            }
        }


    }

    &.horizontal {
        display: flex;

        //flex-direction: row-reverse;
        >.tab-nav {
            flex: 0 0 auto;
            align-items: unset;
            justify-content: unset;
            border: none;
            background: #eee;
            padding-top: 15px;
            padding-bottom: 15px;
            margin-right: 0;
            margin-bottom: 0;
            box-shadow: inset 10px 0 20px rgba(0, 0, 0, 0.1);
            display: block;
            display: flex;
            flex-direction: column;
            align-items: center;

            >.ui-action {
                .toggle {
                    margin-right: 0;
                }
            }

            .tab-nav-items {
                display: block;
                flex: 1 1 auto;

                .item {
                    border: unset;
                    line-height: 42px;
                    font-size: 14px;
                    display: block;
                    padding: 0 8px;
                    color: #777;

                    >icon {
                        margin: 8px;
                        font-size: 30px;
                        line-height: 40px;
                    }

                    &:hover {
                        background: #ddd;
                    }

                    &.active {
                        font-weight: normal;
                        background: #3e597c;
                        color: #fff;
                        position: relative;
                        display: flex;
                        align-items: center;

                        &:before {
                            content: "";
                            height: 1px;
                            border-top: 15px solid transparent;
                            border-right: 15px solid #fff;
                            display: inline-block;
                            border-bottom: 15px solid transparent;
                            position: absolute;
                            right: -8px;
                        }
                    }
                }
            }
        }

        >.tab-content {
            flex: 1 1 auto;
            min-width: 0;
        }
    }

    &:not(.horizontal) {
        >.tab-content {

            display: flex;
            padding: 0 !important;

            >.ui-layout-group {
                // padding: 20px 24px;
                flex: 1 1 auto;
            }

            >.tab-content-action {
                border-left: 1px solid #F3F3F3;

                .action-group {
                    display: flex;
                    flex-direction: column;
                    padding: 23px;
                    min-width: 250px;

                    &:not(:first-child) {
                        border-top: 1px solid #F3F3F3;
                    }

                    >.ui-btn {
                        height: auto;
                        border-radius: 4px;
                        padding: 12px 28px;

                        &:not(:last-child) {
                            margin-bottom: 12px;
                        }
                    }
                }

                >.ui-btn-group {
                    display: flex;
                    flex-direction: column;

                    >.ui-btn {
                        height: auto;
                        border-radius: 4px;
                        margin-bottom: 12px;
                        padding: 12px 28px;
                    }
                }
            }
        }
    }

}