import { Http } from '@iac/core'

const RateComponent = {
    name: "iac-entity-edit",
    props: {
        value: {

        },
    },
    data() {
        return {

        }
    },
    computed: {
        prefix() {
            if (this.value.prefix)
                return this.value.prefix;

            return this.value.title ? "" : "№"
        },
        id() {
            return this.value.id ?? this.value
        },
        display() {
            return this.prefix + (this.value.title ?? this.value.id ?? this.value);
        },
        table() {
            switch (this.value.type) {
                case "company":
                    return "%{table: \"companies\", service: :ms_company, module: MsCompany.Repo}";
                case "user":
                    return "%{table: \"users\", service: :ms_auth, module: MsAuth.Repo}"
                case "purchase_item":
                    return "%{table: \"goods\", service: :common, module: MsPurchaseList.Repo}"
                case "contract":
                    return "%{table: \"contract\", service: :ms_contract, module: MsContract.Repo}"
                case "contest":
                    return "%{table: \"ref_contest\", service: :common, module: MsDict.Repo}"
                case "reduction":
                    return "%{table: \"ref_reduction_object\", service: :common, module: MsDict.Repo}"
                case "selection":
                    return "%{table: \"ref_selection\", service: :common, module: MsDict.Repo}"
                case "tender":
                    return "%{table: \"ref_tender\", service: :common, module: MsDict.Repo}"
                case "request":
                    return "%{table: \"ref_request_ad\", service: :common, module: MsDict.Repo}"
                case "ad":
                    return "%{table: \"ref_online_position\", service: :common, module: MsDict.Repo}"
            }
        }
    },
    methods: {
        async edit() {
            let { data, error } = await Http.api.rpc("debug_panel", {
                action: "get_entity",
                id: this.id,
                _: this.table
            })
            if (error && error.code != "AbortError") {
                Vue.Dialog.MessageBox.Error(error);
            }
        }
    },
    template: `
    <a v-if='$policy.system_entity_editor' href='' v-on:click.prevent='edit'>{{display}}</a>
    <span v-else>{{display}}</span>
    `
}

Vue.component('iac-entity-edit', RateComponent);

Vue.Fields['entity-edit'] = { is: 'iac-entity-edit' }