.ui-content {
    padding: 0 40px;
    &.compact{
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
    }
}

.breadcrumb{
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
    list-style: none;
    font-size: 14px;
    line-height: 20px;
    color: #969595;
    margin-bottom: 8px;
    a{
        color: #201D1D;
        text-decoration: none;
    }
    >*+*{
        padding-left: .5rem;
        &::before{
            display: inline-block;
            padding-right: .5rem;
            content: "/";
        }
    }
}

input[type=checkbox] {
    appearance: none;
    height: 17px;
    width: 17px;
    cursor: pointer;
    vertical-align: middle;
    border: 1px solid #DADADA;
    box-sizing: border-box;
    border-radius: 4px;
    position: relative;
    outline: none;
    margin-left: 0;
}

input[type=checkbox].tri-state {
    background: @primary-link;
    border: 1px solid transparent;
    opacity: 0.5;
}

input[type=checkbox].always,
input[type=checkbox]:checked {
    background: @primary-link;
    border: 1px solid transparent;
    &:disabled{
        background: #CCC;    
    }
}

input[type=checkbox].tri-state::after,
input[type=checkbox].always::after,
input[type=checkbox]:checked::after{
    width: 5px;
    height: 9px;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    left: 5px;
    top: 2px;
    transform: rotate(45deg);
    content: '';
    display: block;
    position: absolute;
}

.bg-red {
    background: rgb(255, 235, 235);
}
.bg-blue {
    background: #F2FAFB;
}
.bg-green {
    background: rgb(235,255, 235);
    &_dark{
        background: rgb(219, 255, 219);    
    }
}
