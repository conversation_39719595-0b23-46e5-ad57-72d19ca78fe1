import { Entity, DataSource } from '@iac/data'
import { Http, Language } from '@iac/core'
import { Context} from '@iac/kernel'

let format_date_time = new Intl.DateTimeFormat("ru-RU", {
    timeZone: "asia/tashkent",
    // dateStyle: 'full',
    dateStyle: 'short',
    timeStyle: 'long',
})

let additional_languages = ["uz", "ru", 'en']

export default class Model extends Entity {
    constructor(context = {}) {
        super(context)

        this.id = context.id;
        this.type = context.type;
        this.status = context.status;
        this.version = context.id;
        this.data = context.data || {};


        this.meta = context.meta || {}
        
        this.title = context.meta.title || ""
        this.comment = context.meta.comment || ""

        this.languages = this.data.languages || []
        this.content = this.data.content || [];

        this.versions = new DataSource({
            //displayExp: "id",
            store: {
                ref: "ref_documentation_pages",
                context: (context) => {
                    context.meta = context.meta || {}
                    context.name = format_date_time.format(new Date(context.updated_at));
                    //context.name = context.updated_at

                    context.desc = context.meta.comment ? `${Language.t(context.status)}: ${context.meta.comment}` : context.status
                    return context
                },
                injectQuery: (params) => {
                    params.filters = params.filters || {};
                    params.filters.type = this.type

                    // params.filters.updated_at_lte ="2024-03-20T05:07:45.204147Z"


                    params.fields = ["id", "inserted_at", "status", "updated_at", "meta"];
                    return params;
                }
            }
        })
        this.updated_at = context.updated_at;
        this.update = new Date();
    }

    async save() {
        if(this.validate())
            return;

        let languages = additional_languages.reduce((acc,curr)=>{
            acc[curr] = 1
            return acc
        },{})

        let checkLanguage = (data) => {
            for (let lng of additional_languages) {
                languages[lng] *= !!data[lng]
            }
        }

        for (let section of this.content) {
            checkLanguage(section.title);
            for (let item of section.items) {
                checkLanguage(item)
            }
        }

        this.languages = additional_languages.filter((lng)=>{
            return languages[lng]
        });        

        let { error, data } = await Http.api.rpc("ref", {
            ref: "ref_documentation_pages",
            op: "update",
            data: {
                meta: {
                    ...this.meta,
                    title: this.title,
                    comment: this.comment
                },
                data: {
                    languages: this.languages,
                    content: this.content
                },
                id: this.id,
                type: this.type
            }
        })
        if (error) {
            Vue.Dialog.MessageBox.Error(error)
            return false;
        }


        this.update = new Date();
        return true;
    }

    async public() {
        if (!await this.save())
            return false;
    

        if (this.languages.length <=1 && await Vue.Dialog.MessageBox.Question("Не на всех языках заполнена документация.\n\nПродолжить публикацию?") != Vue.Dialog.MessageBox.Result.Yes) {
            return;
        }

        let { error, data } = await Http.api.rpc("ref", {
            ref: "ref_documentation_pages",
            op: "public",
            data: {
                id: this.id
            }

        })
        if (error) {
            Vue.Dialog.MessageBox.Error(error)
            return false;
        }
        this.status = "published"
        return true;

    }

    async new_version() {

    }

    get readonly(){
        return this.status != "draft" || (!Context.Access.policy.company_buyer_admin_cm_dp_update && !Context.Access.policy.company_buyer_admin_cm_dp_create)
    }

    props(){
        return {
            title: {
                label: "-title",
                required: true,
                attr: {
                    react: true
                },
                readonly: this.readonly
            },
            comment: {
                label: "comment",
                type: 'text',
                attr: {
                    react: true
                },
                readonly: this.readonly
            }
        }
    }

    static async get(id, version) {
        let { error, data } = await Http.api.rpc("ref", {
            ref: "ref_documentation_pages",
            op: "get",
            filters: {
                type: id,
                id: version ? Number(version) : undefined
            },
            limit: 1
        });
        if (error) {
            return {
                error
            }
        } else if (!data)
            return {
                error: { messages: "Нет данных" }
            };

        return { error: error, data: new Model(data) }
    }
}