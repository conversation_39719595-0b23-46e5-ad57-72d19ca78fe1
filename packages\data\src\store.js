export default class Store {
    constructor(options) {
        options = options || {};
        this._key = options.key || 'id';
        this._keyType = options.keyType || 'string';
        
        this.context = options.context || function (context) {
            return context
        }

        this.inject = options.inject || function (items) {
            return items
        }

        this.injectQuery = options.injectQuery || function (params) {
            return params
        }
    }

    get key() {
        return this._key;
    }

    get keyType() {
        return this._keyType;
    }

    async load(options) {
        options = options || {};
        let { error, data } = await this.queryByOptions(options);


        data = await this.inject(data, options);
        
        return { error, data }
    }

    async queryByOptions() {
        throw "queryByOptions"
    }

}