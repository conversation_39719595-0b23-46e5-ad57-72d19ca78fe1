.ui-btn{
    pointer-events:auto;
    display: inline-block;
    margin-bottom: 0; // For input.btn
    font-weight: @btn-font-weight;
    text-align: center;
    vertical-align: middle;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    //text-transform: capitalize;
    overflow: hidden;
    text-overflow: ellipsis;

    &.disabled,
    &[disabled],
    fieldset[disabled] & {
        cursor: not-allowed;
        opacity: 0.65;
        box-shadow: none;
    }

    .button-size(@padding-base-vertical; @padding-base-horizontal; @font-size-base; @line-height-base; @btn-border-radius-base);

    &-default{
        .button-variant(@btn-default-color; @btn-default-bg; @btn-default-border);
    }
    &-primary {
        .button-variant(@btn-primary-color; @btn-primary-bg; @btn-primary-border);
    }
    &-secondary {
        .button-variant(@btn-secondary-color; @btn-secondary-bg; @btn-secondary-border);
    }   
    &-success {
        .button-variant(@btn-success-color; @btn-success-bg; @btn-success-border);
    }
    &-info {
        .button-variant(#009AB8; #F2FAFB; #F2FAFB);
    }
    &-warning {
        .button-variant(@btn-warning-color; @btn-warning-bg; @btn-warning-border);
    }
    &.ui-btn-danger {
        .button-variant(#CC3C3C; #FCF5F5; #FCF5F5);
    }
}

.ui-btn-big {
    .button-size(0; 16px; 12px; 48px; 5px);
    min-width: 185px;
    font-weight: bold;
    text-transform: uppercase;
}

.ui-btn-lg {
    .button-size(@padding-large-vertical; @padding-large-horizontal; @font-size-large; @line-height-large; @btn-border-radius-large);
}
.ui-btn-sm {
    .button-size(@padding-small-vertical; @padding-small-horizontal; @font-size-small; @line-height-small; @btn-border-radius-small);
}
.ui-btn-xs {
    .button-size(@padding-xs-vertical; @padding-xs-horizontal; @font-size-small; @line-height-small; @btn-border-radius-small);
}