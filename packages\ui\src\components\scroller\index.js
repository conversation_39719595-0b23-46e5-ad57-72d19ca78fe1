export var Scroller = {
    name: "ui-scroller",
    data: function () {
        return {
            show_scroll: false
        }
    },
    mounted: function () {
        window.addEventListener('resize', this.onWindowResize);
        this.$refs.frame.contentWindow.addEventListener('resize', this.onFrameResize);
        this.$refs.scroll.addEventListener('scroll', this.onScroll);
        this.$refs.main.addEventListener('mousemove', this.onMouseMove)
    },
    beforeDestroy() {
        if (window)
            window.removeEventListener('resize', this.onWindowResize);
        if (this.$refs.frame.contentWindow)
            this.$refs.frame.contentWindow.removeEventListener('resize', this.onFrameResize);
        if (this.$refs.scroll)
            this.$refs.scroll.removeEventListener('scroll', this.onScroll);
        if (this.$refs.main)
            this.$refs.main.removeEventListener('mousemove', this.onMouseMove)
    },
    methods: {
        calcShowScroll() {
            if (this.$refs.scroll && this.$refs.scroll.clientWidth < this.$refs.frame.offsetWidth)
                this.show_scroll = true;
            else
                this.show_scroll = false;
        },
        onWindowResize() {
            this.calcShowScroll();
        },
        onFrameResize() {
            this.$refs.scroll_content.style.width = this.$refs.frame.offsetWidth + "px"
            this.calcShowScroll();
        },
        onScroll() {
            this.$refs.main.scrollLeft = this.$refs.scroll.scrollLeft
        },
        onMouseMove() {
            const mouseoverEvent = new Event('mousemove');
            this.$refs.scroll.dispatchEvent(mouseoverEvent);
        }
    },
    template: `
    <div class='ui-scroller'>
        <main ref='main'><div class='content'>
            <iframe ref='frame' class='frame'/>
            <slot/>
        </div></main>
        <footer style='bottom: 20px; position: sticky; text-align: right;' >
            <slot name='footer'/>
        </footer>
        <div ref='scroll' class='scroll-block'>
            <div ref='scroll_content' class='content'>
                <div v-if='show_scroll' style='height: 18px;'>&nbsp;</div>
            </div>
        </div>
    </div>
    `
}