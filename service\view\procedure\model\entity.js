import { Entity } from '@iac/data'
import { Http, Event } from '@iac/core'
import ProcedureProperty from './property';


export default class ProcedureEntity extends Entity {

    async Upload(value) {
        let path = this.path;
        var send = async (file) => {
            let formData = new FormData();
            formData.append('scope_tender_participant', path[0] && path[0].id);
            formData.append('data', file, file.name);
            let { data, error } = await Http.upload.form('tender/attach', formData);
            if (error) {
                await Vue.Dialog.MessageBox.Error(error);
                return;
            }

            return {
                id: data.uuid,
                name: data.meta.name,
                meta: {
                    "type": data.meta.type,
                    "content_type": data.meta.content_type,
                    "type_group": data.meta.group,
                    "size": data.meta.size
                }
            }
        }

        if (Array.isArray(value)) {
            for (let key in value) {
                let item = value[key];
                if (item && item.file && item.file.name) {
                    let _value = await send(item.file);
                    value[key] = _value;
                }
            }
            value = (value || []).filter((item) => {
                return item;
            })
        } else {
            let item = value;
            if (item && item.file && item.file.name) {
                let _value = await send(item.file);
                value = _value
            }
        }
        return value
    }

    @Event async onChangeProperty(event) {
        if (!event.data)
            return;

        //if(!event.data.property.sync)
        //    return;

        let field = event.data;
        

        if (field.property.type == 'static') {
            return;
        }

        if (field.property.type == 'model' && field.property.readonly) {
            return;
        }

        //if (field.property.type == 'info') {
        //     return;
        //}
        
        field.property.wait = true;
        let value = field.value;
        if (field.value && field.value.exp && field.value.exp.value != undefined) {
            value = value;
        }

        // Костыль
        if (field.name == 'product' && field.property.value && field.property.type == 'entity') {
            value = {
                id: field.property.value.id,
                name: field.property.value.name,
            }
        }


        // Обновляем поле
        let path = this.path;


        if (field && field.property)
            if (value == "" && (field.property.type == 'text' || field.property.type == 'string')) {
                value = null
            }

        let lot_id = path[1] && path[1].id;
        let item_id = path[2] && path[2].id;

        let params = {
            proc_id: path[0] && path[0].id,
            lot_id: lot_id,
            item_id: item_id,
            field: {
                id: field.name,
                value: value
            }
        }

        let _procedure = this;
        while (_procedure.parent) {
            _procedure = _procedure.parent;
        }

        let field_name = `${lot_id || 0}_${item_id || 0}_${field.name}`;


        let { data, error } = 1 ? await Http.proc.rpc("upd_value", params) : await this.upd_value_witch_socket(_procedure.curr_channel, params);

        if (error) {

            if (item_id) {
                let group = field.property.group.split('/')[0];
                group = group.replace(/[-!\}\{\\<\\>]+/gi, '');

                _procedure.fields_status[lot_id] = _procedure.fields_status[lot_id] || {};
                _procedure.fields_status[lot_id][item_id] = _procedure.fields_status[lot_id][item_id] || {};
                _procedure.fields_status[lot_id][item_id][field.name] = {
                    group: _procedure.getItemGroups(group || "tech_fields"),
                    name: field.name,
                    type: "error",
                    message: error.message,
                    data: error.data,
                    value: value
                }
            }

            field.property.status = {
                type: 'error',
                message: error.message,
                data: error.data
            }

        } else {
            let fields_status_item = _procedure.fields_status[lot_id]?.[item_id];
            if (fields_status_item)
                delete fields_status_item[field.name];
        }

        field.property.wait = false;
        this.onUpdate();

        _procedure.updateErrors();


    };

    upd_value_witch_socket(channel, params) {
        return new Promise((resolve, reject) => {

            channel.push("upd_value", {
                ...params
            }).receive("ok", (resp) => {
                resolve({
                    data: {

                    }
                })
            }).receive("error", (resp) => {
                resolve({
                    error: {
                        message: "error"
                    }
                })
            }).receive("timeout", (resp) => {
                resolve({
                    error: {
                        message: "timeout"
                    }
                })
            })
        })
    }

    constructor(context = {}, parent) {
        super(context);
        this.rights = undefined;
        this.context = context;
        this.parent = parent;
    }

    get propertyModel() {
        return ProcedureProperty;
    }

    get actions() {
        return this._actions;
    }

    access(name) {
        if (this.rights && this.rights[name] && this.rights[name] == "granted")
            return true;
        return this.parent ? this.parent.access(name) : false;
    }

    get path() {
        if (!this._path) {
            this._path = [];
            let _entity = this;
            while (_entity) {
                this._path.unshift(_entity);
                _entity = _entity.parent;
            }
        }
        return this._path;
    }

    async updateFields(fields) {
        let change_value = false;
        for (let fieldID in fields) {
            let attributes = fields[fieldID];

            if (attributes.offer_src && attributes.offer_src != this.path[0].part_id)
                continue;

            let field = this.own_properties[fieldID];
            if (!field) {
                attributes.id = fieldID
                this.addField(attributes);
                continue;
            }

            if (attributes.type != field.type) {
                field._value = undefined;
            } else {
                delete attributes.type;
                //delete attributes.dataSource;
            }
            let _value = attributes.value;
            delete attributes.value;

            if (field._value != _value)
                change_value = true;


            field.setAttributes({ ...attributes, status: attributes.status ? attributes.status : (field._value != _value ? undefined : field.status) });
            field._value = _value;
        }
        return change_value;
    }

    async addField(attributes) {
        let prop = new this.propertyModel({
            model: this,
            name: attributes.id,
            attributes: attributes
        });

        this._own_properties[prop.name] = prop;
        this._properties = undefined;
        this._fields = undefined;
    }

    async deleteFields(fields) {
        for (let fieldID in fields) {
            if (!this._own_properties[fieldID]) {
                continue
            }

            if (this._own_properties[fieldID].member) {
                continue;
            }

            delete this[fieldID];
            delete this._own_properties[fieldID];// = undefined;
            this._properties = undefined;
            this._fields = undefined;
        }
    }

    async delField(field) {
        let path = this.path;
        let { data, error } = await Http.proc.rpc("del_field", {
            field_id: field.name,
            proc_id: path[0] && path[0].id,
            lot_id: path[1] && path[1].id,
            item_id: path[2] && path[2].id,
        })
        if (!error) {
            delete this[field.name];
            delete this._own_properties[field.name];// = undefined;
            this._properties = undefined;
            this._fields = undefined;
        }
    }
}