import orderReportsSource from "./order_reports"
import readyReportsSource from "./ready_reports"

export default {
    data() {
        return {
            orderReportsSource,
            readyReportsSource
        }
    },
    template: `
        <iac-access :access='$policy.report_list' class='page-report'>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('nav.reports')}}</li>
                </ol>
                <div class='title'>
                    <h1 style='margin: 0;'>{{$t('nav.reports')}}</h1>
                </div>
                <div class='links'>
                    <router-link to='/workspace/report/order'>{{$t('reports_order')}}</router-link>
                    <router-link to='/workspace/report/ready'>{{$t('reports_ready')}}</router-link>
                </div> 
            </iac-section>
            <iac-section>
                <router-view/>
            </iac-section>
        </iac-access>
    `
}