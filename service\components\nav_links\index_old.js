export default {
  methods: {
    isExternalLink(url) {
      if (url.slice(0, 4) !== 'http') {
        return false;
      }
      return !url.includes(location.href);
    }
  },
  template: `
    <nav class='nav-links'>

      <ul class='nav-links__list' v-if='$content.top_links'>

        <li v-for='item in $content.top_links' class='nav-links__item'>
          <a :title='item.desc' v-if='isExternalLink(item.url)' :href='item.url' class='nav-links__link' target='_blank'>
            {{ item.title}}
          </a>
          <router-link :title='item.desc' v-else :to='item.url' class='nav-links__link'>
            {{ item.title }}
          </router-link>
        </li>
      </ul>
    </nav>
  `,
};
