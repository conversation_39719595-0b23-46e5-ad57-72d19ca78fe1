import {Data} from "@iac/kernel"
import {Http} from "@iac/core"

class Model extends Data.Entity {
    constructor(context={}){
        context.props = context.props || context.fields;
        super(context)
    }
}

const Component = {
    props: ["request"],
    data: function(){
        return {
            model: undefined,
            error: undefined,
        }
    },
    mounted(){
        this.$wait(async ()=>{
            let {error, data} = await Http[this.request.host].rpc(this.request.method, this.request.params)
            if(error){
                this.error = error
            }else{
                this.model = new Model(data)
            }
        })
    },
    template: `
    <div>
        <ui-alert type='warning'>{{$t('under_development')}}</ui-alert>
        <ui-layout v-if='model' :fields='model.fields' />
        <ui-error v-else-if='error' :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
    </div>
    `
}

Vue.component('iac-model', Component);