
import { Entity, DataSource, RemoteStore, Query } from '@iac/data'
import { Http } from '@iac/core';
import { Context } from '@iac/kernel'

let hidden_edit = () => {
    return !Context.Access.policy['company_settings_edit'];
}

class Settings extends Entity {
    constructor(context = {}) {
        super(context)


        this.trusted_accept_income = context.trusted_accept_income;
        this.trusted_ecp_income = context.trusted_ecp_income
    }
    props() {
        return {
            trusted_accept_income: {
                type: "enum",
                group: "contracts",
                label: "setting.trusted_accept_income",
                description: "$setting.trusted_accept_income_desc",
                dataSource: {
                    store: {
                        method: 'get_company_users'
                    }
                },
                hidden: () => {
                    if (!Context.Access.policy['company_settings_trusted_accept_income_crud'])
                        return true;
				},
                readonly: () => {
                    return hidden_edit()
                }
            },
            trusted_ecp_income: {
                type: "enum",
                group: "contracts",
                label: "setting.trusted_ecp_income",
                description: "$setting.trusted_ecp_income_desc",
                dataSource: {
                    store: {
                        method: 'get_company_users'
                    }
                },
                hidden: () => {
                    if (!Context.Access.Develop.trusted_ecp_income_develop)
                        return true;
                },
                readonly: () => {
                    return hidden_edit()
                }
            }
        }
    }

    async save() {
        let params = this.fields.filter((field) => {
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).map((field) => {
            let value = field.value;
            if (field.value && field.value.exp && field.value.exp.value != undefined) {
                value = field.value.exp.value
            }
            return {
                name: field.name,
                value: value
            }
        }).reduce((prev, curr) => {
            prev[curr.name] = curr.value
            return prev;
        }, {})


        let { error, data } = await Http.api.rpc('set_company_settings', params)
        if (error) {
            await Vue.Dialog.MessageBox.Error(error);
            this.setError(error)
        } else {
            if (data.message)
                await Vue.Dialog.MessageBox.Success(data.message);
        }
    }

    static async get() {
        let { error, data } = await Http.api.rpc("get_company_settings");
        if (!error) {
            return {
                data: new Settings(data || {})
            }
        }
        return {
            error,
            data
        }
    }
}

export default {
    data: function () {
        return {
            model: undefined,
            error: undefined,
        }
    },
    mounted: async function () {
        let { error, data } = await Settings.get();
        this.error = error;
        this.model = data;

    },
    computed: {
        has_edit() {
            return !hidden_edit();
        }
    },
    methods: {
        async save() {
            this.$wait(async () => {
                await this.model.save();
            })
        }
    },
    template: `
        <iac-access :access='$policy.company_settings_edit || $policy.company_settings_read'>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('nav.settings')}}</li>
                </ol>
                <h1>{{$t('nav.settings')}}</h1>
            </iac-section>

            <iac-section v-if='model'>
                <ui-layout :fields='model.fields'/>
                <ui-btn v-if='has_edit' type='primary' v-on:click.native='save'>{{$t('save')}}</ui-btn>
            </iac-section>
            <iac-section v-else-if='error' style='margin-top: 30px'>
                <ui-error :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
            </iac-section>
        </iac-access>
    `
}