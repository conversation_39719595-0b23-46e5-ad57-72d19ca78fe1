.iac--ebp-multilang-tree-select {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  margin-bottom: 8px;

  >label {
    flex: 0 0 auto;
  }

  >div {
    flex: 1 1 auto;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: stretch;
    position: relative;

    >.select {
      position: absolute;
      z-index: 10;
      left: 0;
      right: 0;
      top: 100%;
      margin-top: 2px;
      max-height: 200px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: stretch;
      border: 1px solid transparent;
      border-radius: 4px;
      background-color: white;
      border-color: #006f85;
      overflow-x: hidden;
      overflow-y: auto;
    }

    >.current {
      flex: 1 1 auto;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: stretch;
      margin-left: -6px;

      >div {
        flex: 1 1 auto;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: stretch;
        border: 1px solid transparent;
        border-radius: 4px;
        border-color: #006f85;
        overflow: hidden;
        height: 35px;
        position: relative;
        margin-left: 6px;

        >div {
          flex: 0 0 auto;
          text-decoration: none;
          display: inline-block;
          font-weight: normal;
          text-align: center;
          vertical-align: middle;
          padding: 6px;
          font-size: 14px;
          line-height: 1.5;
          background-color: #00859f;
          max-width: 250px;
        }

        >input {
          flex: 1 1 auto;
          width: 100%;
          border: none;
          outline: none;

          &:disabled {
            background-color: #f6f6f6;
            border-color: #e6e6e6;
            cursor: unset;
            color: #201D1D;
          }
        }
      }

      >button {
        position: absolute;
        font-size: 24px;
        color: #006f85;
        right: -20px;
        top: 3px;
        cursor: pointer;
        border: none;
        background: transparent;

        &:hover {
          color: red
        }
      }
    }
  }
}

.iac--ebp-multilang-tree-select-tree-list {
  padding: 1px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;

  >div {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    position: relative;
    padding-left: 10px;

    >div:first-child {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      padding: 1px 0;
      margin-left: -10px;

      >div {
        overflow: hidden;
        height: 35px;
        cursor: pointer;

        &:first-child {
          flex: 0 0 auto;
          width: 30px;

          &.children {
            border: 1px solid transparent;
            border-radius: 4px;
            padding: 0 6px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            background-color: #009ab8;
            border-color: #00859f;

            >div {
              font-weight: bold;
              transition: transform 0.5s ease-in-out;
              color: white;
            }

            &.show> {
              background-color: #00859f;
              border-color: #00859f;

              div {
                transform: rotate(90deg);
              }
            }
          }
        }

        &:last-child {
          flex: 1 1 auto;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: stretch;
          margin-left: 1px;
          border: 1px solid transparent;
          border-radius: 4px;

          &:hover {
            background-color: #00859f48;
          }

          >div {
            border: 1px solid transparent;
            border-radius: 4px;
            border-color: #006f85;
            max-width: 260px;
            flex: 1 1 auto;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: stretch;
            cursor: pointer;
            margin-left: 4px;

            >div {
              flex: 0 0 auto;
              text-decoration: none;
              display: inline-block;
              font-weight: normal;
              text-align: center;
              vertical-align: middle;
              padding: 6px;
              font-size: 14px;
              line-height: 1.5;
              background-color: #00859f;
            }

            >input {
              flex: 1 1 auto;
              width: 100%;
              border: none;
              outline: none;
              pointer-events: none;

              &:disabled {
                background-color: #f6f6f6;
                border-color: #e6e6e6;
                cursor: unset;
                color: #201D1D;
              }
            }
          }
        }
      }
    }
  }
}