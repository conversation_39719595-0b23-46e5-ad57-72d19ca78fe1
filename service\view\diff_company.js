import { DataSource, RemoteStore, Query } from '@iac/data'
import { Context } from '@iac/kernel';
import { Http } from '@iac/core';

export default {
    data: function () {
        return {
            searchDataSource: new DataSource({
                request_count: true,
                query: {

                },
                store: {
                    method: "company_ref",
                    ref: "diff_companies",
                    injectQuery:params=>{
                        
                        return params
                    },
                },
                template: "template-company"
            })
        }
    },
    template: `
        <div>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('hp.registries.name9')}}</li>
                </ol>
                <h1>{{$t('hp.registries.name9')}}</h1>
            </iac-section>
            <iac-section>
                <ui-layout-group>
                    <a style='margin: 16px 0' target='_blank' href='https://xt-xarid.uz/static-pages/uz/differencial_org.pdf'>{{$t('hp.registries.name9.desc')}}</a>
                    <ui-data-view :dataSource='searchDataSource'>
                    </ui-data-view>
                </ui-layout-group>
            </iac-section>
        </div>        
    `
}