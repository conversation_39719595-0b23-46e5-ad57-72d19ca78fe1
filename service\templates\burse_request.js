import Vue from "vue";
import { Language } from '@iac/core'

const Component = {
  props: ["model"],
  data() {
    return {}
  },
  computed: {
    product_name() {
      const name = this.model.product.name
      return name[Language.local] || name[Object.keys(name)[0]] || name[Object.keys(name).find(key => name[key])]
    }
  },
  template: `
        <ui-data-view-item :model='model'>            
            <template slot='header'>
                <div>
                  <iac-entity-edit  v-if='model.number' :value='{id: model.id, type: "burse_product_request", title: "№"+model.number}' />
                </div>
                <div>{{$t('bpr_'+model.status)}}</div>
            </template>
            
            <template slot='title'>
                <div>{{($t('burse_product_request_short') + ' №' + model.number) }}</div>
                <div>{{product_name}}</div>
            </template>

            <template slot='description'>
                <div class='clamp_2'>
                    <label>{{ $t('contract.organizer') }}: </label>
                    <span :title='model.company && model.company.title'>{{ model.company && model.company.title }}</span>
                </div>
            </template>
            <template slot='props'>
                
            </template>
        
        </ui-data-view-item>
    `
}

Vue.component("template-burse_request", Component)
