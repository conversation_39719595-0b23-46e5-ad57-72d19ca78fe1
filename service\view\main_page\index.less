.iac-main-page {
    overflow: hidden;
    background: #fff;

    .ui-layout-tab.services {
      margin-top: 20px;
      min-height: calc(100vh - 133px);
      display: flex;
      flex-direction: column;
      >.tab-content{
        flex: 1 1 100%;
        >.ui-layout-group{
          display: flex;
        }
      }
      >.tab-header{
        justify-content: center;
        border: none;

        >.tab-nav-items{
          position: relative;
          .prefix{
            position: absolute;
            white-space: nowrap;
            right: 100%;
            line-height: 55px;
            font-size: 20px;
            margin-right: 20px;
          }
          .ui-btn{
            padding: 12px;
            width: 200px;
            icon{
              font-size: 18px;
            }
          }
        }
      }
    }
  }

  .iac-service-banners{
    >.content{
      display: flex;
      transition: margin .3s ease-out;


      .banner{
        display: flex; 
        height: 100%;
        justify-content: center;
        >.body{
          flex: 1 1 auto; 
          margin: 20px 0;
          >.title{
            //margin: 16px 0;
            color: #5e5d5d; 
            font-size: 35px; 
            font-weight: 500; 
            white-space: pre-line;
          }
          >.content{
            margin: 16px 0;
            color: #5e5d5d; 
            font-size: 20px; 
            line-height: 26px;
            ul{
              line-height: 22px; 
              font-size: 14px; 
              padding: 0 16px;
            }
          }
        }
        >.image{
          flex: 0 0 auto; 
          align-self: end;
          img{
            height: 270px; 
            vertical-align: bottom;
          }
        }
      }

    }
    >.indicator{
      text-align: center;
      padding: 5px;
      >span{
        padding: 5px;
        display: inline-block;
        cursor: pointer;
        line-height: 8px;
        &::before{
          content: " ";
          display: inline-block;
          background: #ccc;
          width: 8px;
          height: 8px;
          line-height: 8px;
          transition: width .3s ease-out;
          border-radius: 4px;
        }
        &:hover{
          &::before{
            background: @brand-warning;
          }  
        }        
        &.active{
          cursor: default;
          &::before{
            background: @brand-primary;
            width: 24px;
          }
        }

      }
    }
  }

  @media (max-width: 800px) {
    .iac-service-banners{
      .banner{
        flex-wrap: wrap-reverse;
        >.body{
          display: flex;
          flex-direction: column;
          >.content{
            flex: 1 1 auto;
          }

        }
        .ui-btn{
          width: 100%;
        }
      }
    }
  }
  @media (max-width: 500px) {
    .iac-service-banners{
      .image{
        display: none;
      }
    }
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.2s ease;
  }
  
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }