import Vue from 'vue';

export function addNull(num) {
    if (num < 10) {
        return `0${num}`;
    }
    return num;
}

var Component = {
    props: ["maximize","parent"],
    data: function(){
        return {
            first: true
        }
    },
    computed: {
        classes() {
            return [
                "iac-maximize",
                "iac-page",
                {
                    "full": this.maximize
                }]
        },
    },
    watch: {
        maximize: {
            immediate: false,
            async handler(value, oldValue) {
                if(!oldValue && !value)
                    return;
                
                let body = document.getElementsByTagName('body')[0];
                //let service_content = document.getElementsByClassName("iac-service-content")[0]
                if (!body || this.parent)
                    return;
                if(value){
                    if(body.classList.contains("full_show"))
                        this.first = false;

                    //service_content.style["position"] = "fixed"
                    body.classList.add("full_show")
                }else{
                    if(this.first)
                    body.classList.remove("full_show")
                    //service_content.style["position"] = "unset"
                }
            }
        }
    },
    template: `
    <div v-bind:class='classes'>
      <slot/>
    </div>
  `,
};

Vue.component('iac-maximize', Component);
