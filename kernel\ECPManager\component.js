import Manager from './manager'
import {Entity} from '@iac/data'

class ManagerModel extends Entity{

}

var ecpComponent = {
    data: function(){
        return {
            manager: new Manager({
                kg: {
                    pinfl: "20108199350059",
                    //certificate: {
                    //    organizationInn: "00304202310123",
                    //    organization: "Бла бла"
                    //}
                }        
            })
        }
    },
    mounted(){
        var vm = this
        this.manager.onChange.bind(this.onChange);

    },
    before<PERSON><PERSON><PERSON>() {
        this.manager.onChange.unbind(this.onChange);
    },
    methods:{
        onChange(event){
            console.log(event)
            this.$emit('input',event.data);
            this.$emit('change',event.data);
        }
    },
    template: `
        <ui-layout style='min-width: 0;' :fields='manager.fields' />
    `
}


Vue.component('iac-ecp-manager', ecpComponent);

Vue.Fields["ecp-manager"] = { is: 'iac-ecp-manager' }