import { Query } from '@iac/data'
import { Context } from '@iac/kernel'
import SearchBusiness from './search_business'
import Layout from '../procedure/details'
import Core from '../procedure/details/core'
import Claim from '../procedure/details/claim'
import Logs from '../procedure/details/logs'
import Reports from '../procedure/details/reports'

const Search = {
  data: function() {
    return {
      user: Context.User,
      query: {
        green: new Query({
          green: {
            type: "entity",
            label: "!green_procedures",
            group: "green_procedures",
            has_del: true,
            dataSource: [
              { id: true, name: "yes" },
              { id: false, name: "no" }
            ],
            hidden: () => !this.$settings?.procedures?._green
          }
        }),
        search: new Query({
          queryText: {
            icon: 'search',
            label: "!Search",
            hidden: true,
          }
        }),
        area: new Query({
          area_path: {
            group: "area",
            label: "!area",
            type: "enum-tree",
            dataSource: "ref_uz_region_lv4",
            order: -1,
            onChange: (value) => {
              let area_path = value || []
              area_path = area_path.filter((val) => {
                if(val.indexOf('.') < 0) return false
                return true
              })
              this.query.area.set({ area_path: area_path })
            }
          }
        })
      }
    }
  },
  template: `
    <div>
      <iac-section type='header'>
        <ul class='breadcrumb'>
          <li><router-link to='/'>{{$t('home')}}</router-link></li>
          <li>{{$t("reestr_business")}}</li>
        </ul>
        <h1>{{$t("business_title")}}</h1>
      </iac-section>
      <iac-section>
        <router-view :query='query' />
      </iac-section>
    </div>
  `
}

// Конфигурация маршрутов
const params = {
  component: Layout,
  redirect: to => `${to.path}/core`,
  children: [
    { path: 'core', component: Core },
    { path: 'claim', component: Claim },
    { path: 'logs', component: Logs },
    { path: 'reports', component: Reports }
  ]
}

export default [
  {
    path: '/business',
    redirect: to => `${to.path}/ad`,
    component: Search,
    children: [
      { path: 'ad', component: SearchBusiness, props: { type: "ad" } }
    ]
  },
  {
    path: 'business/:id',
    ...params,
    props: { type: "business" }
  }
]
