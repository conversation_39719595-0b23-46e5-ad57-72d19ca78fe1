import { Assets } from '@iac/core';

var Component = {
    data: function(){
        return {
            loading: true 
        }
    },
    async mounted() {
        this.$wait(async ()=>{
            await Assets.script('iac.practice.js',true);
            this.loading=false            
        })
    },
    template: `
      <div><practice-component v-if='!loading' /></div>
    `
}

export default {
    path: "/practice",
    component: Component
}