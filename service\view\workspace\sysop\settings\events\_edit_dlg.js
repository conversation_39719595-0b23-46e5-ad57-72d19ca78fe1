import { Entity } from '@iac/data'
import { Http } from '@iac/core'
import { Language } from '@iac/core'
class ModelProvider extends Entity {
    constructor(context = {}) {
        super(context)
        console.log(context)
        this.id = context.id;
        this.title = context.title;
        this.event_start = context.start;
        this.event_end = context.end;
        this.calendar_id = context.calendar_id;


        try {
            if (typeof context.products == 'string') {
                context.products = JSON.parse(context.products);
            }
            this.products = context.products;
        } catch (e) {

        }

    }
    props() {
        return {
            //id: {
            //    type: "hidden"
            //},
            title: {
                group: "{tab}/general",
                required: true,
            },
            event_start: {
                group: "{tab}/general/!-date",
                label: "event_start",
                //icon: "date",
                type: "date-time",
                prefix: Language.t("event_from"),
                required: true,
                //suffix: "GMT+3"
            },
            event_end: {
                group: "{tab}/general/!-date",
                label: "event_end",
                //icon: "date",
                type: "date-time",
                prefix: Language.t("event_to"),
                required: true,
                //suffix: "GMT+3"
            },
            calendar_id: {
                type: "entity",
                group: "{tab}/general",
                label: "event_calendar_type",
                required: true,
                dataSource: {
                    store: {
                        data: [
                            { id: "burse", name: Language.t('event_exchange') },
                            { id: "events", name: Language.t('event_events') },
                            { id: "holidays", name: Language.t('event_holidays') }
                        ]
                    }
                }
            },

            products: {
                type: "enum-tree",
                label: "!",
                required: true,
                dataSource: {
                    store: {
                        ref: "ref_enkt_tree_products"
                    }
                },
                group: "{tab}/link.products",
                hidden: () => {
                    return !this.calendar_id || this.calendar_id.id != "burse"
                }
            }

        }
    }

    async save() {

        if (this.validate && this.validate()) {
            return {
                error: {
                    message: "Ошибка формы"
                }
            }
        }

        let params = this.fields.filter((field) => {
            if (field.type == 'bool' && !field.value)
                return false;
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).filter((field) => {
            if (field.readonly && typeof field.readonly == 'function') {
                return !field.readonly();
            }
            return !field.readonly;
        }).map((field) => {
            let value = field.value;

            if (field.type == 'date-time') {
                value = new Date(value);
            }

            if (field.value && field.value.exp && field.value.exp.value != undefined) {
                value = field.value.exp.value
            }

            if (Array.isArray(value) && value.length <= 0)
                value = undefined;


            return {
                name: field.name,
                value: value
            }
        }).filter((field) => {
            return field.value != undefined
        }).reduce((prev, curr) => {
            prev[curr.name] = curr.value
            return prev;
        }, {})


        if (params.event_start >= params.event_end) {
            this.properties.event_end.status = {
                type: "error",
                message: "Должно быть больше стартовой даты"
            }
            return {
                error: {
                    message: "Ошибка формы"
                }
            }
        }

        if (params.products) {
            params.products = JSON.stringify(params.products);
        } else {
            params.products = "[]";
        }

        console.log(params);
        params.id = this.id;
        if (!params.id) {
            params.deleted_at = null;
        }


        let { error, data } = await Http.api.rpc("ref", {
            ref: "ref_calendar_events",
            op: this.id ? "update" : "create",
            //filters: {
            //    id: this.id
            //},
            data: params
        })

        if (error) {
            if (!this.setError(error)) {

            }
        }


        if (data) {
            data.start = data.event_start
            data.end = data.event_end
        }

        return {
            error: error,
            data: data
        }
    }
}

export default Vue.Dialog("EditEvent", {
    props: ["event"],
    data: function () {
        return {
            model: new ModelProvider(this.event),
        }
    },
    methods: {
        async delete_event() {
            if (await Vue.Dialog.MessageBox.Question(Language.t('question'), Language.t('delete')) == Vue.Dialog.MessageBox.Result.Yes) {

                this.$wait(async () => {
                    let { error, data } = await Http.api.rpc("ref", {
                        ref: "ref_calendar_events",
                        op: "delete",
                        //filters: {
                        //    id: this.model.id
                        //},
                        data: {
                            id: this.model.id
                        }
                    })

                    if (error) {
                        return await Vue.Dialog.MessageBox.Error(error)
                    }

                    this.Close({

                    });
                })


            }
        },
        async save() {

            this.$wait(async () => {
                let { error, data } = await this.model.save();
                if (error) {
                    await Vue.Dialog.MessageBox.Error(error);
                } else {
                    this.Close(data);
                }
            })

        }
    },
    template: `
        <div>
            <main>
                <ui-layout :fields='model.fields'/>
            </main>
            <footer>
                <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
                <ui-btn type='danger' v-if='model.id' v-on:click.native='delete_event'>{{$t('delete')}}</ui-btn>
                <ui-btn type='primary' v-on:click.native='save'>{{$t('save')}}</ui-btn>
            </footer>
        </div>
    `
})
