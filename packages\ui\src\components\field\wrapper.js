var itemLink = {
    props: ["prop"],
    computed: {
        link() {
            if (typeof this.prop.link == "function")
                return
            return this.prop.link
        }
    },
    methods: {
        onclick() {
            if (typeof this.prop.link == "function") {
                this.prop.link();
            }
        }
    },
    template: `
        <router-link  v-if='link && (link.indexOf("//") < 0)' :to='link'>{{prop.text}}</router-link>
        <a v-else-if='link' :href='link'>{{prop.text}}</a>
        <a v-else href='javascript:void(0)' v-on:click='onclick'>{{prop.text}}</a>
    `
}

let Wrapper = {
    props: {
        checkbox: {
            default: false
        },
        disabled: {

        },
        readonly: {

        },
        value: {

        },
        actions: {

        },
        label: {

        },
        description: {

        },
        content: {
            default: {}
        }
    },
    data: function(){
        return {
            show_description: true
        }
    },
    computed: {
        info() {
            if (this.content?.info) {
               return Array.isArray(this.content.info) ? this.content.info : [this.content.info]
            }
            if(this.label)
            return [
                {
                    title: this.label,
                }
            ]

        }
    },
    methods: {
        update(event) {
            this.$emit('input', event.target.checked)
            this.$emit('change', event.target.checked)
        }
    },
    components: {
        itemLink: itemLink,
    },
    template: `
    <div class='ui-field-wrapper'>

        <div v-if='content.header || (!readonly && !disabled && actions)' class='header' style='display: flex; justify-content: space-between;align-items: center;'>
            <div style='flex: 1 1 100%;'><span v-if='content.header'>{{content.header}}</span></div>
            <span class='eye' v-if='content.description && content.description.length > 0' v-on:click='show_description=!show_description'><icon>{{!show_description ? "eye-off" : "eye"}}</icon></span>
            <ui-action v-if='!readonly && !disabled && actions' icon='action' :actions='actions' />
        </div>

        <div class='body' v-if='info || content.description'>
            <div v-if='info' class='info-wrapper'>
                <div v-if='!item.disabled' class='info' v-for='item, index in info'>
                    <div class='title'>
                        <label style='display: flex;'><input v-if='!index && checkbox' type='checkbox' :checked='value' @change="update" :disabled="disabled || readonly" />
                        <div v-if='item.title' style='flex: 1 1 100%;'>{{item.title}}</div></label>
                    </div>
                    <div class='label' v-if='item.label'>{{item.label}}</div>
                    <div class='content' v-if='item.content'>{{item.content}}</div>
                </div>
            </div>

            <div v-if='content.description && show_description' class='description'>
                <div v-for='desc, idx in content.description'>
                    <label v-if='desc.label'>{{desc.label}}: </label>
                    <template v-if='desc.link'>
                        <itemLink :prop='desc' />
                    </template>
                    <span v-else>{{desc.text}}</span>
                </div>
            </div>
        </div>

        <slot />
        
    </div>
    
    `
}

Vue.component('ui-field-wrapper-default',Wrapper)