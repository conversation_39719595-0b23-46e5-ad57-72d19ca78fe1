import { Http } from '@iac/core';

export default {
  props: ['model'],
  methods: {
    async download_contract() {
      this.$wait(async () => {
        const { data, error } = await Http.report.rpc('render_free_report', {
          ref: 'contract_birja',
          template: 'contract_birja_base',
          type: 'pdf',
          params: {
            number: this.model.number,
          },
        }, {
          format: 'blob'
        });
        if (error && error.code == "AbortError")
        return;
        if (error !== undefined) {
          await Vue.Dialog.MessageBox.Error(error);
          return;
        }
        const url = URL || webkitURL;
        const fileUrl = url.createObjectURL(data);
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = `${this.$t('contract')}_№${this.model.number}.pdf`;
        link.click();
        url.revokeObjectURL(data);
      });
    }
  },
  template: `
    <div class='grid'>
      <div class='row'>
        <label class='col-sm-3'>{{ $t('contract.file') }}:</label>
        <div class='col-sm-5'>
          <div class='file-body'>
            <icon>doc</icon> 
            <a :href='$t("contract") + "_№"+model.number+".pdf"' @click.prevent='download_contract'>{{ $t('contract') }}</a>
          </div>
        </div>
      </div>
    </div>
  `,
};
