import { Language } from '@iac/core';

export default {
  props: ['model'],
  methods: {
    async save() {
      await this.$wait(async () => {
        await this.model.edit_specification();
      });
    },
    no_data() {
      Vue.Dialog.MessageBox.Info(Language.t('no_data'));
    },
    show_properties(product) {
      if (!product || !product.product_properties)
        return this.no_data();
      //event.preventDefault();
      Vue.Dialog({
        props: ['model'],
        template: `
            <div>
              <header>{{model.product_name || model.name}}</header>
              <main>
                <iac-layout-static :value='model.product_properties' />
              </main>
              <footer>
                <ui-btn type='secondary' v-on:click.native='Close'>{{$t('Close')}}</ui-btn>
              </footer>
            </div>
          `
      }).Modal({
        model: product
      })
    },
    getPrice(spec) {
      if (['tender', 'selection'].includes(this.model.proc_type)) {
        return spec.price_with_vat || spec.price
      } else {
        return spec.price
      }
    },
    getPriceVat(spec) {
      return (spec.price_with_vat && spec.price_without_vat && (spec.price_with_vat - spec.price_without_vat)) || 0
    },
    getTotalCostItem(spec) {
      if (['tender', 'selection'].includes(this.model.proc_type)) {
        return spec.totalcost_item_with_vat || spec.totalcost_item
      } else {
        return spec.totalcost_item
      }
    },
    getTotalCostItemVat(spec) {
      return (spec.totalcost_item_with_vat && spec.totalcost_item_without_vat && (spec.totalcost_item_with_vat - spec.totalcost_item_without_vat)) || 0
    },
    getTotalCost() {
      if (Array.isArray(this.model.spec) && ['tender', 'selection'].includes(this.model.proc_type)) {
        return this.model.spec.reduce((prev, spec) => {
          prev += (spec.totalcost_item_with_vat || spec.totalcost_item);
          return prev
        }, 0)
      } else {
        return this.model.spec_totalcost
      }
    },
    getTotalCostVat() {
      return this.model.spec.reduce((prev, spec) => {
        prev += this.getTotalCostItemVat(spec)
        return prev
      }, 0)
    },
    getSuggestedTotalCostVat() {
      return this.model.spec.reduce((prev, spec) => {
        prev += (spec.suggested_totalcost_item_with_vat && spec.suggested_totalcost_item_without_vat && (spec.suggested_totalcost_item_with_vat - spec.suggested_totalcost_item_without_vat)) || 0
        return prev
      }, 0)
    },
    needToShowVats(){
      return ['tender', 'selection'].includes(this.model.proc_type)
    }
  },
  template: `
  <div class='grid' v-if='model.spec && model.spec.length > 0'>
    <div class='row'>
      <h2>{{ $t('contract.spec') }}</h2>
    </div>
    <div class='row' v-for='(spec, index) in model.spec'>
      <div class='col-md-4'>{{ index + 1 }}. <span class='product_name' @click='show_properties(spec)'>{{ spec.product.name }}</span></div>  
      <div class='col-md-4'>

        <div class='row' v-if='spec.proc_id'>
          <label class='col-sm-6'>{{ $t('request') }}:</label>
          <div class='col-sm-6'>
            <router-link :to='"/procedure/"+spec.proc_id'>{{spec.proc_id}}</router-link>
          </div>
        </div>


        <div class='row'>
          <label class='col-sm-6'>{{ $t('contract.amount') }}:</label>
          <div class='col-sm-6'>
            <iac-number :value='spec.amount' delimiter=' ' />&nbsp;<ui-ref source='ref_unit' :value='spec.unit' /> 
          </div>
        </div>
        <div class='row'>
          <label class='col-sm-6'>{{ $t('contract.price') }}:</label>
          <div class='col-sm-6'>
            <iac-number :value='getPrice(spec)' delimiter=' ' part='2' />
            {{model.contract_currency}}
            <span v-if='needToShowVats()'>({{$t('vat_value_into')}}: <iac-number :value='getPriceVat(spec)' delimiter=' ' part='2' /> {{model.contract_currency}})</span>
            <span v-else>({{$t('contract.vat')}})</span>
          </div>
        </div>
        <div class='row' v-if='needToShowVats()'>
          <label class='col-sm-6'> {{$t('vat_percent_value')}}:</label>
          <div class='col-sm-6'>{{ spec.vat }}%</div>
        </div>
        <div class='row' v-if='spec.delivery_period_days'>
          <label class='col-sm-6'>{{ $t('contract.delivery_period_days') }}:</label>
          <div class='col-sm-6'><iac-number :value='spec.delivery_period_days' delimiter=' ' /></div>
        </div>
        <div class='row'>
          <label class='col-sm-6'>{{ $t('contract.position_cost') }}:</label>
          <div class='col-sm-6'>
            <iac-number :value='getTotalCostItem(spec)' delimiter=' ' part='2' />
            {{model.contract_currency}}
            <span v-if='needToShowVats()'>({{$t('vat_value_into')}}: <iac-number :value='getTotalCostItemVat(spec)' delimiter=' ' part='2' /> {{model.contract_currency}})</span>
            <span v-else>({{$t('contract.vat')}})</span>
          </div>
        </div>
        <div class='row' v-if='spec.year'>
          <label class='col-sm-6'>{{ $t('contract.product_year') }}:</label>
          <div class='col-sm-6'>
            <iac-number :value='spec.year' delimiter='' />
          </div>
        </div>
        <div class='row' v-if="spec.country">
          <label class='col-sm-6'>{{ $t('contract.producing_country') }}:</label>
          <div class='col-sm-6'><span>{{model.country_ids[spec.country]}}</span></div>
        </div>
      </div>
      <div class='col-md-4'>
        <div class='row'>
          <ui-input v-if='model.rights.edit_specification' type='float' label='contract.suggested_price' v-model='spec.suggested_price' has_del />
          <div v-else class='ui-control ui-input opened readonly'>
            <div class='container'>
              <label>{{ $t('contract.suggested_price') }}</label>
              <div class='control'>
                <iac-number :value='spec.suggested_price || spec.last_price' delimiter=' ' part='2'/>&nbsp;<span>{{model.contract_currency}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class='row'>
      <div class='col-md-4'></div>
      <div class='col-md-4'>
        <div class='row'>
          <div class='col-sm-6'>{{ $t('contract.total') }}:</div>
          <div class='col-sm-6'>
            <div class='total'><iac-number :value='getTotalCost()' delimiter=' ' part='2' /> {{model.contract_currency}}</div>
            <label>{{ $t('contract.totalcost_item') }} </label>
            <div v-if='needToShowVats()'>{{$t('including')}} <iac-number :value='getTotalCostVat()' delimiter=' ' part='2' /> {{model.contract_currency}} {{$t('vat_value')}}</div>            
          </div>
        </div>
      </div>
      <div class='col-md-4'>
        <div class='row'>
          <div class='total'><iac-number :value='model.spec_suggested_totalcost' delimiter=' ' part='2' /> {{model.contract_currency}}</div>
          <label>{{ $t('contract.suggested_total_price') }}</label>
          <div v-if='needToShowVats()'>{{$t('including')}} <iac-number :value='getSuggestedTotalCostVat()' delimiter=' ' part='2' /> {{model.contract_currency}} {{$t('vat_value')}}</div>
        </div>
      </div>
    </div>

    <div class='row' v-if="model.isugf_invoices_price">
      <div class='col-md-4'></div>
      <div class='col-md-4'>
        <div class='row'>
          <div class='col-sm-6'>{{ $t('contract.isugf_invoices') }}:</div>
          <div class='col-sm-6'>
            <div class='total'><iac-number :value='model.isugf_invoices_price' delimiter=' ' part='2' /> {{model.base_currency}}</div>
          </div>
        </div>
      </div>
    </div>

    <div v-if='model.rights.edit_specification' class='row'>
      <div class='text-center'>
        <ui-btn type='primary' @click.native='save'>{{ $t('save') }}</ui-btn>
      </div>
    </div>
  </div>    
  `,
};
