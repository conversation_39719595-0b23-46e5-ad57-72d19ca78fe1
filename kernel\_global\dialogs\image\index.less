.image-dlg{
    .scene{
        position: relative;
        overflow: hidden;
    }
    .react-crop{
        cursor: move;
        position: absolute;
        >div{
            box-sizing: border-box;
            width: 16px;
            height: 16px;
            position: absolute;
            &[data-ord='nw']{
                border-left: 2px solid #FFF; 
                border-top: 2px solid #FFF;
                left: 0;
                top: 0;
                cursor:nwse-resize;
            }
            &[data-ord='sw']{
                border-left: 2px solid #FFF;
                border-bottom: 2px solid #FFF; 
                left: 0; 
                bottom: 0;
                cursor:nesw-resize;
            }
            &[data-ord='ne']{
                border-right: 2px solid #FFF;
                border-top: 2px solid #FFF; 
                right: 0; 
                top: 0;
                cursor:nesw-resize;
            }
            &[data-ord='se']{
                border-right: 2px solid #FFF; 
                border-bottom: 2px solid #FFF; 
                right: 0; 
                bottom: 0;
                cursor:nwse-resize;
            }

            &.mask {
                width: 100%;
                height: 100%;
                box-shadow: 0 0 0 9999em #000A;
                overflow: hidden;
                border-radius: 50%;
                >.grid {
                    pointer-events: none;
                    &.vertical{
                        position: absolute;
                        border-left: 1px dashed #fff7;
                        border-right: 1px dashed #fff7;

                        width: 33%;
                        left: 33%;
                        top: 0;
                        height: 100%;
                    }
                    &.horizontal{
                        position: absolute;
                        border-top: 1px dashed #fff7;
                        border-bottom: 1px dashed #fff7;

                        height: 33%;
                        top: 33%;
                        left: 0;
                        width: 100%;
                    }
                }
            }
        }

    }
    &.handle{
        &_move .scene {
            cursor:move; 
        }
        &_nw .scene,&_se .scene{
            cursor:nwse-resize; 
        }
        &_ne .scene,&_sw .scene{
            cursor:nesw-resize; 
        }
        .react-crop{
            cursor: unset;
            >div{
                cursor: unset;
            }
        }
    }
}