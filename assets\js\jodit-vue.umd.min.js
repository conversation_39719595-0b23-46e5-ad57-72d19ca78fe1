!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("jodit")):"function"==typeof define&&define.amd?define(["exports","jodit"],e):e((t=t||self).JoditVue={},t.Jodit)}(this,(function(t,e){"use strict";e=e&&e.hasOwnProperty("default")?e.default:e,function(t){function e(){this.globalThis=this,delete t.prototype._T_}"object"!=typeof globalThis&&(this?e():(t.defineProperty(t.prototype,"_T_",{configurable:!0,get:e}),_T_))}(Object);var n=function(t,e,n,o,i,r,s,u,d,a){"boolean"!=typeof s&&(d=u,u=s,s=!1);var l,f="function"==typeof n?n.options:n;if(t&&t.render&&(f.render=t.render,f.staticRenderFns=t.staticRenderFns,f._compiled=!0,i&&(f.functional=!0)),o&&(f._scopeId=o),r?(l=function(t){(t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),e&&e.call(this,d(t)),t&&t._registeredComponents&&t._registeredComponents.add(r)},f._ssrRegister=l):e&&(l=s?function(){e.call(this,a(this.$root.$options.shadowRoot))}:function(t){e.call(this,u(t))}),l)if(f.functional){var c=f.render;f.render=function(t,e){return l.call(e),c(t,e)}}else{var h=f.beforeCreate;f.beforeCreate=h?[].concat(h,l):[l]}return n},o={name:"JoditEditor",props:{value:{type:String,required:!0},buttons:{type:Array,default:null},extraButtons:{type:Array,default:null},config:{type:Object,default:function(){return{}}}},data:function(){return{editor:null}},computed:{editorConfig:function(){var t=Object.assign({},this.config);return this.buttons&&(t.buttons=this.buttons,t.buttonsMD=this.buttons,t.buttonsSM=this.buttons,t.buttonsXS=this.buttons),this.extraButtons&&(t.extraButtons=this.extraButtons),t}},watch:{value:function(t){this.editor.value!==t&&(this.editor.value=t)}},mounted:function(){var t=this;this.editor=new e(this.$el,this.editorConfig),this.editor.value=this.value,this.editor.events.on("change",(function(e){return t.$emit("input",e)}))},beforeDestroy:function(){this.editor.destruct()}},i=function(){var t=this.$createElement;return(this._self._c||t)("div")};i._withStripped=!0;var r=n({render:i,staticRenderFns:[]},void 0,o,void 0,!1,void 0,void 0,void 0);function s(t){s.installed||(s.installed=!0,t.component("JoditEditor",r),t.component("JoditVue",r))}var u={install:s};globalThis.Vue&&globalThis.Vue.use(u),t.Jodit=e,t.JoditEditor=r,t.JoditVue=r,t.default=u,t.install=s,Object.defineProperty(t,"__esModule",{value:!0})}));
