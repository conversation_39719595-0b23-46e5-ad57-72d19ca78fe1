import { DataSource, RefStore, Query } from '@iac/data'
import { Http, Language } from '@iac/core'
import "@iac/service/templates/company"

const accept_deleted = async () => {
    if (await Vue.Dialog.MessageBox.Question(Language.t("question_do_you_really_want_to_delete_this_record")) != Vue.Dialog.MessageBox.Result.Yes) {
        return;
    }
};

const delete_action = (policy, ref, context, datasource) => {
    return {
        icon: "delete",
        label: "delete",
        btn_type: "danger sm",
        hidden: () => !policy,
        question: Language.t("question_do_you_really_want_to_delete_this_record"),
        handler: async () => {
            let { error, data } = await Http.api.rpc("company_ref", {
                ref: ref,
                op: "delete",
                filters: { id: context.id }
            })

            !error && datasource.reload();
        }
    }
}

const props_gen = (ctx) => (label, fetch_field_func, text_func) => {
    const $t = Language.t;
    let r = null;

    if (typeof fetch_field_func === 'function') r = fetch_field_func(ctx)
    else r = fetch_field_func

    let text = null;
    text_func = text_func || (id => id)

    if (r) {
        text = text_func(r)
    } else {
        return null
    }

    return {
        label: $t(label),
        text: $t(text)
    }
}

const edit_action = (policy, ref, context, datasource) => {
    return {
        icon: "action",
        label: "edit",
        btn_type: "danger sm",
        hidden: () => !policy,
        handler: async () => {
            let { error, data } = await Http.api.rpc("company_ref", {
                ref: ref,
                op: "form",
                data: {
                    id: context.id,
                }
            })

            !error && datasource.reload();
        }
    }
}

export default {
    data: function () {
        return {
            driversDataSource: new DataSource({
                store: new RefStore({
                    method: "company_ref",
                    ref: "drivers",
                    context: context => {
                        const prop = props_gen(context)

                        return {
                            actions: [delete_action(
                                this.$policy.company_drivers_edit,
                                "drivers",
                                context,
                                this.driversDataSource
                            ), edit_action(
                                this.$policy.company_drivers_edit,
                                "drivers",
                                context,
                                this.driversDataSource
                            )],
                            id: context.id,
                            header: [`${this.$t('pinfl')} ${context?.pinfl}`],
                            title: {
                                text: context?.meta?.full_name,
                                link: async () => {
                                    let { error, data } = await Http.api.rpc("company_ref", {
                                        ref: "drivers",
                                        op: "form",
                                        data: {
                                            id: context.id,
                                        }
                                    })

                                    !error && this.driversDataSource.reload();

                                }
                            },
                            props: [
                                prop('phone',
                                    c => c?.meta?.phone_number),
                                prop('email',
                                    c => c?.meta?.email),
                                prop('date_of_birth',
                                    c => c?.meta?.date_of_birth),
                                prop('certificate_number',
                                    c => c?.meta?.certificate_number),
                                prop('certificate_date',
                                    c => c?.meta?.certificate_date),
                            ].filter(x => x),
                            ...context
                        };
                    },
                }),
                actions: [{
                    label: "add",
                    handler: this.createDriver,
                    hidden: () => !this.$policy.company_drivers_edit,
                }],
            }),
            vehiclesDataSource: new DataSource({
                store: new RefStore({
                    method: "company_ref",
                    ref: "vehicles",
                    context: context => {
                        const prop = props_gen(context)

                        return {
                            actions: [delete_action(
                                this.$policy.company_vehicles_edit,
                                "vehicles",
                                context,
                                this.vehiclesDataSource
                            ), edit_action(
                                this.$policy.company_vehicles_edit,
                                "vehicles",
                                context,
                                this.vehiclesDataSource
                            )],
                            id: context.id,
                            header: [`${this.$t('vehicle_number')} ${context?.number}`],
                            title: {
                                text: context?.meta?.mark_or_model,
                                link: async () => {
                                    let { error, data } = await Http.api.rpc("company_ref", {
                                        ref: "vehicles",
                                        op: "form",
                                        data: {
                                            id: context.id,
                                        }
                                    })

                                    !error && this.vehiclesDataSource.reload();
                                }
                            },
                            props: [
                                prop('full_weight',
                                    c => c?.meta?.full_weight,
                                    fw => `${fw?.massa} ${fw?.unit}`),
                                prop('weight_without_load',
                                    c => c?.meta?.weight_without_load,
                                    fw => `${fw?.massa} ${fw?.unit}`),
                                prop('type',
                                    c => c?.meta?.type_of_vehicle),
                                prop('fule_type',
                                    c => c?.meta?.fuel_type),
                                prop('body_type',
                                    c => c?.meta?.body_type),
                                prop('date_of_issue',
                                    c => c?.meta?.date_of_issue),
                                prop('date_of_certificate_issue',
                                    c => c?.meta?.date_of_certificate_issue),
                                prop('certificate_of_auto_region',
                                    c => c?.meta?.certificate_of_auto_region),
                            ].filter(x => x),
                            ...context
                        };
                    },
                }),
                actions: [{
                    label: "add",
                    hidden: () => !this.$policy.company_vehicles_edit,
                    handler: this.createVehicle
                }],
            }),
            affiliatedTransportDataSource: new DataSource({
                store: new RefStore({
                    method: "company_ref",
                    ref: "affiliated_transport_companies",
                    inject: async items => {
                        if (!items) return;
                        const uniq_ids = [...new Set(items.map(it => it.affiliated_company_id))];

                        let { error, data } = await Http.api.rpc("company_ref", {
                            ref: "companies",
                            op: "read",
                            filters: { id: uniq_ids },
                            limit: 51
                        })

                        if (!data) return items;

                        const kv = data.reduce((acc, it) => {
                            acc[it.id] = it;
                            return acc;
                        }, {});

                        return items.map(it => {
                            return { ...it, ...kv[it.affiliated_company_id] }
                        });

                        return data;
                    },
                    context: context => {
                        return {
                            actions: [delete_action(
                                this.$policy.company_affiliated_transport_company_edit,
                                "affiliated_transport_companies",
                                context,
                                this.affiliatedTransportDataSource
                            )],
                            ...context,
                        }
                    },
                }),
                actions: [{
                    label: "add",
                    hidden: () => !this.$policy.company_affiliated_transport_company_edit,
                    handler: this.createAffilatedTransport
                }],
                template: 'template-company'
            }),
            stocksDataSource: new DataSource({
                store: new RefStore({
                    method: "company_ref",
                    ref: "stocks",
                    context: context => {
                        const prop = props_gen(context)

                        return {
                            actions: [delete_action(
                                this.$policy.logistic_stocks_crud,
                                "stocks",
                                context,
                                this.stocksDataSource
                            ), edit_action(
                                this.$policy.logistic_stocks_crud,
                                "stocks",
                                context,
                                this.stocksDataSource
                            )],
                            id: context.id,
                            header: [`${this.$t('country')} ${context?.meta?.country?.name}`],
                            //header: [`${this.$t('name')} ${context?.stock_name}`],
                            title: { text: context?.stock_name, },
                            //title: { text: context?.meta?.address, },
                            props: [
                                prop('region', c => c?.meta?.area?.name),
                                prop('address', c => c?.meta?.address),
                            ].filter(x => x),
                            ...context,
                        }
                    }
                }),
                actions: [{
                    label: "add",
                    hidden: () => !this.$policy.logistic_stocks_crud,
                    handler: this.createStock
                }]
            })
        }
    },
    methods: {
        async createDriver() {
            let { error, data } = await Http.api.rpc("company_ref", {
                ref: "drivers",
                op: "form"
            })

            !error && this.driversDataSource.reload();
        },
        async createVehicle() {
            let { error, data } = await Http.api.rpc("company_ref", {
                ref: "vehicles",
                op: "form"
            })

            !error && this.vehiclesDataSource.reload();
        },
        async createAffilatedTransport() {
            let { error, data } = await Http.api.rpc("company_ref", {
                ref: "affiliated_transport_companies",
                op: "form"
            })

            !error && this.affiliatedTransportDataSource.reload();
        },
        async createStock() {
            let { error, data } = await Http.api.rpc("company_ref", {
                ref: "stocks",
                op: "form"
            })

            !error && this.stocksDataSource.reload();
        },
    },
    template: `
    <iac-access class='iac-drivers-list' :access='$policy.company_edit || $policy.company_drivers_edit || $policy.company_vehicles_edit || $policy.logistic_stocks_crud'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.drivers')}}</li>
            </ol>
            <div class='title'>
                <h1>{{$t('nav.drivers')}}</h1>
            </div>
        </iac-section>

        <iac-section>
            <ui-layout-tab>
                <ui-layout-group key='drivers' label='drivers'>
                    <ui-layout-group>
                        <ui-data-view :dataSource='driversDataSource'/>
                    </ui-layout-group>
                </ui-layout-group>
                <ui-layout-group key='vehicles' label='vehicles'>
                    <ui-layout-group>
                        <ui-data-view :dataSource='vehiclesDataSource'/>
                    </ui-layout-group>
                </ui-layout-group>
                <ui-layout-group key='affiliated_transport' label='affiliated_transport'>
                    <ui-layout-group>
                        <ui-data-view :dataSource='affiliatedTransportDataSource'/>
                    </ui-layout-group>
                </ui-layout-group>
                <ui-layout-group key='stocks' label='stocks'>
                    <ui-layout-group>
                        <ui-data-view :dataSource='stocksDataSource'/>
                    </ui-layout-group>
                </ui-layout-group>
            </ui-layout-tab>
        </iac-section>
    </iac-access>
    `
}