import hljs from 'highlight.js';

const Component = {
    name: "doc-code-editor",
    props: ["autoclose", "icon", "label", "status", "value", "readonly", "disabled", "name", "type", "wait", "actions", "required"],
    data: function(){
        return {
            scrollHeight: 15
        }
    },
    mounted(){

        let x1,x2;
        this.$refs.editor.style.overflowX = "scroll";
        x1 = this.$refs.editor.clientHeight;
        this.$refs.editor.style.overflowX = "hidden";
        x2 = this.$refs.editor.clientHeight;

        this.scrollHeight = x2-x1

        this.$refs.editor.style.overflowX = "scroll";

        this.resize();
    },
    computed: {
        classes() {
            return [

            ]
        },
        opened() {
            if (this.value || this.value === 0) {
                return true
            }
            return false;
        },
        code() {
            return hljs.highlight((this.value || "") + " ", { language: "js" }).value;
        },
        inputListeners: function () {
            var vm = this
            return Object.assign({},
                this.$listeners,
                {
                    input: function (event) {
                        vm.resize()
                        vm.value = event.target.value
                        vm.$emit('input', event.target.value)
                    },
                    change: function (event) {
                        vm.value = event.target.value
                        vm.$emit('change', event.target.value)
                    }
                }
            )
        },
        lines() {
            return (this.value || "").split('\n')
        }
    },
    methods: {
        resize(){
            if(!this.$refs.editor)
                return;

            //console.log(this.$refs.editor.scrollHeight,this.scrollHeight);
            //this.scrollHeight = 0;
                
            //this.$refs.editor.style.height = "auto";
            this.$refs.editor.style.height = (this.$refs.editor.scrollHeight+this.scrollHeight)+'px';
        },
        insertText(text, pos = 0) {
            const { activeElement } = document;
            const { selectionStart } = activeElement;
            if (text)
                document.execCommand('insertText', false, text)
            else
                document.execCommand('delete')
            //activeElement.setRangeText(text);
            activeElement.selectionStart = activeElement.selectionEnd = selectionStart + (pos || text.length);

            //}
        },
        action(event) {
            if (event == 'clear') {
                this.value = undefined;
                this.$emit('input', undefined)
                this.$emit('change', undefined)
            }
        },
        add_tab({ target }) {
            this.insertText("\t");
            this.$emit('input', target.value)
        },
        enter({ target }) {
            let value = target.value || "";
            let start = value.lastIndexOf("\n", target.selectionStart - 1);

            let line = value.substring(start + 1, target.selectionStart)

            const regex = /^\s+/gm;
            let tabs = (regex.exec(line) || [""])[0];
            let text = "\n" + tabs;
            let pos = undefined;

            if (value[target.selectionStart - 1] == '{' || value[target.selectionStart - 1] == '[') {
                pos = (text + '\t').length;
                text = text + '\t';

                if (value[target.selectionEnd] == '}' || value[target.selectionEnd] == ']') {
                    text = text + "\n" + tabs;
                }
            }


            this.insertText(text, pos);
            this.$emit('input', target.value)


        },
        onKeyDown(e) {
            if (!this.autoclose)
                return;

            if (e.key == '(') {
                this.insertText("()", 1);
                e.preventDefault();
                e.stopPropagation();
            }
            if (e.key == '{') {
                this.insertText("{}", 1);
                e.preventDefault();
                e.stopPropagation();
            }
            if (e.key == '[') {
                this.insertText("[]", 1);
                e.preventDefault();
                e.stopPropagation();
            }
            if (e.key == '"') {
                this.insertText("\"\"", 1);
                e.preventDefault();
                e.stopPropagation();
            }
            if (e.key == '\'') {
                this.insertText("\'\'", 1);
                e.preventDefault();
                e.stopPropagation();
            }
        },
        onScroll({ target }) {
            this.$refs.view.scrollLeft = target.scrollLeft
            this.$refs.view.scrollTop = target.scrollTop
        }
    },
    template: `<ui-control class='doc-code-editor' v-bind:class="classes" 
    :icon='icon' :label='label' :opened='opened' :status='status' v-on:action='action' :wait='wait' :readonly='readonly' :disabled='disabled' :actions='actions' :required='required'>
    
    <textarea ref='editor' class='control thin-scroll' spellcheck="false"
        v-bind="$attrs"
        v-bind:value="value"
        v-on="inputListeners"
        :disabled="disabled"
        :readonly="readonly"
        v-on:keydown.tab.prevent.stop='add_tab'
        v-on:keydown.enter.prevent.stop='enter'
        v-on:keydown='onKeyDown'
        v-on:scroll='onScroll'
    />
    <pre class='highlighted-code' ref='view'><div class='wrapper'>
        <div class='line-number'><div v-for='line,index in lines'>{{index+1}}</div></div>
        <code class='code' v-html='code'/>
    <div></pre>
        
    </ui-control>
    `
}
Vue.component('doc-code-editor', Component);

Vue.Fields['code-editor'] = {
    is: 'doc-code-editor'
}