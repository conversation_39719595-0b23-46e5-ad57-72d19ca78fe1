import { DataSource, RemoteStore } from '@iac/data'

var ProductComponent = {
    name: "iac-product",
    props: {
        icon: String,
        label: String,
        status: Object,
        value: Object,
        readonly: <PERSON><PERSON><PERSON>,
        disabled: <PERSON><PERSON><PERSON>,
        wait: <PERSON><PERSON><PERSON>,
        actions: Array,
        has_del: <PERSON><PERSON><PERSON>,
        required: <PERSON><PERSON><PERSON>,
        short: {
            type: Boolean,
            default: false
        },
        eye: {
            type: Boolean,
            default: false
        },
        source: {
            type: String,
            default: 'ktru'//"ktru"||"main"||"bk"
        }
    },
    data: function () {
        return {
            dataSource: new DataSource({
                search: "product_name",
                query: {
                    //parent_id: {
                    //    value: $this.category_id && `0.${$this.category_id}`
                    //},
                    product_name: {

                    }
                },
                store: new RemoteStore({
                    method: "tree_products",
                    context: (context) => {
                        context.select_btn = false;
                        return context
                    },
                })
            })
        }
    },
    computed: {
        controlProps() {
            return {
                icon: this.controlIcon,
                wait: this.wait,
                actions: this.actions,
                label: this.label,
                status: this.status,
                readonly: this.readonly,
                disabled: this.disabled,
                opened: this.opened,
                class: this.classes,
                has_del: this.has_del
            }
        },
        opened() {
            if (this.value) {
                return true
            }
            return false;
        }
    },
    methods: {
        set_product(product) {
            if (this.short && (this.source == 'ktru' || this.source == 'bk')) {
                product.name = product.product_name;
                product.properties = product.product_properties;

                delete product.product_name;
                delete product.product_properties;
            }
            product = product && { ...product, source: this.source }
            this.$emit('change', product)
            this.$emit('input', product)
        },
        async select(product) {
            if (this.readonly)
                return;

            if (this.source == 'bk')
                product = await Vue.Dialog.SelectBurseProduct.Modal({ size: 'right' })
            else
                product = await Vue.Dialog.SelectProduct.Modal({
                    size: 'right'
                })

            if (product) {
                this.set_product(product);
            }
        },
        action(event) {
            if (event == 'clear') {
                //this.value = undefined;
                this.set_product(undefined);
            }
        },
        show_properties(event) {
            event.preventDefault();
            Vue.Dialog({
                props: ['model'],
                template: `
                  <div>
                    <header>{{model.product_name || model.name}}</header>
                    <main>
                      <iac-layout-static :value='model.product_properties || model.properties' />
                    </main>
                    <footer>
                      <ui-btn type='secondary' v-on:click.native='Close'>{{$t('Close')}}</ui-btn>
                    </footer>
                  </div>
                `
            }).Modal({
                model: this.value
            })
        }
    },
    template: `
        <ui-control v-if='source == "ktru" || source == "bk"' class='ui-entity ui-input' v-bind="controlProps" v-on:action='action' :required='required'>
            <div class='control' @click.prevent='select'>
                <div style='flex: 1 1 auto'>{{value && (value.product_name || value.name)}}</div>
                <div class='action eye' style='color: #666' v-if='eye && value && (value.product_properties || value.properties)' v-on:click.prevent.stop='show_properties'><icon>eye</icon></div> 
            </div>
        </ui-control>
        <ui-entity v-else v-bind="controlProps" :value='value' :dataSource='dataSource' v-on:input='set_product' />
    `
}

Vue.component('iac-product', ProductComponent);

Vue.Fields.product = {
    is: 'iac-product'
}