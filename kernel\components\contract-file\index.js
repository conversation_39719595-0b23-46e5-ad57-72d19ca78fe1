
import { Http, Language } from '@iac/core'

let edit_file = Vue.Dialog("editContractFileBody", {
    props: ["value", "onSave", "readonly", "number"],
    data: function () {
        return {
            source: undefined
        }
    },
    mounted() {
        this.$wait(async () => {
            if(this.value?.body){
                this.source = this.value.body
                return;
            }else if(this.value?.link){
                try {
                    let response = await fetch(this.value.link, {
                        method: "GET",
                    });

                    let body = await response.blob();
                    this.source = await body.text();
                } catch (e) {

                }
            }else if(this.number){
                let { error, data } = await Http.api.rpc('contract_ref', {
                    op: "get_contract_body",
                    ref: "contract_public_registry",
                    data: {number: this.number},
                  });;
                if(data){
                    this.source = data.body
                }
            }
        });
    },
    methods: {
        save() {
            this.$wait(async () => {
                if (this.onSave) {
                    let { error, data } = await this.onSave(this.source);
                    if (!error) {
                        Vue.Dialog.MessageBox.Question(Language.t('contract.save.close_question')).then((result) => {
                            if (result == Vue.Dialog.MessageBox.Result.Yes)
                                this.Close();
                        })
                    }
                }
            })
        },
        print() {
            const contentWindow = this.$refs.frame.contentWindow;
            contentWindow.document.open();
            contentWindow.document.write(this.source);
            contentWindow.print();
            contentWindow.document.close();
        }
    },
    template: `
        <div>
            <header>{{$t('contract')}}</header>
            <main>
                <template v-if='readonly'>
                    <ui-scroller class='' style='border: 1px solid #ccc'>
                        <div class='jodit_wysiwyg' v-html='source' style='overflow-x: auto;padding: 10px;'/>
                    </ui-scroller>
                    <iframe ref='frame' style='display: none;' />
                </template>
                <ui-html v-else :readonly='readonly' v-model='source' />

            </main>
            <footer style='position: sticky; bottom: 20px;pointer-events: none;'>
                <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
                <ui-btn type='primary' v-if='readonly' v-on:click.native='print'>{{$t('print')}}</ui-btn>
                <ui-btn type='primary' v-if='!readonly' v-on:click.native='save'>{{$t('save')}}</ui-btn>
            </footer>
        </div>
    `
})

const ContentComponent = {
    props: {
        value: String,
        readonly: Boolean,
        checkbox: {
            default: true
        }
    },
    data: function () {
        return {
            attach: this.value ? true : false
        }
    },
    computed: {

    },
    methods: {
        meta_to_field(meta={}){
            return {
                id: meta.uuid,
                name: meta.name,
                custom: meta.custom || false,
                meta: {
                    type: meta.type,
                    content_type: meta.content_type,
                    size: meta.size,
                    created_at: meta.created_at
                }
            }
        },
        append_file() {
            this.$refs.input.value = null;
            this.$refs.input.click()
        },
        edit_file() {
            edit_file.Modal({
                size: 'full',
                readonly: this.readonly,
                value: this.value,
                onSave: async (contract_body) => {
                    var file = new File([contract_body], "contract.html", { type: "text/plain" });
                    let formData = new FormData();
                    let file_name = file.name || "";
                    formData.append('data', file, file_name);
                    let response;
                    
                    if(this.value?.link){
                        response = await Http.file.form(this.value?.id, formData, {
                            method: 'PATCH'
                        });
                        if(response.data?.result)
                        response.data.result = {...this.value, meta: response.data?.result}
                    }else{
                        response = await Http.upload.form('user/put_contract_body', formData);
                    }

                    if (response?.data?.result) {
                        this.value = {...response.data?.result, custom: true};
                        this.$emit("change", this.value)
                        this.$emit("input", this.value)
                    }else if(response.error){
                        Vue.Dialog.MessageBox.Error(response.error);
                    }

                    return response
                }
            })
        },
        delete_file() {
            this.value = undefined;
            this.$emit("change", undefined)
            this.$emit("input", undefined)
        },
        open_file(e){
            e.preventDefault();

            if(this.value.custom || this.value.body){
                return this.edit_file();
            }
            this.$wait(async ()=>{
                    //await Context.User.refreshToken();
                    window.location = `${this.value.link}`
                    //return;
            })
        },
        onChange(event) {
            this.$wait(async () => {
                let file = event.target.files && event.target.files[0];

                if (file.size > 15 * 1024 * 1024) {
                    Vue.Dialog.MessageBox.Error(Language.t('file_size_exceeds_some_kb').replace('_____', Number(15 * 1024).toFixed(2)))
                    this.$refs.input.value = null;
                    return;
                }

                let formData = new FormData();
                let file_name = file.name || "";
                formData.append('data', file, file_name);
                let response = await Http.upload.form('user/put_contract_body', formData);
                if (response.error) {
                    Vue.Dialog.MessageBox.Error(response.error);
                } else {
                    this.value = response.data?.result
                    this.$emit("change", this.value)
                    this.$emit("input", this.value)
                }
            })
        }
    },
    template: `
<div>
    <template v-if='!value'>
        <template v-if='readonly'>
            {{$t("no_data")}}
        </template>
        <template v-else>
            <input ref="input" style='display: none' type="file" v-on:change='onChange' /> 
            <ui-btn type='primary' :disabled='!attach' v-on:click.native='append_file'>{{$t('contract.upload_file')}}</ui-btn>
            <ui-btn type='primary' :disabled='!attach' v-on:click.native='edit_file'>{{$t('contract.edit_file')}}</ui-btn>
            <p v-if='checkbox'><label>
                <input type='checkbox' v-model='attach' />
                {{typeof checkbox == "string" ? checkbox : $t('contract.upload_file_desc')}}
            </label></p>
        </template>

    </template>
    <template v-else>
        <div style='display: flex; align-items: baseline;'>
            <icon style='font-size: 16px; color: #009ab8;'>field</icon>
            <a style='word-break: break-word; margin: 0 4px;' v-bind:href='value.link || "javascript:void(0)" ' v-on:click='open_file'>{{ (value.custom || !value.meta.name) ? $t('contract') : value.meta.name}}</a>
            <icon style='cursor: pointer; color: #201D1D; font-size: 12px;' v-if='!readonly' v-on:click='delete_file'>delete</icon>
        </div>
        <div style='padding-left: 18px; color: #201D1D; font-size: 14px;'>
            <div v-if='value.meta.size'><label>{{$t('contract.file_size')}}:</label> <span>{{value.meta.size}}</span></div>
            <div v-if='value.meta.created_at'><label>{{$t('contract.file_date')}}:</label> <iac-date :date='value.meta.created_at' full/></div>
        </div>
    </template>
</div>    
    `
}
Vue.component('iac-contract-file', ContentComponent);
Vue.Fields['contract-file'] = { is: 'iac-contract-file' }