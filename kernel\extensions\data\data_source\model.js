import { Entity, Property } from '@iac/data'
import { Http, ModelProvider } from '@iac/core'
import Config from './../../../config'
import Context from './../../../context'

class EntityProperty extends Property {
    constructor(context) {
        super(context)

        this.update_field_react();
        
    }

    update_field_react(field = this) {
        field.attr = field.attr || {};
        field.attr.react = true;
        if (field.type == 'model') {
            field.fields.forEach(field => {
                this.update_field_react(field);
            });
        }
    }

    get meta() {
        if (this.type == 'file' && this.value) {
            return {
                ...this._meta,
                url: (value) => {
                    if (!value.id)
                        return;
                    return `${Config.api_server}/file/${value.id}`
                },
                // Это для приватного файла
                _url_mod: async (value) => {
                    if (!Context.User.access_token) {
                        Vue.Dialog.MessageBox.Info(Language.t("view_file.unauthorized"))
                        return;
                    }
                    let url = this.meta.url(value);
                    if (!url)
                        return;

                    await Context.User.refreshToken();
                    return `${url}?token=${Context.User.access_token}`
                }
            }
        }

    }
    set meta(value) {
        this._meta = value
    }

}

@ModelProvider("CrudEntityModel")
export default class EntityModel extends Entity {
    constructor(context = {}) {
        super(context)

        this.fields.forEach((field)=>{
            this[field.name] = context[field.name]
        })

    }

    get propertyModel() {
        return EntityProperty;
    }

    async send_file(file) {
        let formData = new FormData();
        formData.append('data', file, file.name);

        let { data, error } = await Http.upload.form('tender/attach', formData);
        if (error) {
            return { error };
        }

        return {
            data: {
                id: data.uuid,
                name: data.meta.name,
                meta: {
                    "type": data.meta.type,
                    "content_type": data.meta.content_type,
                    "type_group": data.meta.group,
                    "size": data.meta.size
                }
            }
        }
    }

    async init_and_send_file(model = this) {
        let model_error;

        let models = model.fields.filter((field) => {
            return field.type == 'model'
        });
        for(let _model of  models){
            let response = await this.init_and_send_file(_model);
            if(response?.error){
                model_error = model_error || {};
                model_error.data = model_error.data || [];
                model_error.data.push(response.error)
            }
        }

        let fields = model.fields.filter((field) => {
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden && field.type == 'file' && field.value;
        })


        if (!fields || fields.length <= 0)
            return {error: model_error}

        for (let field of fields) {
            let field_error;
            if (Array.isArray(field.value)) {

                for (let key in field.value) {
                    let item = field.value[key];
                    if (item?.file?.name) {
                        let { error, data: _value } = await this.send_file(item.file);
                        if (error) {
                            field_error = field_error || {
                                name: field.name,
                            }
                            field_error.data = field_error.data || [];
                            field_error.data.push({
                                i: key,
                                name: field.name,
                                message: error.message
                            })
                        } else {
                            Vue.set(field.value,key, _value);
                            //field.value[key] = _value;
                        }
                    }else if(!item.id){
                        field_error = field_error || {
                            name: field.name,
                        }
                        field_error.data = field_error.data || [];
                        field_error.data.push({
                            i: key,
                            name: field.name,
                            message: "Файл не был загружен. Пожалуйста замените его"
                        })
                    }
                }
            } else {
                let item = field.value;
                if (item?.file?.name) {
                    let { error, data: _value } = await this.send_file(item.file);
                    //value = field.property._value = _value
                    if (error) {
                        field_error = field_error || {
                            name: field.name,
                            message: error.message
                        }
                    } else {
                        field.value = _value;
                    }
                }
            }
            if (field_error) {
                model_error = model_error || {};
                model_error.data = model_error.data || [];
                model_error.data.push(field_error)
            }
        }
        if (model_error) {
            if (!model.setError(model_error) && model_error.code != "AbortError") {
                Vue.Dialog.MessageBox.Error(model_error);
            }
            return {
                error: model_error
            }
        }

    }

    get get_params() {
        if (this.validate(false))
            return
        return async () => {
            
            if (this.validate())
                return

            let response = await this.init_and_send_file();
            if(response?.error)
                return;

            let params = this.fields.filter((field) => {
                
                switch(field.type){
                    case "static":
                    case "action":
                    case "link":
                    case "widget":
                        return false;
                }

                if (field.hidden && typeof field.hidden == 'function') {
                    return !field.hidden();
                }
                return !field.hidden;
            }).map((field) => {
                if(field.type == 'model'){
                    field.reCalcValue();
                }

                let value = field.value;
                if (field.value && field.value.exp && field.value.exp.value != undefined) {
                    value = field.value.exp.value
                }

                if (Array.isArray(value) && value.length <= 0)
                    value = undefined;

                if(value == undefined || value == '')
                    value = null;

                return {
                    name: field.name,
                    value: value
                }
            }).reduce((prev, curr) => {
                prev[curr.name] = curr.value
                return prev;
            }, {})

            return params;   
        }
    }

}