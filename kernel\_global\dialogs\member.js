import Context from './../../context'
import Config from './../../config'
import ecp from '../../ecp'
import Settings from './../../settings'

import { Http, Language } from '@iac/core'
import { Entity, DataSource, ArrayStore, RemoteStore } from '@iac/data'

class Model extends Entity {
    constructor(context) {
        super(context)

        this.company_id = context.company_id;
        this.email = context.email;
        this.inn = context.inn || undefined;
        this.pinfl = context.pinfl || undefined;

        this.surname = context.surname;
        this.name = context.name;
        this.patronymic = context.patronymic;
        this.phone = context.phone;

        this.meta = context.meta || {
            
        };

        this.photo_id = this.meta && this.meta.photo && this.meta.photo
        this.photo = this.photo_id && `${Config.api_server}/file/${this.photo_id}`
    }

    props() {
        return {
            photo: {
                type: "member_image",
                label: "!",
                //hidden: true,
                attr: {
                    width: 100,
                    circle: true
                },
                onChange: async (value, oldValue) => {
                    
                    if (!value){
                        Context.User.photo = undefined;
                        if(oldValue){
                            await Http.api.rpc("update_face", {
                                meta: { ...this.meta, photo: null }
                            });
                        }
                        return;
                    }

                    this.wait = true;

                    function urltoFile(url, filename, mimeType) {
                        return (fetch(url)
                            .then(function (res) { return res.arrayBuffer(); })
                            .then(function (buf) { return new File([buf], filename, { type: mimeType }); })
                        );
                    }
                    urltoFile(value, 'photo.png', 'image/png')
                        .then(async (file) => {


                            let formData = new FormData();
                            formData.append('data', file, file.name);

                            let { data, error } = await Http.upload.form('system/public-face', formData);

                            if (data && data.result){
                                Context.User.photo = data.result.id;
                                await Http.api.rpc("update_face", {
                                    meta: { ...this.meta, photo: data.result.id }
                                });
}
                            this.wait = false;
                        });

                }
            },
            email: {
                label: "-email",
                type: "static"
            },
            inn: {
                label: "-inn",
                type: "static",
                hidden: () => {
                    return !this.inn
                }
            },
            pinfl: {
                label: "-pinfl",
                type: "static",
                //group: "!-ecp/<inn>",
                //readonly: true,
                hidden: () => {
                    return !this.pinfl
                }
            },
            ecp: {
                group: "!-ecp/<inn>",
                label: '!',
                type: "action",
                buttons: true,
                actions: [
                    {
                        label: "disconnect_ecp",
                        btn_type: "danger",
                        handler: async () => {
                            let { error, data } = await Http.api.rpc("disconnect_ecp", {});
                            if (error) {
                                Vue.Dialog.MessageBox.Error(error);
                            } else if (data) {
                                if (data.message) {
                                    Vue.Dialog.MessageBox.Success(data.message);
                                }
                                this.inn = undefined;
                                this.pinfl = undefined;
                            }
                        },
                        hidden: () => {
                            if(!ecp.provider)
                                return true;
                            return (!this.inn && !this.pinfl);// || Context.User.id != this.id
                        }
                    },
                    {
                        label: "connect_ecp",
                        btn_type: "info",
                        handler: async () => {
                            let result = await ecp.subscribe("Я люблю тех кому я нравлюсь... У них хороший вкус");
                            if (!result)
                                return;
                            if (result.error) {
                                return Vue.Dialog.MessageBox.Error(result.error);
                            }

                            let { error, data } = await Http.api.rpc("connect_ecp", {
                                sign: result.data
                            });
                            if (error) {
                                Vue.Dialog.MessageBox.Error(error);
                            } else if (data) {
                                this.inn = data.inn && data.inn;
                                this.pinfl = data.pinfl && data.pinfl;
                                this.surname = data.surname && data.surname;
                                this.name = data.name && data.name;
                                this.patronymic = data.patronymic && data.patronymic;
                                await this.update_user_data();
                            }
                        },
                        hidden: () => {
                            if(!ecp.provider)
                                return true;
                            return this.inn || this.pinfl;// || Context.User.id != this.id
                        }
                    }
                ]
            },




            surname: {
                label: "-surname",
                required: true,
                readonly: () => {
                    //if (Context.User.id != this.id && !Context.Access.policy['user_profile_change'])
                    //    return true;
                    return this.inn
                },
                attr: {
                    react: true
                },
            },
            name: {
                label: "-name",
                required: true,
                readonly: () => {
                    //if (Context.User.id != this.id && !Context.Access.policy['user_profile_change'])
                    //    return true;
                    return this.inn
                }
            },
            patronymic: {
                label: "-patronymic",
                readonly: () => {
                    //if (Context.User.id != this.id && !Context.Access.policy['user_profile_change'])
                    //    return true;
                    return this.inn
                }
            },

            phone: {
                label: "-phone",
                required: true,
                readonly: () => {
                    //if (Context.User.афсу_id != this.id && !Context.Access.policy['user_profile_change'])
                    //    return true;
                }
            },
            qr: {
                label: "-signin_by_qr_mobile",
                value: "",
                type: "qr",
                size: '50',
                description: "signin_by_qr_mobile_message",
                hidden: () => {
                    return !Settings?.qr_auth
                    }

            }
        }
    }

    async update_user_data() {
        //await Context.User.refreshToken();
        let name = `${this.surname || ""} ${this.name || ""} ${this.patronymic || ""}`
        Context.User.name = name.trim();
    }

    get save() {
        //if (Context.User.id != this.id && !Context.Access.policy['user_profile_change'])
        //    return;
        return async () => {
            const { error, data } = await Http.api.rpc("update_face", {
                // user_id: this.id,

                surname: this.surname != undefined ? this.surname : null,
                name: this.name != undefined ? this.name : null,
                patronymic: this.patronymic != undefined ? this.patronymic : null,
                phone: this.phone != undefined ? this.phone : null,

            });

            if (error) {
                if (!this.setError(error)) {
                    await Vue.Dialog.MessageBox.Error(error);
                }
            } else {
                await this.update_user_data();
            }

            return { error, data };
        }
    }

    static async get(id) {
        if (!id || id == 'profile')
            id = Context.User.face_id
        let { error, data } = await Http.api.rpc("get_face_info", { face_id: id });
        
        if (!error && data) {
            const model = new Model(data);
            

            
            return { error, data: model };
        }
        
        return { error, data: null };
    }
}

Vue.Dialog("MemberDlg", {
    props: ["id"],
    data: function () {
        return {
            model: undefined,
            error: undefined,
            qrTimer: null,
            qrChannel: null
        }
    },
    mounted() {
        this.$wait(async () => {
            const { error, data } = await Model.get(this.id);
            this.model = data;
            this.error = error;
            
            this.loadQrCode();
        })
    },
    beforeDestroy() {
        // Очищаем таймер при уничтожении компонента
        if (this.qrTimer) {
            clearTimeout(this.qrTimer);
            this.qrTimer = null;
        }
        // Закрываем веб-сокет канал
        this.closeQrChannel();
    },
    methods: {
        async save() {
            if (!this.model)
                return;
            this.$wait(async () => {
                await this.model.save();
            })
        },
        // загрузка QR-кода и открытие сокета
        loadQrCode() {
            if (!Settings?.qr_auth || !this.model?.properties?.qr) {
                return;
            }
            
            this.$wait(async () => {
                this.closeQrChannel();
                
                if (this.qrTimer) {
                    clearTimeout(this.qrTimer);
                    this.qrTimer = null;
                }
                
                const response = await iac.Core.Http.auth.rpc("create_qr_code");
                
                if (response.data?.code) {
                    this.model.properties.qr.value = response.data.code;
                    this.openQrChannel(response.data.code);
                    this.startQrTimer();
                }
            });
        },
        
        // обновление QR-кода
        updateQrCode() {
            this.loadQrCode();
        },
        // старт таймера для обновления qr
        startQrTimer() {
            if (this.qrTimer) {
                clearTimeout(this.qrTimer);
            }
            // Устанавливаем новый таймер на 2.5 минуты
            this.qrTimer = setTimeout(() => {
                this.updateQrCode();
            }, 150000);
        },
        
        // открытие веб-сокет канала
        openQrChannel(code) {
            if (!code) return;
            this.qrChannel = iac.Core.Http.api.socket.join(`qr:${code}`, (channel) => {
                channel.on('qr_code_auth_completed', () => {
                    this.updateQrCode();
                });
                
                channel.on('expired', () => {
                    this.updateQrCode();
                });
            });
        },
        
        // закрытие веб-сокет канала
        closeQrChannel() {
            if (this.qrChannel) {
                iac.Core.Http.api.socket.leave_channel(this.qrChannel);
                this.qrChannel = null;
            }
        },
    },
    template: `
      <div class='member_dlg'>
        <header>{{$t("nav.profile")}}</header>
        <main v-if='model' >
          <ui-layout :fields='model.fields' />
        </main>

        <footer>
          <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
          <ui-btn type='primary' v-if='model && model.save' v-on:click.native='save'>{{$t('save')}}</ui-btn>
        </footer>
      </div>
    `
})