import { Context, <PERSON>elo<PERSON>,Settings } from '@iac/kernel'
import { Action } from '@iac/core'
import { DataSource, RefStore, Query } from '@iac/data'

let ktru_product = new Query({
    product_id: {
      type: "entity",
      group: "choose_product",
      label: "!choose_product",
      has_del: true,
      dataSource: new DataSource({
        valueExp: "product_id",
        displayExp: "product_name",
        search: true,
        store: new RefStore({
          ref: "ref_enkt_products",
          key: "product_id",
        }),
      }),
      multiple: true,
      hidden: () => {
        return !Settings.procedures?._filter_product
      }
    },
  });

let query =  new Query({
    green: {
        type: "entity",
        label: "!green_procedures",
        group: "green_procedures",
        has_del: true,
        dataSource: [{id: true,name: "yes"},{id: false,name: "no"}],
        hidden: ()=>!Settings?.procedures?._green
    }
})
let date_query = new Query({
 close_at_gte: {
    group: 'close_at',
    type: 'date',
    label: 'from',
    has_del: true,
    bind: {
      status: 'close_at_error && {"type":"error"}'
    }
  },
  close_at_lte: {
    group: 'close_at',
    type: 'date',
    label: 'to',
    has_del: true,
    bind: {
      status: 'close_at_error && {"type":"error"}'
    }
  },
  close_at_error: {
    sync: false,
    group: 'close_at',
    type: "model",
    label: "!",
    bind: {
      value: "close_at_gte > close_at_lte",
      status: (model) => {
        return model.close_at_error && { "type": "error", "message": "Дата 'от' не может быть больше даты 'до'" }
      }
    }
  }
});

export default {
    data: function () {
        return {
            user: Context.User,
            reduction_own: new DataSource({
                query: new Query({
                    status: {
                        type: 'enum',
                        dataSource: "status_reduction",
                        value: [],
                    },
                    my_procedures: {
                        type: 'entity',
                        dataSource: [{ id: false, name: "no" }, { id: true, name: "yes" }],
                        has_del: true,
                        hidden: () => {
                            return !Develop.filter_develop
                        }
                    },
                    relation: {
                        value: "owner",
                        type: "hidden",
                        sync: false,
                    }
                },[date_query, ktru_product, query]),
                store: new RefStore({
                    ref: "ref_reduction_object",
                    injectQuery: (params) => {
                        params.fields = ["green","id","publicated_at","status","name","good_count","close_at","totalcost","currency", "lang","part_count","meta","start_price","last_price","remain_time"]
                        params.filters.close_at_error = undefined;
                        return params;
                    },

                }),
                template: 'template-reduction'
            }),
            team_reduction_party_own: new DataSource({
                query: new Query({
                    status: {
                        type: 'enum',
                        dataSource: "participant_status_reduction",
                        value: [],
                    },
                    relation: {
                        value: "participant",
                        type: "hidden",
                        sync: false,
                    }
                },
                [date_query, ktru_product, query]),
                store: new RefStore({
                    ref: "ref_reduction_object",
                    injectQuery: (params) => {
                        params.fields = ["green","id","publicated_at","status","name","good_count","close_at","totalcost","currency", "lang","part_count","meta","start_price","last_price","remain_time"]
                        params.filters.close_at_error = undefined;
                        return params;
                    },

                }),
                template: 'template-reduction'
            })
        }
    },
    methods: {
        async create() {

            let response = await Action["procedure.create"]();

            if (!response)
                return;
            let { error, data } = response;
            if (!error) {
                this.$router.push({ path: `/procedure/${data[0].proc_id}/core` });
            }
        }
    },
    template: `
    <iac-access :access='$policy.reduction_list_own || $policy.reduction_list_participant || $policy.reduction_create || $policy.reduction_offer_create'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('reductions')}}</li>
            </ol>
            <div class='title'>
                <h1>{{$t('reductions')}}</h1>
                <div v-if='0 && $policy.reduction_create'><ui-btn type='primary' v-on:click.native='create'>{{$t('create_reduction')}}</ui-btn></div>
            </div>
        </iac-section>
        <iac-section>
            <ui-layout-tab :clear='true'>
                
                <ui-layout-group key='Organize' v-if='$policy.reduction_list_own' label='Organize'>
                    <ui-data-view   :dataSource='reduction_own'/>
                </ui-layout-group> 

                <ui-layout-group key='Participate' v-if='$policy.reduction_list_participant' label='Participate'>
                    <ui-data-view  :dataSource='team_reduction_party_own'/>
                </ui-layout-group>  

            </ui-layout-tab>
        </iac-section>
    </iac-access>
    `
}