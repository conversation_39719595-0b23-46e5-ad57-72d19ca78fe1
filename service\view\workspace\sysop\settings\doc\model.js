import { Entity, DataSource } from '@iac/data'
import { Http, Language } from '@iac/core'

let format_date_time = new Intl.DateTimeFormat("ru-RU", {
    timeZone: "asia/tashkent",
   // dateStyle: 'full',
   dateStyle: 'short',
    timeStyle: 'long',
})

export default class Model extends Entity {
    static Types = ["procurement"]; // Доступные типы документов
    constructor(context) {
        super(context)

        this.id = context.id;
        this.status = context.status;
        this.version = context.id;
        this.data = context.data || {};
        
        this.meta = context.meta || {}
        this.comment = context.meta.comment || ""

        this.languages = this.data.languages || ["uz", "ru"],
        this.content = this.data.content || [];

        this.versions = new DataSource({
            //displayExp: "id",
            store: {
                ref: "ref_documentation_pages",
                context: (context)=>{
                    context.meta = context.meta || {}
                    context.name = format_date_time.format(new Date(context.updated_at));
                    //context.name = context.updated_at

                    context.desc = context.meta.comment ? `${Language.t(context.status)}: ${context.meta.comment}` : context.status
                    return context
                },
                injectQuery: (params) => {
                    params.filters = params.filters || {};
                    params.filters.type = "procurement"

                    // params.filters.updated_at_lte ="2024-03-20T05:07:45.204147Z"
                    

                    params.fields = ["id", "inserted_at", "status", "updated_at","meta"];
                    return params;
                }
            }
        })
        this.updated_at = context.updated_at;
        this.update = new Date();
    }

    async save() {
        let {error,data} = await Http.api.rpc("ref",{
            ref: "ref_documentation_pages",
            op: "update",
            data: {
                meta: {
                    ...this.meta,
                    comment: this.comment
                },
                data: {
                    languages: this.languages,
                    content: this.content
                },
                id: this.id
            }
        })
        if(error){ 
            Vue.Dialog.MessageBox.Error(error)
            return false;
        }


        this.update = new Date();
        return true;
    }

    async public(){
        if(!await this.save())
            return false;
        let hasError = (data)=>{
            for(let lng of this.languages){
                if(!data[lng])
                    return true;
            }
            return false;
        }

        // Проверка корректности данных
        for(let section of this.content){
            if(hasError(section.title)){
                Vue.Dialog.MessageBox.Error("Необходимо заполнить все разделы и подразделы")
                return false;
            }
            for(let item of section.items){
                if(hasError(item)){
                    Vue.Dialog.MessageBox.Error("Необходимо заполнить все разделы и подразделы")
                    return false;
                }
            }    
        }

        let {error,data} = await Http.api.rpc("ref",{
            ref: "ref_documentation_pages",
            op: "public",
            data: {
                id: this.id
            }

        })
        if(error){
            Vue.Dialog.MessageBox.Error(error)
            return false;
        }
        this.status = "published"
        return true;

    }

    async new_version(){

    }

    static async get(id) {

        let context = undefined;
        let error = undefined;
        if (Model.Types.includes(id)) {
            // Получаем ID последнего документа
            let { error, data } = await Http.api.rpc("ref", {
                ref: "ref_documentation_pages",
                op: "read",
                filters: {
                    type: "procurement"
                },
                limit: 1
            });

            if (error) {
                return {
                    error
                }
            } else if (!data)
                return {
                    error: {
                        messages: "Нет данных"
                    }
                }

            if (data.length == 0) {
                console.log("Создаем новый документ")
            } else {
                context = data[0];
            }
        } else {
            let { error, data } = await Http.api.rpc("ref", {
                ref: "ref_documentation_pages",
                op: "get",
                filters: {
                    id: Number(id)
                }
            });
            if (error) {
                return {
                    error
                }
            } else if (!data)
                return {
                    error: { messages: "Нет данных" }
                };
            context = data;
        }

        return { error: error, data: new Model(context) }
    }
}