export var Alert = {
  name: "ui-alert",
  props: {
      type: String,
      active: Boolean,
  },
  computed: {
      classes() {
          return [
              (() => {
                  if (this.type)
                      return this.type.split(' ').map((type) => {
                          return `ui-alert-${type}`
                      }).join(" ");
              })(),
              {
                  active: this.active,
              }]
      }
  },
  template: `<div class='ui-alert' v-bind:class="classes"><slot/></div>`
}