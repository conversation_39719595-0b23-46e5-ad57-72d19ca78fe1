export var ButtonGroup = {
    name: "ui-btn-group",
    props: {
        type: String,
        active: Boolean,
    },
    computed: {
        classes() {
            return [
                (() => {
                    if (this.type)
                        return this.type.split(' ').map((type) => {
                            return `ui-btn-group-${type}`
                        }).join(" ");
                })()]
        }
    },
    template: `<div class='ui-btn-group' v-bind:class="classes"><slot/></div>`
}