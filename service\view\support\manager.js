import Model from './_model'

export default {
    data() {
        return {
            model: undefined,
            error: undefined,
        }
    },
    mounted() {
        this.$wait(async () => {
            const { error, data: model } = await Model.get()
            this.error = error
            this.model = model
        });
    },
    methods: {
        async openRateDialog() {
            await Vue.Dialog({
                props: ["model"],
                methods: {
                    async send() { (await this.$wait(async () => this.model.save()), this.Close()) }
                },
                template: `
                    <div>
                        <header>{{model.firstName}} {{model.lastName}}</header>
                        <main>
                            <ui-layout :fields='model.fields' />
                        </main>
                        <footer>
                            <ui-btn type='secondary' @click.native='Close()'>{{$t('close')}}</ui-btn>    
                            <ui-btn type='primary' @click.native='send'>{{$t('send')}}</ui-btn>
                        </footer>
                    </div>
                `
            }).Modal({ model: this.model })
        }
    },
    template: `
        <div class="iac-support-manager">
            <template v-if='error'>
                <ui-alert v-if='!error.status'  type='danger'>{{$t('service_not_running')}}</ui-alert>
                <ui-alert v-else-if='error.status == 404'  type='warning'>{{$t('manager_not_assigned')}}</ui-alert>
                <ui-error v-else :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
            </template>
            <template v-else-if='model'>
                <div class="about">
                    <div>
                        <icon v-if="model.gender==='male'">support_agent_male</icon>
                        <icon v-else>support_agent_female</icon>
                    </div>
                    <h3>{{model.firstName}} {{model.lastName}}</h3>
                    <div>{{$t('personal_manager')}}</div>
                    <div v-if='model.phone'>{{model.phone}}</div>
                </div>
                <div class="actions">
                    <ui-btn v-if='model.phone' type='primary lg' @click.native="window.location.href ='tel:' + model.phone">{{$t('call_the_phone')}}</ui-btn>
                    <ui-btn v-if='model.email' type='info lg' @click.native="window.location.href ='mailto:' + model.email">{{$t('write_the_mail')}}</ui-btn>                                    
                </div>
                <div class="rate" @click="openRateDialog">{{$t('evaluate')}}</div>
            </template>
        </div>
`
}