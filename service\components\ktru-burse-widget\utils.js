import { Http, Language } from '@iac/core'

export default () => ({
    langs: [
        { name: "uz-UZ@cyrillic", active: true, title: "Уз" },
        { name: "uz-UZ@latin", active: false, title: 'Uz' },
        { name: "ru-RU", active: false, title: "Ру" },
        { name: "en-US", active: false, title: "En" }
    ],//брать из сеттингов и core language.js
    unitsColumns: [
        { name: 'master_unit', title: Language.t('master_unit'), type: 'radio' },
        { name: 'name', title: Language.t('name'), type: 'multilang_input' },
        { name: 'ratio', title: Language.t('ratio'), type: 'number' }
    ],
    unitsColumnsWithoutMU: [
        { name: 'name', title: Language.t('name'), type: 'multilang_input' },
        { name: 'ratio', title: Language.t('ratio'), type: 'number' }
    ],
    propsColumns: [
        { name: 'name', title: Language.t('name'), type: 'multilang_input' },
    ],
    async getGroupsQuery(parent_id) {
        const { error: err, data: data } = await Http.api.rpc("ref", {
            ref: "ref_enkt_burse_product_groups",
            op: "read",
            "limit": 51,
            "offset": 0,
            filters: { parent_id },
            fields: ['id', 'parent_id', 'name', 'meta', 'has_children']
        })
        return data
    },
    async getGroups(group_id) {
        let groups = group_id ? undefined : []
        while (!groups || (groups.length && groups[0].parent_id)) {
            const { error, data } = await Http.api.rpc("ref", {
                ref: "ref_enkt_burse_product_groups",
                op: "read",
                limit: 1,
                offset: 0,
                filters: { id: groups?.length ? groups[0].parent_id : group_id },
                fields: ['id', 'parent_id', 'name']
            })
            groups = groups ?? []
            !error && data && data?.length && groups.unshift(data[0])
        }
        groups.forEach(item => delete item.__schema__)
        return groups
    },
    async prepareGroups(currentItem) {
        currentItem.groups = currentItem.group_id ? await this.getGroups(currentItem.group_id) : []
    },
    prepareMU(currentItem) {
        let master_unitFinded = false
        currentItem.units.forEach(unit => {
            if (!master_unitFinded) {
                unit.master_unit = (unit.ratio == 1)
                master_unitFinded = true
            }
        })
        currentItem.units = [...currentItem.units]
    },
    prepareLangs(currentItem, langs) {
        currentItem.units.forEach(unit => {
            langs.forEach(lang => unit.name[lang.name] && (lang.active = true))
        })
        langs[0].active = true
    }
})