import {DataSource} from '@iac/data'

let lotItems = {
    props: ["lot","groups"],
    data: function(){
        return {
            position: -1
        }
    },
    computed:{
        accordion(){
            let _min_pos = this.$settings?.procedures?._accordion_min_position;// || 15;
            return _min_pos &&  this.lot.items.items.length >= _min_pos;
        }
    },
    methods: {
        setPosition(pos){
            if(this.position == pos)
                return this.position = -1;
            this.position = pos
        }
    },
    template: `
        <ui-list :dataSource='lot.items' raw>

            <template slot='template' slot-scope='props'>
                <div v-if='!accordion'>
                    <div class='title'>
                        <h3>{{$t('position')}} #{{props.item.id}}</h3>
                        <icon v-if='props.item.access("del_item")' class='icon' v-on:click='props.item.delete()'>delete</icon>
                    </div>
                    <ui-layout  :fields='props.item.fields' :groups='groups' />
                </div>            
                <div v-else class='accordion-item'>
                    <div style='white-space: normal;' v-bind:class='{"active" : position == props.item.index-1}' class='header' v-on:click='setPosition(props.item.index-1)'>
                        <div class='title'>
                            <div class='clamp_3' style='flex: 1 1 100%; font-size: 16px;padding-right: 6px;'>{{props.item.properties.product.value && props.item.properties.product.value.name}}</div>
                            <ui-badge style='flex: 0 0 30px;' v-if='lot.status && lot.status[props.item.id]' :model='lot.status[props.item.id]' :groups='groups'  />
                            <div >#{{props.item.index}}</div>
                        </div>
                        <div class='props' v-if='props.item.properties.product_properties && props.item.properties.product_properties.value'>
                            <span :title='product_prop.prop_name' v-for='product_prop in props.item.properties.product_properties.value'>{{product_prop.val_name}}</span>
                        </div>
                    </div>
                    <div class='body' v-if='position == props.item.index-1' style='padding: 6px;'>
                        <ui-layout :hidden='["product"]'  :fields='props.item.fields' :groups='groups' />
                    </div>
                </div>
            </template>

        </ui-list>
    `
}

let lots = {
    props: {
        items: {

        },
        groups: {

        }
    },
    data: function(){
        return {
            type: 1,
            current: 1,
            source: DataSource.get(this.items)
        }
    },
    computed: {
        current_lot_id(){
            return this.current?.id || this.current
        },
        current_lot(){
            let id = this.current_lot_id
            let lot = this.items.find(item => item.id == id);
            
            return lot || this.items[0];
        },
        active_lot_index(){
            for(let i=0;i<this.items.length;i++){
                if(this.items[i]?.id == this.current_lot_id)
                    return i
            }
            return 0;
        },
        actions(){
            //if(this.items.length <2){
            //    return []
            //}
            return [
                {
                    icon: "ui_tabs",
                    btn_type: "info",
                    active: this.type == 1,
                    handler: ()=>{this.set_type(1)}
                },
                {
                    icon: "ui_select",
                    btn_type: "info",
                    active: this.type == 2,
                    handler: ()=>{this.set_type(2)}
                }
            ]
        },
        component_is(){
            switch(this.type){
                case 1: return 'ui-layout-tab';
                case 2: return 'ui-layout-group';
                case 3: return 'div';
            }
            return 
        }
    },
    mounted(){
        
    },
    methods: {
        select(id){
            if(this.current_lot_id == id){
                return this.current = -1;
            }

            this.current = id
        },
        set_type(type){
            this.type = type
        },
        onTab(item){
            this.current = this.items[item] || this.current
        }
    },
    components: {
        lotItems: lotItems,
    },
    template: `
        <ui-layout-group label='positions' :actions_buttons='true' :actions='actions' >
            <template v-if='type == 1'>
                <ui-layout-tab :active='active_lot_index' name='lots' v-on:tab='onTab'>
                    <ui-layout-group :label='lot.lot_id+":"+(lot.desc || lot.name)' v-for='lot,index in items'>
                        <p v-if='0'>{{lot.name}}</p>
                        <ui-layout :fields='lot.fields' />
                        <lot-items :lot='lot' :groups='groups' />
                        <ui-action :actions='lot.actions' buttons/>
                    </ui-layout-group>
                </ui-layout-tab>
            </template>
            <template v-if='type == 2'>
                <ui-entity v-model='current' :dataSource='source'/>
                <div :key='current_lot.id'>
                    <ui-layout :fields='current_lot.fields' />
                    <lot-items :lot='current_lot' :groups='groups' />
                    <ui-action :actions='current_lot.actions' buttons/>               
                </div>
            </template>

        </ui-layout-group>
    `
}



export default {
    props: ["model"],
    methods:{
        onStatus(status){
            this.$emit("coreStatus",status)
        }
    },
    components: {
        lotItems: lotItems,
        lots
    },
    template: `
        <div>           
            <div class='procedure-content'>
                <div class='part' v-if='0 && model.title_part'>
                    <b>{{$t('participant')}} {{model.title_part}}</b>
                    <router-link :to='{path: "/procedure/"+model.id+"/core", query: $route.query}'><icon>delete</icon></router-link>
                </div>
                <ui-layout class='right' root='ui-layout-tab' :fields='model.fields' :actions='model.actions' v-on:status='onStatus'>
                    
                    <template slot='tech_fields'>
                        <lots v-if='model._lots_ && model._lots_.length > 1'  :items='model._lots_' :groups='["general", "tech_fields"]' />
                        <template v-else>
                            <ui-layout-group label='positions'   v-if='model.lots'>
                                <lot-items :lot='model.lots.items[0]' :groups='["general", "tech_fields"]' />

                            </ui-layout-group>
                            <ui-action v-if='model.lots' :actions='model.lots.items[0].actions' buttons/>
                        </template>

                    </template>
                    
                    <template slot='commercial_fields'>
                        <lots v-if='model._lots_ && model._lots_.length > 1'  :items='model._lots_' :groups='["commercial_fields"]' />
                        <template v-else>
                            <ui-layout-group label='positions' v-if='model.lots'>
                                <lot-items :lot='model.lots.items[0]' :groups='["commercial_fields"]' />
                            </ui-layout-group>
                            <ui-action v-if='model.lots' :actions='model.lots.items[0].actions' buttons/>
                        </template>

                    </template>
                </ui-layout>
            </div>
        </div>
    `
}
