import Model from './model'
export default class Entity extends Model {
    constructor(context = {}, ext) {
        super(context, ext);
        this.id = context.id;
        this.wait = false;
    }

    validate(status=true) {
        let errors = this.fields.filter((field) => {
            if(field.type == 'static' || field.type == 'link' || field.type == 'widget')
                return;
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).map((field) => {
            let error = field.validate(status);

            return error ? {
                name: field.name,
                message: error != true ? error : undefined
            } : undefined;
        }).filter((error) => {
            return error
        })

        if (errors && errors.length > 0){
            if(!status)
                return true
            else return this.setError({
                data: errors
            })
        }

    }
}