export var Tag = {
    name: "ui-tag",
    props: {
        dataSource: Object,
        value: {        },
        actions: {},
        status: {},
        disabled: <PERSON><PERSON><PERSON>,
        readonly: <PERSON><PERSON><PERSON>,
        label: {},
    },
    computed: {
        valueExp() { return this.dataSource.valueExp },
        displayExp() { return this.dataSource.displayExp }
    },
    mounted: function () {
        
        this.dataSource.onQueryUpdate.bind(this.onQueryUpdate);
    },
    destroyed: function () {
        this.dataSource.onQueryUpdate.unbind(this.onQueryUpdate);
    },
    methods: {
        onQueryUpdate() {
        },
        action(event) {
            if (event == 'clear') {
                this.value = undefined;
            }
        },
        set(value){
            if(value == this.value)
            return this.value = undefined;
            this.value = value;
        }
    },
    watch: {
        value: {
            immediate: true,
            async handler(val, oldVal) {
                this.$emit('input', val)
            }
        }
    },
    template: `<ui-control class='ui-tag' :label='label' opened v-on:action='action' :status='status'>
            <div class='control'>
            <label :key='item[valueExp]' :class='"ui-tag-item "+(item[valueExp] == value ? "active" : "")' v-for='(item,position) in dataSource.items' v-on:click='set(item[valueExp])'>
                {{$t(item[displayExp])}}
                <label v-if='0'><input type='checkbox' :id='item[valueExp]' :value='item[valueExp]' v-model='value' /> {{$t(item[displayExp])}}</label>
            </label>
            </div>
    </ui-control>`
}
