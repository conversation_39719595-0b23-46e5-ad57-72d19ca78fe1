import { Http } from '@iac/core'

export default class Report {
    constructor(proc_id) {
        this.proc_id = proc_id;
    }
    
    async get_available_reports() {
        let procedure = this.proc_id ? { "id": this.proc_id } : undefined;
        let { data, error } = await Http.api.rpc("get_available_reports", { "procedure": procedure });
        if (!error) {
            return {
                data: data.procedure
            };
        }
        return {data, error}
    }

    async generate_report(config) {
        return await Http.api.rpc("generate_report", config);
    }

    async reports() {
        return await Http.tender.rpc("reports", { proc_id: this.proc_id, params: {} });
    }
}