export default {
  name: 'renderTable',
  props: {
    searchResult: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      focusedItem: undefined
    }
  },
  methods: {
    setCurrentItem(item) {
      this.$emit('setCurrentItem', item)
    },
    getNameStyle(item) {
      return item.blocked ? { backgroundColor: 'red' } : {}
    }
  },
  template: `
    <div class="iac--ktru__table"  @mouseleave="focusedItem=undefined" @keydown.enter="e=>setCurrentItem(focusedItem)">
      <div>
        <div>{{$t('code').toUpperCase()}}</div>
        <div>{{$t('product_name').toUpperCase()}}</div>
      </div>
      <div v-for="item in searchResult" @mouseover="focusedItem=item" :class="{ active: focusedItem==item }" @click="e=>setCurrentItem(item)">
        <div>{{item.code}}</div>
        <div :title="item.currentName.VALUE" :style="getNameStyle(item)">{{item.currentName.VALUE}}</div>
      </div>
    </div>
    `
}