import Data from "../../../../data/src";
import Query from "../../../../data/src/query";

// Костыль
var remove_value = (value, id)=>{
    let index = value.indexOf(id)
    if (index !== -1) {
        value.splice(index, 1);
    }
    return value;
}

/*
Array.prototype.remove = function () {
    var what, a = arguments, L = a.length, ax;
    while (L && this.length) {
        what = a[--L];
        while ((ax = this.indexOf(what)) !== -1) {
            this.splice(ax, 1);
        }
    }
    return this;
};*/

export var EnumTree = {
    name: "ui-enum-tree",
    props: {
        dataSource: Object,
        value: {
            type: Array,
            default: []
        },
        actions: {},
        status: {},
        disabled: Bo<PERSON>an,
        readonly: <PERSON><PERSON>an,
        label: {},
    },
    data: function () {
        return {
            component_value: [...this.value]
        }
    },
    inject: ["parent", "tmp_value"],
    provide() {
        return {
            tmp_value: this.tmp_value || this.component_value,
            parent: this.parent || this
        }
    },

    computed: {
        valueExp() { return this.dataSource.valueExp },
        displayExp() { return this.dataSource.displayExp },
        showLoader() {
            return (this.dataSource.state & Data.DataSource.STATE_REQUEST) != 0;
        }
    },
    mounted: function () {
        this.dataSource.onItemsUpdate.bind(this.onItemsUpdate);
    },
    destroyed: function () {
        this.dataSource.onItemsUpdate.unbind(this.onItemsUpdate);
    },
    methods: {
        is_parent_select(item) {

            item.path = item.path || ((id) => {
                id = id + "";
                let _path = [];
                let i = id.indexOf('.');
                while (i >= 0) {
                    _path.push(id.substr(0, i))
                    i = id.indexOf('.', i + 1);
                }
                return _path;
            })(item.code || item.id || "")

            let reg_text = `^(${item.path.join("|")})$`;
            var reg = new RegExp(reg_text, "i");
            return this.value.some(e => reg.test(e));

        },
        is_checked(item) {
            if (this.value.includes(item.id))
                return true;
            return this.is_parent_select(item);
        },
        input_classes(item) {
            var reg_tri = new RegExp(`^${item.id}\\.`, "i");
            return [
                (() => {

                })(),
                {
                    //"always": this.is_parent_select(item),
                    "tri-state": this.value.some(e => reg_tri.test(e))
                }
            ]
        },
        onItemsUpdate() {
            if(this.dataSource.items && this.dataSource.items.length == 1){
                let item = this.dataSource.items[0];
                if(!item.dataSource)
                    this.showChild(1, item);
            }
        },
        action(event) {
            if (event == 'clear') {
                this.tmp_value = [];
            }
        },
        display(item) {
            let name;

            if (typeof this.displayExp == 'function') {
                name = this.displayExp(item)
            } else {
                name = item[this.displayExp];
            }

            return this.$t(name);
        },
        showChild(position, item, event) {
            if (item.has_children) {
                event && event.preventDefault();
                if (item.dataSource) {
                    this.$set(item, "show", !item.show);
                } else {
                    this.$set(item, "show", true);
                    item.dataSource = new Data.DataSource({
                        limit: this.dataSource.take,
                        query: new Query({
                            parent_id: item.id
                        }),
                        valueExp: this.dataSource.valueExp,
                        displayExp: this.dataSource.displayExp,
                        iconExp: this.dataSource.iconExp,
                        descExp: this.dataSource.descExp,
                        store: this.dataSource.store
                    });
                }
            }
        },
        checked(checked, item) {
            
            let value = this.tmp_value || this.component_value;
            
            if (checked) {
                value.push(item.id);
                
                // Удаляем все дочерние
                let reg_text = `^(${item.id})\\.`;
                var reg = new RegExp(reg_text, "i");

                let ids = [];
                for (let i = value.length-1; i >= 0; i--) {
                    if (reg.test(value[i])) {
                        ids.push(value[i]);
                    }
                }
                ids.forEach(id => {
                    value = remove_value(value, id);
                });

                // Проверяем все ли выбраны элементы
                for(let i of this.dataSource.items){
                    if(value.indexOf(i.id) == -1){
                        if(this.parent){
                            this.$emit('checked', false);
                        }else{
                            this.$emit('input', [...value]);
                        }
                        return;
                    }
                }

                if(this.parent){
                    this.$emit('checked', true);
                }else{
                    this.$emit('input', [...value]);
                }
            }
            else if (this.is_parent_select(item)) {
                this.dataSource.items.forEach(element => {
                    
                    if (element.id != item.id)
                    value.push(element.id);
                });
                if(this.parent){
                    this.$emit('checked', false);
                }else{
                    this.$emit('input', [...value]);
                }
            } else {
                value = remove_value(value, item.id);
                if(this.parent){
                    this.$emit('checked', false);
                }else{
                    this.$emit('input', [...value]);
                }
            }
        }
    },
    watch: {
        tmp_value111: {
            immediate: false,
            async handler(val, oldVal) {
                
                if (!this.parent) {
                    
                    this.value = val;
                    this.$emit('input', val);
                }
            }
        }
    },
    template: `<ui-control class='ui-enum-tree' :label='label' opened v-on:action='action' :status='status' :readonly='readonly'>
            <div class='control'>
                <div v-if='showLoader' class='loader'>1</div>
                <div :key='item.key' v-for='(item,position) in dataSource.items'>
                    <div class='label ui-checkbox'>
                        <label>
                        <input v-if='0' type='checkbox' :id='item[valueExp]' :value='item[valueExp]' v-model='value' />
                        <input v-else :disabled='readonly' v-bind:class='input_classes(item)' type='checkbox' :checked='is_checked(item)' :id='item[valueExp]' :value='item[valueExp]' v-on:input="e=>checked(e.target.checked,item)" />
                        <div>   

                            <span class='link' v-if='item.has_children' v-on:click='event=>showChild(position, item,event)'>{{display(item)}}</span>
                            <span v-else>{{display(item)}}</span>
                            <div v-if='item.desc' style='color: #999; font-size: 12px;'>{{item.desc}}</div>

                        </div>
                        </label>
                        
                    </div>
                    <div v-if='item.has_children && item.show'>
                        <ui-enum-tree :value='value'  :dataSource='item.dataSource' v-on:checked="val=>checked(val,item)" :readonly='readonly' />
                    </div>
                </div>
            </div>
    </ui-control>`
}
