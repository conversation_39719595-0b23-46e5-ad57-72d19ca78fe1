import { DataSource, RefStore, Query } from '@iac/data'
import { Http, Action } from '@iac/core';

export default {
    data() {
        return {
            arbitrationComplaintDataSource: new DataSource({
                query: {
										status: {
												group: "status",
												label: "!",
												type: "enum",
												dataSource: "arbitration_complaint_status_private"
										},
										// Время отправки жалобы на рассмотрение
										// Период формирования жалобы
										start_date: {
												group: "complaint.formation_range",
												type: "date",
												label: "!",
												prefix: this.$t("from"),
												has_del: true,
                                                bind: {
                                                    status: `date_error && {"type":"error"}`
                                                }
										},
										end_date: {
												group: "complaint.formation_range",
												type: "date",
												label: "!",
												prefix: this.$t("to"),
												has_del: true,
                                                bind: {
                                                    status: `date_error && {"type":"error"}`
                                                }
										},
                                        date_error: {
                                            sync: false,
                                            group: "complaint.formation_range",
                                            type: "model",
                                            label: "!",
                                            bind: {
                                                value: "start_date > end_date",
                                                status: `date_error && {"type":"error","message":"${this.$t("to_from_error")}"}`
                                            }
                                        }
								},
                store: {
                    method: "contract_ref",
                    ref: "arbitration_complaint_private_registry",
                    injectQuery: (params) => (params.filters = {
                        ...params.filters,
                        date_error: undefined
                    }, params)
                },
                actions: [
                    {
                        label: 'nav.complaints_add',
                        hidden: true,
                        handler: () => this.add_arbitration_complaint()
                    }
                ]
            })
        }
    },
    methods: {
        async add_arbitration_complaint() {
            let { data, error } = await Http.api.rpc("arbitration_complaint_create_dlg")
            if (error && error.code != "AbortError") {
                Vue.Dialog.MessageBox.Error(error);
                return
            }
            this.arbitrationComplaintDataSource.reload()
        },
    },
    template: `
    <iac-access :access='$policy.exchange_arbitration_complaint_list || $policy.exchange_arbitration_complaint_create || $policy.exchange_arbitration_complaint_delete || $policy.exchange_arbitration_complaint_edit || $policy.exchange_arbitration_complaint_moderate || $policy.exchange_arbitration_complaint_postpone || $policy.exchange_arbitration_complaint_reject || $policy.exchange_arbitration_complaint_return || $policy.exchange_arbitration_complaint_review || $policy.exchange_arbitration_complaint_revoke || $policy.exchange_arbitration_complaint_schedule_meeting || $policy.exchange_arbitration_complaint_vote'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('arbitration_complaints')}}</li>
            </ol>
            <div class='title'>
                <h1>{{$t('arbitration_complaints')}}</h1>
            </div>
        </iac-section>

        <iac-section>
					<ui-data-view :dataSource='arbitrationComplaintDataSource'/>
        </iac-section>
    </iac-access>
`
}

