import { ModelProvider } from '@iac/core'

export default class Model extends ModelProvider.CrudEntityModel {
    constructor(context){
        super(context)

    }

    props(){
        return {
            unit:{
                label: "-unit",
                type: "static",
                bind: {
                    value: 'contracts[0].unit'
                }
            },
            contracts: {
                type: "entity",
                has_del: true,
                multiple: true,
                dataSource: {
                    search: true,
                    query: {
                        status: 'approved',
                        unit: {
                            bind: {
                                value: ()=>{
                                    return this.unit
                                }
                            }
                        },
                    },
                    store: {
                        ref: 'ref_exchanges_contracts'
                    }
                }
            }
        }
    }

}