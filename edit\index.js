
import { Marked, Language } from '@iac/core'
import { Entity } from '@iac/data'

class FormModel extends Entity {
    constructor(context) {
        super(context)
        this.source = localStorage.getItem("edit_document_form");
        this.source = this.source || `{}`

        this.update_result(this.source);
    }
    update_result(value) {
        try {
            let fields = new Function(`return ${value}`)()
            this.properties.result.setAttributes({
                fields: { ...fields }
            })

        } catch (e) {
            this.properties.source.status = {
                type: "error",
                message: e
            }
        }
    }
    props() {
        let $this = this;
        return {
            source: {
                type: "code-editor",
                onChange: (value) => {
                    localStorage.setItem("edit_document_form", value)
                    this.update_result(value);

                },
            },
            result: {
                type: "model",
                label: "!",
                fields: {

                }
            }
        }
    }
}


Marked.use({
    extensions: [{
        name: 'iac-field',
        level: 'inline',
        start(src) {
            return src.match(/::/)?.index;
        },
        tokenizer(src, tokens) {

            const rule = /^::([^::\n]+)::/;
            const match = rule.exec(src);
            if (match) {
                let field = this.lexer.inlineTokens(match[1].trim());
                if (!field || !field[0])
                    return;
                field = field[0]
                return {
                    type: 'iac-field',
                    raw: match[0],
                    field: {
                        name: field.text,
                        label: field.text
                    },
                };
            }
        },
        renderer({ field }) {
            if (!field)
                return '';

            return `
            <field-edit :form='form' field='${field.label}' />
            `
            return `
            <span id='${field.label}' style='background: rgb(215 253 196); border-bottom: 1px solid; padding: 0 6px;' v-if='form.properties["${field.label}"]' contentEditable='true' v-on:click.stop.prevent='' v-on:input.stop.prevent='updateForm'>{{form["${field.label}"]}}</span>
            <field-edit field='${field.label}' />
            <span v-else v-on:click.stop.prevent='' contentEditable='true' placeHolder='${field.label}' style='background: rgb(215 253 196); border-bottom: 1px solid; padding: 0 6px;'>
                ${field.label}
            </span>
        `
            return `
            <inline-input :value='model.properties.test.value' v-on:input='value=>model.properties.test.value=value' />
        `
            return `
            <ui-field :model='model.properties.test' />
        `
        }
    }]
})



let doc = localStorage.getItem("edit_document")
try {
    doc = JSON.parse(doc) || {};
    doc.title = doc.title
    doc.city = doc.city
    doc.paragraph = doc.paragraph
    doc.sections = doc.sections
} catch (e) {
    doc = {
        title: "",
        city: "г. Ташкент",
        paragraph: "",
        sections: [
            {
                title: "title {{$t('close')}}",
                items: [
                    "1  {{$t('close')}}",
                    "2  {{$t('close')}}"
                ]
            },
            {
                title: "title",
                items: [
                    "1",
                    "2"
                ]
            },
            {
                title: "title",
                items: [
                    "1",
                    "2",
                    "3"
                ]
            }
        ]
    }
}

var markDown = {
    props: ["text", "test"],
    data: function () {
        return {
            edit: false,
            tt: "awdawd"
        }
    },
    computed: {
        component() {
            return {
                props: ["text", "test"],
                methods: {
                    updateForm() {
                        return false
                    }
                },
                template: `<div>${Marked.parse(this.text || "init")}</div>`
            }
        }
    },
    methods: {
        onEdit() {
            this.edit = !this.edit
            if (!this.edit) {
                this.text = this.tt;
            }
        },
        updateForm() {
            return false
        },
        updateMessage({ target }) {
            this.tt = target.innerText.trim();
        }
    },
    template: `<div><ui-btn v-on:click.native='onEdit'>edit</ui-btn>
        <component v-if='!edit' :is='component' :text='text' :test=11 />
        <pre ref='textarea' v-else  @input='updateMessage' contentEditable='true'>{{text}}</pre>
   </div>`

}

var itemEdit = {
    props: ["value", "contract", "default", "form"],
    data: function () {
        return {
            text: undefined,
            edit: false,
            mark: this.value
        }
    },
    watch: {
        value: {
            immediate: true,
            async handler(value, oldValue) {

                this.text = (value || this.default || "нет данных").replace(/({{\S+}})/ig, '<span class="value">$1</span>');

            }
        }
    },
    computed: {
        component() {
            return {
                props: ["text", "contract", "form"],
                template1: "<div>{{text}}</div>",
                methods: {
                    updateForm({ target }) {
                        this.form[target.id] = target.innerText.trim()
                        return false;
                    },
                },
                components: {
                    fieldEdit: {
                        props: ["field", "form"],
                        data: function () {
                            return {
                                focus: false,
                                text: this.form[this.field],
                            }
                        },
                        computed: {
                            value() {
                                return this.form[this.field]
                            },
                            label(){
                                let prop = this.form.properties[this.field];
                                if(prop){
                                    return prop.label || prop.name || this.field
                                }
                                return this.field;
                            }
                        },
                        watch: {
                            value: {
                                immediate: false,
                                async handler(value, oldValue) {
                                    console.log("value!!!!!!!!!!!!!!!!!!!!!", this.focus);
                                    this.updateText();
                                    //if (!this.focus){
                                    //    this.text = value
                                    //}
                                }
                            }
                        },
                        mounted() {
                            this.updateText();
                        },
                        methods: {
                            updateText() {
                                //if(!this.form || !this.form.properties[this.field])
                                //    return;
                                this.text = this.form[this.field];
                                if (!this.$refs.field)
                                    return;
                                if (!this.focus) {
                                    let value = this.form[this.field];
                                    let label = this.field;
                                    this.$refs.field.innerHTML =  value || " ";// `<span style='color: #0004;'>${label}</span>`
                                }
                            },
                            updateForm({ target }) {
                                this.text = target.innerText.trim()
                                if (!this.form.properties[this.field]) {
                                    return;
                                }

                                
                                this.form[this.field] = target.innerText.trim()


                                //this.$emit("input", target.innerText.trim());
                                //this.form[this.field] = target.innerText.trim();
                                return false;
                            },
                            focusin() {
                                this.focus=true
                                //let value = this.form[this.field];
                                //this.$refs.field.innerHTML =  value || "              " 
                            },
                            focusout() {
                                this.focus=false
                                //let value = this.form[this.field];
                                //let label = this.field;
                                //this.$refs.field.innerHTML =  value || `<span style='color: #0004;'>${label}</span>`
                            },
                        },
                        template: `
                            <span style='background: rgb(215 253 196); border-bottom: 1px solid;min-width: 150px;position: relative;    display: inline-block;'>
                                <span :title='field' v-on:focusin='focusin' v-on:focusout='focusout' ref='field' v-on:click.stop.prevent='' contentEditable='true' style='display: inline-block;    min-width: 100%; padding: 0 6px;' v-on:input.stop.prevent='updateForm'>
                                
                                </span>
                                <span v-if='!focus && !text' style='left:0; bottom: 0;    font-size: 12px; pointer-events: none;white-space: nowrap;position: absolute;width: 100%;    text-align: center;    opacity: 0.5;    overflow: hidden;    text-overflow: ellipsis;'>
                                    {{label}}
                                </span>
                            </span>

                            
                        `,
                        template1: `
                        <span id='field.label' style='background: rgb(215 253 196); border-bottom: 1px solid; padding: 0 6px;' v-if='form.properties["field.label]' contentEditable='true' v-on:click.stop.prevent='' v-on:input.stop.prevent='updateForm'>{{}}</span>
                        `
                    }
                },
                template: `<div style='margin: -1em 0px;'>${Marked.parse(this.text || "init")}</div>`
            }
        }
    },
    methods: {
        updateForm() {

        },
        updateMessage({ target }) {

            this.value = target.innerHtml
            this.$emit("input", this.value)
        },
        onEdit1() {
            //this.$emit("input",222)
        },
        focus() {
            // this.edit = true;
        },
        focusout() {
            // this.mark = this.value
            // this.text = this.value
            // this.edit = false;
        },
        async onEdit() {
            var result = await Vue.Dialog.MessageBox.Form({
                size: "lg",
                fields: [
                    {
                        name: "text",
                        label: "Подраздел",
                        type: "text",
                        value: this.value,
                        status: undefined,
                        required: true

                    }
                ]
            })

            if (!result || !result.text)
                return;
            //this.text = result.text;
            this.$emit("input", result.text)
        }
    },
    template: `
        <div class='edit-item' style='flex: 1 1 100%; cursor: pointer;' v-on:click='onEdit' >
            <component :form='form' :contract='contract' :key='text' :is='component' :text='text' />
        </div>
    `,
    template1: `
        <div style='flex: 1 1 100%; border: 1px solid #eee; '>
            <pre v-on:focusin='focus' v-on:focusout='focusout' v-if='edit' contenteditable='true' style='font-size: 14px; font-weight: normal;'  @input='updateMessage' >{{text}}</pre>
            <component :form='form' v-on:click.native='edit=true' :key='mark' v-else :is='component' :text='value' />
        </div>

    `
}

var docSection = {
    props: ["model", "number", "contract", "form"],
    components: {
        itemEdit
    },
    methods: {
        updateForm() {

        },
        async delete_model(){
            if (await Vue.Dialog.MessageBox.Question("Вы действительно хотите удалить данный раздел?", "Warning") != Vue.Dialog.MessageBox.Result.Yes){
                return;
            }
            this.$emit("delete")
        },
        async delete_model_item(index){
            if (await Vue.Dialog.MessageBox.Question(Language.t("question.delete_section"), Language.t("question.title_warning")) != Vue.Dialog.MessageBox.Result.Yes){
                return;
            }
            this.model.items = this.model.items || []
            this.model.items.splice(index,1);
        }
    },
    template: `
        <div style='margin: 20px 0' class='doc-section'>
            <div class='title'>
                <span>{{number}}</span>&nbsp;<itemEdit  :form='form' style='flex: unset;' :contract='contract' v-model='model.title' /> 
                <div class='action'><span v-on:click='delete_model'><icon style='color: #f00;'>trash</icon></span></div>
            </div>
            <div class='items'>
                <div class='item' style='display: flex;font-size: 14px;    margin: 1em 0;' v-for='item,index in model.items'>
                    <span>{{number}}.{{index+1}}</span>&nbsp;
                    <itemEdit  :form='form' :contract='contract' v-model='model.items[index]' />
                    <div class='action'><span  v-on:click='delete_model_item(index)'><icon style='color: #f00;'>trash</icon></span></div>
                </div>
            </div>
        </div>
    `
}


var EditComponent = {
    data: () => ({
        text: "awdawd",
        doc: doc,
        formModel: new FormModel(),
        contract: {
            number: 1111,
            city: "г. Ташкент",
            date: "01.01.2024",
            type: "tender",
        }
    }),
    computed: {
        component() {
            return {
                props: ["text", "test"],
                template: `<div>${Marked.parse(this.text || "")}</div>`
            }
        }
    },
    components: {
        markDown,
        docSection,
        itemEdit
    },
    mounted() {
        setTimeout(() => {
            this.text = "**SSSS** *{{test}}* SSSSSS"
        }, 2000)

    },
    methods: {
        //execCommand(сommand, showDefaultUI, аrgument) {
        //    document.execCommand("insertHTML", false, "<label>123</label>");
        //    return false;
        //},
        updateForm() {

        },
        save() {
            localStorage.setItem("edit_document", JSON.stringify(this.doc));
        },
        onDeleteSection(index){
            this.doc.sections = this.doc.sections || [];
            this.doc.sections.splice(index,1)

        },
        async addSection(afterIndex){

            var result = await Vue.Dialog.MessageBox.Form({
                size: "lg",
                fields: [,
                    {
                        name: "title",
                        label: "-Заголовок",
                        type: "string",
                        required: true,
                        value: undefined,
                        status: undefined

                    },
                    {
                        name: "text",
                        label: "Подраздел",
                        type: "text",
                        required: true,
                        value: undefined,
                        status: undefined

                    }
                ]
            })

            if(result){
                this.doc.sections = this.doc.sections || [];
                this.doc.sections.splice(afterIndex, 0, {
                    title: result.title,
                    items: result.text ? [result.text] : []
                });            
                
                console.log(this.doc.sections);
            }



        },
        async addSectionItem(section){
            var result = await Vue.Dialog.MessageBox.Form({
                size: "lg",
                fields: [
                    {
                        name: "text",
                        label: "Подраздел",
                        type: "text",
                        required: true,
                        value: undefined,
                        status: undefined
                    }
                ]
            })
            if(result){
                this.doc.sections[section].items.push(result.text)   
            }

            
        }
    },
    template: `
        <div>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('edit')}}</li>
                </ol>
                <div class='title'>
                    <h1>{{$t('form_editor')}}</h1>
                    <ui-btn v-on:click.native='save'>{{$t("save")}}</ui-btn>
                </div>
            </iac-section>
            <iac-section>
                <ui-layout-tab>
                <ui-layout-group class='horizontal' label='договор'>
                    <ui-layout-tab style='max-width: 300px;'>
                        <ui-layout-group label='Опрос'>
                            <ui-field :model='formModel.properties.result' />
                        </ui-layout-group>
                    </ui-layout-tab>
                    <div>
                        <div style='text-align: center;    font-weight: bold; font-size: 18px;'>
                            <itemEdit :form='formModel.properties.result' :contract='contract' v-model='doc.title' default='Договор' />
                        </div>
                        <div style='display: flex; font-size: 14px;'>
                            <div style='flex: 1 1 auto'>
                                <itemEdit :form='formModel.properties.result' :contract='contract' v-model='doc.city' default='город' />
                            </div>
                            <div style='flex: 1 1 auto;text-align: right;'>
                            <itemEdit :form='formModel.properties.result' :contract='contract' v-model='doc.date' default='дата договора' />
                            </div>
                        </div>
                        <p style='margin: 20px 0;font-size: 14px;'>
                            <itemEdit :form='formModel.properties.result' :contract='contract' v-model='doc.paragraph' default='' />
                        </p>
                        <ui-btn type='info xs' v-on:click.native='addSection(0)'>Добавить раздел</ui-btn>
                        <div class='doc-section-wrapper' v-for='section,index in doc.sections'>
                            <docSection v-on:delete='onDeleteSection(index)' :form='formModel.properties.result' :contract='contract' :model='section' :number='index+1' />
                            
                            <ui-btn-group class='action' style='min-height: 22px'>
                                <ui-btn type='info xs' v-on:click.native='addSection(index+1)'>Добавить раздел</ui-btn>
                                <ui-btn type='info xs' v-on:click.native='addSectionItem(index)'>Добавить подраздел</ui-btn>
                            </ui-btn-group>
                        </div>
                    </div>
                </ui-layout-group>
                <ui-layout-group label='Редактор'>
                    <ui-field :model='formModel.properties.source' />
                </ui-layout-group>
                </ui-layout-tab>

            </iac-section>
        </div>
    `
}

Vue.component("edit-component", EditComponent);