import { Property,DataSource } from "@iac/data" 
import eim<PERSON> from "@iac/eimzo" 

export default class eimzoProvider extends Property {
    constructor(context){
        super(context)
        console.log(context);
    }
    props(){
        return {
            certificate: {
                type: "entity",
                label: "-certificate",
                dataSource: DataSource.get("eimzo")
            }
        }
    }
    static async getInfo(data) {
        return data?.certificate?.vo;
    }

    static async createPkcs7(message, data){
        console.log(data.certificate.vo);
        let keyId = await eimzo.loadKey(data.certificate.vo);
        let response = await eimzo.createPkcs7(keyId, message);
        //return await eimzo.createPkcs7(keyId, message);

        return response;
    }
}