import { Http, Language } from '@iac/core'

export default class BurseProductRequestMergeModel {
    constructor(product) {
        this.id = product.id
        this.motherland = product.motherland
        this.meta = product.meta
        this.message = product.message ?? ""
        this.tnved = product.tnved
        this.skp = product.skp
        this._name = product.name
        this._units = product.units ?? []
        this.findMasterUnit()
        this._properties = product.properties ?? []
        this._groups = []
        this.group_id = product.group_id
        this.getGroups()

        this.langs = [
            { name: "uz-UZ@cyrillic", active: true, title: "Уз" },
            { name: "uz-UZ@latin", active: false, title: 'Uz' },
            { name: "ru-RU", active: false, title: "Ру" },
            { name: "en-US", active: false, title: "En" }
        ]//брать из сеттингов и core language.js
        this.unitsColumns = [
            { name: 'master_unit', title: Language.t('master_unit'), type: 'radio' },
            { name: 'name', title: Language.t('name'), type: 'multilang_input' },
            { name: 'ratio', title: Language.t('ratio'), type: 'number' }
        ]
        this.unitsColumnsWithoutMU = [
            { name: 'name', title: Language.t('name'), type: 'multilang_input' },
            { name: 'ratio', title: Language.t('ratio'), type: 'number' }
        ]
        this.propsColumns = [
            { name: 'name', title: Language.t('name'), type: 'multilang_input' },
        ]

        this.prepareLangs()
    }

    disabledFinalApprove = () => {
        return this._units.length == 0
            || this._properties.some(prop =>
                this.langs.some(lang => lang.active && !prop.name[lang.name])
                || prop.values.length == 0
                || prop.values.some(val => this.langs.some(lang => lang.active && !val.name[lang.name]))
            )
            || !this._units.find(unit => unit.master_unit)
    }

    disabledFinalDisapprove = () => {
        return this.langs.some(lang => lang.active && !this.name[lang.name])
            || this._units.length == 0
            || this._units.some(unit => this.langs.some(lang => lang.active && !unit.name[lang.name]))
            || this._properties.some(prop =>
                this.langs.some(lang => lang.active && !prop.name[lang.name])
                || prop.values.length == 0
                || prop.values.some(val => this.langs.some(lang => lang.active && !val.name[lang.name]))
            )
            || !this._units.find(unit => unit.master_unit)
            || !this.message
    }

    get name() {
        return this._name
    }

    set name(newValue) {
        this._name = { ...newValue }
    }

    get units() {
        return this._units
    }

    addUnit = () => {
        const newUnit = { ratio: 1, name: {}, is_new: true }
        this.langs.forEach(lang => newUnit.name[lang.name] = "")
        this._units.push(newUnit)
    }

    updateUnit = (index, newValue) => {
        this._units[index] = { ...newValue }
        if (newValue.master_unit) {
            this._units.forEach((unit, uIndex) => unit.master_unit = (uIndex == index))
        }
        this._units = [...this._units]
    }

    deleteUnit = (index) => {
        const needToFindNewMasterUnit = this._units[index].master_unit
        this._units.splice(index, 1)
        if (needToFindNewMasterUnit) {
            this.findMasterUnit()
        } else {
            this._units = [...this._units]
        }
    }

    findMasterUnit = () => {
        let master_unitFinded = false
        this._units.forEach(unit => {
            if (!master_unitFinded && unit.ratio == 1) {
                unit.master_unit = true
                master_unitFinded = true
            } else {
                unit.master_unit = false
            }
        })
        this._units = [...this._units]
    }

    get props() {
        return this._properties
    }

    addProp = () => {
        const newProp = { values: [], name: { is_new: true } }
        this.langs.forEach(lang => newProp.name[lang.name] = "")
        this._properties.push(newProp)
    }

    updatePropName = (propIndex, newName) => {
        this._properties[propIndex].name = { ...newName, is_new: true }
        this._properties = [...this._properties]
    }

    deleteProp = (index) => {
        this._properties.splice(index, 1)
    }

    addPropVal = (propIndex) => {
        const newPropVal = { name: {}, is_new: true }
        this.langs.forEach(lang => newPropVal.name[lang.name] = "")
        this._properties[propIndex].values.push(newPropVal)
        this._properties = [...this._properties]
    }

    updatePropVal = (propIndex, valIndex, newValue) => {
        this._properties[propIndex].values[valIndex] = { ...newValue, is_new: true }
        this._properties[propIndex].values = [...this._properties[propIndex].values]
        this._properties = [...this._properties]
    }

    deletePropVal = (propIndex, valIndex) => {
        this._properties[propIndex].values.splice(valIndex, 1)
        this._properties[propIndex].values = [...this._properties[propIndex].values]
        this._properties = [...this._properties]
    }

    get groups() {
        return this._groups
    }

    updateGroups = (newGroups = []) => {
        this._groups = [...newGroups]
        this.group_id = newGroups?.[newGroups?.length - 1]?.id ?? null
    }

    clearGroup = () => {
        this.groups = []
        this.group_id = null
    }

    getGroups = async () => {
        const group_id = this.group_id
        let groups = []
        if (group_id) {
            while (!groups.length || groups[0].parent_id) {
                const { error, data } = await Http.api.rpc("ref", {
                    ref: "ref_enkt_burse_product_groups",
                    op: "read",
                    limit: 1,
                    offset: 0,
                    filters: { id: groups?.length ? groups[0].parent_id : group_id },
                    fields: ['id', 'parent_id', 'name']
                })
                groups = groups ?? []
                !error && data && data?.length && groups.unshift(data[0])
            }
            groups.forEach(item => delete item.__schema__)
        }
        this._groups = groups
    }

    prepareLangs = () => {
        this._units.forEach(unit => {
            this.langs.forEach(lang => unit.name[lang.name] && (lang.active = true))
        })
        this.langs[0].active = true
        this.langs = [...this.langs]
    }

    getGroupsQuery = async (parent_id) => {
        const { error: err, data: data } = await Http.api.rpc("ref", {
            ref: "ref_enkt_burse_product_groups",
            op: "read",
            "limit": 51,
            "offset": 0,
            filters: { parent_id },
            fields: ['id', 'parent_id', 'name', 'meta', 'has_children']
        })
        return data
    }

    export = () => {
        const { id, meta, _name: name, skp, tnved, _units: units, _properties: properties, groups, message, motherland } = this

        const master_unit = units.find(unit => unit.master_unit)
        if (master_unit) {
            delete master_unit.master_unit
            delete master_unit.is_new
            delete master_unit.name.is_new
        }

        const exportProduct = {
            id,
            meta,
            product: {
                name: { ...name },
                group_id: groups?.[(groups?.length ?? 1) - 1]?.id ?? null,
                skp,
                tnved,
                master_unit,
                units: units.map(({ name, ratio }) => ({ name: { ...name }, ratio })),
                properties: properties.map(({ name, values }) => ({
                    name,
                    values: values.map((name) => ({ ...name }))
                })),
                message,
                motherland
            }
        }

        delete exportProduct.product.name.is_new
        exportProduct.product.units.forEach(unit => (delete unit.is_new, delete unit.name.is_new, delete unit.master_unit))
        exportProduct.product.properties.forEach(prop => {
            delete prop.name.is_new
            delete prop.is_new
            prop.values.forEach(val => (delete val.name.is_new, delete val.is_new))
        })

        return exportProduct
    }
}
