import { Http } from '@iac/core'

export default Vue.Dialog({
    data: function () {
        return {
            error: undefined,
            fields: [
                {
                    name: "fio",
                    group: 'company-',
                    status: undefined,
                    value: undefined,
                    required: true
                },
                {
                    name: "inn",
                    group: 'company-',
                    status: undefined,
                    value: undefined,
                    required: true
                },
                
                {
                    name: "share_count",
                    group: 'share/<group>',
                    type: "number",
                    status: undefined,
                    value: undefined,
                },
                {
                    name: "share_type",
                    group: 'share/<group>',
                    type: "string",
                    status: undefined,
                    value: undefined,
                },
                {
                    name: "share_percent",
                    group: 'share/<group>',
                    type: "float",
                    status: undefined,
                    value: undefined,
                },
                {
                    name: "evidence_affiliate",
                    group: 'evidence_affiliate',
                    type: "string",
                    label: "!evidence_affiliate",
                    status: undefined,
                    value: undefined,
                    multiple: true,
                },
            ]
        }
    },
    methods: {
        async create() {
            await this.wait(async e => {
                let fields = this.fields.filter((field) => {
                    if (field.hidden && typeof field.hidden == 'function') {
                        return !field.hidden();
                    }
                    return !field.hidden;
                }).reduce((prev, curr) => {
                    if (curr.readonly && curr.readonly())
                        return prev
                    prev[curr.name] = curr.value && curr.value.exp ? curr.value.exp.value : curr.value
                    return prev
                }, {})

                let { error, data } = await Http.api.rpc("bind_affiliated_company", {
                    ...fields
                })
                if(error){
                    this.error = error.message
                    if(!error.data || error.data.length <=0)
                        return;

                    let fields = this.fields.reduce((prev, curr) => {
                        prev[curr.name] = curr
                        prev[curr.name].status = undefined
                        return prev
                    }, {})

                    error.data.forEach(item => {
                        let field = fields[item.name]
                        if(!field)
                            return;
                        field.status = {
                            type: "error",
                            message: item.message
                        }
                    });
                    
                   // await Vue.Dialog.MessageBox.Error(error)

                }else{
                    if(data && data.message){
                        Vue.Dialog.MessageBox.Success(data.message)
                    }
                    this.Close("Ok")
                }
            });
        }
    },
    template: `
    <div>
        <header>{{$t('affiliation.add')}}</header>
        <main v-if='error' class='error'>
            {{error}}
        </main>
        <main>
            <ui-layout :fields='fields' />
        </main>
        <footer>
            <ui-btn type='secondary' @click.native='Close()'>{{$t('cancel')}}</ui-btn>
            <ui-btn type='primary' v-on:click.native='create'>{{$t('add')}}</ui-btn>    
        </footer>
    </div>                
    `
});