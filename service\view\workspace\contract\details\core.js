import Information from './information';
import InformationRequest from './information_request';
import InformationReduction from './information_reduction';
import InformationExchange from './information_exchange';
import Base from './base';
import Specification from './specification';
import Invoices from './invoices';
import Graphic from './graphic';
import File from './file';
import Requisites from './requisites';
import OtherRequests from './other_requests'
import exchangeRates from './exchange-rates';
import PsevdoFile from './psevdo-file'
import SimplifiedCore from './simplified_core';

export default {
  props: ['model'],
  data: function () {
    return {
      debug: false,
    };
  },
  mounted: function () {

  },
  components: {
    Information,
    InformationRequest,
    InformationReduction,
    InformationExchange,
    Base,
    OtherRequests,
    Specification,
    Invoices,
    Graphic,
    File,
    Requisites,
    exchangeRates,
    PsevdoFile,
    SimplifiedCore
  },
  methods: {
    async remove_additional_contract() {
      await this.$wait(async () => {
        await this.model.remove_additional_contract(this);
      });
    },
    async remove_contract() {
      await this.$wait(async () => {
        if(await this.model.remove_contract(this))
          this.$router.push({ path: `/workspace/contract` });
      });
      
    },
    async public_contract() {
      await this.$wait(async () => {
        await this.model.public_contract();
      });
    },
    async org_sign_contract() {
      await this.$wait(async () => {
        await this.model.org_sign_contract();
      });
    },
    async winner_sign() {
      await this.$wait(async () => {
        await this.model.winner_sign();
      });
    },
    async reject_sign(side) {
      await this.$wait(async () => {
        await this.model.reject_sign(side);
      });
    },
    async winner_join_contract() {
      await this.$wait(async () => {
        let {error, data} = await this.model.winner_join_contract();

        if (!error) { await this.model.update_rights(); }
      });
    },
    async cancellation() {
      await this.$wait(async () => {
        await this.model.cancellation(this);
      });
    },
    async modelDevelop(method, show_dlg = false) {
      await this.$wait(async () => {
        if(await this.model.develop(method, show_dlg) && method == 'delete_contract')
          this.$router.push({ path: `/workspace/contract` });
      });
    },
  },
  template: `
    <div v-if='model'>

    <ui-btn-group style='padding-bottom: 20px; display: grid; grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));' v-if='$develop.content_debug'>
      <ui-btn  type='warning' v-key='action' v-for='action in model.actions' @click.native='modelDevelop(action.method)' style='border-radius: 0px; margin: -1px 0 0 -1px;'>{{action.name}}</ui-btn>
    </ui-btn-group>

    <template v-if='model.proc_type == "simplified_contract"'>
      <div style=' padding: 10px; margin: 10px 0;'>
        proc_type: {{model.proc_type}}
      </div>
      <SimplifiedCore :model='model' />
    </template>
    <template v-else-if='model.proc_type == "custom_contract"'>
      <ui-alert v-if='model.isugf_reply_positive == false && model.isugf_reply.ERRMSG' type='danger'>
        <div style='margin-bottom: 20px'>{{model.isugf_reply.ERRMSG}}</div>
      </ui-alert>

      <div class='grid'>
        <ui-layout :wait='model.customBody.wait'  :readonly='model.customBody.readonly' class='row' :fields='model.customBody.fields' />
      </div>
      <File  :model='model' />
      <ui-btn-group >
        <ui-btn v-if='model.rights.publish' :disabled='!model.public_contract' type='primary' @click.native='public_contract'>{{ $t('contract.public') }}</ui-btn>
        <ui-btn v-if='model.rights.org_sign' :disabled='!model.org_sign_contract' type='primary' @click.native='org_sign_contract'>{{ $t('contract.sign') }}</ui-btn>
        <ui-btn v-if='model.rights.org_reject_sign' type='secondary' @click.native='reject_sign("org")'>{{ $t('reject_sign') }}</ui-btn>
        <ui-btn v-if='model.rights.winner_sign' type='primary' @click.native='winner_sign'>{{ $t('contract.sign') }}</ui-btn>
        <ui-btn v-if='model.rights.winner_join_contract' type='primary' @click.native='winner_join_contract'>{{ $t('contract.accompany') }}</ui-btn>
        <ui-btn v-if='model.rights.delete' type='danger' @click.native='remove_contract'>{{ $t('contract.remove') }}</ui-btn>
        <ui-btn v-if='model.rights.cancellation' type='danger' @click.native='cancellation'>{{ $t('rejected_timeout') }}</ui-btn>
        <ui-btn v-if='model.rights.remove_additional_contract' type='danger' @click.native='remove_additional_contract'>{{ $t('contract.remove_additional') }}</ui-btn>
      </ui-btn-group>
    </template>
    <template v-else-if='model.proc_type == "schedule_exchange_contract"'>
      <div class='grid'>
        <ui-layout :wait='model.exchangeBody.wait'  :readonly='model.exchangeBody.readonly' class='row' :fields='model.exchangeBody.fields' />
      </div>
      <PsevdoFile  :model='model' />
      <Requisites :model='model' />
      <div class='text-center' v-if='model.rights.remove_additional_contract'>
        <ui-btn type='primary' @click.native='remove_additional_contract'>{{ $t('contract.remove_additional') }}</ui-btn>
      </div>
    </template>
    <template v-else>
      <InformationRequest v-if='model.proc_type == "request"' :model='model' />
      <InformationReduction v-else-if='model.proc_type == "reduction"' :model='model' />
      
      <Information v-else :model='model' />

      <Base :model='model' />
      <exchange-rates v-if="model.exchange_rates" :model='model.exchange_rates' />

      <OtherRequests v-if='model.other_requests && model.other_requests.length' :model='model'/>
      <Specification :model='model' />
      <Invoices :model='model' />
      <Graphic v-if='model.graphic && model.graphic.length' :model='model' />

      <File  :model='model' />
      <Requisites :model='model' />
      <div class='text-center' v-if='model.rights.remove_additional_contract'>
        <ui-btn type='primary' @click.native='remove_additional_contract'>{{ $t('contract.remove_additional') }}</ui-btn>
      </div>
    </template>

    <template v-if="$develop.access_debug">
      <details style="padding: 7px 0px;" open>
        <summary style="padding-bottom: 5px;">Permission list (only for dev/qa): </summary>
        <span v-for="(v, key) in model.rights" style="padding-left: 8px; padding-right: 8px; margin: 0px" class="ui-tag-item" :style='{color: v ? "green" : "rgb(204, 60, 60)"}'>
          {{ key }}
        </span>
      </details>

      <details style="padding: 7px 0px;">
        <summary style="padding-bottom: 5px;">Additional data (only for dev/qa): </summary>
        <div>
          <pre style='overflow: auto;border: 1px solid #ccc;padding: 5px;'>{{ model.debug }}</pre>
          <ui-btn type='primary' @click.native='modelDevelop("repeat_event")'>Сбрасывать финальные состояния</ui-btn>
          <ui-btn type='danger' @click.native='modelDevelop("delete_contract")'>{{ $t('contract.remove') }}</ui-btn>
        </div>
      </details>
    </template>
    </div>
  `,
};
