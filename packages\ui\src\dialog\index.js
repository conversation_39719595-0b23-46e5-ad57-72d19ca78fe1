import Guid from "../../../core/src/guid";
import MessageBox from './message_box'
var dlg_list = [];

var Plugin = {
    install: (Vue, args) => {
        Vue.component("iac-dialog-container", {
            data: () => {
                return {
                    items: dlg_list
                }
            },
            created() {
                document.addEventListener('keydown', this.keydown)
            },
            destroyed() {
                document.removeEventListener('keydown', this.keydown)
            },
            methods: {
                Close(e) {

                },
                keydown(e) {
                    if (e.keyCode == 27) {
                        let index = this.items.length - 1
                        let item = this.items[index]
                        if (item && !item.wait) {
                            dlg_list.splice(index, 1);
                            item.callback(undefined);
                        }
                    }
                },
                getScrollbarWidth() {
                    const scrollDiv = document.createElement('div')
                    scrollDiv.className = "modal-scrollbar-measure"
                    document.body.appendChild(scrollDiv)
                    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth
                    document.body.removeChild(scrollDiv)
                    return scrollbarWidth
                }
            },
            watch: {
                items: {
                    immediate: true,
                    handler(val, oldVal) {

                        let body = document.getElementsByTagName('body')[0];
                        if (!body)
                            return;

                        let navTop = document.getElementsByClassName('iac-nav-top')[0];

                        if (val && val.length > 0) {

                            if (this.$el)
                                this.$el.focus();
                            let scrollbarWidth = this.getScrollbarWidth();
                            body.classList.add("modal_show")
                            body.style.paddingRight = `${scrollbarWidth}px`;
                            if (navTop && body.className.indexOf('header') >= 0) {
                                navTop.style.paddingRight = `${scrollbarWidth + 20}px`;
                            }

                        } else {
                            body.classList.remove("modal_show")
                            body.style.paddingRight = null;
                            if (navTop) {
                                navTop.style.paddingRight = null;
                            }
                        }
                    }
                }
            },
            render: function (h) {
                let enable_close = false;
                return h('div', {
                    attrs:
                    {
                        id: 'iac-dialog-container',
                        class: 'iac-app',
                        tabIndex: -1
                    }

                }, this.items.map((item, index) => {
                    return h('div', {
                        class: 'iac-dialog',
                        attrs: {
                            focus: true,
                            tabIndex: -1
                        }
                    }, [
                        h('div', {
                            class: 'iac-dialog_backdrop',
                        }),
                        h('div', {
                            class: 'iac-dialog_modal',
                            on: {
                                mousedown:(e)=>{
                                    if (e.target.className == 'iac-dialog_modal') {
                                        enable_close = true;
                                    }
                                },
                                click: (e) => {
                                    if(!enable_close){
                                        return;
                                    }
                                    enable_close = false;

                                    if (item.wait)
                                        return;

                                    let close_dlg_feature = Vue.Dialog.close_dlg_feature;
                                    if(!close_dlg_feature)
                                        return;

                                    if (e.target.className == 'iac-dialog_modal') {
                                        dlg_list.splice(index, 1);
                                        item.callback();
                                    }
                                }
                            }
                        },
                            [h('div', {
                                class: [
                                    'iac-dialog_modal_box',
                                    item.size && item.size == 'right' ? 'modal-right' : '',
                                    item.size && item.size == 'lg' ? 'modal-lg' : '',
                                    item.size && item.size == 'full' ? 'modal-full' : '']
                            }, [
                                h(item.component, {
                                    key: item.key,
                                    props: item.props,
                                    class: {
                                        "iac-dialog_modal_box-content": true,
                                        "iac-wait": item.wait
                                    }
                                }),
                                h("span", {
                                    class: 'iac-dialog_modal_box-close',
                                    on: {
                                        click: (e) => {
                                            if (item.wait)
                                                return;
                                            dlg_list.splice(index, 1);
                                            item.callback();
                                        }
                                    }
                                }, [h("icon", "delete")])
                            ])]
                        )
                    ])
                }))
            }
        })

        Vue.Dialog = function (...args) {

            let name, component;
            if (args.length > 1) {
                name = args[0];
                component = args[1];
            } else {
                component = args[0];
            }
            //component.props = ["dlgSize"]
            component.Modal = async function (props) {
                return new Promise((resolve, reject) => {
                    dlg_list.push({
                        size: props && props.size,
                        key: Guid.newGuid(),
                        wait: false,
                        component: this,
                        props: props,
                        callback: (result) => {
                            resolve(result);
                        }
                    });
                })
            }

            if (name)
                Vue.Dialog[name] = component;
            return component;
        }

        Vue.Dialog("MessageBox", MessageBox);

        Vue.mixin({
            beforeCreate: function () {
                const { Modal } = this.$options;
                if (Modal) {
                    this.Close = (result) => {
                        dlg_list.forEach((item, index) => {
                            if (item.key == this.$vnode.key) {
                                dlg_list.splice(index, 1);
                                item.callback(result);
                            }
                        })
                    }

                    this.wait = async (fn) => {

                        let index;
                        dlg_list.forEach((item, i) => {
                            if (item.key == this.$vnode.key) {
                                index = i;
                            }
                        })

                        dlg_list[index].wait = true;
                        await fn();
                        if (dlg_list[index])
                            dlg_list[index].wait = false;
                    }
                }
            }
        });


    }
}

export default Plugin;

if (typeof window !== 'undefined' && window.Vue) {
    window.Vue.use(Plugin)
}