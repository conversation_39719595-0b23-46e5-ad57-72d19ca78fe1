import { DataSource, Entity } from '@iac/data'
import { Http, Language } from '@iac/core';
import { Settings, Context } from '@iac/kernel';
const linkBase = '/workspace/contract/';

//96189543853918221663 586

const $t = Language.t;

class BackAccount extends Entity {
    constructor(context = {}) {
        super(context)
    }
    props() {
        return {
            name: {
                required: true
            },
            bank_name: {
                required: true
            },
            bank_account_type_id: {
                type: 'entity',
                dataSource: "ref_bank_account_type",
                value: 2
            },
            bank_account: {
                required: true
            },
            bank_mfo_code: {
                required: true,
                label: [Settings._country + ".bank_mfo_code", "bank_mfo_code"],
                type: 'entity',
                dataSource: "ref_bank_mfo"
            }
        }
    }
    async save() {
        let params = this.fields.filter((field) => {
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).map((field) => {
            let value = field.value;
            if (field.value && field.value.exp && field.value.exp.value != undefined) {
                value = field.value.exp.value
            }
            return {
                name: field.name,
                value: value
            }
        }).reduce((prev, curr) => {
            prev[curr.name] = curr.value
            return prev;
        }, {})


        let { error, data } = await Http.api.rpc("create_company_bank_account", {
            ...params
        })
        if (error) {
            if (!this.setError(error)) {
                await Vue.Dialog.MessageBox.Error(error);
            }
        } else {
            data = {
                id: data.id,
                ...params
            }
        }
        return { error, data };
    }
}

var requisitesNotFound = {
    props: ["fn"],
    methods: {
        async add_bank_account() {
            let value = await Vue.Dialog({
                data: function () {
                    return {
                        model: new BackAccount()
                    }
                },
                methods: {
                    async save() {
                        this.$wait(async () => {
                            let { error, data } = await this.model.save();
                            if (!error) {
                                this.Close(data);
                            }
                        })
                    },
                },
                template: `
                    <div>
                        <header>{{$t("company_bank")}}</header>
                        <main>
                            <ui-layout :fields='model.fields'/>
                        </main>
                        <footer>
                            <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
                            <ui-btn type='primary' v-on:click.native='save'>{{$t('create')}}</ui-btn>
                        </footer>
                    </div>
                `
            }).Modal({

            })
            if (value && this.fn) {
                this.fn(value);
            }
        }
    },
    template: `
        <div>
            <ui-alert type='danger'>
                {{$t('requisites_not_found')}}
                <p v-if='!$policy.company_edit'>{{$t("add_requisites_not_access")}}</p>
            </ui-alert>
            <ui-btn type='info' v-if='$policy.company_edit' v-on:click.native='add_bank_account'>{{$t('add')}}</ui-btn>
        </div>
    `
}

export default {
    props: ["model"],
    data: function () {       
        const simple_button = (action, type = 'primary') => {
            return {
                if_: () => this.model.rights[action],
                type: type,
                action: async value => {
                    await this.$wait(async () => {
                        await this.model[action]();
                    })
                },
                label: $t(`contract.${action}`),
            }
        };

        return {
            orgDataSource: DataSource.get("get_company_bank_accounts"),
            buttons: {
                left: [
                    {
                        if_: () => this.model.rights.pause_contract,
                        type: 'primary',
                        // action: async value => pause_contract,
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.pause_contract("org", value);
                            });
                        },
                        label: $t('pause_contract'),
                    },
                    {
                        if_: () => this.model.rights.return_on_execution,
                        type: 'primary',
                        // action: async value => return_on_execution,
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.return_on_execution("org", value);
                            });
                        },
                        label: $t('return_on_execution'),
                    },
                    {
                        if_: () => this.model.rights.reject_without_fine_from_org,
                        type: 'primary',
                        // action: async value => reject_without_fine_from_org,
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.reject_without_fine_from_org();
                            });
                        },
                        label: $t('contract.reject_without_fine'),
                    },
                    {
                        if_: () => this.model.rights.org_confirm_income,
                        type: 'primary',
                        // action: async value => org_confirm_income,
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.org_confirm_income();
                            });
                        },
                        label: $t('org_confirm_income_btn'),
                    },
                    {
                        if_: () => this.model.rights.accept_delivery_timeout,
                        type: 'primary',
                        // action: async value => accept_delivery_timeout,
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.accept_delivery_timeout();
                            });
                        },
                        label: $t('accept_delivery_timeout'),
                    },
                    {
                        if_: () => this.model.rights.org_rejected_winner_timeout,
                        type: 'secondary',
                        // action: async value => rejected_timeout("org"),
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.rejected_timeout("org")
                            });
                        },
                        label: $t('rejected_timeout'),
                    },
                    {
                        if_: () => this.model.rights.org_sign && this.model.status != "new_wait_org_sign",
                        type: 'primary',
                        // action: async value => sign("org"),
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.sign("org");
                            });
                        },
                        label: $t('subscribe'),
                    },
                    {
                        if_: () => this.model.rights.org_sign && this.model.status == "new_wait_org_sign",
                        type: 'primary',
                        // action: async value => sign_2("org"),
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.sign("org", 'question_do_you_want_to_withdraw_from_the_contract_without_penalty');
                            });
                        },
                        label: $t('withdrawal_without_penalty'),
                    },
                    {
                        if_: () => this.model.rights.ac_sign_org,
                        type: 'primary',
                        // action: async value => ac_sign("org"),
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.ac_sign("org");
                            });
                        },
                        label: $t('subscribe'),
                    },
                    {
                        if_: () => this.model.rights.cancel_contract_org,
                        type: 'primary',
                        // action: async value => cancel_contract("org"),
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.cancel_contract("org");
                            });
                        },
                        label: $t('cancel_contract'),
                    },
                    {
                        if_: () => this.model.rights.org_reject_sign,
                        type: 'secondary',
                        // action: async value => reject_sign("org"),
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.reject_sign("org");
                            });
                        },
                        label: $t('reject_sign'),
                    },
                    {
                        if_: () => this.model.rights.org_reject,
                        type: 'danger',
                        // action: async value => reject("org"),
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.reject("org");
                            });
                        },
                        label: $t('reject'),
                    },
                    {
                        if_: () => this.model.rights.org_complaint,
                        type: 'warning',
                        // action: async value => complaint("org"),
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.complaint("org")
                            });
                        },
                        label: $t('contract.org_complaint'),
                    },
                    {
                        if_: () => this.model.rights.ac_reject && this.model.status == "ac_wait_org_sign",
                        type: 'warning',
                        // action: async value => ac_reject("org"),
                        action: async value => {
                            this.$wait(async () => {
                                const number = await this.model.ac_reject("org");
                                if (number) {
                                  this.$router.push(`${linkBase}${number}/core`);
                                }
                            });
                        },
                        label: $t('contract.ac_reject'),
                    },
                    simple_button("cancel_org_return", "warning"),
                    simple_button("cancel_org_sign"),
                    simple_button("finalize_contract_org"),
                    simple_button("finalize_org_sign"),
                    simple_button("finalize_org_return"),
                    simple_button("reserve_complaint_seller"),
                    simple_button("return_rkp_pays"),
                ],
                right: [
                    {
                        if_: () => this.model.rights.winner_rejected_org_timeout,
                        type: 'secondary',
                        // action: async value => rejected_timeout("contragent"),
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.rejected_timeout("contragent")
                            });
                        },
                        label: $t('rejected_timeout'),
                    },
                    {
                        if_: () => this.model.rights.winner_sign,
                        type: 'primary',
                        // action: async value => sign("winner"),
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.sign("winner");
                            });
                        },
                        label: $t('subscribe'),
                    },
                    {
                        if_: () => this.model.rights.ac_sign_winner,
                        type: 'primary',
                        // action: async value => ac_sign("winner"),
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.ac_sign("winner");
                            });
                        },
                        label: $t('subscribe'),
                    },
                    {
                        if_: () => this.model.rights.winner_reject_sign,
                        type: 'secondary',
                        // action: async value => reject_sign("winner"),
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.reject_sign("winner");
                            });
                        },
                        label: $t('reject_sign'),
                    },
                    {
                        if_: () => this.model.rights.winner_reject,
                        type: 'danger',
                        // action: async value => reject("winner"),
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.reject("winner");
                            });
                        },
                        label: $t('reject'),
                    },
                    {
                        if_: () => this.model.rights.cancel_contract_winner,
                        type: 'primary',
                        // action: async value => cancel_contract("winner"),
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.cancel_contract("winner");
                            });
                        },
                        label: $t('cancel_contract'),
                    },
                    {
                        if_: () => this.model.rights.ready_for_delivery,
                        type: 'success',
                        // action: async value => sign_action("ready_for_delivery"),
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.sign_action("ready_for_delivery",'question.call.action')
                            });
                        },
                        label: $t('contract.ready_for_delivery'),
                    },
                    {
                        if_: () => this.model.rights.contragent_complaint,
                        type: 'warning',
                        // action: async value => complaint("contragent"),
                        action: async value => {
                            await this.$wait(async () => {
                                await this.model.complaint("contragent")
                            });
                        },
                        label: $t('contract.contragent_complaint'),
                    },
                    {
                        if_: () => this.model.rights.ac_reject && this.model.status == "ac_wait_winner_sign",
                        type: 'warning',
                        // action: async value => ac_reject("contragent"),
                        action: async value => {
                            this.$wait(async () => {
                                const number = await this.model.ac_reject("contragent");
                                if (number) {
                                  this.$router.push(`${linkBase}${number}/core`);
                                }
                            });
                        },
                        label: $t('contract.ac_reject'),
                    },
                    simple_button("confirm_income_timeout_expired"),
                    simple_button("reject_without_fine"),
                    simple_button("buyers_blocked"),
                    simple_button("cancel_winner_return", "warning"),
                    simple_button("cancel_winner_sign"),
                    simple_button("finalize_contract_winner"),
                    simple_button("finalize_winner_sign"),
                    simple_button("finalize_winner_return"),
                    simple_button("edit_banking_details"),
                ]
            }
        }
    },
    methods: {
        async buyers_blocked(){
            await this.$wait(async () => {
                await this.model.buyers_blocked();
            });
        },
        async cancel_contract(part) {
            await this.$wait(async () => {
                await this.model.cancel_contract(part);
            });
        },
        async sign(part) {
            await this.$wait(async () => {
                await this.model.sign(part);
            });
        },
        async ac_sign(part) {
            await this.$wait(async () => {
                await this.model.ac_sign(part);
            });
        },
        async sign_2(part) {
            await this.$wait(async () => {
                await this.model.sign(part, 'question_do_you_want_to_withdraw_from_the_contract_without_penalty');
            });
        },
        async reject_sign(part) {
            await this.$wait(async () => {
                await this.model.reject_sign(part);
            });
        },
        async reject(part) {
            await this.$wait(async () => {
                await this.model.reject(part);
            });
        },
        async pause_contract(value) {
            await this.$wait(async () => {
                await this.model.pause_contract("org", value);
            });
        },
        async return_on_execution(value) {
            await this.$wait(async () => {
                await this.model.return_on_execution("org", value);
            });
        },
        async on_org_banking_details(value) {
            await this.$wait(async () => {
                await this.model.edit_banking_details("org", value);
            });
        },
        async on_contragent_banking_details(value) {
            await this.$wait(async () => {
                await this.model.edit_banking_details("contragent", value);
            });
        },
        async org_confirm_income(value) {
            await this.$wait(async () => {
                await this.model.org_confirm_income();
            });
        },
        async reject_without_fine_from_org(value) {
            await this.$wait(async () => {
                await this.model.reject_without_fine_from_org();
            });
        },
        async accept_delivery_timeout(value) {
            await this.$wait(async () => {
                await this.model.accept_delivery_timeout();
            });
        },
        async confirm_income_timeout_expired(value) {
            await this.$wait(async () => {
                await this.model.confirm_income_timeout_expired();
            });
        },
        async reject_without_fine(value) {
            await this.$wait(async () => {
                await this.model.reject_without_fine();
            });
        },
        async rejected_timeout(part) {
            await this.$wait(async () => {
                await this.model.rejected_timeout(part)
            });
        },
        async complaint(part) {
            await this.$wait(async () => {
                await this.model.complaint(part)
            });
        },
        async ac_reject(part) {
            this.$wait(async () => {
                const number = await this.model.ac_reject(part);
                if (number) {
                  this.$router.push(`${linkBase}${number}/core`);
                }
            });
        },
        async sign_action(action) {
            await this.$wait(async () => {
                await this.model.sign_action(action,'question.call.action')
            });
        },
        debug_login(e, id) {
          e.preventDefault();
          Context.User.authorization_god(id);
        }
    },
    components: {
        requisitesNotFound: requisitesNotFound
    },
    computed: {
        _initiator_user_debug() {
            return `u${this.model?.initiator?.user_details?.id}c${this.model?.initiator?.company_details?.id}`;
        },
        _contragent_user_debug() {
            return `u${this.model?.contragent?.user_details?.id}c${this.model?.contragent?.company_details?.id}`;
        },
        left_visible() {
            let res = this.buttons.left.map(it => it.if_(this.model)).reduce((acc, x) => acc || !!x);
            console.log("Left visible res: ", res);
            return res;
        },
        right_visible() {
            let res = this.buttons.right.map(it => it.if_(this.model)).reduce((acc, x) => acc || !!x);
            console.log("Right visible res: ", res);
            return res;
        },
        initiator_broker_visible() {
            return this.model.proc_type == "schedule_exchange_contract"  &&  this.model.initiator_broker?.company_details?.id
        },
        initiator_trader_visible() {
            return this.model.proc_type == "schedule_exchange_contract"  &&  this.model.initiator?.user_details?.name
        },

        contragent_broker_visible() {
            return this.model.proc_type == "schedule_exchange_contract" &&  this.model.contragent_broker?.company_details?.id
        },
        contragent_trader_visible() {
            return this.model.proc_type == "schedule_exchange_contract" &&  this.model.contragent?.user_details?.name
        }
    },
    // <ui-btn v-for="btn in buttons.left" v-if='btn.if_(model)' :type='btn.type' v-on:click.native='btn.action(model)'>{{btn.label}}</ui-btn>
    template: `
    <div>
      <div class='requisites'>
        <div class='grid'>
            <div class='row'>
                <h2>{{$t([model.proc_type+'.initiator_requisites','contract.initiator_requisites'])}}</h2>

                <div>
                    <router-link :to='"/company/" + model.initiator.company_details.id'>
                        {{ model.initiator.company_details.title }}
                    </router-link>
                    <span v-if='$develop.content_debug'>
                        {{_initiator_user_debug}}
                    </span>
                    <a v-if='$develop.content_debug && model.initiator.user_details.id' title='Телепортироваться' v-on:click='e => debug_login(e, model.initiator.user_details.id)' href="#">🕳️ </a>
                </div>
            </div>
            <div class='row fill'>
                <ui-entity
                    label='company_bank'
                    :dataSource='orgDataSource'
                    :value='model.org_banking_details'
                    v-on:input='on_org_banking_details'
                    :readonly='!model.rights.edit_org_banking_details'
                    v-if='model.rights.edit_org_banking_details'
                    >
                    <requisitesNotFound slot='not-found' :fn='on_org_banking_details'/>
                </ui-entity>
                <div v-if='model.org_banking_details'>
                    <div><label>{{$t('bank_name')}}:</label> {{model.org_banking_details.bank_name}}</div>
                    <div><label>{{$t([$settings._country+'.bank_mfo_code','bank_mfo_code'])}}:</label> {{model.org_banking_details.bank_mfo_code}}</div>
                    <div><label>{{$t('bank_account')}}:</label> {{model.org_banking_details.bank_account}}</div>
                    <div v-if='model.org_reject_reason' class='mt-20'><label>{{ $t('reject_sign_reason') }}:</label> {{ model.org_reject_reason }}</div>
                    
                    <div style='margin-top: 15px' >
                        <div v-if='initiator_broker_visible'><label>{{$t('contract.broker_name')}}:</label>
                            <router-link :to='"/company/" + model.initiator_broker.company_details.id'>{{ model.initiator_broker.company_details.title }}</router-link>
                            <span>(ID: {{ model.initiator_broker.company_details.id}}) </span> 
                        </div>  
                        <div v-if='initiator_trader_visible'><label>{{$t('contract.trader_name')}}:</label> 
                        {{[model.initiator.user_details.surname, 
                           model.initiator.user_details.name, 
                           model.initiator.user_details.patronymic]
                           .filter(Boolean)
                           .join(' ')
                        }}</div>
                    </div>    
                </div>
                <ui-alert v-else-if='model.proc_type != "schedule_exchange_contract"' type='warning'>{{$t("not_found_banking_details_warning")}}</ui-alert>
                <div v-if='model.status == "wait_offer_sum" || model.status == "new_wait_offer_sum"'>
                    <ui-alert type='danger' style='margin: 24px 0 0;'>{{$t('wait_offer_sum_danger')}} <iac-date v-if='model.end_wait_offer_date' :date='model.end_wait_offer_date'></iac-date> </ui-alert>
                </div>
                <div v-if='model.return_rkp_pays_click_datetime'>
                    <ui-alert type='info' style='margin: 24px 0 0;'>{{$t('return_rkp_pays_message')}}&nbsp;<iac-date :date='model.return_rkp_pays_click_datetime' :full="true"></iac-date></ui-alert>
                </div>
            </div>
            <div class='row' v-if='this.left_visible'>
                <ui-btn v-for="btn in buttons.left" v-if='btn.if_()' :type='btn.type' v-on:click.native='btn.action'>{{btn.label}}</ui-btn>
            </div>
        </div>
        <div class='grid'>
            <div class='row'>
                <h2>{{$t([model.proc_type+'.contragent_requisites','contract.contragent_requisites'])}}</h2>
                <div>
                <router-link :to='"/company/" + model.contragent.company_details.id'>
                    {{ model.contragent.company_details.title }}
                </router-link>
                <span v-if='$develop.content_debug'>
                    {{_contragent_user_debug}}
                </span>
                <a v-if='$develop.content_debug && model.contragent.user_details.id' title='Телепортироваться' v-on:click='e => debug_login(e, model.contragent.user_details.id)' href="#">🕳️ </a>
                </div>
            </div>
            <div class='row fill'>
                <ui-entity
                    label='company_bank'
                    :dataSource='orgDataSource'
                    :value='model.contragent_banking_details'
                    v-on:input='on_contragent_banking_details'
                    :readonly='!model.rights.edit_contragent_banking_details'
                    v-if='model.rights.edit_contragent_banking_details'
                    >
                    <requisitesNotFound slot='not-found' :fn='on_contragent_banking_details'/>
                </ui-entity>
                <div v-if='model.contragent_banking_details'>
                    <div><label>{{$t('bank_name')}}:</label> {{model.contragent_banking_details.bank_name}}</div>
                    <div><label>{{$t([$settings._country+'.bank_mfo_code','bank_mfo_code'])}}:</label> {{model.contragent_banking_details.bank_mfo_code}}</div>
                    <div><label>{{$t('bank_account')}}:</label> {{model.contragent_banking_details.bank_account}}</div>
                    <div v-if='model.winner_reject_reason' class='mt-20'><label>{{ $t('reject_sign_reason') }}:</label> {{ model.winner_reject_reason }}</div>
                    <div style='margin-top: 15px' >
                        <div v-if='contragent_broker_visible'><label>{{$t('contract.broker_name')}}:</label>
                            <router-link :to='"/company/" + model.contragent_broker.company_details.id'>{{ model.contragent_broker.company_details.title }}</router-link>
                             <span>(ID: {{ model.contragent_broker.company_details.id}}) </span> 
                        </div>  
                        <div v-if='contragent_trader_visible'><label>{{$t('contract.trader_name')}}:</label> 
                            {{[model.contragent.user_details.surname, 
                            model.contragent.user_details.name, 
                            model.contragent.user_details.patronymic]
                            .filter(Boolean)
                            .join(' ')
                            }}
                        </div>
                    </div>      

                </div>
                <ui-alert v-else-if='model.proc_type != "schedule_exchange_contract"' type='warning'>{{$t("not_found_banking_details_warning")}}</ui-alert>
                <div v-if='model.status == "wait_offer_sum" || model.status == "new_wait_offer_sum"'>
                    <ui-alert type='warning' style='margin: 24px 0 0;'>{{$t('wait_offer_sum_danger_2')}}</ui-alert>
                </div>
            </div>

            <div class='row' v-if='this.right_visible'>
                <ui-btn v-for="btn in buttons.right" v-if='btn.if_()' :type='btn.type' v-on:click.native='btn.action'>{{btn.label}}</ui-btn>
            </div>
        </div>
        <div class='grid' v-if='model.rights.send_to_arbitration || model.rights.reviewer_sign'>
        <div class='row'>
            <ui-btn v-if='model.rights.send_to_arbitration' type='primary' v-on:click.native='sign_action("send_to_arbitration")'>{{$t('send_to_arbitration')}}</ui-btn>
            <ui-btn v-if='model.rights.reviewer_sign' type='primary' v-on:click.native='sign_action("reviewer_sign")'>{{$t('subscribe')}}</ui-btn>
        </div>
        </div>
    </div>
    </div>
    `
}