import Procedure from './procedure';
import { <PERSON>elo<PERSON> } from '@iac/kernel'

@Procedure.Registration("master_agreement")
export default class MasterAgreement extends Procedure {

    constructor(context = {}) {
        super(context);
    }

    get procedure_actions(){
        return [
            { buttons: 1, group: 'general', order: 1000 },
            { buttons: 3, group: 'tech_fields', order: 1000 },
        ]
    }

    static async init_context(context) {
        
        return {

        }
    }
}