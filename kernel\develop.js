import Entity from './data/entity'
import { Event, Language } from '@iac/core'
import { RefStore } from '@iac/data'
class Develop extends Entity {

    constructor(context) {
        super(context)

        let setting = localStorage.getItem("develop_setting") || "{}";
        this.setting = JSON.parse(setting);

        this._lock = true
        this.fields.forEach(field => {
            this[field.name] = this.setting[field.name] || undefined;
        });
        this._lock = false
    }

    @Event onChangeProperty(event) {
        if(this._lock)
            return

        let setting = this.fields.filter((field) => {
            return field.type == 'bool' || field.type == 'string'  || field.type == 'float' || field.type == 'entity'
        }).map((field) => {
            return {
                name: field.name,
                value: field.value?.id || field.value
            }
        }).reduce((prev, curr) => {
            prev[curr.name] = curr.value
            return prev
        }, {})

        localStorage.setItem("develop_setting", JSON.stringify(setting))
    }

    set_active() {

    }

    props() {
        return {
            old_home_page: {
                group: '{dev_tab}/Feature',
                type: 'bool',
                label: '<b>old_home_page</b></br>Старая главной страницы'
            },
            inside_ktru_version: {
                group: '{dev_tab}/Feature',
                type: 'bool',
                label: '<b>inside ktru version</b></br>Включает локальную внутрисистемную версию КТРУ',
                onChange(value, oldValue) {
                    Vue.Dialog.inside_ktru_version = value
                }
            },
            close_dlg_feature: {
                group: '{dev_tab}/Feature',
                type: 'bool',
                label: '<b>close_dlg_feature</b></br>Позволяет закрывать окна при клике вне окна',
                onChange(value, oldValue) {
                    Vue.Dialog.close_dlg_feature = value
                }
            },
            disable_authcheck_debug: {
                group: '{dev_tab}/Debug',
                type: 'bool',
                label: '<b>disable_authcheck_debug</b></br>Отключаем требование к авторизации'
            },
            access_debug: {
                group: '{dev_tab}/Debug',
                type: 'bool',
                label: '<b>access_debug</b></br>Выводит подсказки по правам в левом меню и при наведении на логин'
            },
            field_debug: {
                group: '{dev_tab}/Debug',
                type: 'bool',
                label: '<b>field_debug</b></br>Выводит подсказки по полям ввода. Отображается только в сгенерированных формах'
            },
            number_debug: {
                group: '{dev_tab}/Debug',
                type: 'bool',
                label: '<b>number_debug</b></br>Выводит номер без округления'
            },
            content_debug: {
                group: '{dev_tab}/Debug',
                type: 'bool',
                label: '<b>content_debug</b></br>Выводит доп информацию для QA',
                onChange: (value)=>{
                    if(value)
                        RefStore.Introspect = true
                    else
                        RefStore.Introspect = false
                }
            },
            token_from_session: {
                group: '{dev_tab}/Debug',
                type: 'bool',
                label: '<b>token_from_session</b></br>Хранение токена в sessionStore'
            },
            copy_qr_value: {
                group: '{dev_tab}/Debug',
                type: 'bool',
                label: '<b>copy_qr_value</b></br>Копировать содержимое qr-кода по клику'
            },
            theme_debug:{
                group: '{dev_tab}/Debug',
                type: "entity",
                dataSource:["default","valkyrie"],
                label: 'theme_debug',
                has_del: true,
            },
            filter_develop: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: '<b>filter_develop</b></br>Выводит доп фильтры которые сейчас в разработке'
            },
            terminal_select_color: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: '<b>terminal_select_color</b></br>Ручной выбор цвета для индикаторов'
            },
            company_delete_user_develop: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: '<b>company_delete_user_develop</b></br>Удаление пользователя из компании'
            },
            soc_develop: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: '<b>soc_develop</b></br>Вход через соцсети'
            },
            purchase_develop: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: '<b>purchase_develop</b></br>Выводить предупреждение в заявках и в плане-график'
            },
            add_field_show_request: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: '<b>add_field_show_request</b></br>Отображать запрос к серверу',
            },
            nav_menu_develop: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: '<b>nav_menu_develop</b></br>Разработка новой навигации'
            },
            moderate_dlg_develop: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: '<b>moderate_dlg_develop</b></br>Модерация в диалоговом окне'
            },
            procedures_documentation_develop: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: '<b>procedures_documentation_develop</b></br>Закупочная документация'
            },
            new_ecp_develop: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: '<b>new_ecp_develop</b></br>Новый механизм ECP'
            },
            ms_pp_instance: {
                group: '{dev_tab}/Develop',
                type: 'string',
                label: 'ms_pp_instance',
                has_del: true,
                description: "$Надо Владу"
            },         
            lng_debug: {
                group: '{dev_tab}/Debug',
                type: 'bool',
                label: '<b>lng_debug</b></br>Работа с переводами',
                onChange(value, oldValue) {
                    Language.debug = value;
                    if (oldValue == undefined)
                        return;

                    Language.onUpdate();
                }
            },
            debug_raw: {
                group: '{dev_tab}/Debug',
                type: 'bool',
                label: '<b>debug_raw</b></br>Не показывать переводы',
                onChange(value, oldValue) {
                    Language.debug_raw = value;
                    if (oldValue == undefined)
                        return;

                    Language.onUpdate();
                }
            },
            backend_setting: {
                group: '{dev_tab}/BackEnd',
                type: 'widget',
                label: "!",
                widget: {
                    name: 'iac-model',
                    props: {
                        request: {
                            host: "api",
                            method: "debug_panel"
                        }
                    }
                }
            }
        }
    }
}

export default new Develop();