import { Language } from '@iac/core'
import BasicCalc from './basic.model'
import { Settings } from '@iac/kernel'

export class Seller extends BasicCalc {
  constructor(currentBRV, showResult) {
    super(currentBRV, showResult)
  }

  props() {
    return {
      start_price: {
        description:'$calc.start_price_description',
        required: true,
        label: 'enter_procedure_start_price',
        type: 'float'
      },
      commercial_price: {
        required: true,
        label: 'enter_commercial_price_with_vat',
        type: 'float'
      }
    }
  }

  calcResult() {
    const result = { data: { deposit_percent: 3, currentBRV: this.currentBRV, price: 0 }, title: '', total: 0 }
    result.fields = this.fields.map(({ name, required, type, value }) => (result.data[name] = value, { name, required, type, value: value?.exp?.value ?? value }))

    result.error = this.prevalidate(result)
    if (result.data.commercial_price > result.data.start_price) {
      this.addErrorToResult(result, 'commercial_price', Language.t('commercial_price_cannot_be_highter_start'))
      this.addErrorToResult(result, 'start_price', Language.t('commercial_price_cannot_be_highter_start'))
    }
    const maximumStartPrice = 25000 * result.data.currentBRV
    if (result.data.start_price >= maximumStartPrice) {
      this.addErrorToResult(result, 'start_price', `${Language.t('start_price_must_be_lower_25000BRV')} (${maximumStartPrice} ${Settings._default_currency})`)
    }
    if (result.error) {
      this.setError(result.error)
      return
    }

    const computed = { deposit: 0, anti_dumping: 0, commission: 0 }
    result.data.price = result.data.commercial_price
    const anti_dumping_percent = Math.min(100 - result.data.price * 100 / result.data.start_price, 100)
    if (anti_dumping_percent >= 20) {
      computed.anti_dumping = result.data.start_price - result.data.price
      const brv_anti_dumping_koefficient = (result.data.start_price <= 2500 * result.data.currentBRV) ? 100 : 1000
      computed.anti_dumping = this.uzRound(Math.min(computed.anti_dumping, brv_anti_dumping_koefficient * result.data.currentBRV))
    }
    computed.deposit = this.uzRound(result.data.commercial_price * result.data.deposit_percent * 0.01)
    computed.commission = this.uzRound(result.data.price * 0.0015)
    result.total = this.calcTotal(computed.deposit, computed.anti_dumping, computed.commission)

    result.computed = computed
    this.showResult(result)
  }
}

export class Buyer extends BasicCalc {
  constructor(currentBRV, showResult) {
    super(currentBRV, showResult)
  }

  props() {
    return {
      start_price: {
        description:'$calc.start_price_description',
        required: true,
        label: 'enter_procedure_start_price',
        type: 'float',
        attr: { react: true }
      }
    }
  }

  calcResult() {
    const result = { data: { deposit_percent: 3, currentBRV: this.currentBRV, price: 0 }, title: '', total: 0 }
    result.fields = this.fields.map(({ name, required, type, value }) => (result.data[name] = value, { name, required, type, value: value?.exp?.value ?? value }))
    
    result.error = this.prevalidate(result)
    const maximumStartPrice = 25000 * result.data.currentBRV
    if (result.data.start_price >= maximumStartPrice) {
      this.addErrorToResult(result, 'start_price', `${Language.t('start_price_must_be_lower_25000BRV')} (${maximumStartPrice} ${Settings._default_currency})`)
    }
    if (result.error) {
      this.setError(result.error)
      return
    }

    const computed = { deposit: 0, commission: 0 }
    result.data.price = result.data.start_price
    computed.deposit = this.uzRound(result.data.price * result.data.deposit_percent * 0.01)
    computed.commission = this.uzRound(Math.min(result.data.price * 0.0015, this.maxCommission.buyer))
    result.total = this.calcTotal(computed.deposit, computed.commission)

    result.computed = computed
    this.showResult(result)
  }
}
