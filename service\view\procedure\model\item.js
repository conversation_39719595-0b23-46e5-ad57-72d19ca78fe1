import ProcedureEntity from './entity';
import { Language } from '@iac/core';

export default class Item extends ProcedureEntity {
    constructor(context = {}, lot) {

        /**Костыль - желательно перенести на сторону бэка */
        let {product} = context.fields
        if(product && product.inserted_ref){
            product.value = product.inserted_ref
        }

        context.props = { ...context.fields }

        

        /*if (context.props.product) {

            context.props.product.actions = [
                {
                    label: 'delete_position',
                    handler: async () => {
                        if (await Vue.Dialog.MessageBox.Question(Language.t('question_delete_position')) == Vue.Dialog.MessageBox.Result.Yes) {
                            await lot.del_item(this);
                        }
                    },
                    hidden: e => !this.access("del_item")
                }
            ]
        }*/

        super(context, lot)
        this.lot = lot;
        this.name = context.name;
    }

    async delete(){
        if (await Vue.Dialog.MessageBox.Question(Language.t('question_delete_position')) == Vue.Dialog.MessageBox.Result.Yes) {
            await this.lot.del_item(this);
        }
    }
}