import Procedure from './procedure';
import { Develop } from '@iac/kernel'
@Procedure.Registration("tender")
export default class Tender extends Procedure {

    constructor(context = {}) {
        super(context);
    }

    get procedure_actions() {
        return [
            { buttons: 1, group: 'general', order: 1000 },
            { buttons: ( (this.gos || this.gup) ) ? 0 : 3, group: 'quality_fields', order: 299 },
            { buttons: ( (this.gos || this.gup) ) ? 2 : 3, group: 'tech_fields', order: 1000 },
            /*{ buttons: 3, group: 'commercial_fields', order: 1000,
            hidden:(button)=>{
                if(button == 2 && this.method_marks == 'min_price')
                    return true;
            } },*/
        ]
    }

    static async init_context(context) {
        //=================START=================
        //Временный костыль для сокрытия на фронте полей в формах, пока не поправили на бэке.
        // TODO:Удалить как исправят на бэке. Согласовано на планерке с Костей и ДД от 4 апреля 2022
        if (context.status !== "close" && context.part_id) {
            [
                //form1
                "contragent_face_proposal_",
                "contragent_phone_proposal_",
                "contragent_title_form_1_proposal_",
                //form2
                "contragent_title_form_2_proposal_",
                //form3
                "contragent_title_form_3_proposal_",
                "legal_address_proposal_",
                "legal_contact_proposal_",
                "register_proposal_",
                //form4
                "contragent_title_form_4_proposal_"
            ]
                .map(item => `${item}${context.part_id}`)
                .forEach(item => context.props[item] && (context.props[item].hidden = true))
        }
        //==================END==================
        return {

        }
    }
}