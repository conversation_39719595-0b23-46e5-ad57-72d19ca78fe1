

import {Http} from '@iac/core'

export default class Model {

    constructor(context) {
        this.year = context.year
        this.data = context.data
        this._months = undefined
    }

    get months() {
        if (!this._months) {
            this._months = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11].map((month, index) => {

                let w = new Date(this.year, month, 1).getDay() - 1;
                if (w < 0)
                    w = 6;
                var days = new Date(this.year, month + 1, 0).getDate()
                days = Array(days).fill(0).map((item, index) => {
                    let key = `${this.year}-${('00'+(month+1)).slice(-2)}-${("00"+(index + 1)).slice(-2)}`
                    let is_work_day = true;
                    if(this.data[key] && this.data[key].is_work_day != undefined){
                        is_work_day = this.data[key].is_work_day;
                    }else{
                        is_work_day =(((index+w+2) % 7) && ((index+w+1) % 7)) ? true : false
                    }

                    return {
                        key: key,
                        value: index + 1,
                        is_work_day: is_work_day,
                        set: async function(context){
                            let {error, data} = await Http.api.rpc("common_ref",{
                                "ref": "ref_calendar",
                                "op": "update",
                                data: {
                                    date: key,
                                    data: context
                                }
                            })
                            if(!error){
                                this.is_work_day = context.is_work_day
                            }
                        }
                    };
                })
                return {
                    index: index,
                    days: days,
                    index_days: Array(w).fill(null).concat(days)
                }
            })
        }
        return this._months;
    }

    static async get(year) {

        let {error, data} = await Http.api.rpc("common_ref", {
            ref: "ref_calendar",
            op: "read",
            filters: {
                date_gte: `${year}-01-01`,
                date_lte: `${year}-12-31`
            },
            fields: [
                "date",
                "short_data"
            ]
        })

        if(error){
            return {
                error: error
            }}

        data = data.reduce((acc,curr)=>{
            acc[curr.date] = {
                is_work_day: curr.short_data.is_work_day
            }
            return acc
        },{})

        return {
            data: new Model({
                year: year,
                data: data
            })
        }
    }

}