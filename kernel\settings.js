import { Event } from '@iac/core'
class Settings {
    @Event onUpdate;
    constructor(context, proto) {
        this.enable = undefined;
        if (context)
            this.set(context);
    }

    set(context) {

        for (let name in this) {
            delete this[name];
        }

        this.enable = context.enable;
        for (let name in context.params) {
            Vue.set(this, "_" + name, context.params[name])
        }

        for (let name in context.childs) {
            if (context.childs[name].enable) {
                Vue.set(this, name, new Settings(context.childs[name], this))
                this[name].__proto__ = this;
            } else {
                Vue.set(this, name, undefined)
            }
        }
        this.onUpdate();
    }
}

export default new Settings();
