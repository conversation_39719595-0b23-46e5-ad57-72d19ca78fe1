    server {
        listen 80 ;

        server_name  localhost;



        location / {
            etag   on;
            root   /usr/share/nginx/_html;
            index  index.html index.htm;

            try_files $uri /index.html; #try_files работает быстрее условия
            if ($request_filename ~ "^(.*/)[index]\.html$") {
                add_header Last-Modified $date_gmt;
                add_header Cache-Control 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0, s-maxage=0';
                add_header Pragma 'no-cache';
                expires off;
            }

            gzip on;
            # Минимальная длина ответа, при которой модуль будет жать, в байтах
            gzip_min_length  1000;
            # Разрешить сжатие для всех проксированных запросов
            gzip_proxied     any;
            # MIME-типы которые необходимо жать
            gzip_types       text/plain application/xml application/x-javascript application/javascript text/javascript text/css text/json;
            # Запрещает сжатие ответа методом gzip для IE6  (старый вариант gzip_disable     "msie6";)
            gzip_disable "MSIE [1-6]\.(?!.*SV1)";
            # Уровень gzip-компрессии
            gzip_comp_level  4;

            location ~* \.(eot|ttf|woff|woff2|svg)$ {
                add_header Access-Control-Allow-Origin *;
            }
        }
    }