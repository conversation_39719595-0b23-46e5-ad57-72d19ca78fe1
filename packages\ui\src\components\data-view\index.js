import DataSource from "../../../../data/src/data_source"
import RefStore from "../../../../data/src/ref_store"
import {DataViewItemDefault} from './item'
export * from './item'

var fieldValue = {
    props: ["field", "value"],
    data: function () {
        return {
            componentName: undefined
        }
    },
    async mounted() {
        this.componentName = "div"
    },
    template1: `
     <div  >{{value}}</div>
    `,
    template: `
        <iac-date v-if='field.type == "date"' :date="value"  withoutTime withMonthName/>
        <ui-ref v-else-if='field.type == "entity"' :source='field.source' :value='value'/>
        <iac-number v-else-if='field.type == "float"' :value='value' delimiter=' ' part='2'/>
        <template v-else>{{value}}</template>
    `
}



export var DataView = {
    name: "ui-data-view",
    //props: ["dataSource", "type"],
    props: {
        sync: {
            type: Boolean,
            default: true
        },
        dataSource: Object,
        type: String,
        toolbar: {
            type: Boolean,
            default: true
        },
        search: {
            type: Boolean,
            default: true
        },
        showCheckedItems: {
            type: Boolean,
            default: true
        }
    },
    data: function () {
        let $this = this;
        return {
            get view_type() {
                if (!$this.toolbar) {
                    return $this.type || "tile"
                }
                return $this.type || localStorage.getItem("dataView-type") || "tile"
            },
            set view_type(value) {
                localStorage.setItem("dataView-type", value)
            },
            get show_filter() {
                return localStorage.getItem("dataView-show_filter") == 'false' ? false : true
            },
            set show_filter(value) {
                localStorage.setItem("dataView-show_filter", value)
            },
            get show_filter_modal() {
                return localStorage.getItem("dataView-show_filter_modal") == 'true' ? true : false
            },
            set show_filter_modal(value) {
                localStorage.setItem("dataView-show_filter_modal", value)
            },
            show_filter_actions: [
                {
                    label: "close_filter",
                    icon: "sliders_delete",
                    hidden: () => {
                        return !this.show_filter;
                    },
                    handler: () => {
                        this.show_filter_modal = false;
                        this.show_filter = false
                    }
                },
                {
                    label: "show_filter_on_the_right",
                    icon: "sliders",
                    hidden: () => {
                        return this.show_filter;
                    },
                    handler: () => {
                        this.show_filter_modal = false;
                        this.show_filter = true
                    }
                },
                {
                    type: "sep"
                },
                {
                    label: "show_filter_on_the_side",
                    icon: "sliders_up",
                    handler: () => {
                        this.show_filter = false;
                        this.show_filter_modal = true;
                        this.show_filter_dlg();
                    }
                }
            ],
            checkedSource: new DataSource({
                template: this.dataSource.template,
                store: new RefStore({
                    ...this.dataSource.store,
                    op: "get",
                    injectQuery: async (params) => {
                        params.filters = {
                            id: this.dataSource.checkedItems
                        }
                        return params;
                    }
                })
            })
        }
    },
    computed: {
        filters() {
            return this.dataSource.query.fields.filter((field) => {
                if (field.hidden && typeof field.hidden == 'function') {
                    return !field.hidden();
                }
                return !field.hidden;
            })
        },
        classes() {
            return [
                "ui-data-view",

                (() => {
                    return `view_type_${this.view_type}`
                })(),
                {

                }
            ]
        },
        columns() {
            if (!this.dataSource.columns)
                return;
            return Object.keys(this.dataSource.columns).map((name) => {
                let column = this.dataSource.columns[name];
                column.name = name
                column.label = column.label || name
                column.field = column.field || name
                return {
                    ...column,
                }
            })
        },
        groups() {
            let groups = {};
            if (!this.columns)
                return;
            this.columns.forEach(column => {
                column.group = column.group || "general"
                groups[column.group] = groups[column.group] || [];
                groups[column.group].push(column)
            });
            return groups;
        },
        action_filter_icon() {
            if (this.show_filter_modal) {
                return "sliders_up"
            } else if (this.show_filter) {
                return "sliders_delete"
            }
            return "sliders"
        }
    },
    methods: {
        set_view(type) {
            this.view_type = type;
        },
        show_filter_dlg() {
            Vue.Dialog({
                props: ["fields"],
                template: `
                    <div>
                        <main>
                            <div class='ui-data_filter'>
                                <div class='content'>
                                    <ui-layout :fields='fields' />
                                </div>
                            </div>
                        </main>
                        <footer style='background: linear-gradient(0, #FFF, #FFF0); position: sticky;bottom: 0;margin: 0;padding: 24px;pointer-events: none;'>
                            <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('Close')}}</ui-btn>
                        </footer>
                    </div>
                `
            }).Modal({
                size: "right",
                fields: this.dataSource.query.fields
            })
        },
        onItemHandler(start) {
            if (start)
                this.$el.classList.add("iac-wait");
            else
                this.$el.classList.remove("iac-wait");
        }
    },
    components: {
        fieldValue: fieldValue,
        defaultTemplate: DataViewItemDefault,
    },
    template: `
        <div v-bind:class="classes">
            <div class='ui-data_content'>
                <div class='search' v-if='search'>
                    <ui-input 
                        :has_del='true' icon='search' label='search' 
                        :value='dataSource.query.properties.queryText.value'
                        v-on:change="dataSource.query.properties.queryText.value = $event">
                        <div v-if='filters.length > 0' class='action sliders up' slot='toolbar' v-on:click='show_filter_dlg()'><icon>sliders_up</icon></div>
                        <ui-action  v-if='filters.length > 0' class='sliders' :handler='()=>{show_filter_modal ? show_filter_dlg() :  show_filter=!show_filter}' :icon='action_filter_icon' slot='toolbar' :actions='show_filter_actions'/>
                    </ui-input>
                </div>
                <div class='toolbar' v-if='toolbar'>
                    <div class='sort'>
                        <ui-field :model='dataSource.query.properties.order_by' />
                        <ui-entity v-if='0' icon='sort'  label='sort_by' :value='{name: "По дате"}' />

                        <ui-entity disabled :dataSource='checkedSource' v-if='0 && dataSource.checkedItems && dataSource.checkedItems.length > 0' icon='check2' label='sort_by' :value='{name: dataSource.checkedItems.length}' />
                        
                        <ui-btn type='primary empty' disabled v-if='showCheckedItems && dataSource.checkedItems && dataSource.checkedItems.length > 0'>
                            <icon>check2</icon> {{dataSource.checkedItems.length}}
                        </ui-btn>
                        <ui-action v-on:item_handler='onItemHandler' buttons icon='action' :actions='dataSource.actions' />   
                        <div style='color: #888; text-align: right;' v-if='dataSource.count'>{{$t("items")}}: {{dataSource.items.length}} / {{dataSource.count}}</div> 
                    </div> 
                    
                    <div class='view' v-if='!type'>
                        <icon :class='view_type == "tile" && "active"' v-on:click='set_view("tile")'>grid_3x3</icon>
                        <icon :class='view_type == "grid" && "active"' v-on:click='set_view("grid")'>grid</icon>
                        <icon :class='view_type == "row" && "active"' v-on:click='set_view("row")'>grid_1x3</icon>
                        <icon v-if='columns' :class='view_type == "table" && "active"' v-on:click='set_view("table")'>table</icon>
                    </div>

                    <div class='search_action' v-if='!search'>
                        <div v-if='filters.length > 0' class='action sliders up' v-on:click='show_filter_dlg()'><icon>sliders_up</icon></div>
                        <ui-action  v-if='filters.length > 0' class='sliders' :handler='()=>{show_filter_modal ? show_filter_dlg() :  show_filter=!show_filter}' :icon='action_filter_icon' :actions='show_filter_actions'/>
                    </div>

                    
                </div>
                
                <ui-list :sync="sync" class='content sticky' :dataSource='dataSource' :raw='true' >
                    <div v-if='view_type == "table"' class='table-wrapper' slot='items' slot-scope='props'>
                        <table>
                            <thead>
                                <tr>
                                    <th/>
                                    <th v-for='column in columns' >{{$t(column.label)}}</th>    
                                </tr>
                            </thead>
                            <tbody>
                                <tr :key='item.key' v-for='(item,position) in props.items'>
                                    <td><input type='checkbox' :id="item.id" :value="item.id" v-model='dataSource.checkedItems' /></td>
                                    <td v-for='column in columns' >
                                        <field-value :field='column' :value='item[column.name]' />
                                    </td>
                                </tr> 
                            </tbody>
                        </table>
                    </div>
                    <div class='data-view-item' slot='item' slot-scope='props'>
                        <slot name='item' :item='props.item'>
                            <component v-if='dataSource.template' 
                                :is='dataSource.template' 
                                :model='props.item' />
                            <default-template v-else :model='props.item'/>

                        </slot>
                    </div>   
                    <slot name='not-found' slot='not-found' slot-scope='props'>
                                    
                    </slot>  
                </ui-list>
                <div style='padding: 6px 0; color: #888;' v-if='0 && dataSource.count'>{{$t("items")}}: {{dataSource.items.length}} / {{dataSource.count}}</div>
            </div>
            <div class='ui-data_filter border' v-if='filters.length > 0 && show_filter'>
                <div class='content'>
                    <ui-layout :fields='dataSource.query.fields' />
                </div>
            </div>
        </div>
    `
}