.card {
  padding: 28px 32px;
  background-color: #fff;
  color: #201d1d;

  &__number {
    margin-bottom: 4px;
    color: #919191;
  }

  &__table {
    margin-bottom: 4px;

    td {
      border-bottom: 1px solid #f2f2f2;
      padding: 11px 0 8px;
    }

    a {
      color: #009ab8;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  &__label {
    padding-right: 10px;
    color: #919191;
  }
}

.list {
  margin: 0;
  padding: 0;
  list-style: none;

  &__item {
    position: relative;
    margin-bottom: 8px;
    padding-left: 16px;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      width: 8px;
      height: 8px;
      background-color: #009ab8;
      border-radius: 50%;
      transform: translateY(-50%);
    }

    &:last-child {
      margin-bottom: 3px;
    }
  }
}