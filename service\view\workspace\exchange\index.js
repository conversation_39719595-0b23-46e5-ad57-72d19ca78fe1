import QuotationList from './registry/quotation_list'
import CommissionList from './registry/commission_list'
import CommissionArbitration from './registry/commission_arbitration'
import ArbitrationComplaintsList from './registry/arbitration_complaint_list'
import ArbitrationComplaintDetailed from './arbitration_complaint_details'
import BrokersList from './registry/brokers_list'
import BrokerRequestsList from './registry/broker_requests_list'
import Exposition from '../../registry/exposition_list'
import IssuingSchedule from './registry/issuing_schedule'
import Shipmentchedule from './registry/shipment_schedule'
import BurseProductRequest from './registry/burse_product_request'
import Admin from './admin'
import BlackList from './black_list'
import Reassign from './reassign'

export default [{
    path: 'admin',
    component: Admin
}, {
    path: 'quotation_list',
    component: QuotationList,
}, {
    path: 'commission_list',
    component: CommissionList
}, {
    path: 'brokers_list',
    component: BrokersList
}, {
    path: 'broker_requests_list',
    component: BrokerRequestsList
}, {
    path: 'exposition_list',
    component: Exposition,
    props: { scope: 'private' },
}, {
    path: 'issuing_schedule',
    component: IssuingSchedule,
    props: { scope: 'private' },
}, {
    path: 'shipment_schedule',
    component: Shipmentchedule,
    props: { scope: 'private' },
}, {
    path: 'commission_arbitration',
    component: CommissionArbitration
}, {
    path: 'arbitration_complaint_list',
    component: ArbitrationComplaintsList
}, {
    path: 'arbitration_complaint_list/:id',
    component: ArbitrationComplaintDetailed
}, {
    path: 'burse_product_request',
    component: BurseProductRequest
}, {
    path: 'black_list',
    component: BlackList
},{
    path: 'reassign',
    component: Reassign
}]
