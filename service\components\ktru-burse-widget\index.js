import userSelectSpecification from './modals/user_select_specification'
import userCreateSpecification from './modals/user_create_specification'

import userSelectBurseProductRequest from './modals/user_select_burse_product_request'
import userCreateBurseProductRequest from './modals/user_create_burse_product_request'
import userShowBurseProductRequest from './modals/user_show_burse_product_request'

import adminMergeBurseProductRequest from './modals/admin_merge_burse_product_request'
import adminDashboardSelect from './modals/admin_dashboard_select'

export default {
  showProduct(currentItem) {
    userShowBurseProductRequest.Modal({ size: "lg", currentItem, })
  },
  async selectSpecification(onSelect) {
    return await userSelectSpecification.Modal({ size: "right", onSelect })
  },
  async createSpecification(currentItem, onSelect) {
    return await userCreateSpecification.Modal({ size: "lg", currentItem, onSelect })
  },
  async addProduct(onSelect) {
    return await userSelectBurseProductRequest.Modal({ size: "right", onSelect })
  },
  async editProduct(currentItem, onSelect) {
    return await userCreateBurseProductRequest.Modal({ size: "lg", currentItem, onSelect })
  },
  async mergeProduct(currentItem, onSelect) {
    return await adminMergeBurseProductRequest.Modal({ size: "lg", currentItem, onSelect })
  },
  async dashboardProduct(onSelect) {
    return await adminDashboardSelect.Modal({ size: "right", onSelect })
  },
}