import { Language } from '@iac/core'
import { DataSource, Property } from '@iac/data'

let codes = [
    [true, , , "42,46,64,68,84", "40,45", "41,73", , "49,58,64,70,71,84", "21,58,67,84,87", "09,29,49,68,69,76", "39"],
    [, "", "-9", "-9", "-9", "-9", "-9", "-9", "", "", "-9"],
    [, "", "", "", "", "", "-9", "", "-9", "-9", ""],
    [, "", "", "-9", "", "", "", "", "", "", ""],
    [, "-9", "", "", "", "", "", "", "", "", "-9"],
    [, "", "", "", "", "", "", "", "-9", "-9", "-9"],
    [true], //[true, , , , "", "", , "", "", "", ""],
    [, "-9", "", "", "", "", "-9", "", "-9", "-9", ""],
    [, "", "", "", "", "", "", "-9", "-9", "", "-9"]
]

let country_codes = [];
codes.forEach((line, first) => {
    first++;
    line.forEach((second, index) => {
        if (index == 0 && second) {
            return country_codes.push(first);
        }

        index--;
        second.split(",").forEach((suffix) => {
            let [start, end] = suffix.split("-")
            if (!end) {
                country_codes.push(Number(`${first}${index}${start}`));
            } else {
                for (let i = start || 0; i <= end; i++) {
                    country_codes.push(Number(`${first}${index}${i}`));
                }
            }
        })
    })
})
//let country_codes_sort = [...country_codes].sort((a,b)=>{return a - b;});



let country_source = new DataSource({
    search: true,
    store: {
        data: country_codes.map((id) => {
            return {
                id,
                name: "+" + id
            }
        })
    },
    template: {
        props: ["model"],
        template: `
            <div><span style='width: 16px; display: inline-block;'></span><span><span style='color: #333'>{{model.country}}</span>&nbsp;<span style='color: #999'>+{{model.id}}</span></span></div>
        `
    }
})

let parse = function (value = '') {
    value = value.replace(/\D/g, '')
    let code, number = undefined;

    for (let country_code of [...country_source.store._array].sort((a, b) => { return b.id - a.id })) {
        if (value.indexOf(country_code.id) == 0) {
            code = country_code
            number = value.substring((country_code.id + "").length)
            break;
        }
    }
    return {
        code: code,
        number: number
    }
}


const PhoneComponent = {
    name: "iac-phone",
    props: {
        icon: String,
        label: String,
        status: Object,
        value: Object,
        readonly: Boolean,
        disabled: Boolean,
        wait: Boolean,
        actions: Array,
        has_del: Boolean,
        required: Boolean,
        default_code: {
            type: Number,
            default: 998
        }
    },
    data: function () {
        return {
            code: undefined,
            number: undefined,
            source: country_source
        }
    },
    watch: {
        value: {
            immediate: true,
            async handler(value, oldValue) {
                if (value && value == oldValue)
                    return;
                this.parse(value);
            }
        }
    },
    computed: {
        controlProps() {
            return {
                icon: this.controlIcon,
                wait: this.wait,
                actions: this.actions,
                label: this.label,
                status: this.status,
                readonly: this.readonly,
                disabled: this.disabled,

                //class: this.classes,
                has_del: this.has_del
            }
        },
        phone_number() {
            return `+${this.code && this.code.id ? this.code.id : this.code}${this.number || ''}`
        },
        listeners: function () {
            var vm = this
            return Object.assign({},
                this.$listeners,
                {
                    input: function (number) {
                        vm.set_number(number, 'input');
                    },
                    change: function (number) {
                        vm.set_number(number, 'change');
                    }
                }
            )
        }
    },
    methods: {
        async parse(value) {
            if (!value) {
                this.code = this.default_code && await country_source.byKey(this.default_code);
                this.number = undefined;
                if (this.code) {
                    this.$emit('change', "+" + this.default_code)
                    this.$emit('input', "+" + this.default_code)
                }

                return;
            }
            value = value.replace(/\D/g, '')

            let code, number = undefined;

            for (let country_code of [...country_source.store._array].sort((a, b) => { return b.id - a.id })) {
                if (value.indexOf(country_code.id) == 0) {
                    code = country_code
                    number = value.substring((country_code.id + "").length)
                    break;
                }
            }

            this.code = code || this.default_code && await country_source.byKey(this.default_code);
            this.number = number;

            if (value != `+${code && code.id ? code.id : code}${this.number || ''}`) {
                value = `+${code && code.id ? code.id : code}${this.number || ''}`
                this.$emit('change', value)
                this.$emit('input', value)
            }

        },
        set_code(code) {

            let value = `+${code && code.id ? code.id : code}${this.number || ''}`;

            this.$emit('change', value)
            this.$emit('input', value)
        },
        set_number(number, event) {
            let value = `+${this.code && this.code.id ? this.code.id : this.code}${number || ''}`;
            this.$emit(event, value)
        }
    },
    template: `
        <ui-input class='iac-phone' v-bind="controlProps" :readonly='readonly || !code' type='number' :value='number' v-on="listeners" >
            <div slot='prefix' >
                <ui-entity :search_timeout='0' :readonly='readonly' class='code' :dataSource='source' :value='code' v-on:input='set_code'/>
            </div>
        </ui-input>
    `
}

Vue.component('iac-phone', PhoneComponent);
Vue.Fields.phone = { is: 'iac-phone' }

Property.validate.phone = function () {
    if (this.required) {
        let { number } = parse(this.value);
        if (!number)
            return Language.t("required_field")
    }
}