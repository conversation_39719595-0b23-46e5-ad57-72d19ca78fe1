import { DataSource, Query, RefStore } from '@iac/data'
import { Http, Language } from '@iac/core'
import { Context, Config, Develo<PERSON>, Settings } from '@iac/kernel'

// получаем список трейдеров
function createTraderStore() {
    return {
        method: "ref_exchange_reassign",
        injectQuery: (params) => {
            params.op = "list_traders";
            return params;
        },
        inject: function(items) {
            // Сначала активные, потом неактивные
            if (!items || !Array.isArray(items)) return items;
            
            return items.sort((a, b) => {
                return (b.is_active - a.is_active) || 0;
            });
        },
        context: (traderContext) => {
            traderContext.status_type = traderContext.is_active === false ? 'warning' : null;
            if (traderContext.is_active === false && traderContext.name) {
                traderContext.name = `${traderContext.name} (${Language.t('user.inactive')})`;
            }
            return traderContext;
        }
    };
}

// Функция для форматирования имени трейдера
function formatTraderName(userDetails) {
    if (!userDetails) return '';
    
    const name = `${userDetails.surname || ''} ${userDetails.name || ''} ${userDetails.patronymic || ''}`.trim() || userDetails.id;
    
    if (userDetails.is_active === false) {
        return `${name} (${Language.t('user.inactive')})`;
    }
    
    return name;
}

let contract_dialog = Vue.Dialog({
    props: ["source"],
    template: `
    <div>
        <main>
            <ui-data-view :sync="false" :dataSource="source"/>
        </main>
        <footer>
            <ui-btn type='secondary' @click.native='Close()'>{{$t('close')}}</ui-btn>
        </footer>
    </div>
    `
});

export default {
    data: function () {
        return {
            statusMap: {},

            sourceInitiator: new DataSource({
                store: {
                    method: "ref_exchange_reassign",
                    ref: "ref_exchange_reassign",
                    injectQuery: (params) => {
                        params.filters.position = "initiator";
                        return params;
                    },
                    context: (itemContext) => {
                        itemContext.checkbox = true;    
                        itemContext.status_type = itemContext.user_details?.is_active === false ? 'warning' : null;
                        itemContext.title = {
                            text: itemContext.name || itemContext.id,
                            link: `/procedure/${itemContext.id}/core`
                        };
                        itemContext.header = [
                            `№${itemContext.id}`,
                            this.statusMap[itemContext.status]?.name || itemContext.status
                        ];
                        itemContext.description = [
                            {
                                props: ["model"],
                                template: `
                                    <div>
                                    <label>{{ $t('reports_inserted_at')}}:</label>
                                    <iac-date :date="model.created_at" withoutTime withMonthName/>
                                    </div>
                                `
                            },
                            {
                                label: Language.t('reassign.trader'),
                                text: formatTraderName(itemContext.user_details)
                            }
                        ];
                        itemContext.sub_title = {
                            text: `${itemContext.count} ${Language.t("reassign.contract", { count: itemContext.count, plural: true })}`,
                            link: itemContext.count > 0 ? () => {
                                contract_dialog.Modal({
                                    source: itemContext.sourceInitiator,
                                    size: "full"
                                });
                            } : null
                        };

                        const $itemContext = itemContext;

                        itemContext.sourceInitiator = new DataSource({
                            valueExp: "number",
                            store: new RefStore({
                                method: "ref_exchange_reassign",
                                ref: "ref_exchange_reassign",                                
                                injectQuery: (params) => {
                                    params.filters.parent_id = itemContext.id;
                                    params.filters.position = "initiator";
                                    params.filters.can_reassign = true;
                                    return params;
                                },
                                context: (contractContext) => {
                                    contractContext.checkbox = true;
                                    contractContext.status_type = contractContext.initiator?.user_details?.is_active === false ? 'warning' : null;
                                    
                                    contractContext.title = {
                                        text: `${Language.t('contract')} №${contractContext.number}`,
                                        link: `/workspace/contract/${contractContext.number}/core`
                                    };
                                    contractContext.description = [
                                        {
                                            label: Language.t("schedule_exchange_contract.initiator"),
                                            text: contractContext.initiator?.company_details?.title
                                        },
                                        {
                                            label: Language.t("schedule_exchange_contract.contragent"),
                                            text: contractContext.contragent?.company_details?.title
                                        },
                                        {
                                            label: Language.t('reassign.trader'),
                                            text: formatTraderName(contractContext.initiator.user_details)
                                        }
                                    ];
                                    contractContext.actions = [
                                        {
                                            label: "contract.reassign_trader",
                                            handler: async () => {
                                                const { error, data } = await Http.api.rpc('contract_action', {
                                                    action: 'reassign_trader',
                                                    number: contractContext.number,
                                                });
                                                if (error) {
                                                    Vue.Dialog.MessageBox.Error(error);
                                                    return;
                                                }
                                                await $itemContext.sourceInitiator.reload();
                                                await Vue.Dialog.MessageBox.Success(Language.t('data.save_success'));
                                                return data?.number || data;
                                            }
                                        }
                                    ];
                                    return contractContext;
                                }
                            }),

                            query: {
                                trader_id: {
                                    group: '!filter-',
                                    type: 'entity',
                                    has_del: true,
                                    dataSource: {
                                        store: createTraderStore()
                                    }
                                }
                            },
                            actions: [
                                {
                                    label: "contract.reassign_trader",
                                    hidden: () => !$itemContext.sourceInitiator.checkedItems?.length > 0,
                                    handler: async () => {
                                        const { error, data } = await Http.api.rpc('ref_exchange_reassign', {
                                            op: 'reassign',
                                            position: "initiator",
                                            contract_numbers: $itemContext.sourceInitiator.checkedItems
                                        });
                                        if (error) {
                                            Vue.Dialog.MessageBox.Error(error);
                                            return;
                                        }
                                        await $itemContext.sourceInitiator.reload();
                                        Vue.Dialog.MessageBox.Success(Language.t('data.save_success'));
                                        return data?.number || data;
                                    }
                                }
                            ]
                        });

                        return itemContext;
                    }
                },
                query: {
                    status: {
                        group: '!filter-',
                        type: 'entity',
                        has_del: true,
                        dataSource: {
                            store: {
                                ref: "ref_status_exchanges_contracts"
                            }
                        },
                    },
                    trader_id: {
                        group: '!filter-',
                        type: 'entity',
                        has_del: true,
                        dataSource: {
                            store: createTraderStore()
                        }
                    }
                },
                actions: [
                    {
                        label: "contract.reassign_trader",
                        hidden: () => !this.sourceInitiator.checkedItems?.length > 0,
                        handler: async () => {
                            const { error, data } = await Http.api.rpc('ref_exchange_reassign', {
                                op: 'reassign',
                                parent_ids: this.sourceInitiator.checkedItems,
                                position: "initiator"
                            });
                            if (error) {
                                Vue.Dialog.MessageBox.Error(error);
                                return;
                            }
                            await this.sourceInitiator.reload();
                            Vue.Dialog.MessageBox.Success(Language.t('data.save_success'));
                            return data?.number || data;
                        }
                    }
                ]
            }),

            sourceContragent: new DataSource({
                store: {
                    method: "ref_exchange_reassign",
                    ref: "ref_exchange_reassign",
                    injectQuery: (params) => {
                        params.filters.position = "contragent";
                        return params;
                    },
                    context: (itemContext) => {
                        itemContext.checkbox = true;
                        itemContext.title = itemContext.name || itemContext.id;
                        itemContext.status_type = itemContext.user_details?.is_active === false ? 'warning' : null;

                        itemContext.header = [
                            `№${itemContext.id}`,
                            this.statusMap[itemContext.status]?.name || itemContext.status
                        ];
                        itemContext.description = [
                            {
                                props: ["model"],
                                template: `
                                    <div>
                                    <label>{{ $t('reports_inserted_at')}}:</label>
                                    <iac-date :date="model.created_at" withoutTime withMonthName/>
                                    </div>
                                `
                            }
                        ];
                        itemContext.sub_title = {
                            text: `${itemContext.count} ${Language.t("reassign.contract", { count: itemContext.count, plural: true })}`,
                            link: itemContext.count > 0 ? () => {
                                contract_dialog.Modal({
                                    source: itemContext.sourceContragent,
                                    size: "full"
                                });
                            } : null
                        };

                        const $itemContext = itemContext;

                        itemContext.sourceContragent = new DataSource({
                            valueExp: "number",
                            store: new RefStore({
                                method: "ref_exchange_reassign",
                                ref: "ref_exchange_reassign",
                                injectQuery: (params) => {
                                    params.filters.parent_id = itemContext.id;
                                    params.filters.position = "contragent";
                                    params.filters.can_reassign = true;
                                    return params;
                                },
                                context: (contractContext) => {
                                    contractContext.checkbox = true;
                                    contractContext.status_type = contractContext.contragent?.user_details?.is_active === false ? 'warning' : null;    
                                    contractContext.title = {
                                        text: `${Language.t('contract')} №${contractContext.number}`,
                                        link: `/workspace/contract/${contractContext.number}/core`
                                    };
                                    contractContext.description = [
                                        {
                                            label: Language.t("schedule_exchange_contract.initiator"),
                                            text: contractContext.initiator?.company_details?.title
                                        },
                                        {
                                            label: Language.t("schedule_exchange_contract.contragent"),
                                            text: contractContext.contragent?.company_details?.title
                                        },
                                        {
                                            label: Language.t('reassign.trader'),
                                            text: formatTraderName(contractContext.contragent.user_details)
                                        }
                                    ];
                                    contractContext.actions = [
                                        {
                                            label: "contract.reassign_trader",
                                            handler: async () => {
                                                const { error, data } = await Http.api.rpc('contract_action', {
                                                    action: 'reassign_trader',
                                                    number: contractContext.number,
                                                });
                                                if (error) {
                                                    Vue.Dialog.MessageBox.Error(error);
                                                    return;
                                                }
                                                await $itemContext.sourceContragent.reload();
                                                await Vue.Dialog.MessageBox.Success(Language.t('data.save_success'));
                                                return data?.number || data;
                                            }
                                        }
                                    ];
                                    return contractContext;
                                }
                            }),
                            query: {
                                trader_id: {
                                    group: '!filter-',
                                    type: 'entity',
                                    has_del: true,
                                    dataSource: {
                                        store: createTraderStore()
                                    }
                                }
                            },
                            actions: [
                                {
                                    label: "contract.reassign_trader",
                                    hidden: () => !$itemContext.sourceContragent.checkedItems?.length > 0,
                                    handler: async () => {
                                        const { error, data } = await Http.api.rpc('ref_exchange_reassign', {
                                            op: 'reassign',
                                            position: "contragent",
                                            contract_numbers: $itemContext.sourceContragent.checkedItems
                                        });
                                        if (error) {
                                            Vue.Dialog.MessageBox.Error(error);
                                            return;
                                        }
                                        await $itemContext.sourceContragent.reload();
                                        Vue.Dialog.MessageBox.Success(Language.t('data.save_success'));
                                        return data?.number || data;
                                    }
                                }
                            ]
                        });

                        return itemContext;
                    }
                },
                query: {
                    status: {
                        group: '!filter-',
                        type: 'entity',
                        has_del: true,
                        dataSource: {
                            store: {
                                ref: "ref_status_exchanges_contracts"
                            }
                        },
                    },
                    trader_id: {
                        group: '!filter-',
                        type: 'entity',
                        has_del: true,
                        dataSource: {
                            store: createTraderStore()
                        }
                    }
                },
                actions: [
                    {
                        label: "contract.reassign_trader",
                        hidden: () => !this.sourceContragent.checkedItems?.length > 0,
                        handler: async () => {
                            const { error, data } = await Http.api.rpc('ref_exchange_reassign', {
                                op: 'reassign',
                                parent_ids: this.sourceContragent.checkedItems,
                                position: "contragent"
                            });
                            if (error) {
                                Vue.Dialog.MessageBox.Error(error);
                                return;
                            }
                            await this.sourceContragent.reload();
                            Vue.Dialog.MessageBox.Success(Language.t('data.save_success'));
                            return data?.number || data;
                        }
                    }
                ]
            })
        };
    },

    async mounted() {
        const response = await Http.api.rpc('ref', {
            ref: "ref_status_exchanges_contracts",
            op: "read"
        });
        if (response?.data) {
            this.statusMap = response.data.reduce((acc, item) => {
                acc[item.id] = item;
                return acc;
            }, {});
        }
    },

    template: `
    <iac-access :access='$policy.exchange_contract_reassign_trader'>
    <iac-section type='header'>
        <ol class='breadcrumb'>
            <li><router-link to='/'>{{$t('home')}}</router-link></li>
            <li>{{$t('contract.reassign_trader')}}</li>
        </ol>
        <div class='title'>
            <h1>{{$t('contract.reassign_trader')}}</h1>
        </div>
    </iac-section>
    <iac-section>
        <ui-layout-tab>
            <ui-layout-group label='Organize'>
                <ui-data-view :dataSource='sourceInitiator' />
            </ui-layout-group>
            <ui-layout-group label='participant_request'>
                <ui-data-view :dataSource='sourceContragent' />
            </ui-layout-group>
        </ui-layout-tab>
    </iac-section>
    </iac-access>
    `

};

