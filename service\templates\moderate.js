import Vue from "vue";

const Component = {
  props: ["model"],
  computed: {
    product_id(){
      return this.model.product && (this.model.product.product_id || "").replace('_','-');
    },
    link() {
      const model = this.model
      return `/procedure/${model.id}/core`
    }
  },
  methods: {
    async details(event){

      await Vue.Dialog.Procedure.Modal({
          proc_id:this.model.id,
          size: "full",
          actions: [
            "dislike_reduction",
            "dislike_ad",
            "publish_reduction",
            "publish_ad"
          ]
      })
      if(this.model.updateContext){
        this.model.updateContext();
      }
    }
  },
  template: `
        <ui-data-view-item :model='model'>
            
            <template slot='header'>
              <div>
                <span>№{{ model.id }}</span>
                <span v-if='model.updated_at'><iac-date :date='model.updated_at' full icon /></span>
              </div>
              <div v-if='model.status'>
                <ui-ref :source='"status_"+model.proc_type' :value='model.status'/>
              </div>
            </template>
            
            <template v-if='!$develop.moderate_dlg_develop' slot='title'>
                <router-link v-if='model.product_name' :to='link'>{{model.product_name}}</router-link>
                <router-link v-else :to='link'>{{$t(model.proc_type)}} №{{model.id}}</router-link>
            </template>
            <template v-else slot='title'>
                <a :href='link' v-if='model.product_name' v-on:click.stop.prevent='details'>{{model.product_name}}</a>
                <a v-else :href='link' v-on:click.stop.prevent='details'>{{$t(model.proc_type)}} №{{model.id}}</a>
            </template>

            <template slot='sub_title'></template>

            <template slot='description'>
              <div v-if='product_id'>
                <label>{{ $t('code') }}: </label>
                <span>{{product_id}}</span>
              </div>
              <div v-if="model.properties">
                <span>{{ model.properties }}</span>
              </div>  
              <div v-if='model.reject_reason'>
                <label>{{ $t('reject_reason') }}: </label>
                <ul>
                  <li v-for='item in model.reject_reason'> {{item}} </li>
                </ul>
              </div>
            </template>

            <template slot='props'>              
            </template>

        </ui-data-view-item>
    `,
}

Vue.component("template-moderate", Component)
