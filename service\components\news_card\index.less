.news-card {
  position: relative;
  height: 100%;
  font-size: @14px;
  background-color: @white;
  color: @gray;
  line-height: 1.43;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 0 24px rgba(223, 223, 223, 0.2);

  p {
    margin: 0;
  }

  &__link {
    display: block;
    height: 100%;
    color: inherit;
    text-decoration: none;
  }

  &__image {

    img {
      width: 100% !important;
      height: 160px;
      object-fit: cover;
    }
  }

  &__wrap {
    position: relative;
    padding: 12px 16px 36px;
    z-index: 1;
  }

  &__date {
    display: block;
    margin-bottom: 4px;
  }

  &__content {
    max-width: 448px;
  }
}

.news-title {
  display: block;
  margin: 0 0 4px;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: @dark;
}

.news-meta {
  display: flex;
  margin: 0 -4px;
  flex-wrap: wrap;
  align-items: center;

  &__date {
    padding: 0 4px;
  }
}

.news-date {
  font-size: @14px;
  color: @gray;
  line-height: 1.43;
}

.ui-tag-item {
  display: inline-block;
  margin: 4px;
  padding: 4px 8px;
  font-size: 14px !important;
  background: @pre-action;
  color: @brand-primary;
  line-height: 20px;
  text-decoration: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  border-radius: 4px;
  cursor: pointer;
}