import Contract from '../../model/contract'

let HistoryComponent = {
    props: ["model"],
    data: function () {
        return {
            general_setting: Vue.Tiling["settings"].general_setting,
            contract: undefined,
            columns: [
                {label: "tile.order_book",field:"order_id"},
                //"status",
                {label: "event_start",field: "session_starts_at",type: "date"},
                {label: "event_end",field: "session_ends_at",type: "date"}
            ]
        }
    },
    watch: {
        "model.contract": {
            immediate: true,
            async handler(contract, oldVal) {
                if (contract && this.contract && contract.id == this.contract.id)
                    return;

                if (this.contract) {
                    this.contract.unwatch(Contract.Event_General)
                    this.contract = undefined
                }
                if (contract && contract.id) {
                    this.contract = Contract.get(contract)
                    this.contract.watch(Contract.Event_General)
                }

            },
        }
    },
    computed: {
        $general_setting(){
            return this.general_setting.params
        },
        classes() {
            return [
                "iac-history-tile",
                {
                    striped: this.$general_setting.striped,
                }]
        },
    },
    beforeD<PERSON>roy() {
        setTimeout(() => {
            this.contract.unwatch(Contract.Event_General);
        }, 200)
    },
    methods: {
        onItem(item){
            Vue.Dialog.Procedure.Modal({
                proc_id: item.order_id,
                size: "full"
            })
        }
    },
    template: `
    <div v-bind:class="classes">
        <div class='content thin-scroll' style='overflow-y: scroll; flex: 1 1 100%;'>
            <ui-data-grid  class='on-item' :scroller='false' v-if='contract' :dataSource='contract.sourceHistory' :columns='columns' v-on:item='onItem' />
        </div>
    </div>
    `
}

Vue.Tiling("history", {
    component: HistoryComponent,
    // setting: OrderBookSetting,
    select_contract: true,
    select_group: true,
    required_contract: true
})