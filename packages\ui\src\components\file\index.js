//import { Http, Event, Language } from '@iac/core'
import Language from "../../../../core/src/language";

export var File = {
    name: "ui-file",
    props: ["icon", "meta", "label", "status", "value", "readonly", "disabled", "name", "type", "wait", "actions", "has_del", "required", "show_comment", "show_photo", "min", "max"],
    computed: {
        component_status() {
            if (typeof this.status == "string") {
                return {
                    type: this.status,
                    message: undefined
                }
            }
            return this.status;
        },
        classes() {
            return [
                (() => {
                    return this.component_status ? ["status", this.component_status.type] : '';
                })(),
                {
                    "opened": this.opened,
                    "disabled": this.disabled,
                    "readonly": this.readonly,
                    "wait": this.wait,
                    "show_photo": this.show_photo
                }
            ]
        },
        opened() {
            if (this.value || this.value === 0) {
                return true
            }
            return false;
        },
        has_del_file() {
            if (!this.opened || this.disabled || this.readonly || this.wait)
                return false;
            return true;
        },
        ctrl_action() {
            return [
                (!this.disabled && !this.readonly) ? {
                    label: "add_file",
                    icon: "field",
                    btn_type: "info",
                    handler: async () => {
                        
                        this.select();
                    }

                } : undefined,
                ...(this.actions ? this.actions : [])
            ]
        },
        url() {
            if (!this.value)// || (!this.disabled && !this.readonly))
                return;
            if (!this.meta)
                return;
            if (typeof this.meta.url == 'function')
                return this.meta.url(this.value)
            return this.meta.url;
        },
        file_size() {
            if (!this.value || !this.value.meta || !this.value.meta.size)
                return;

            return Number(this.value.meta.size / 1024).toFixed(2);
        },
        comment_ctrl() {
            if (!this.url || !this.show_comment)
                return false;
            if (!this.value.comment && (this.readonly || this.disabled)) {
                return false;
            }

            return true;
        }
    },
    methods: {
        onChangeComment(comment) {
            let value = { ...this.value }

            value.comment = comment;
            this.$emit('input', value)
        },
        onChange(event) {
            let file = event.target.files && event.target.files[0];
            let max_size = this.max || 15*1024*1024;
            if (file.size > max_size) {
                Vue.Dialog.MessageBox.Error(Language.t('file_size_exceeds_some_kb').replace('_____',Number(max_size / 1024).toFixed(2)))
            }else{
                this.$emit('input', { file, name: file.name })   
            }
            
            event.target.value = ""
        },
        action(event) {
            if (event == 'clear') {
                this.value = undefined;
                this.$emit('input', undefined)
                this.$emit('change', undefined)
            }
        },
        select() {
            if (!this.disabled && !this.readonly && this.$refs.input) {
                this.$refs.input.click()
            } else if (this.value) {
                
            }
        },
        async open(e) {
            if (!this.meta.url_mod)
                return;
            e.preventDefault();

            let url = await this.meta.url_mod(this.value);
            if (url)
                window.location = url;
        },
        photo() {
            if (this.url)
                return `url("${this.url}")`;
        }
    },
    template: `
        <div class='ui-file' v-bind:class="classes">
            <div v-if='value' class='file'>
                <div v-bind:style='{backgroundImage: photo()}'
                     class='icon'><icon>field</icon></div>
                <div class='content'>
                    <div class='name'>
                        <span v-if='!url'>{{value && value.name}}</span>
                        <a v-if='url' v-on:click='open' :href='url'>{{value && value.name}}</a>  
                        <icon class='close' v-if='has_del_file' v-on:click='action("clear")'>delete</icon>                  
                    </div>

                    <div class='size' v-if='file_size'>{{$t("file_size")}}: {{file_size}} КБ</div>
                    <div v-if='status && status.message' class='status_message'>{{status.message}}</div>
                    <div v-if='0' class='size' style='border:1px solid' contenteditable=true>Курсы javascript. Главная » Справочник » Объекты » replace. ... Первый параметр всегда содержит полную совпавшую подстроку. Если при вызове replace указано регулярное выражение, а не строка, то последующие параметры будут содержать значения скобочных групп. Наконец, последние два параметра</div>
                    
                    <ui-text v-if='comment_ctrl' :disabled='wait || disabled || readonly' label='comment' :value='value.comment' v-on:change='onChangeComment' />

                </div>
                
            </div>
            <div v-else class='add'>
                <span v-if='(disabled || readonly) && ctrl_action.length == 1' style='opacity: 0.2; font-size: 14px;'>{{$t('no_data')}}</span>
                <input ref="input" 
                    style='display: none' 
                    :name="name" 
                    type="file"
                    v-on:change='onChange'
                    :disabled="disabled || readonly"
                    :readonly="readonly"
                    />
                <ui-action :actions='ctrl_action' buttons/>
            </div>
        </div>
    `,
    template1: `
    <ui-control class='ui-input' v-bind:class="classes" 
    :icon='icon' :label='label' :opened='opened' :status='status' v-on:action='action' :wait='wait' :readonly='readonly' :disabled='disabled' :actions='actions' :has_del='has_del' :required='required'>
        <input ref="input" 
            style='display: none' 
            :name="name" 
            type="file"
            v-on:change='onChange'
            :disabled="disabled || readonly"
            :readonly="readonly"
            />    
        <div class='control' v-on:click='select'>
            <div v-if='!url'>{{value && value.name}}</div>
            <a v-if='url' v-on:click='open' :href='url'>{{value && value.name}}</a>
        </div> 
    </ui-control>
    
    `
}
