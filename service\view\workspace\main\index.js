import { Context } from '@iac/kernel'

import notificationContent from './../notification/content'

export default {
    data: function () {
        return {
            user: Context.User,
            access: Context.Access,
        }
    },
    components: {
        notificationContent
    },
    computed: {
        policy() {
            return Object.keys(this.access.policy).length
        }
    },
    template: `
        <div v-if='!user.name'>
            <iac-section>
                <ui-error class='page' code='403' :message='$t("NoAccess")'  />
            </iac-section>     
        </div>
        <div v-else>
            <iac-section type='header'>
                <h1>{{$t('workspace')}}</h1>
            </iac-section>
            <iac-section>
                <div v-if='!user.team_id'>
                    <ui-alert type='info'>{{$t('wp.reg_info')}}</ui-alert>

                </div>
                <div v-else>
                    <ui-alert v-if='!policy' type='warning'>
                        {{$t('ws.unroles')}}
                    </ui-alert>
                </div>
                <ui-layout-group label='nav.notification'>
                    <notificationContent />
                </ui-layout-group>
                
            </iac-section>
        </div>
    `
}