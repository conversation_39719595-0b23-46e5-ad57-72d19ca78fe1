import { Http } from '@iac/core'
import { findCurrentocaleItem } from './ktru_helper'

const renderTreeNode = {
    name: "renderTreeNode",
    components: {
        'renderTreeNode': renderTreeNode
    },
    data() {
        return {
            loadingCategories: false,
            loadingProducts: false,
            categories: [],
            products: []
        }
    },
    methods: {
        async getCategories(rootCategory = {}) {
            this.loadingCategories = true
            const { id: parent_id = undefined } = rootCategory
            const loadLimit = 90
            const tempItems = []
            let needMoreItems = true
            while (needMoreItems) {
                const { error: errorCat, data: dataCat } = await Http.api.rpc("ref", {
                    ref: "ref_enkt_categories",
                    op: "read",
                    limit: loadLimit,
                    offset: tempItems.length,
                    filters: { parent_id, is_root: parent_id ? undefined : true },
                    fields: ['id', 'code', 'data']
                })
                if (!errorCat && dataCat) {
                    dataCat.forEach(item => (delete item.__schema__, item.currentName = findCurrentocaleItem(item.data), item.isShow = false))
                    tempItems.push(...dataCat)
                }
                needMoreItems = tempItems?.length == loadLimit
            }
            this.categories = tempItems.sort((itemA, itemB) => (itemA.code < itemB.code) ? -1 : 1)
            this.loadingCategories = false
        },
        async getProducts(rootCategory = {}) {
            this.loadingProducts = true
            const { id: category_id = undefined, code: code_r = "" } = rootCategory
            const loadLimit = 90
            const tempItems = []
            let needMoreItems = true
            while (needMoreItems) {
                const { error: errorProd, data: dataProd } = await Http.api.rpc("ref", {
                    ref: "ref_enkt_products",
                    op: "read",
                    limit: loadLimit,
                    offset: tempItems.length,
                    filters: { code_r },
                    fields: ['id', 'name', 'code', 'type']
                })
                if (!errorProd && dataProd) {
                    dataProd.forEach(item => (delete item.__schema__, item.currentName = findCurrentocaleItem(item.name)))
                    tempItems.push(...dataProd)
                }
                needMoreItems = tempItems?.length == loadLimit
            }
            this.products = tempItems.sort((itemA, itemB) => (itemA.code < itemB.code) ? -1 : 1)
            this.loadingProducts = false
        },
        setCurrentItem(item) {
            this.$emit('setCurrentItem', item)
        },
        getNameStyle(item) {
          return item.blocked ? { backgroundColor: 'red' } : {}
        }
    },
    mounted() {
        this.getCategories(this.data)
        this.getProducts(this.data)
    },
    template: `
    <icon v-if="loadingCategories || loadingProducts" class="iac--to-spin">spinner</icon>
    <div class="iac--ktru__tree-node" v-else>
      <div class="iac--ktru__tree-category" v-for="(item,index) in categories" :key="index">
        <div @click="item.isShow = !item.isShow">
          <div>+</div>
          <div>{{item.code}}</div>
          <div :title="item.currentName.VALUE">{{item.currentName.VALUE}}</div>
        </div>
        <renderTreeNode v-if="item.isShow" :data="item"/>
      </div>
      <div class="iac--ktru__tree-product" v-for="(item,index) in products" :key="index" @click="e=>setCurrentItem(item)">
        <div>{{item.code}}</div>
        <div :title="item.currentName.VALUE" :style="getNameStyle(item)">{{item.currentName.VALUE}}</div>
      </div>
    </div>
    `
}

const renderTree = {
    name: "renderTree",
    components: {
        'renderTreeNode': renderTreeNode
    },
    data() {
        return {}
    },
    methods: {
        setCurrentItem(item) {
            this.$emit('setCurrentItem', item)
        }
    },
    template: `
      <div class="iac--ktru__tree" >
        <div>
          <div>{{$t('code').toUpperCase()}}</div>
          <div>{{$t('product_name').toUpperCase()}}</div>
        </div>
        <renderTreeNode v-else @setCurrentItem="setCurrentItem"/>
      </div>
    `
}

export default renderTreeNode