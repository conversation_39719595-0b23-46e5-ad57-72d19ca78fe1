import { DataSource } from '@iac/data'
import { Language } from '@iac/core'
import { Config, Context } from '@iac/kernel'

export default {
    data() {
        return {
            readyReportsSource: new DataSource({
                valueExp: 'report',
                displayExp: (item) => item.report_name,
                query: {
                    report_method: {
                        type: 'entity',
                        has_del: true,
                        label: '!report_type',
                        group: 'report_type/<report_type>',
                        dataSource: new DataSource({
                            valueExp: 'report',// id
                            displayExp: 'report_name',
                            store: {
                                method: 'report',
                                key: 'report'
                            },
                        })
                    },
                    inserted_at_gte: {
                        group: 'reports_inserted_at/<min>',
                        type: 'date',
                        label: 'from',
                        has_del: true,
                        bind: {
                            status: `inserted_at_error && {"type":"error"}`
                        },
                    },
                    inserted_at_lte: {
                        group: 'reports_inserted_at/<max>',
                        type: 'date',
                        label: 'to',
                        has_del: true,
                        bind: {
                            status: `inserted_at_error && {"type":"error"}`
                        },
                    },
                    inserted_at_error: {
                        sync: false,
                        group: 'reports_inserted_at',
                        type: 'model',
                        label: '!',
                        bind: {
                            value: `
                                inserted_at_gte >= inserted_at_lte
                            `,
                            status: `
                                inserted_at_error && {
                                    "type": "error",
                                    "message": inserted_at_gte >= inserted_at_lte 
                                        ? "${Language.t('end_date_of_the_period_cannot_be_earlier_than_starting_date')}"
                                        : "${Language.t('end_date_of_the_period_cannot_be_later_than_current_date')}"
                                }
                            `
                        },
                    },
                },
                store: {
                    method: 'free_report_async',
                    ref: 'free_report_async',
                    context: (context) => {
                        context.type = context.args.report_method
                        context.date = context.inserted_at
                        context.periodFrom = context.args.params.from
                        context.periodTo = context.args.params.to
                        context.format = context.args.type
                        context.state = this.$t(context.state)
                        context.actions = []
                        context.meta.storage_uuid && context.actions.push({
                            label: 'download',
                            async handler() {
                                await Context.User.refreshToken();
                                const url = `${Config.api_server}/file/${context.meta.storage_uuid}?token=${Context.User.access_token}`

                                var a = document.createElement("a");
                                document.body.appendChild(a);
                                a.style = "display: none";
                                a.href = url;
                                a.click();
                                window.URL.revokeObjectURL(url);
                                a.remove();
                            }
                        })

                        return context
                    },
                    injectQuery(params) {
                        if (params.filters.report_method) {
                            const [report_method, template_name] = (params.filters.report_method || '').split(':tmpl')
                            params.filters = { ...params.filters, report_method/*, template_name */ }
                        }
                        params.filters.inserted_at_error = undefined
                        return params
                    }
                },
                columns: [
                    { field: 'id', label: 'column_id' },
                    { field: 'type', label: 'type', style: 'width:100%' },
                    { field: 'state', label: 'column_state' },
                    { field: 'date', label: 'date', type: 'date' },
                    { field: 'periodFrom', label: 'period_from', type: 'date' },
                    { field: 'periodTo', label: 'period_to', type: 'date' },
                    { field: 'format', label: 'column_format' }
                ]
            })
        }
    },
    methods: {
        refreshOnSocketMessage(event) {
            if (["finished", "error"].includes(event.data.status)) {
                this.readyReportsSource.reload()
            }
        }
    },
    template: `
        <ui-layout-group key='ready'>
            <ui-data-grid :dataSource='readyReportsSource':columns="readyReportsSource.columns" :buttons="true">
            <template slot='date' slot-scope='props'>
                <iac-date :date='props.item.date' full/>
            </template>
            <template slot='periodFrom' slot-scope='props'>
                <iac-date :date='props.item.periodFrom' withoutTime/>
            </template>
            <template slot='periodTo' slot-scope='props'>
                <iac-date :date='props.item.periodTo' withoutTime/>
            </template>
            </ui-data-grid>
        </ui-layout-group>
    `,
    mounted() {
        Context.User.onReportStatusChange.bind(this.refreshOnSocketMessage)
    },
    beforeDestroy() {
        Context.User.onReportStatusChange.unbind(this.refreshOnSocketMessage)
    }
}