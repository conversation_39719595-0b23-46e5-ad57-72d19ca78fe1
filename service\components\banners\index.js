import { DataSource, BackEndStore, Entity } from '@iac/data'
import { Language } from '@iac/core'
import { Context } from '@iac/kernel'
import { Guid } from '@iac/core'

var data = {
    get banners_hidden_hash() {
        let banners_hidden_hash = localStorage.getItem("banners_hidden_hash")
        try {
            return JSON.parse(banners_hidden_hash) || {};
        } catch (e) {
            return {};
        }
    },
    source: new DataSource({
        store: new BackEndStore({
            scope: "global",
            storage_key: 'store_banners',
            context: (context) => {

                let string = `${context.id}/${context.active}/${context.date_start}/${context.date_end}/${context.message}`;

                context.hash = 0;
                for (var i = 0; i < string.length; i++) {
                    var character = string.charCodeAt(i);
                    context.hash = ((context.hash << 5) - context.hash) + character;
                    context.hash = context.hash & context.hash; // Convert to 32bit integer    
                }

                Vue.set(context, "hidden", data.banners_hidden_hash[context.hash] ? true : false);

                context.message = {
                    'ru-RU': context.message[0],
                    'uz-UZ@cyrillic': context.message[1],
                    'uz-UZ@latin': context.message[2],
                    'en-US': context.message[3],
                }

                if (context.url && context.url != '') {

                    context.url = context.url.replace(/\/$/gm, '')
                    //context.url = context.url.replace(/\/\$/gm,'$')
                    // context.url = context.url.replace("*$",'')
                    context.url = context.url.replace("*", '.*')


                    //context.url = context.url.replace(/\$/gm,'(?:\/(?=$))?$')
                    context.url = context.url.replace(/\//gm, '\\/')
                    context.url = context.url.replace(/:\w+/gm, '((?:[^\\/]+?))')
                    context.url = context.url.trim().split('\n')
                }

                context.position = context.position || "float";
                context.date_start = context.date_start ? new Date(context.date_start) : undefined
                context.date_end = context.date_end ? new Date(context.date_end) : undefined

                return context
            }
        }),
    })
}

function createNewOrderBanner(orderData) {
    let id
    let uz_Latin
    let uz_Cyrillic
    let ru_RU
    let en_US
    if (orderData.status === "finished") {
        id = "finish-task-message" + orderData.id + "_" + Guid.newGuid()
        uz_Latin = `${orderData.id}-sonli hisobot tayyor.`
        uz_Cyrillic = `${orderData.id}-сонли ҳисобот тайёр.`
        ru_RU = `Отчет №${orderData.id} готов.`
        en_US = `Order #${orderData.id} is ready.`
        data.source.push_item({
            uz_Latin,
            uz_Cyrillic,
            ru_RU,
            en_US,
            status_type: "info",
            position: "float",
            message: [ru_RU, uz_Cyrillic, uz_Latin, en_US],
            id,
            date_start: undefined,
            date_end: undefined,
            active: true
        })
    } else if (orderData.status === "starting") {
        id = "start-task-message" + orderData.id + "_" + Guid.newGuid()
        uz_Latin = `${orderData.id}-sonli hisobot tuziladi.`
        uz_Cyrillic = `${orderData.id}-сонли ҳисобот тузилади.`
        ru_RU = `Отчет №${orderData.id} генерируется.`
        en_US = `Order #${orderData.id} is generating.`
        data.source.push_item({
            uz_Latin,
            uz_Cyrillic,
            ru_RU,
            en_US,
            status_type: "info",
            position: "float",
            message: [ru_RU, uz_Cyrillic, uz_Latin, en_US],
            id,
            date_start: undefined,
            date_end: undefined,
            active: true
        })
    } else if (orderData.status === "error") {
        id = "error-task-message_" + Guid.newGuid()
        uz_Latin = 'Hisobotni tuzishda xatolik yuz berdi.'
        uz_Cyrillic = 'Ҳисоботни тузишда хатолик юз берди.'
        ru_RU = 'При формировании отчета произошла ошибка.'
        en_US = 'An error occurred when generating the report.'
        data.source.push_item({
            uz_Latin,
            uz_Cyrillic,
            ru_RU,
            en_US,
            status_type: "info",
            position: "float",
            message: [ru_RU, uz_Cyrillic, uz_Latin, en_US],
            id,
            date_start: undefined,
            date_end: undefined,
            active: true
        })
    }
}

data.source.reload()

Context.User.onReportStatusChange.bind((event) => {
    createNewOrderBanner(event.data)
})

let Component = {
    props: {
        position: {
            type: String,
            default: 'float'
        }
    },
    data: function () {
        return {
            source: data.source,
            lng: Language,
            current_time: new Date(),
            tid: undefined
        }
    },
    mounted() {
        //this.source.reload();
        this.tid = setInterval(() => {
            this.current_time = new Date();
        }, 60000)
    },
    beforeDestroy() {
        if(this.tid){
            clearInterval(this.tid);
        }
    },
    computed: {
        items() {
            if (!this.source.items)
                return;
            return this.source.items.filter((item) => {
                if (this.position != item.position)
                    return false;

                if (data.banners_hidden_hash && data.banners_hidden_hash[item.hash]) {
                    return false;
                }
                if (item.date_start && item.date_start > this.current_time)
                    return false

                if (item.date_end && item.date_end < this.current_time)
                    return false

                if (!item.active || item.hidden || !item.message[this.lng.local])
                    return false;

                if (item.url && item.url.length > 0) {
                    for (let path of item.url) {
                        let regExp = RegExp(`^${path}(?:\/(?=$))?$`)

                        if (regExp.test(this.$route.path))
                            return true;
                    }
                } else {
                    return true;
                }

                return false;
            }).filter((item, index) => {
                return index < 3;
            });
        }
    },
    methods: {
        close(item) {
            item.hidden = true;
            // Пересобираем
            let banners_hidden_hash = this.source.items.filter((item) => {
                return item.hidden
            }).reduce((prev, curr) => {
                prev[curr.hash] = true;
                return prev;
            }, {})
            localStorage.setItem("banners_hidden_hash", JSON.stringify(banners_hidden_hash));
        }
    },
    template: `
        <div class='iac-banners' v-if='items && items.length > 0'>
            <transition-group name="list" tag="div"  >
                <ui-alert :key='item.id' :type='item.status_type || "info"' class='banner' v-for='item in items'>
                    
                    <span class='info'>
                        <ui-markdown-view style='margin: -1em 0;' v-if='item.message[lng.local]' :content='item.message[lng.local]' />
                    </span>


                    <icon class='close' v-on:click='close(item)'>delete</icon>
                </ui-alert>
            </transition-group>
        </div>
    `,
};

Vue.component('iac-banners', Component);
