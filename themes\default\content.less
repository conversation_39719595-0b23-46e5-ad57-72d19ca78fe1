
body{
    background: #FBFBFB;
}

section.section{
    &::before{
        content: " ";
        height: 1px;
        display: block;
        margin-top: -1px;
    }
    &::after{
        content: " ";
        height: 1px;
        display: block;
        margin-bottom: -1px;
    }
}

.develop_dlg{
    .ui-field.field-type-bool{
        margin: 0;
        .ui-checkbox.field{
            margin-bottom: 8px;
            color: #999;
            font-size: 14px;
            b{
                color: #333;
            }
            &:hover{
                color: #666;
            }
        }
    }
}


#iac-page-preloader {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255,255,255,0.1);
    z-index: 5000;
    &::before{
        display: block;
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 25%;
        height: 3px;
        background: @brand-primary;
        transform-origin: 0 0;
        transform:scaleX(0);

        animation: PageProgressAnimation 1s infinite;
            animation-timing-function: ease;
            animation-delay: 0s;
        animation-timing-function: cubic-bezier(0.4,0.0,1,1);
        animation-delay: .1s;
    }
    &.active{
        display: block;
    }
}

.content_debug, .content_debug * {
    color: #070 !important; 
}

.blackout{
    >.content {
        background: #fff;
        margin: 50px auto;
        max-width: 600px;
        padding: 10px;
        border-radius: 5px;
    }
}

.iac-service .blackout{
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 100000;
    background: #00000077;
}




code {
    background: #fffdf6;
    color: #a31515;
    padding: 0 5px;
}

pre code {
    color: #333;
    padding: 0;
    background: unset;
}

.ui-markdown-view > pre  {
    overflow: auto;
}

.field-type-contract-file {
    >.field-content>.field-label {
        >.label-wrapper {
            margin-top: 0!important;
        }
    }
}