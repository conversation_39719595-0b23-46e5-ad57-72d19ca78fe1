import ebpLangSelect from '../components/ebp_lang_select'
import ebpMultilangInput from '../components/ebp_multilang_input'
import ebpInput from '../components/ebp_input'
import ebpMultilangTreeSelect from '../components/ebp_multilang_tree_select'
import ebpMultilangTable from '../components/ebp_multilang_table'

import BurseProductEditModel from '../models/burse_product.edit.model'

export default Vue.Dialog({
  props: {
    currentItem: {
      type: Object,
      required: true
    }
  },
  components: {
    'ebpLangSelect': ebpLangSelect,
    'ebpMultilangInput': ebpMultilangInput,
    'ebpInput': ebpInput,
    'ebpMultilangTreeSelect': ebpMultilangTreeSelect,
    'ebpMultilangTable': ebpMultilangTable
  },
  data() {
    return {
      model: new BurseProductEditModel(this.currentItem)
    }
  },
  methods: {
    async saveProduct() {
      const modelForExport = this.model.export()
      this.Close({ op: "save", id: modelForExport.id, product:modelForExport.product, meta: modelForExport.meta })
    },
    close() {
      this.Close()
    },
    async deleteProduct() {
      this.Close({ op: "delete", id: this.model.id })
    }
  },
  template: `
  <div>
    <header>{{$t("ebp_specification_edit")}}</header>
    <main>
      <div v-if="model.motherland">{{$t('ebp_created_from')}} {{model.motherland.source}}({{model.motherland.id}})</div>
      <ebp-lang-select v-model="model.langs" :label="$t('ebp_select_languages')"/>
      <ebp-multilang-input v-model="model.name" :langs="model.langs" :label="$t('product_name')"
       :placeholder="$t('product_name')" :enableShowChanges="true"/>      
      <div class="iac--ebp-row">
        <ebp-input v-model="model.skp" :label="$t('skp')" :placeholder="$t('skp')"/>
        <ebp-input v-model="model.tnved" :label="$t('tnved')" :placeholder="$t('tnved')"/>
      </div>
      <ebp-multilang-tree-select :data="model.groups" :langs="model.langs" :label="$t('group')"
       :placeholder="$t('group')" :request="model.getGroupsQuery" :clear="model.clearGroup" :update="model.updateGroups"/>
      <ebpMultilangTable v-model="model.units" :columns="model.unitsColumns"
       :addItem="model.addUnit" :updateItem="model.updateUnit" :deleteItem="model.deleteUnit" :langs="model.langs" :label="$t('units')"/>

      <h3>{{$t("properties")}}:</h3>
      <ebpMultilangTable v-for="(prop,index) in model.props" v-model="prop.values" :columns="model.propsColumns"
       :langs="model.langs" :label="prop.name" :updateLabel="newName=>model.updatePropName(index, newName)" :deleteTable="()=>model.deleteProp(index)"
       :addItem="()=>model.addPropVal(index)" :updateItem="(valIndex,newValue)=>model.updatePropVal(index,valIndex,newValue)" :deleteItem="(valIndex)=>model.deletePropVal(index,valIndex)"/>
       <ui-btn type='primary' @click.native="model.addProp">{{$t('add')}}</ui-btn>
       <ebpTextarea v-model="model.message" :label="$t('ebp_comment')"/>
    </main>
    <footer>
      <ui-btn type='danger' v-if="currentItem.id" @click.native="deleteProduct">{{$t('delete')}}</ui-btn>  
      <ui-btn type='primary' @click.native="saveProduct">{{$t('save')}}</ui-btn>
      <ui-btn type='primary' @click.native="close">{{$t('cancel')}}</ui-btn>
    </footer>
  </div>
    `
})