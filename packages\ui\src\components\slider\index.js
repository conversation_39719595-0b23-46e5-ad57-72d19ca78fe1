//import { Language } from '@iac/core'

import Language from "../../../../core/src/language";

export var Slider = {
    name: "ui-slider",
    // props: ["icon", "label", "status", "value", "readonly", "disabled", "name", "type", "wait", "actions","min","max"],
    props: {
        icon: {}, label: {}, status: {}, value: {}, readonly: {}, disabled: {}, name: {}, type: {}, wait: {}, actions: {}, has_del: {}, required: <PERSON><PERSON><PERSON>,
        min: {
            default: 0
        },
        max: {
            default: 100
        }
    },
    data: function () {
        return {
            focus: false,
            key: "",
            keyTimeoutID: undefined
        }
    },
    computed: {
        component_status() {
            if (typeof this.status == "string") {
                return {
                    type: this.status,
                    message: undefined
                }
            }
            return this.status;
        },
        display() {
            if (this.value || this.value === 0) {
                return this.value
            }
            return Language.t('select_value');
        },
        classes() {
            return [
                (() => {
                    return this.component_status ? ["status", this.component_status.type] : '';
                })(),
                {
                    non_value: !this.opened,
                    disabled: this.disabled,
                    readonly: this.readonly
                }
            ]
        },
        valueStyle() {
            let percent = (this.value - this.min) * 100 / (this.max - this.min);
            if (percent < 0)
                percent = 0
            if (percent > 100)
                percent = 100;
            return {
                left: `${percent}%`
            }
        },
        opened() {
            if (this.value || this.value === 0) {
                return true
            }
            return false;
        },
        listeners: function () {
            var vm = this
            return Object.assign({},
                this.$listeners,
                {
                    input: function (event) {
                        vm.value = event.target.value
                        vm.$emit('input', event.target.value)
                    },
                    change: function (event) {
                        vm.value = event.target.value
                        vm.$emit('change', event.target.value)
                    }
                }
            )
        }
    },
    methods: {
        action(event) {
            if (event == 'clear') {
                this.value = undefined;
                this.$emit('input', undefined)
                this.$emit('change', undefined)
            }
        },
        keyDown(event) {
            let key = event.key;
            if ("-.0123456789".split('').indexOf(key) < 0)
                return;

            this.key += key;

            if (this.keyTimeoutID) {
                clearTimeout(this.keyTimeoutID);
            }


            this.keyTimeoutID = setTimeout(() => {
                let val = Number(this.key);
                if (!Number.isNaN(val)) {
                    this.value = val;
                    this.$emit('input', val)
                    this.$emit('change', val)
                }
                this.key = "";
            }, 400)
        }
    },
    template: `<div class='ui-slider' v-bind:class="classes">
        <div class='control'>
            <span>{{min}}</span>
            <div class='range'>
                <input :name="name" type="range"
                    v-on:keydown="keyDown"
                    v-bind="$attrs"
                    v-bind:value="value"
                    v-on="listeners"
                    :disabled="disabled || readonly"
                    :readonly="readonly"
                    :min="min"
                    :max="max"
                    @focus="focus = true"
                    @blur="focus = false"
                /> 
                <div class='value' :style='valueStyle'>{{value}}</div>
            </div>
            <span>{{max}}</span>
        </div>
        
    </div>`
}
