import Vue from "vue";
import { Context } from "@iac/kernel";

const Component = {
  props: ["model"],
  data: function () {
    return {
      user: Context.User,
      calculated_ref_status: this.ref_status || "contract_status",
    };
  },
  computed: {
    title() {
      return this.model.title || this.model.full_title || this.model.name;
    },
    roles() {
      if (!this.model.roles && !this.model.role) return null;

      let roles;
      if (this.model.role && Array.isArray(this.model.roles) && this.model.roles.length == 0) {
        roles = [this.model.role];
      } else if (this.model.roles && this.model.roles != []) {
        roles =  this.model.roles;
      } else {
        return null;
      }

      return roles.map(it => this.$t(it)).join(", ");
    },
    tags() {
      const model = this.model;
      const tags = [
        (model.meta && model.meta.gup) ? this.$t("ГУП") : null,
        (model.meta && model.meta.ie) ? this.$t("ИП") : null,
        ((!model.meta || !model.meta.gup) && model.buyer_type_id == 1) ? this.$t("Гос") : this.$t("Корп")
      ].filter(x => x).map(x => this.$t(x));

      return tags.length == 0 && null || tags;
    }
  },
  methods: {
    showRejectReasonDialog(msg) {
      Vue.Dialog.MessageBox.Info(msg);
    },
    link(model) {
      if (model.is_broker) {
        return {
          path: `/company/${model.id}`,
          query: {
            'is_broker': 'true'
          }
        };
      } else {
        return `/company/${model.id}`;
      }
    }
  },
  template: `
        <ui-data-view-item :model='model'>
            <template slot='header'>
                <div>
                  <iac-entity-edit  v-if='$develop.content_debug && model.id' :value='{id: model.id, type: "company"}' />
                  <template v-if='model.title_text'>
                      {{model.title_text}}
                    </template>
                    <template v-else>
                      {{ $t('inn') }}:{{model.inn||model.code}}
                    </template>
                  </div>
                <div :title="model.liquidation_reason" v-if='model.registration_status === "liquidated"'>
                    {{ $t('liquidated') }}
                  </div>
                  <div :title='model.meta.blocking_reason' v-else-if='model.blocked'>
                    {{ $t('blocked_funds_short') }}
                  </div>
              </template>

            <template slot='title'>
            <router-link v-if='model.id' :to='link(model)'>{{ title }}</router-link>
                <div v-else>{{ title }}</div>
            </template>

            <template slot='sub_title'>
            </template>

            <template slot='description'>
              <div v-if='roles'>
                <label>{{ $t('roles') }}: </label>
                <span v-for="role in roles">{{ role }}</span>
              </div>
              <div v-if='model.contacts'>
                <label>{{ $t('contacts') }}: </label>
                <span>{{ model.contacts.trim() }}</span>
              </div>
              <div v-else-if='model.legal_address'>
                <label>{{ $t('legal_address') }}: </label>
                <span>{{ model.legal_index && model.legal_index.trim() }} {{ model.legal_address.trim() }}</span>
              </div>
              <div v-else-if='model.address'>
                <label>{{ $t('address_legal') }}:</label>
                <span>{{ model.address.trim() }}</span>
              </div>
              <div v-else-if='model.postal_address'>
                <label>{{ $t('postal_area') }}:</label>
                <span>{{ model.postal_area_id && model.postal_area_id.trim() }} {{ model.postal_address.trim() }}</span>
              </div>
              <div v-if='model.meta && model.meta.ignore_limits_of_brv_for_selection && model.meta.ignore_limits_of_brv_for_selection_to'>
                <label>{{ $t('ability_to_create_selection_above_25000_brv_until') }} <span style='text-transform: lowercase;'>{{$t('to')}}</span></label>
                <iac-date :date='model.meta.ignore_limits_of_brv_for_selection_to' withoutTime />
              </div>
              <div v-if='model.note'>
                <label>{{ $t('block_reason') }}: </label>
                <span>{{ model.note }}</span>
              </div>
              <div v-if='model.reason'>
                <label>{{ $t('reason') }}: </label>
                <span>{{ model.reason }}</span>
              </div>
              <div v-if='model.description'>
                <label>{{ $t('description') }}: </label>
                <span>{{ model.description.trim() }}</span>
              </div>
              <div v-if='model.scope'>
                <label>{{ $t('scope') }}:</label>
                <span>{{ model.scope.trim() }}</span>
              </div>
              <div v-if='model.location'>
              <label>{{ $t('location') }}: </label>
                <span>{{ model.location.trim() }}</span>
              </div>
            </template>

            <template slot='props'>
              <div :title='model.director_name' v-if='model.director_name'>
                <label>{{$t('director_name')}}:</label>
                <div>{{model.director_name}}</div>
              </div>
              <div v-if='model.date_begin'>
                <label>{{ $t('block_begin') }}:</label>
                <div><iac-date :date='model.date_begin' withoutTime /></div>
              </div>
              <div v-if='model.date_end'>
                <label>{{ $t('block_end') }}:</label>
                <div><iac-date :date='model.date_end' withoutTime /></div>
              </div>
              <div v-if='model.oked'>
                <label>{{ $t('oked_code') }}:</label>
                <div>{{ model.oked.trim() }}</div>
              </div>
              <div v-if='model.okpo'>
                <label>{{ $t('okpo_code') }}:</label>
                <div>{{ model.okpo.trim() }}</div>
              </div>
              <div v-if='model.mfo'>
                <label>{{ $t([$settings._country+'.bank_mfo_code','bank_mfo_code']) }}:</label>
                <div>{{ model.mfo.trim() }}</div>
              </div>
              <div v-if='model.soogu'>
                <label>{{ $t('soogu_code') }}:</label>
                <div>{{ model.soogu.trim() }}</div>
              </div>
              <div v-if='model.region && !model.location'>
                <label>{{ $t('region') }}:</label>
                <div>{{ model.region.trim() }}</div>
              </div>
              <div v-if='model.town && !model.location'>
                <label>{{ $t('town-distict') }}:</label>
                <div>{{ model.town.trim() }}</div>
              </div>
              <div v-if='model.form'>
                <label>{{ $t('form_ownership') }}:</label>
                <div>{{ model.form.trim() }}</div>
              </div>
              <div v-if='model.cheif'>
                <label class='text-wrap'>{{ $t('parent_org_name') }}:</label>
                <div>{{ model.cheif.trim() }}</div>
              </div>
              <div v-if='model.phone'>
                <label>{{ $t('phone') }}:</label>
                <div>{{ model.phone.trim() }}</div>
              </div>
              <div v-if='model.email'>
                <label>{{ $t('email') }}:</label>
                <div>{{ model.email.trim() }}</div>
              </div>
              <div v-if='model.fax'>
                <label>{{ $t('fax') }}:</label>
                <div>{{ model.fax.trim() }}</div>
              </div>
              <div v-if='model.proportion'>
                <label>{{ $t('government_proportion') }}:</label>
                <div>{{ model.proportion }}%</div>
              </div>


              <div v-if='model.registration_status !== "liquidated" && model.blocked_from'>             
                <label>{{ $t('block_begin') }}:</label>
                <div><iac-date :date='model.blocked_from' withoutTime /></div>
              </div>
              <div v-if='model.registration_status !== "liquidated" && model.blocked_to'>
                <label>{{ $t('block_end') }}:</label>
                <div><iac-date :date='model.blocked_to' withoutTime /></div>
              </div>
              <div v-if='model.registration_status !== "liquidated" && model.meta && model.meta.blocking_reason'>
                <label>{{ $t('blocked_funds_short') }}:</label>
                <div>
                  <a
                    href="#"
                    @click.prevent="showRejectReasonDialog(model.meta.blocking_reason)"
                    :title="model.meta.blocking_reason"
                  >
                    {{ $t(model.meta.blocking_reason) }}
                  </a>
                </div>
              </div>
              <div v-if='model.deleted_at'>
                <label>{{ $t('deleted_at') }}:</label>
                <div><iac-date :date='model.deleted_at' withoutTime /></div>
              </div>
              <div v-if='model.contract_pay_percent != undefined'>
                <label>{{ $t('differentiated_company_percentage_of_advance_payment') }}:</label>
                <div>{{model.contract_pay_percent}}%</div>
              </div>
              <div v-if='$develop.content_debug && model.meta && model.meta.gup && model.meta.org_code2'>
                <label>{{ $t('org_code') }}:</label>
                <div>{{model.meta && model.meta.org_code2}}</div>
              </div>

              <template v-if="model.broker_request">
                <div v-if="model.broker_request.inserted_at">
                  <label> {{ $t('broker_request.request_sended') }}: </label>
                  <div><iac-date :date='model.broker_request.inserted_at' full /></div>
                </div>
                <div v-if="model.broker_request.meta.accepted_at">
                  <label> {{ $t('broker_request.request_accepted') }}: </label>
                  <div><iac-date :date='model.broker_request.meta.accepted_at' full /></div>
                </div>
                <div v-if="model.broker_request.meta.torned_at">
                  <label> {{ $t('broker_request.request_torned') }}: </label>
                  <div><iac-date :date='model.broker_request.meta.torned_at' full /></div>
                </div>
                <div v-if="model.broker_request.meta.canceled_at">
                  <label> {{ $t('broker_request.request_canceled') }}: </label>
                  <div><iac-date :date='model.broker_request.meta.canceled_at' full /></div>
                </div>
                <div v-if="model.broker_request.meta.rejected_at">
                  <label> {{ $t('broker_request.request_rejected') }}: </label>
                  <div><iac-date :date='model.broker_request.meta.rejected_at' full /></div>
                </div>

                <div>
                  <label> {{ $t('status') }}: </label>

                  <div v-if="model.broker_request.meta.status == 'accept'"> {{ $t('broker_request.status.accepted') }} </div>
                  <div v-else-if="model.broker_request.meta.status == 'wait'"> {{ $t('broker_request.status.waiting') }} </div>
                  <div v-else-if="model.broker_request.meta.status == 'cancel'">
                    <a href="#" @click.prevent="showRejectReasonDialog(model.broker_request.meta.cancel_reason)" :title="model.broker_request.meta.cancel_reason"> {{ $t('broker_request.status.canceled') }} </a>
                  </div>
                  <div v-else-if="model.broker_request.meta.status == 'torn'">
                    <a href="#" @click.prevent="showRejectReasonDialog(model.broker_request.meta.torn_reason)" :title="model.broker_request.meta.torn_reason"> {{ $t('broker_request.status.torned') }} </a>
                  </div>
                  <div v-else-if="model.broker_request.meta.status == 'reject'">
                    <a href="#" @click.prevent="showRejectReasonDialog(model.broker_request.meta.reject_reason)" :title="model.broker_request.meta.reject_reason"> {{ $t('broker_request.status.rejected') }} </a>
                  </div>
                  <div v-else>{{ $t(model.broker_request.meta.status) }}</div>

                </div>
              </template>

              <div v-if="$develop.content_debug">
                <span v-for='tag in tags' class="ui-tag-item">{{ tag }}</span>
              </div>
            </template>

        </ui-data-view-item>
    `,
};

Vue.component("template-company", Component);
