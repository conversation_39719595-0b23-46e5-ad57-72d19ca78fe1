.ui-data-list{

    
    flex-direction: row;
    flex-wrap: wrap;
    display: flex;
    margin: 0 -8px;  
        
    >.data-list_content{
        flex: 1 1 0 !important;
        margin-left: 8px;
        margin-right: 8px;
        
    }

    .data-list_filter_button{
        display: none;
        flex: 0 0 auto !important;
        margin-top: 4px;
        margin-bottom: 15px;
        align-self: normal;
    }

    >.data-list_filter{
        flex: 0 0 300px !important;
        //margin-bottom: 30px;
        //border: 1px solid #ccc;
        //padding: 10px;
        margin-left: 8px;
        margin-right: 8px;

        border-radius: 5px;
        box-sizing: border-box;

        >.content >.ui-layout-group{
            border: 1px solid #F3F3F3;
            border-radius: 4px;
            background: #fff;
            padding: 20px;
            >.content{
                >.ui-layout-group{
                    margin: 0 -20px;
                    &:first-child{
                        >.label{
                            //margin-top: 0;
                        }
                    }
                    &:not(:last-child){
                        border-bottom: 1px solid #F3F3F3;
                    }
                    >.label{
                        padding: 0 20px;
                        border: none;
                        color: #969595;
                        //margin: 20px 0 8px;
                        >.title{
                            font-weight: 500;
                            font-size: 13px;
                            line-height: 16px;

                            /* identical to box height, or 123% */
                            letter-spacing: 0.05em;
                            text-transform: uppercase;
                        }
                    }
                    >.content{
                        padding: 0 20px;
                    }
                }
            }
        }
        
        .ui-enum,
        .ui-tag{
            &.ui-control {
                border: none;
                >.container{
                    margin: 0;
                }
            }
        }

        .ui-enum-tree {
            border: none;
            min-width: 0;
            >.container{
                margin: 0;
            }
            .ui-control {
                border: none;
                margin-bottom: 0;
                border-left: 1px solid #eee;
                border-radius: 0;
                margin-left: 8px;
                >.container{
                    margin: 0 15px;
                }
            }
        }



    }
    >.item{
        //border: 1px solid #ccc;
    }

    &.top_filter{
        flex-direction:column-reverse;
        flex-wrap: nowrap;
        >.data-list_filter{
            flex: 1 1 auto !important;
        }
    }
}

.tile_widget {
    margin-bottom: 12px;
    display: block;
    border: 1px solid @light-gray;
    padding: 12px 16px 7px;
    background-color: @white;
    cursor: initial;
    box-shadow: 0 0 24px rgba(223, 223, 223, 0.2);
    border-radius: 4px;
}

@media (max-width:1023px) {
    .ui-data-list{
        >.data-list_filter{
            display: none;
        }
        .data-list_filter_button{
            display: inline-block;
        }
    }
}

@media (max-width:767px) {
    .ui-data-list{

        .data-list_filter_button{
            display: inline-block;
            flex: 1 1 100% !important;
        }
    }
}