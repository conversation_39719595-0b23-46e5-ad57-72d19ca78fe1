import { Model } from '@iac/data'
import Settings from './../../../settings'
import Context from './../../../context'
import { Http, Guid, Language, ModelProvider } from '@iac/core'
import ecp from './../../../ecp'

const alphabetRegExpsStrings = [
    "a-zA-Z",
    "0-9",
    ".,:;?!*+%\\-<>@\\[\\]\\{\\}\\/\\\\_\\$\\#"
]
const allAlphabetRegExps = alphabetRegExpsStrings.map(string => new RegExp(`[${string}]`))//all groups needed
allAlphabetRegExps.push(new RegExp(`^[${alphabetRegExpsStrings.join('')}]+$`)) //only this symbols available

class LogIn extends Model {
    constructor(context){
        super(context)

        this.login = context.email
        if(this.login){
            this.properties.login.readonly = true;
        }
    }
    validate() {
        let formError = { data: [] }
        if (!this.login) {
            formError.data.push({ name: "login", message: Language.t('required_field') });
        }
        if (!this.password) {
            formError.data.push({ name: "password", message: Language.t('required_field') });
        }
        if (formError.data.length > 0) {
            this.setError(formError);
            return formError;
        }
    }
    get sub_actions() {
        return [
            {
                icon: "qr",
                title: "signin_by_qr",
                type: "primary",
                handler: async () => {
                    let qrDialog = await Vue.Dialog({
                        data() {
                            return {
                                qrValue: "",
                                channel: null,
                                code: null
                            };
                        },
                        methods: {
                            loadQrCode() {
                                this.$wait(async () => {
                                    // закрыть предыдущий канал, если он существует
                                    this.closeSocketChannel();

                                    // Очищаем предыдущий таймер, если он существует
                                    if (this.timer) {
                                        clearTimeout(this.timer);
                                        this.timer = null;
                                    }

                                    const response = await iac.Core.Http.auth.rpc("create_qr_code");

                                    if (response.error) {
                                        Vue.Dialog.MessageBox.Error(response.error);
                                        this.Close();
                                        return;
                                    }

                                    if (response.data?.code) {
                                    this.code = this.qrValue = response.data.code;

                                        // новый канал для этого QR-кода
                                        this.openSocketChannel();

                                        // Запускаем новый таймер для следующего обновления
                                        this.startTimer();
                                    } else {
                                        Vue.Dialog.MessageBox.Error("Не удалось получить QR-код");
                                        this.Close();
                                    }
                                });
                            },

                            openSocketChannel() {
                                if (!this.code) return;
                                this.channel = iac.Core.Http.api.socket.join(`qr:${this.code}`, (channel) => {
                                    channel.on('auth_data', async (data) => {
                                        await Context.User.set_tokens(data);
                                        this.Close(true);

                                    });
                                });
                            },

                            closeSocketChannel() {
                                if (this.channel) {
                                    iac.Core.Http.api.socket.leave_channel(this.channel);
                                    this.channel = null;
                                }
                            },
                            startTimer() {
                                this.timer = setTimeout(() => {
                                    this.loadQrCode();
                                }, 150000);
                            }
                        },
                        mounted() {
                            this.loadQrCode();
                        },
                        beforeDestroy() {
                            if (this.timer) {
                                clearTimeout(this.timer);
                            }
                            this.closeSocketChannel();
                        },
                        template: `
                            <div >
                                <header>
                                </header>
                                <main class='info' >
                                 {{$t("signin_by_qr_message")}}
                                </main>
                                <main style="text-align:center;">                                   
                                    <iac-qr v-if="code" :value="qrValue" :size="200"></iac-qr>
                                </main>
                                <footer>
                                </footer>
                            </div>
                        `
                    }).Modal({
                        size: "sm"
                    });
                    return qrDialog;
                },

                hidden: () => {
                    return !Settings?.qr_auth
                }
            },
           {
                icon: "ecp",
                title: "signin_by_ecp_title",
                type: "primary",
                handler: async () => {
                    let error = await Context.User.authorization_by_ecp(this.properties.login.readonly && this.login);
                    if (error && error.code != "AbortError") {
                        Vue.Dialog.MessageBox.Error(error);
                    }
                    if (!error)
                        return true;
                },
                hidden: !Context.User.authorization_by_ecp
            }
        ]

    }

    get actions() {
        return [
            {
                label: Language.t("signin"),
                type: "primary",
                title: "signin_title",
                handler: async () => {
                    if (this.validate())
                        return;
                    let error = await Context.User.authorization(this.login, this.password, this.remember);
                    if (error && error.code != "AbortError") {
                        Vue.Dialog.MessageBox.Error(error);
                    }
                    if (!error)
                        return true;
                }
            },
            
        ]
    }

    props() {
        return {
            login: {
                required: true,
                type: 'email',
                
                attr: {
                    autocomplete: 'off',
                    react: true,
                    placeholder: "login_placeholder",
                },
            },
            password: {
                type: "password",
                required: true,

                attr: {
                    autocomplete: 'new-password',
                    react: true,
                    placeholder: "password_placeholder",
                },
            },
            social: {
                type: "widget",
                label: "!",
                widget: {
                    name: {
                        methods: {
                            oauth(type) {
                                let temp_id = Guid.newGuid();
                                var authWindow = window.open('https://accounts.google.com/o/oauth2/v2/auth?client_id=************-oc3p3phealue9qqi32909mj3apj9bscl.apps.googleusercontent.com&redirect_uri=http://localhost:8080&response_type=code&scope=https://www.googleapis.com/auth/userinfo.profile email&state=' + temp_id,
                                    "hello", "width=600,height=600");

                                authWindow.focus();
                            }
                        },
                        template: `
                            <main class='login' v-if='0'>
                                <span class='or'>&nbsp;{{$t("or")}}&nbsp;</span>
                                <ui-layout-group style='margin: 8px 0;'>
                                    <ui-btn type='primary empty' v-on:click.native='oauth("google")' style='margin: 4px 0;'>Log in with Google</ui-btn>
                                    <ui-btn type='primary empty' v-on:click.native='oauth("facebook")' style='margin: 4px 0;'>Log in with Facebook</ui-btn>
                                    <ui-btn type='primary empty' v-on:click.native='oauth("mail")' style='margin: 4px 0;'>Log in with Mail</ui-btn>
                                </ui-layout-group>                               
                            </main>
                        `
                    }
                }
            }
        }
    }
}

class Forgot extends Model {
    validate() {
        let formError = { data: [] }
        if (!this.login) {
            formError.data.push({ name: "login", message: Language.t('required_field') });
        }
        if (formError.data.length > 0) {
            this.setError(formError);
            return formError;
        }
    }
    get actions() {
        return [
            {
                label: Language.t("request_new_password"),
                type: "primary",
                title: "request_password_title",
                handler: async () => {
                    if (this.validate())
                        return;
                    const { error, data } = await Http.api.rpc('send_reset_password', { type: 'email', value: this.login });

                    if (error && error.code != "AbortError") {
                        Vue.Dialog.MessageBox.Error(error);
                        return;
                    }
                    if (data) {
                        Vue.Dialog.MessageBox.Success(data.message);
                        return false;
                    }

                }
            }
        ]
    }
    props() {
        return {
            login: {
                required: true,
                type: 'email',
                attr: {
                    autocomplete: 'off',
                    react: true,
                    placeholder: "reg_login_placeholder",
                }
            }
        }
    }
}



@ModelProvider("registration_face")
class Registration extends Model {

    validate() {
        let formError = { data: [] }

        formError.data = this.fields.filter((field) => {
            if (field.type == 'bool' && !field.value)
                return false;
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).filter((field) => {
            return field.required && field.value == undefined
        }).map((field) => {
            return { name: field.name, message: Language.t('required_field') }
        }) || [];


        if (this.password && this.confirmPassword && this.confirmPassword != this.password) {
            formError.data.push({ name: "confirmPassword", message: Language.t('confirm_password_does_not_match') });
        }

        if (formError.data && formError.data.length > 0) {
            this.setError(formError);
            return formError;
        }
    }

    async pkcs7B64(params) {

        if(!ecp.provider){
            return {
                data: "ignore_eimzo"
            }
        }

        let value = this.ecp;
        if (value && value.exp && value.exp.value != undefined) {
            value = value.exp.value
        }
        if (!value) {
            return {};
        }

        let keyId = await ecp.loadKey(value);
        if (!keyId) {
            return {};
        }

        return await ecp.createPkcs7(keyId, JSON.stringify(params));
    }

    init_params() {
        let params = this.fields.filter((field) => {
            if (field.type == 'bool' && !field.value)
                return false;
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).map((field) => {
            let value = field.value;
            if (field.value && field.value.exp && field.value.exp.value != undefined) {
                value = field.value.exp.value
            }

            if (Array.isArray(value) && value.length <= 0)
                value = undefined;

            return {
                name: field.name,
                value: value
            }
        }).reduce((prev, curr) => {
            prev[curr.name] = curr.value
            return prev;
        }, {})

        params.confirmPassword = undefined;
        params.conditions = undefined;

        return params;
    }


    get actions() {
        let $this = this;
        return [
            {
                label: Language.t("signon"),
                title: "sign_on_title",
                type: "primary",
                get disabled() {

                    if (!$this.conditions || $this.conditions.length !== 3 || !$this.agree_to_terms)
                        return true;
                    return false;
                },
                handler: async () => {
                    if (await this.validate())
                        return;
                    let params = this.init_params();

                    let { error: error_pkcs, data: pkcs7B64 } = await this.pkcs7B64(params)

                    if (error_pkcs) {
                        await Vue.Dialog.MessageBox.Error(error_pkcs.message)
                        return
                    } else {
                        params.pkcs7B64 = pkcs7B64;
                        params.ecp = undefined;
                    }

                    const { error, data } = await Http.api.rpc('register_face', params);

                    if (error) {
                        if (!this.setError(error)) {
                            Vue.Dialog.MessageBox.Error(error);
                        }
                        return;
                    } else {

                        if(data && data.sms_sended){
                            return await Vue.Dialog({
                                props: ['email','phone'],
                                data: function(){
                                    return {
                                        code: {
                                            label: "!code",
                                            name: "code",
                                            type: "string",
                                            value: undefined,
                                            attr: {
                                                react: true
                                            }
                                        }
                                    }
                                },
                                methods:{
                                    send(){
                                        this.$wait(async ()=>{
                                            let {error,data} = await Context.User.activateUser(this.email,this.code.value,'sms');
                                            if(error){
                                                Vue.Dialog.MessageBox.Error(error);
                                            }else{
                                                this.Close({
                                                    data
                                                })
                                            }
                                        })
                                    }
                                },
                                template: `
        <div>
            <header>{{$t('registration.sms.success.title')}}</header>
            <main>

                <p>{{$t('registration.sms.success.info',{phone: phone, email: email})}}</p>
                <ui-field :model='code'/>  
                <div style='font-size: 14px;opacity: 0.8;'>{{$t('registration.sms.success.sub_info',{phone: phone, email: email})}}</div>         
            </main>
            <footer>
                <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('cancel')}}</ui-btn>
                <ui-btn type='primary' :disabled='!code.value' v-on:click.native='send'>{{$t('send')}}</ui-btn>
            </footer>
        </div>                        
                                
                                `
                            }).Modal({
                                email: this.email,
                                phone: this.phone
                            })
                            
                        }else if (data && data.message) {
                            Vue.Dialog.MessageBox.Success(data.message);
                        }
                        return false;
                    }

                }
            }
        ]
    }

    props() {
        const checkPassConfirm = () => {
            const { password: pass, confirmPassword: passConf } = this
            this.properties.confirmPassword.status = (pass == passConf) ? { type: 'success' } : undefined

            if(!this.conditions || this.conditions.length != 3)
            this.properties.confirmPassword.status = undefined;
        }
        return {
            ecp: {
                type: 'ecp',
                label: '-ecp',
                has_del: true,
                //required: true,
                value: undefined,
                attr: {
                    createPkcs7: false,// true
                    placeholder: "ecp_placeholder",
                    //title: "ecp_placeholder",
                },
                hidden: ()=>{
                    return !ecp.provider
                },
                onChange: (value) => {
                    if (!value)
                        return;

                    let { CN, UID, PINFL } = value;
                    CN = (CN || "").split(" ");
                    this.inn = UID;
                    this.pinfl = PINFL;

                    this.surname = CN.shift();// CN[0]
                    this.name = CN.shift(); //CN[1]
                    this.patronymic = CN.join(" ");// CN[2]
                }
            },
            email: {
                required: true,
                type: 'email',
                label: "-email",
                attr: {
                    autocomplete: 'off',
                    react: true,
                    placeholder: "email_placeholder",
                }
            },
            inn: {
                label: "-inn",
                readonly: true,
                hidden: function () {
                    return !this.model.ecp && !Settings.reg_only_ecp;
                }
            },
            pinfl: {
                label: "-pinfl",
                readonly: true,
                hidden: function () {
                    return !this.model.ecp && !Settings.reg_only_ecp;
                }
            },
            surname: {
                label: "-surname",
                required: true,
                readonly: function () {
                    return this.model.ecp || Settings.reg_only_ecp;
                }
            },
            name: {
                label: "-name",
                required: true,
                readonly: function () {
                    return this.model.ecp || Settings.reg_only_ecp;
                }
            },
            patronymic: {
                label: "-patronymic",
                readonly: function () {
                    return this.model.ecp || Settings.reg_only_ecp;
                }
            },
            phone: {
                label: "-phone",
                required: true,
            },



            password: {
                type: "password",
                label: "-password",
                required: true,
                attr: {
                    autocomplete: 'new-password',
                    react: true
                },
                onChange(val = "") {
                    let conditions = []
                    const valLength = val?.length ?? 0
                    if (8 <= valLength && valLength <= 30) {
                        conditions.push('length')
                    }

                    if(/^(?=.*[a-zA-Z])(?=.*[0-9])(?=.*[\.\,\:\;\?\!\+\%\-\<\>\@\[\]\{\}\/\_\$\#])[0-9a-zA-Z\.\,\:\;\?\!\+\%\-\<\>\@\[\]\{\}\/\_\$\#]+$/g.test(val)){
                        conditions.push('alphabet')
                    }
                    //if (allAlphabetRegExps.every(re => re.test(val))) {
                    //    conditions.push('alphabet')
                    //}
                    if (val.toUpperCase() !== val && val.toLowerCase() !== val) {
                        conditions.push('registr')
                    }

                    this.model.conditions = conditions
                    if (conditions.length === 3) {
                        this.status = { type: 'success' }
                    }
                    checkPassConfirm()
                }
            },
            confirmPassword: {
                label: "-confirmPassword",
                type: "password",
                confirm: ['Password', Language.t('repeated_password_does_not_match')],
                required: true,
                attr: {
                    react: true
                },
                onChange(val = "") {
                    checkPassConfirm()
                }
            },
            conditions: {
                type: "enum",
                label: "!",
                readonly: true,
                dataSource: [
                    { id: 'length', name: 'pass_condition_length' },
                    { id: "alphabet", name: 'pass_condition_alphabet' },
                    { id: "registr", name: 'pass_condition_registr' }
                ]
            },
            agree_to_terms: {
                type: 'bool',
                value: false,
                attr: {
                    title: Language.t("agree_to_terms_placeholder"),
                }
            }
        }
    }
}


Vue.Dialog("SignIn", {
    props: {
        email: {
            type: String,
        },
        view: {
            default: 7
        }
    },
    data: function () {
        return {
            //modelLogIn: new LogIn(),
            //modelRegistration: new Registration(),

            tabs: [
                ((this.view & 1) != 0) && { label: "signin:form_signin_title", model: new LogIn({email: this.email}) },
                ((this.view & 2) != 0) && { label: "forgot_password:form_forgot_password_title", model: new Forgot() },
                ((this.view & 4) != 0) && { label: "SignOn.title:sign_on_title", model: new Registration() },
            ].filter((item)=>{
                return item
            }),
            current: 0
        }
    },
    computed: {
        currentModel() {
            return this.tabs[this.current].model
        },
        currentSubActions() {

            return this.currentModel?.sub_actions?.filter((action) => {
                if (action.hidden && typeof action.hidden == 'function') {
                    return !action.hidden();
                }
                return !action.hidden;
            })
        }
    },
    methods: {
        onTab(index) {
            this.current = index
        },
        async handler(handler) {
            await this.wait(async () => {
                let redirect = await handler();

                if (redirect == undefined)
                    return;

                this.Close(redirect);
            });
        }

    },
    template: `
        <div>
            <header>{{$t('welcome_reg')}}</header>
            <main>
                <ui-layout-tab name='authorization' v-on:tab='onTab' :sync='false'>
                    <ui-layout-group :label='tab.label' v-for='tab in tabs'>

                        <ui-alert type='danger' v-if='tab.model.error'>{{tab.model.error.message}}</ui-alert>

                        <ui-layout class="iac-register-fields" :fields='tab.model.fields' />
                    </ui-layout-group>

                </ui-layout-tab>
            </main>

            <footer style= 'justify-content: space-between; align-items: center'>
                <div>
                    <icon class='ui-btn ui-btn-primary' v-for='action,index in currentSubActions' 
                         :key='index' 
                         :disabled='action.disabled' 
                         :title='$t(action.title)' 
                         style="color: #fff; border-radius: 4px; font-size: 18px; margin-right: 4px; cursor: pointer; width: 35px; height: 35px; display: inline-flex; align-items: center; justify-content: center;"
                         v-on:click='handler(action.handler)'>{{action.icon}}</icon>
                </div>
                <div> 
                    <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('Close')}}</ui-btn>
                    <ui-btn :title='$t(action.title)' :key='index' v-if='!action.hidden' :disabled='action.disabled' :type='action.type' v-for='action,index in currentModel.actions' v-on:click.native='handler(action.handler)'>{{action.label}}</ui-btn>
                </div>
            </footer>

        </div>
    `
})
