import { Context, Config, Develop } from '@iac/kernel';
import { DataSource, RefStore, RemoteStore, Query } from '@iac/data';
import { Http,Util } from '@iac/core';
import Router from '../../../../router';

var inject = async (items) => {
    if (!items)
        return;

    let ad_ids = [];
    items.forEach((item) => {
        item.product_props = item.rop && item.rop.properties;
        item.product_name = item.rop && item.rop.product && item.rop.product.name;
        item.product_unit = item.rop && item.rop.unit;
        item.product_image = item.rop && item.rop.images && `${Config.api_server}/file/${item.rop.images[0]}`;
        item.product_currency = item.rop && item.rop.currency;

        item.price = (item.info && item.info.price) || (item.rop && item.rop.price);
        item.request_url = item && `/procedure/${item.id}/core`;



        item.sum = (item.price && item.amount) && item.price * item.amount;
        item.debug_title = JSON.stringify(item, null, '\t');

        item.bindClass = [
            (() => {
                if (item.info && item.info.win)
                    return "ui-alert ui-alert-success";
                if (item.info && item.info.accept)
                    return "ui-alert ui-alert-warning";
                if (item.info)
                    return "ui-alert ui-alert-danger";

            })(),
        ]

        item.actions = [
            {
                get label() {
                    return "contract"
                },
                btn_type: item.info ? "success" : "primary",
                handler: async () => {
                    let number = item.contract_number;
                    if (!number && item.info) {
                        number = item.info.contract_number;
                    }
                    Router.push(`/workspace/contract/${number}/core`);
                    // /workspace/contract/724.8.1/core
                },
                hidden: e => {
                    if (!item.info && item.contract_number)
                        return false;

                    if (item.info && item.info.contract_number)
                        return false;

                    return true;
                }
            }
        ]
    })

    return items;
}

let Grid = {
    props: ["source"],
    data: function () {
        return {
            columns: [
                "id",
                Develop.content_debug && { field: 'batch_id' },
                { field: "inserted_at", type: "date" },
                //"photo",
                //"ad",
                { field: "product_name", style: "width:100%; text-align: left" },
                "amount",
                //"company_id",
                //"info",
                "price",
                { label: "summa", field: "sum" },
                {
                    field: "contract_pay_percent", label: "dif_org", style: "width:100%; text-align: right; white-space: nowrap;", title: "contract_pay_percent", display: (value) => {
                        return value != undefined ? `${value} %` : ""
                    }
                },
                { field: "expires_at", type: "date" },
                "status"
            ]
        }
    },
    computed: {
        debug() {
            return Develop.field_debug;
        }
    },
    template: `
        <ui-data-grid class='top_filter' :dataSource='source':columns="columns" buttons action_name='contract'>
            <template slot='id' slot-scope='props'>
                <iac-entity-edit :title='props.item.debug_title'  v-if='props.item.id' :value='{id: props.item.id, type: "request", prefix: " "}' />
            </template>
            <template slot='photo' slot-scope='props'>
                <div v-if='props.item.product_image'>
                    <img width='50px' :src='props.item.product_image' />
                </div>
            </template>
            <template slot='product_name' slot-scope='props'>                            
                <router-link style="word-wrap: anywhere;" :title="props.item.product_name || $t('request')" class='alert-link clamp_5' v-if='props.item.request_url' :to='props.item.request_url'>
                    {{props.item.product_name || $t('request')}}
                </router-link>
                <div style="word-wrap: anywhere;" :title="props.item.product_props" class="clamp_5">{{props.item.product_props}}</div>
            </template>
            <template slot='amount' slot-scope='props'>
                <span  style='white-space: nowrap;'>                            
                <iac-number :value='props.item.amount' delimiter=' ' />&nbsp;<ui-ref source='ref_unit' :value='props.item.product_unit'/>
                </span>
            </template>
            <template slot='price' slot-scope='props'>                            
                <span v-if='props.item.price' style='white-space: nowrap;'><iac-number :value='props.item.price' delimiter=' ' part='2' />&nbsp;{{props.item.product_currency}}</span>
            </template>
            <template slot='sum' slot-scope='props'>                         
                <span v-if='props.item.sum' style='white-space: nowrap;'>
                    <iac-number :value='props.item.sum' delimiter='\u00A0' part='2' />&nbsp;{{props.item.product_currency}}
                </span>
            </template>
            <template slot='inserted_at' slot-scope='props'>                         
                <iac-date :date='props.item.inserted_at' full icon></iac-date>
            </template>
            <template slot='expires_at' slot-scope='props'>                         
                <iac-date :date='props.item.expires_at' full icon></iac-date>
            </template>
            <template slot='status' slot-scope='props'>                         
                <ui-ref source='status_request' :value='props.item.status'></ui-ref>
            </template>
        </ui-data-grid>
    `
}

let search = new Query({
    text: {
        label: "!search",
        //hidden: true,
        has_del: true,
        group: '!top-',
        order: -1
    },
    spaces: {
        group: '!remark',
        label: "spaces_filter",
        type: "enum",
        has_del: true,
        order: 1,
        dataSource: "ref_spaces_request_ad",
        attr: {
            style: "max-width: 250px"
        }
    },
    nad: {
        type: "hidden",
        hidden: true,
        label: "!Тип магазина",
        //has_del: true,
        sync: false,
        dataSource: [
            // {id: undefined,name: "Все"},
            { id: false, name: "Магазин" },
            { id: true, name: "Нац магазин" }
        ]
    }
});

export default {
    props: {
        nad: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            currentBRV: '',
            request_subscription_cash: [],
            user: Context.User,
            owner_request: new DataSource({
                query: new Query({
                    relation: {
                        value: "owner",
                        type: "hidden",
                        sync: false,
                    },
                    //...query
                }, [search]),
                store: new RefStore({
                    ref: 'ref_request_ad_private',
                    inject: inject,
                    injectQuery: (params) => {
                        params.fields = ["id", "contract_pay_percent", "rop", "info", "amount", "status", "inserted_at", "expires_at", "product_image", "batch_id", "contract_number"]
                        params.query = params.filters.text;
                        params.filters.text = undefined;

                        //params.filters.

                        return params;
                    }
                }),
            }),
            participant_request: new DataSource({
                query: new Query({
                    relation: {
                        value: "not_owner",
                        type: "hidden",
                        sync: false,
                    },
                    is_participant: {
                        value: true,
                        type: "hidden",
                        sync: false,
                    },
                    accept: {
                        group: 'top-',
                        label: "!accept",
                        type: "entity",
                        has_del: true,
                        dataSource: [
                            { id: false, name: "not_submited_proposal" },
                            { id: true, name: "submited_proposal" }
                        ],
                        attr: {
                            style: "max-width: 250px"
                        }
                    },
                    win: {
                        group: 'top-',
                        label: "!win",
                        type: "entity",
                        has_del: true,
                        dataSource: [
                            { id: false, name: "i_dont_winner" },
                            { id: true, name: "i_winner" }
                        ],
                        hidden: function () {
                            if (!this.model.accept || !this.model.accept.id) {
                                return true;
                            }
                            return false;
                        },
                    },
                    status: {
                        group: 'top-',
                        label: "!status",
                        type: "entity",
                        has_del: true,
                        dataSource: "status_request",
                        attr: {
                            style: "max-width: 250px"
                        }
                    },
                    test: {
                        type: "widget",
                        label: 'remark',
                        group: 'remark',
                        order: 2,
                        widget: {
                            name: {
                                template: `
                                    <ul>
                                        <li class='text_warning'>{{ $t('proposal_by_this_request_submited') }}</li>
                                        <li class='text_success'>{{ $t('you_winner_by_this_request') }}</li>
                                    </ul>
                                `
                            }
                        }
                    }
                }, [search]),
                store: new RefStore({
                    ref: 'ref_request_ad_private',
                    inject: inject,
                    injectQuery: async (params) => {

                        params.fields = ["id", "contract_pay_percent", "rop", "info", "amount", "status", "inserted_at", "expires_at", "product_image", "batch_id", "contract_number"]

                        params.query = params.filters.text;
                        params.filters.text = undefined;

                        if (!params.filters.is_participant) {
                            params.filters.accept = undefined;
                            params.filters.win = undefined;
                        }

                        if (!params.filters.accept) {
                            params.filters.win = undefined;
                        }


                        return params;
                    }
                }),
            }),
            participant_request_sub_2: new DataSource({
                query: new Query({
                    relation: {
                        value: "not_owner",
                        type: "hidden",
                        sync: false,
                    },
                    analog: {
                        value: true,
                        type: "hidden",
                        value: true,
                        sync: false,
                    },
                    is_participant: {
                        type: "hidden",
                        value: false,
                        sync: false,
                    },
                    /*status: {
                        group: 'filter-',
                        label: "!status",
                        type: "entity",
                        has_del: true,
                        dataSource: "status_request"
                    },*/
                }, [search]),
                store: new RefStore({
                    ref: 'ref_request_ad_private',
                    inject: inject,
                    injectQuery: async (params) => {
                        params.fields = ["id", "contract_pay_percent", "rop", "info", "amount", "status", "inserted_at", "expires_at", "product_image", "batch_id", "contract_number"]
                        params.query = params.filters.text;
                        params.filters.text = undefined;

                        return params;
                    }
                })
            }),
            request_limits: new DataSource({
                query: new Query({
                    queryText: {
                        label: "!Search",
                        group: '!filter-',
                      },
                    has_expense: {
                        type: 'entity',
                        has_del: true,
                        group: '!filter-',
                        label: "!request_limits_current",
                        value: true,
                        dataSource: [
                            { id: true, name: "has_expense" },
                            { id: false, name: "all" },
                        ],
                        attr: {
                            style: "max-width: 320px"
                        }
                    }
                }),
                limit: 50,
                store: new RefStore({
                    method: "contract_ref",
                    op: "read",
                    ref: 'cat_sum',
                    injectQuery: async (params) => {
                        return params;
                    }
                })
            }),
            request_limits_columns_brv: [
                { field: "name", label: "request_limits_category" },
                { field: "current_brv", label: "request_limits_current", style: "white-space: nowrap; text-align: right", display: (value)=>{
                    return Util.Number(value, ' ', 2);
                } },
                { field: "limit_brv", label: "request_limits_limit", style: "white-space: nowrap; text-align: right", display: (value)=>{
                    return Util.Number(value, ' ', 0);
                } },
            ],
            request_limits_columns_soms: [
                { field: "name", label: "request_limits_category" },
                { field: "current", label: "request_limits_current", style: "white-space: nowrap; text-align: right", display: (value)=>{
                    return Util.Number(value, ' ', 2);
                } },
                { field: "limit", label: "request_limits_limit", style: "white-space: nowrap; text-align: right", display: (value)=>{
                    return Util.Number(value, ' ', 0);
                } },
            ]
        };
    },
    methods: {
        async getCurrentBRV() {
            let { error, data } = await Http.api.rpc("common_ref", {
                ref: "last_base_calculated_value",
                op: "read"
            })
            if (!error && data) {
                this.currentBRV = data?.[0]?.value ?? 1
            } else {
                this.currentBRV = ""
            }
        }
    },
    watch: {
        nad: {
            immediate: true,
            async handler(value) {
                this.search_query.nad = this.nad
                //this.search_query.set({nad: this.nad})
            }
        }
    },
    components: {
        Grid: Grid
    },
    computed: {
        search_query() {
            return search;
        }
    },
    mounted() {
        this.getCurrentBRV()
    },
    template: `
        <iac-access :access='$policy.request_list_own || $policy.request_list_participant || $policy.comm_request_list_own || $policy.comm_request_list_participant'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{ $t('home') }}</router-link></li>
                <li>{{ nad ? $t('nav.nad_request') : $t('nav.request') }}</li>
            </ol>
            <div class='title' style='align-items: flex-end;'>
                <h1>{{ nad ? $t('nav.nad_request') : $t('nav.request') }}</h1>
                <div><ui-field style='margin: 0;' :model='search_query.properties.nad' /></div>
            </div>
            
        </iac-section>
        <iac-section>
        
            <ui-layout-tab :clear='true'>

                <ui-layout-group key='owner_request' v-if='$policy.request_list_own || $policy.comm_request_list_own' label="inbox">
                    <Grid :source='owner_request'  />
                </ui-layout-group>

                <ui-layout-group key='participant_request' v-if='$policy.request_list_participant || $policy.comm_request_list_participant' label="participant_request">
                    <Grid :source='participant_request' />
                </ui-layout-group>

                <ui-layout-group key='participant_request_sub' v-if='$policy.request_list_participant || $policy.comm_request_list_participant' label="participant_request_sub_2">
                    <Grid :source='participant_request_sub_2' />
                </ui-layout-group>

                <ui-layout-group key='request_limits'  label="request_limits">
                    <ui-layout-tab name='limits' >
                        <ui-layout-group key='request_limits_brv'  label="request_limits_brv">
                            <h4>{{$t('request_limits_brv_value')}}: {{currentBRV}}</h4>
                            <ui-data-grid class='top_filter' :dataSource='request_limits' :columns="request_limits_columns_brv"/>
                        </ui-layout-group>
                        <ui-layout-group key='request_limits_soms'  label="request_limits_soms">
                            <ui-data-grid class='top_filter' :dataSource='request_limits' :columns="request_limits_columns_soms"/>
                        </ui-layout-group>
                    </ui-layout-tab>
                </ui-layout-group>
            </ui-layout-tab>
            
        </iac-section>
        </iac-access>
    `
}
