export default {
    data: function(){
        return {
            name: {
                type: "string",
                label: "-First Name",
                description: "^First Name",
                value: undefined,
                attr: {
                    react: true
                }
            }
        }
    },
    markdown: `
# Демонстрация работы филдов
> First name: {{name.value}}

<ui-field :model='name' style='max-width: 500px' />

- [x] Test1
- [ ] {{name.value}}

<ui-alert type='danger'> 

**First name**:  {{name.value}}
> First name {{name.value}}

</ui-alert>

        // js code
        import {Test} from 'test'
        console.log(test)

`
}