
const ebpMultilangTreeSelectTreeList = {
    name: "ebpMultilangTreeSelectTreeList",
    props: {
        langs: {
            type: Array,
            required: true
        },
        parent: Object,
        request: {
            type: Function,
            required: true
        },
        hiddenGroupsIds: {
            type: Array,
            default: []
        }
    },
    components: {
        'ebpMultilangTreeSelectTreeList': ebpMultilangTreeSelectTreeList
    },
    data() {
        return {
            list: undefined
        }
    },
    methods: {
        change(currentGroup, nextGroups) {
            const groups = [currentGroup, ...nextGroups].filter(item => Boolean(item))
            this.$emit('change', groups)
        },
        async loading(parent) {
            const loadedData = await this.request(parent?.id)
            this.list = loadedData.filter(item => !this.hiddenGroupsIds.includes(item.id))
        },
        switchNodeChild(node) {
            node.show = !node.show
            this.list = [...this.list]
        }
    },
    mounted() {
        this.loading(this.parent)
    },
    template: `
    <div class="iac--ebp-multilang-tree-select-tree-list">
        <div v-if="list" v-for="node in list">
            <div>
                <div v-if="node.has_children" class="children" :class="{'show':node.show}" @click="e=>switchNodeChild(node)">
                    <div>></div>
                </div>
                <div v-else/>
                <div @click="()=>change(node, [])">
                    <div v-for="(lang, index) in langs" v-if="lang.active">
                        <div>{{lang.title}}</div>
                        <input v-model="node.name[lang.name]" :disabled="true"/>
                    </div>            
                </div>
            </div>
            <ebp-multilang-tree-select-tree-list v-if="node.show && node.has_children" :langs="langs"
             @change="groups => change(node, groups)" :parent="node" :request="request" :hiddenGroupsIds="hiddenGroupsIds"/>
        </div>
    </div>
    `
}


export default {
    name: "ebpMultilangTreeSelect",
    components: {
        'ebpMultilangTreeSelectTreeList': ebpMultilangTreeSelectTreeList
    },
    model: {
        prop: 'data',
        event: 'change'
    },
    props: {
        langs: {
            type: Array,
            required: true
        },
        data: {
            type: Object,
            required: true
        },
        label: String,
        placeholder: String,
        request: {
            type: Function,
            required: true
        },
        hiddenGroupsIds: {
            type: Array,
            default: []
        },
        clear: {
            type: Function,
            required: true
        },
        update: {
            type: Function
        }
    },
    data() {
        return {
            showSelect: false,
            emptyName: { name: {} }
        }
    },
    methods: {
        onClickInput() {
            !this.showSelect && this.showSelectHandler()
        },
        change(itemsArray) {
            this.update?.(itemsArray.map(({ id, parent_id, name }) => ({ id, parent_id, name })))
            this.closeSelectHandler()
        },
        clickOutsideEvent(e) {
            if (this.showSelect && this.$refs.select && !this.$refs.select.contains(e.target)) {
                this.closeSelectHandler()
            }
        },
        showSelectHandler() {
            this.showSelect = true
            setTimeout(() => document.body.addEventListener('click', this.clickOutsideEvent), 100)
        },
        closeSelectHandler() {
            this.showSelect = false
            document.body.removeEventListener('click', this.clickOutsideEvent)
        }
    },
    computed: {
        showedValue() {
            const { data, emptyName } = this
            return data.length ? data[data.length - 1] : emptyName
        }
    },
    async created() {
        this.langs.forEach(lang => this.emptyName[lang.name] = "")
    },
    template: `
    <div class="iac--ebp-multilang-tree-select" v-if="data">
        <label>{{label}}</label>
        <div>
            <div class="select" v-if="showSelect" ref="select">
                <ebp-multilang-tree-select-tree-list :hiddenGroupsIds="hiddenGroupsIds" :langs="langs"
                 @change="change" :parent="null" :request="request"/>
            </div>
            <div class="current">
                <div v-for="(lang, index) in langs" v-if="lang.active">
                    <div>{{lang.title}}</div>
                    <input :value="showedValue.name[lang.name]"
                        @click="onClickInput"
                        :placeholder="placeholder"
                        :disabled="!(update||clear) || showSelect"/>
                </div>
                <button v-if="clear && data && data.length" @click="clear">x</button>
            </div>
        </div>
    </div>
    `
}