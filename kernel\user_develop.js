import Entity from './data/entity'
import { Event, Language, Http } from '@iac/core'
import Develop from './develop';
import Config from './config';
import properties from '../demo/properties';

export default class UserDevelop extends Entity {

    constructor(context = {}, entity) {
        super(context)
        this.entity = entity;
        this.updateData(context);
    }
    @Event async onChangeProperty(event) {
        if (!event.data)
            return;

        let field = event.data;
        if (field.property.type != 'file' || !field.value) {
            return;
        }

        var send = async (file) => {
            let formData = new FormData();
            formData.append('data', file, file.name);
            let { data, error } = await Http.upload.form('tender/attach', formData);
            if (error) {
                await Vue.Dialog.MessageBox.Error(error);
                //field.property.wait = false;
                return;
            }

            return {
                id: data.uuid,
                name: data.meta.name,
                //desc: context.description,
                meta: {
                    "type": data.meta.type,
                    "content_type": data.meta.content_type,
                    "type_group": data.meta.group,
                    "size": data.meta.size
                }
            }
        }

        if (field?.value?.file?.name) {
            field.value = field.property._value = await send(field.value.file);
        }
    }


    props() {
        return {
            create_lots_procedures: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: '<b>create_lots_procedures</b></br>Создавать многолотовые процедуры'
            },
            develop_tools: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: '<b>develop_tools</b></br>Доступ к девелоперским настройкам'
            },
            custom_contract_develop: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: '<b>custom_contract_develop</b></br>Работа с прямыми договорами'
            },
            cart_develop: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: '<b>cart_develop</b></br>Работа с корзиной'
            },
            trusted_ecp_income_develop: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: `<b>trusted_ecp_income_develop</b></br>${Language.t('setting.trusted_ecp_income')}`,
                hidden: () => {
                    return this.entity != "company";
                }
            },
            limit_as_gos_in_eshop: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: '<b>limit_as_gos_in_eshop</b></br>Включить лимит бюджетного заказчика в ЭМ для корпоративного заказчика',
                hidden: () => {
                    return this.entity != "company";
                }
            },
            disable_deposit_block_in_eshop: {
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: '<b>disable_deposit_block_in_eshop</b></br>Исключить блокировку залоговых средств для корпоративного заказчика в ЭМ',
                hidden: () => {
                    return this.entity != "company";
                }
            },
            ignore_limits_of_brv_for_selection: {
                selected: false,
                group: '{dev_tab}/Develop',
                type: 'bool',
                label: `<b>ignore_limits_of_brv_for_selection</b></br>${Language.t('permission_to_publish_selections_over_25000_brv')}`,
                hidden: () => {
                    return this.entity != "company";
                }
            },
            ignore_limits_of_brv_for_selection_from: {
                required: true,
                group: '{dev_tab}/Develop',
                type: 'date',
                label: 'from',
                has_del: true,
                hidden: () => !this.ignore_limits_of_brv_for_selection
            },

            ignore_limits_of_brv_for_selection_to: {
                required: true,
                group: '{dev_tab}/Develop',
                type: 'date',
                label: 'to',
                has_del: true,
                validate() {
                    if (!this.model.ignore_limits_of_brv_for_selection_from
                        || !this.model.ignore_limits_of_brv_for_selection_to) {
                        return;
                    }


                    if (this.model.ignore_limits_of_brv_for_selection_from > this.model.ignore_limits_of_brv_for_selection_to) {
                        return Language.t('to_from_error');
                    }
                },
                hidden: () => !this.ignore_limits_of_brv_for_selection
            },

            ignore_limits_of_brv_for_selection_file: {
                required: true,
                group: '{dev_tab}/Develop',
                type: 'file',
                label: 'permitting_document',
                // multiple: true,
                hidden: () => !this.ignore_limits_of_brv_for_selection,
                meta: {
                    url: (value) => {
                        if (!value.id)
                            return;
                        return `${Config.api_server}/file/${value.id}`
                    },
                }
            }

        }
    }

    async updateData(context) {
        if (!this.entity) {
            context = {
                meta: {
                    ...context.face_meta,
                    ...context.company_meta,
                    ...context.user_meta,
                }
            }
        }

        this.context = context
        this.fields.forEach((field) => {
            console.log(field.name, context.meta[field.name]);
            field.value = context.meta[field.name]
        })
    }

    async save() {
        if (this.validate()) {
            return

        }

        let params = this.fields.filter((field) => {
            if (field.type == 'bool' && !field.value)
                return false;
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).filter((field) => {
            if (field.readonly && typeof field.readonly == 'function') {
                return !field.readonly();
            }
            return !field.readonly;
        }).map((field) => {
            let value = field.value;
            if (field.value && field.value.exp && field.value.exp.value != undefined) {
                value = field.value.exp.value
            }

            if (Array.isArray(value) && value.length <= 0)
                value = undefined;

            if (field.type == 'bool' && !field.value)
                value = undefined;

            return {
                name: field.name,
                value: value
            }
        }).reduce((prev, curr) => {
            prev[curr.name] = curr.value
            return prev;
        }, {})

        let meta = { ...this.context.meta}
        this.fields.forEach((field)=>{
            meta[field.name] = params[field.name]
        })

        if (!params.ignore_limits_of_brv_for_selection) {
            delete meta.ignore_limits_of_brv_for_selection_from;
            delete meta.ignore_limits_of_brv_for_selection_to;
            delete meta.ignore_limits_of_brv_for_selection_file;
            delete meta.ignore_limits_of_brv_for_selection;
        }

        let response;

        if (this.entity == 'user') {
            response = await Http.default.rpc("update_user_meta", {
                user_id: this.context.id,
                meta: meta
            });
        } else if (this.entity == 'company') {
            response = await Http.default.rpc("update_company_meta", {
                company_id: this.context.id,
                meta: meta
            });
        } else {
            return;
        }

        if (response?.error) {
            Vue.Dialog.MessageBox.Error(response?.error)
        } else {
            Vue.Dialog.MessageBox.Success(Language.t('updated'))
        }

        this.context.meta = meta

        return {
            data: params
        }
    }

    static edit(context, entity) {
        Vue.Dialog({
            props: ['model'],
            computed: {
                classes() {
                    return [
                        {
                            "iac-wait": this.model.wait
                        }]
                },
            },
            methods: {
                save() {
                    this.$wait(async () => {
                        await this.model.save();
                    })
                }
            },
            template: `
                <div v-bind:class='classes'>
                    <main><ui-layout :fields='model.fields'/></main>
                    <footer>
                        <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
                        <ui-btn type='primary' v-on:click.native='save()'>{{$t('save')}}</ui-btn>
                    </footer>
                </div>
            `
        }).Modal({
            model: new UserDevelop(context, entity)
        })
    }

}
