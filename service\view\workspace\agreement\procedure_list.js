import { DataSource, RemoteStore, Query } from '@iac/data'
import { Context } from '@iac/kernel'
//Context.Access.policy['user_change_role'];
export default {
    data: function () {
        return {
            dataSource: new DataSource({
                valueExp: 'id',
                displayExp: 'title',
                query: new Query({
                    relation: (() =>{
                      const policies = [];
                      Context.Access.policy['agreement_all_procedures'] ? policies.push({id: 'admin', name: 'agreement.admin'}) : null;
                      Context.Access.policy['agreement_own_procedures'] ? policies.push({id: 'owner', name: 'agreement.owner'}) : null;
                      Context.Access.policy['agreement_voter_procedures'] ? policies.push({id: 'voter', name: 'agreement.voter'}) : null;
                      
                      if(policies.length == 1) {
                        return policies[0].id
                      } else {
                        return {
                          type: 'entity',
                          dataSource: policies,
                          has_del: true
                        }  
                      }
                    })()
                }),
                store: new RemoteStore({
                    method: "get_agreement_procedures",
                    context: (context)=>{
                        context.rights = context.actions
                        context.actions = undefined;
                        return context
                    }
                })
            }),
            columns: [
                "object",
                { field: "title", style: "width:100%" },
                "action"
            ],
        }
    },
    methods: {

    },
    template: `
    <iac-access :access='$policy.agreement_list_procedures' class='iac-agreements-list'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.agreements_procedures')}}</li>
            </ol>
            <div class='title'>
                <h1>{{$t('nav.agreements_procedures')}}</h1>
            </div>
        </iac-section>
        <iac-section>
            <ui-data-tile :dataSource='dataSource'>
                <div class='tile' slot='template' slot-scope='props'>
                    <div class='fill'>
                        <div v-if=0 v-if1='!props.item.rights.can_edit'>{{props.item.object_title}}</div>
                        <div v-else><router-link :to='"/workspace/agreement/procedure/"+props.item.id'>{{props.item.object_title}}</router-link></div>
                    </div>
                    <div class='props'>
                        <div><label>{{$t('object')}}:</label> <span><router-link class='object' :title='props.item.object_title' :to='"/procedure/"+props.item.object_id+"/core"'>{{$t([props.item.object_type+'_short',props.item.object_type])}} №{{props.item.object_id}}</router-link></span></div>
                        <div><label>{{$t('commission_secretary')}}:</label> <span class='text-wrap'>{{props.item.creator_name}}</span></div>
                        <div><label>{{$t('status')}}:</label> <span><ui-ref source='ref_status_agreement_procedure' :value='props.item && props.item.status'/></span></div>
                    </div>
                    <div v-if='props.item.rights.can_vote || props.item.rights.can_sign' style='margin-top: 10px; padding-top: 10px; border-top: 1px solid #ccc;'>
                        <router-link v-if='props.item.rights.can_vote' class='ui-btn ui-btn-primary' :to='"/workspace/agreement/procedure/"+props.item.id+"/vote"'>{{$t('nav.agreements')}}</router-link>
                        <router-link v-if='props.item.rights.can_sign' class='ui-btn ui-btn-primary' :to='"/workspace/agreement/procedure/"+props.item.id+"/vote"'>{{$t('subscribe')}}</router-link>
                    </div>
                </div>
            </ui-data-tile>
        </iac-section>
    </iac-access>
    `
}