/** Class representing a Guid. */
export default class Guid {
    /**
     * @param {string} guid 
     */
    constructor(guid) {
        this.guid = guid || Guid._newGuid();
    }
    
    static _newGuid(){
        let S4 = function () {
            return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
        }
        return S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4();
    }

    /**
     * @returns {Guid}
     */
    static newGuid() {
        return new Guid(Guid._newGuid());
    }

    /**
     * @returns {string}
     */
    toString() {
        return this.guid;
    }
}