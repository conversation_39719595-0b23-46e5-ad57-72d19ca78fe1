.ui-data-view-item.claim-item{
    .procedure_link {
        >div {
            position: relative;
            cursor: pointer;
            >div{
                display: none;
                position: absolute;
                right: 0;
                background: #fff;
                border: 1px solid #ccc;
                border-radius: 4px;
                box-shadow: 2px 2px 4px rgb(219, 219, 219);
                z-index: 1;
                >a{
                    padding: 6px 18px;
                    display: block;
                    white-space:nowrap;
                    &:hover {
                        background-color: #eee;
                    }
                }
            }
            &:hover{
                >div{
                    display: block;
                }
            }
        }
    }

}

/*.clamp_2{
    -webkit-box-orient: vertical;
    -webkit-box-flex: 1;
    -webkit-line-clamp: 2;
    display: -webkit-box;
    overflow: hidden;
}*/

.generate-clamp(@i: 2) when (@i =< 7) {
    .clamp_@{i} {
        -webkit-box-orient: vertical;
        -webkit-box-flex: 1;
        -webkit-line-clamp: @i;
        display: -webkit-box;
        overflow: hidden;
    }  
    .generate-clamp(@i + 1);
  }  
.generate-clamp();