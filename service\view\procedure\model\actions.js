import { Http, Language } from '@iac/core'

import Router from '../../../router';
import ParticipantsDlg from './_dlg_participants'
import ProcedureBeginAgreementDlg from './_dlg_begin_agreement'
import PublishProcedureDlg from './_dlg_publish_procedure'

export default function () {

    let $this = this;

    let label = (name) => {
        return [`action.${this.procedure}.${name}`, `action.${name}`]
    }

    return [
        {
            name: 'participants',
            //type: 'info',
            group: 'top',
            get label() {
                return 'participants'
            },
            hidden: () => {
                return !this.access('get_participants')
            },
            handler: async () => {
                return await ParticipantsDlg.Modal({
                    size: "lg",
                    model: this
                })
            }
        },
        ...[
            ...Object.keys(this.context.actions).map((key) => {
                return {
                    method: key,
                    ...this.context.actions[key]
                }
            }),
        ].map((action) => {
            return {
                name: action.method,
                type: action.type,
                group: action.group,
                question: action.question,
                get label() {
                    return action.label || label(action.method)
                },
                hidden: () => {
                    return !this.access(action.method)
                },
                handler: async (request={}) => {

                    let pkcs7B64;
                    if (action.subscribe) {
                        let result = await this.digest(action.subscribe);
                        if (!result)
                            return;

                        if (result.error) {
                            if (result.error.code != "AbortError") {
                                await Vue.Dialog.MessageBox.Error(result.error.message);
                            }
                            return;
                        }
                        
                        pkcs7B64 = result.data;
                    }

                    let params = action.params || {proc_id: this.id, proc_key: this.proc_key};
                    params = {...params,...request}
                    params.pkcs7B64 = pkcs7B64;

                    let { data, error } = await Http.proc.rpc(action.method, params);
                    if (!error) {
                        if (data && data.message) {
                            await Vue.Dialog.MessageBox.Success(data.message);
                        }
                        this.onReload();
                    } else if (error.code != "AbortError") {
                        if (!action.subscribe) {
                            this.clearError();
                            this.setError(error);
                        }
                        await Vue.Dialog.MessageBox.Error(error);
                    }
                    return { data, error };
                }
            }
        }),
        {
            name: "chat",
            group: 'chat',
            type: 'info',
            get label() {
                return "chat"
            },
            get badge(){
                return $this.unread_messages_count
            },
            handler: async () => {
                await Vue.Dialog.Chat.Modal({
                    size: "full",
                    object: {
                        type: this.procedure,
                        id: this.id
                    }
                })
                $this.update_unread_messages_count();   
            },
            hidden: () => {
                return !this.access("get_chats")

            }
        },
        {
            name: "agreement_procedure",
            get label() {
                return label("agreement_procedure")
            },
            hidden: () => {
                return !this.access("update_agreement_procedure") || !this.access("get_agreement_procedure_id")
            },
            handler: async () => {
                let { data, error } = await Http.proc.rpc("get_agreement_procedure_id", { proc_id: this.id });
                if (error) {
                    return await Vue.Dialog.MessageBox.Error(error);
                }
                if (data && data.id) {
                    Router.push(`/workspace/agreement/procedure/${data.id}`)
                }
                return { data, error };
            }
        },
        {
            name: "agreement_procedure_vote",
            get label() {
                return label("agreement_procedure_vote")
            },
            hidden: () => {
                return !this.access("put_agreement_vote") || !this.access("get_agreement_procedure_id")
            },
            handler: async () => {
                let { data, error } = await Http.proc.rpc("get_agreement_procedure_id", { proc_id: this.id });
                if (error) {
                    return await Vue.Dialog.MessageBox.Error(error);
                }
                if (data && data.id) {
                    Router.push(`/workspace/agreement/procedure/${data.id}/vote`)
                }
                return { data, error };
            }
        }
    ]
}