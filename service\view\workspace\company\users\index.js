import { DataSource, RemoteStore, Entity } from '@iac/data'
import { Http, Language } from '@iac/core'
import { Context, Develop } from '@iac/kernel'

import Invite from './invite_model'
import SetUserRoleDlg from './_dlg_set_user_role'
import SendInviteDlg from './_dlg_send_invite'
import './ip_access'

export default {
    data: function () {
        return {
            dataSourceUsers: new DataSource({
                query: {
                    has_role: {
                        type: "entity",
                        label: "!has_role",
                        group: "has_role",
                        has_del: true,
                        dataSource: [{
                            id: false, name: "has_role_false"
                        },{
                            id: true, name: "has_role_true"
                        }]
                    }
                },
                store: new RemoteStore({
                    method: "get_company_users",
                    context: (context) => {
                        context.roles = context.roles || []
                        context.actions = [
                            {
                                label: "set_roles",
                                hidden: () => {
                                    return !(
                                        Context.User.id != context.id &&
                                        Context.Access.policy['user_change_role'] &&
                                        (context?.meta?.broker_info == null)
                                    );
                                },
                                handler: async () => {
                                    let result = await Vue.Dialog.set_user_role.Modal({
                                        id: context.id
                                    })
                                    if (result)
                                        this.dataSourceUsers.reload();
                                }
                            },
                            {
                                label: "change_company",
                                hidden: () => {
                                    return !Context.Access.policy['user_change_company'];
                                },
                                handler: async () => {
                                    let { error, data } = await Http.api.rpc("change_company_user", {
                                        user_id: context.id
                                    })

                                    if (error) {
                                        if (error.code != 'AbortError' && error.message)
                                            Vue.Dialog.MessageBox.Error(error)
                                    } else {
                                        this.dataSourceUsers.reload();
                                    }
                                }
                            },
                            // {
                            //     type: 'sep',
                            //     hidden: ()=>{
                            //         return context.blocked;
                            //     },
                            // },
                            {
                                label: "delete_from_company",
                                hidden: () => {
                                    if (!Develop.company_delete_user_develop)
                                        return true;
                                    return !Context.Access.policy['user_profile_change'];
                                },
                                handler: async () => {
                                    if (await Vue.Dialog.MessageBox.Question(Language.t("delete_from_company_question")) != Vue.Dialog.MessageBox.Result.Yes) {
                                        return;
                                    }

                                    let { error, data } = await Http.api.rpc("update_user", {
                                        user_id: context.id,
                                        company_id: null
                                    })

                                    if (error) {
                                        if (error.code != 'AbortError' && error.message)
                                            Vue.Dialog.MessageBox.Error(error)
                                    } else {
                                        if (Context.User.id != context.id) {
                                            await Context.User.refreshToken();
                                        }
                                        this.dataSourceUsers.reload();

                                    }
                                }
                            },
                            {
                                // Админ КБ исключает трейдера из своей компании.
                                label: "exclude_trader_by_client",
                                hidden: () => {
                                    return !Context.Access.policy.exchange_client_trader_exclude ||
                                        Context.Access.policy.exchange_broker_trader_exclude_everywhere ||
                                        !context.roles.find(e => e.id == "exchange_trader") ||
                                        context.roles.length == 0;
                                },
                                handler: async () => {
                                    let { error, data } = await Http.api.rpc("exclude_trader_by_client", {
                                        trader_id: context.id,
                                        data: {request_question: true},
                                    });

                                    if (error) {
                                        if (error.code != 'AbortError' && error.message) {
                                            Vue.Dialog.MessageBox.Error(error);
                                        }
                                    } else if (data) {
                                        if (Context.User.id != context.id) {
                                            await Context.User.refreshToken();
                                        }
                                        this.dataSourceUsers.reload();
                                    }
                                }
                            },
                            {
                                // Админ брокера исключает трейдера из своей компании тем самым исключает его и из
                                // всех КБ компаний.
                                label: "exclude_trader_everywhere",
                                hidden: () => {
                                    return !Context.Access.policy.exchange_broker_trader_exclude_everywhere ||
                                        !context.roles.some(e => e.id == "exchange_trader") ||
                                        context.roles.length == 0;
                                },
                                handler: async () => {
                                    let { error, data } = await Http.api.rpc("exclude_trader_everywhere", {
                                        trader_id: context.id,
                                    });

                                    if (error) {
                                        if (error.code != 'AbortError' && error.message) {
                                            Vue.Dialog.MessageBox.Error(error);
                                        }
                                    } else if (data) {
                                        if (Context.User.id != context.id) {
                                            await Context.User.refreshToken();
                                        }
                                        this.dataSourceUsers.reload();
                                    }
                                }
                            },
                        ]
                        return context;
                    }
                }),
                template: 'template-user'
            }),
            dataSourceRequests: new DataSource({
                displayExp: "user_name",
                store: new RemoteStore({
                    method: "get_requests_participation_in_company",
                    context: context => {
                        context.id = context.user_id
                        //context.checkbox = true
                        context.actions = [
                            {
                                label: "activate",
                                handler: async () => { this.active(context.user_id) }
                            }
                        ]
                        return context
                    }
                }),
                template: 'template-company_request',
                actions: [
                    {
                        label: 'activate',
                        hidden: () => {
                            return (this.dataSourceRequests.checkedItems && this.dataSourceRequests.checkedItems.length > 0) ? false : true
                        },
                        handler: async () => {
                            this.active(this.dataSourceRequests.checkedItems || [])
                        }
                    }
                ]
            }),
            dataSourceInvites: new DataSource({
                store: new RemoteStore({
                    method: "get_company_invites",
                    context: (context) => {
                        const invite = new Invite(context);
                        invite.actions = [
                            {
                                label: "invite_again",
                                handler: async () => {
                                    await invite.invite_user_again();
                                }
                            },
                        ]
                        return invite;
                    }
                }),
                template: 'template-company_invite',
                actions: [
                    {
                        label: 'send_invite',
                        handler: async () => {
                            const result = await SendInviteDlg.Modal({ invite: new Invite() })
                            if (result) this.dataSourceInvites.reload()
                        }
                    }
                ]
            })
        }
    },
    /*mounted: function () {
        this.dataSourceRequests.checkedItems = [64]
    },*/
    methods: {
        ip_access(){
            Vue.Dialog.ip_access.Modal({
                size: "right"
            })
        },
        edit(item) {
            return `/workspace/user/${item.id}`
        },
        async active(id) {
            let roles = await Vue.Dialog({
                data: function () {
                    return { dataSourceRoles: DataSource.get('get_roles') }
                },
                methods: {
                    save() { this.Close(this.dataSourceRoles.checkedItems) }
                },
                template: `
                    <div>
                        <header></header>
                        <main>
                            <ui-list check :dataSource='dataSourceRoles' />
                        </main>
                        <footer>
                            <ui-btn type="secondary" @click.native="Close()">{{$t('cancel')}}</ui-btn>
                            <ui-btn type="primary" v-on:click.native="save">{{$t('select')}}</ui-btn>
                        </footer>
                    </div>
                `
            }).Modal({})

            if (!roles)
                return

            let { error, data } = await Http.api.rpc("accept_request_participation_in_company", {
                user_id: id,
                role_ids: roles
            })
            if (error) {
                await Vue.Dialog.MessageBox.Error(error);
            } else {
                await Vue.Dialog.MessageBox.Success(data.message);
                this.dataSourceRequests.reload();
            }
        }
    },
    template: `
    <iac-access :access='$policy.user_list || $policy.user_accept_participation_request || $policy.user_change_policy || $policy.user_change_role || $policy.user_clear_role || $policy.company_invite_user'>
        <iac-section type='header'>

            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('members')}}</li>
            </ol>

            <div class='title'>
                <h1>{{$t('members')}}</h1>
                <ui-btn v-if='$policy.company_whitelist_moderator' type='warning' v-on:click.native='ip_access'>{{$t('ip_access')}}</ui-btn>
            </div>
        </iac-section>
        <iac-section>
            <ui-layout-tab>
                <ui-layout-group v-if='$policy.user_list' key='members' label='members'>
                    <ui-data-view :dataSource='dataSourceUsers' :search="false"/>
                </ui-layout-group>
                <ui-layout-group key='invites' label='invites' v-if='$policy.company_invite_user'>
                    <ui-data-view :dataSource='dataSourceInvites' :search="false" />
                </ui-layout-group>
            </ui-layout-tab>
        </iac-section>
    </iac-access>
    `
}
