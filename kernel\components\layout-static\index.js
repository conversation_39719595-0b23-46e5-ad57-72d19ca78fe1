const Component = {
    props: ["value"],
    data() {
        return {
            fields: [],
        };
    },
    watch: {
        value: {
            immediate: true,
            async handler(value) {
                this.parse(value);
            }
        }
    },
    methods: {
        parse(text) {

            if(Array.isArray(text)){
                text = text.map((item)=>{
                    return `${item.title || item.prop_name}: ${item.value || item.val_name}`;
                }).join('\n')
            }

            let get_value = (value) => {
                if (!value || value == '')
                    return;
                let arr = value.split(/:\/?\/?/g);
                if (arr.length > 1 && ["tel","http","https","mailto"].includes(arr[0].trim())) {
                    let href = value.replace(/[\(|\)|\s]/g,'');
                    return `<a href='${href}'>${arr[1]}</a>`
                }

                return value;
            }

            this.fields = [];

            if (!text || text == '')
                return;

            let current_group = '';

            let lines = text.trim().split("\n");
            for (let line of lines) {
                let group = /^\[(.*)\]/g.exec(line)
                if (group) {
                    current_group = group[1]
                    continue;
                }

                let value = /^\s+(.*)/g.exec(line)
                field = this.fields[this.fields.length - 1];
                if (value && field) {
                    field.value += `<div>${get_value(value[1])}</div>`
                    continue;
                }

                let field = /^.+/g.exec(line)
                if (field) {
                    field = field[0];
                    let index = field.indexOf(":");
                    value = '';
                    if (index > 0) {
                        value = `<div>${get_value(field.substring(index + 1))}</div>`;
                        field = field.substring(0, index);
                    }
                    
                    this.fields.push({
                        name: field[0],
                        type: 'static',
                        group: current_group,
                        label: `-${field}`,
                        value: value
                    })
                    continue;
                }

            }
        }
    },
    template: `
        <ui-layout class='iac-layout-static' :fields='fields' />
    `
}

Vue.component('iac-layout-static', Component);

Vue.Fields['layout-static'] = {
    is: 'iac-layout-static'
}