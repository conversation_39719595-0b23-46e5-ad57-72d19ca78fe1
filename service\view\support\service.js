export default {
    props: {
        model: Object
    },
    methods: {
        routePhoneLink() {
            window.location.href = `tel:${this.model.phoneLink}`
        },
        routeMailLink() {
            window.location.href = `mailto:${this.model.email}`
        }
    },
    template: `
<div class="iac-support-manager">
        <div class="about">
            <div>
                <icon>support_service</icon>
            </div>
            <h3>{{model.title}}</h3>
            <div v-if="model.phoneText">{{model.phoneText}}</div>
            <div v-if="model.email">{{model.email}}</div>
        </div>
        <div class="actions">
            <ui-btn type='primary lg' v-if="model.phoneLink" @click.native="routePhoneLink">{{$t('call_the_phone')}}</ui-btn>
            <ui-btn type='info lg' v-if="model.email" @click.native="routeMailLink">{{$t('write_the_mail')}}</ui-btn>                                    
        </div>
</div>
`
}