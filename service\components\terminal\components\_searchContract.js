export default Vue.Dialog("SearchContract",{
    props: ["setting"],
    methods: {
        onItem(contract){
            this.Close(contract)
        }
    },
    template: `
        <div>
            <header>{{$t('terminal.select_contract')}}</header>
            <main style='min-height: 100px'>
                <ui-scroller>
                    <iac-bkl-exchange-contract v-on:item='onItem' /> 
                </ui-scroller>
            </main>
            <footer>
                <ui-btn type='secondary' v-on:click.native='Close()'>
                {{$t("close")}}
                </ui-btn>
            </footer>
        </div>
    `
})