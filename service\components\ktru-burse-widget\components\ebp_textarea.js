export default {
    name: "ebpTextarea",
    model: {
        prop: 'data',
        event: 'change'
    },
    props: {
        data: {
            type: Object,
            required: true
        },
        label: String,
        placeholder: String,
        disabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {}
    },
    methods: {
        onChangeInput(newValue) {
            this.$emit('change', newValue)
        }
    },
    template: `
    <div class="iac--ebp-textarea">
        <label>{{label}}</label>
        <textarea v-model="data"
            @input="e=>onChangeInput(e.target.value)"
            :placeholder="placeholder"
            :disabled="disabled"/>
    </div>
    `
}