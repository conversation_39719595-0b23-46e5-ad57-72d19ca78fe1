import { DataSource, RefStore, Query } from '@iac/data'
import { Http, Language } from '@iac/core'
import EditorDlg from './_editor_dlg'

export default {
    data: function () {
        return {
            dataSource: undefined,
            store: undefined,
            scheme: undefined,
            introspect: undefined,
        }
    },
    mounted() {
        this.update();
    },
    watch: {
        $route(to, from) {
            this.update();
        }
    },
    methods: {
        async update() {

            this.$wait(async () => {
                let [source_method, method] = this.$route.params.id.split(":");
                let scheme = {
                    source_method, method
                }
                if (this.scheme && (scheme.source_method == this.scheme.source_method && scheme.method == this.scheme.method))
                    return;
                this.scheme = { ...scheme };
                
                this.store = new RefStore({
                    method: this.scheme.source_method,
                    ref: this.scheme.method,
                    injectQuery: async (params) => {
                        params.query = params.filters.text; 
                        params.filters.text = undefined;
                        return params;
                    },
                    context: (context) => {
                        context.context = {...context}
                        context.key = context[this.store._key]
                        context.actions = []
                        context.is_del = false;
                        context.actions.push({
                            //label: "!",
                            icon: "delete",
                            btn_type: "danger sm",
                            handler: async () => {
                                
                                if (await Vue.Dialog.MessageBox.Question(Language.t("question_do_you_really_want_to_delete_this_record")) == Vue.Dialog.MessageBox.Result.Yes) {
                                    let { error, data } = await Http.api.rpc(this.scheme.source_method, {
                                        ref: this.scheme.method,
                                        op: "delete",
                                        data: {
                                            [this.store._key]: context[this.store._key]
                                        }
                                    })
                                    context.is_del = true;
                                    context.bindClass = [
                                        "ui-alert ui-alert-danger"
                                    ]
                                }
                            },
                            hidden: () => {
                                return !this.introspect || context.is_del || !this.introspect.create_fields;
                            }

                        })
                        context.bindClass = context.bindClass || [

                        ]
                        return context;
                    }
                })

                this.dataSource = new DataSource({
                    store: this.store,
                    query: new Query({
                        text: {
                            icon: 'search',
                            label: "!Search",
                            group: "Search",
                            order: 1,
                            //hidden: true,
                        },
                        create: {
                            type: "action",
                            label: '!',
                            group: '!filter-',
                            buttons: true,
                            actions: [
                                {
                                    label: "add",
                                    handler: async () => {
                                        let item = await this.onItem();
                                        if (!item)
                                            return;
                                        item.bindClass = [
                                            "ui-alert ui-alert-success"
                                        ]
                                        this.dataSource.unshift_item(item)
                                    },
                                    hidden: () => {
                                        return !this.introspect || !this.introspect.create_fields;
                                    }
                                }
                            ]
                        },
                        remark: {
                            type: "widget",
                            label: '!',
                            group: '!filter-/remark',
                            widget: {
                                name: {
                                    template: `
                                        <ul>
                                            <li class='text_danger'>Только что удаленные</li>
                                            <li class='text_warning'>Только что измененные</li>
                                            <li class='text_success'>Только что добавленные</li>
                                        </ul>
                                    `
                                }
                            }
                        }
                    })
                })

                this.introspect = undefined;

                let { error, data } = await this.store.introspect();
                if (error) {
                    this.introspect = null;
                } else {
                    this.introspect = data;
                    this.store._key = this.introspect.id;
                }
            })
        },
        async onItem(item = {}) {

            if (item.is_del) {
                return Vue.Dialog.MessageBox.Error("Данный элемент удален")
            }

            if(!this.introspect || this.introspect == null || !this.introspect.create_fields){
                await Vue.Dialog({
                    props: ["item","introspect"],
                    data: function(){
                        return {
                            value: Object.keys(this.item).map((key)=>{
                                let value = item[key];
                                try{
                                    value = JSON.stringify(value, null, '\t')
                                }catch(e){

                                }
                                return {prop_name: key, value: value}
                            })
                        }
                    },
                    template: `
                        <div>
                            <main v-if='!introspect' class='error'>У данного справочника отсутствует introspect</main>
                            <main>
                                <iac-layout-static :value='value' />
                            </main>
                            <footer>
                            
                            </footer>
                        </div>
                    `
                }).Modal({item: item.context,introspect: this.introspect})
                return;
            }

            let model = await EditorDlg.Modal({
                item: {...item},
                introspect: this.introspect,
                scheme: this.scheme,
                size: "right",
            })

            if (model) {
                for (let field_name in model) {
                    item[field_name] = model[field_name];
                }

                let select = this.introspect.select;
                if (select && model["name"] == undefined) {
                    for (let field of select) {
                        let [field_from, field_to] = field;
                        if (field_to == "name")
                            item[field_to] = model[field_from];
                    }
                }
                item.bindClass = [
                    "ui-alert ui-alert-warning"
                ]
                return item;
            }
        }
    },
    template: `
        <div>
            <ui-data-grid v-if='dataSource' class='compact top_filter' :key='$route.params.key' :dataSource='dataSource' :columns='[{ field: "name", style: "width:100%;"}]'  buttons>
                <template slot='name' slot-scope='props'>

                    <a class='alert-link' href="javascript:void(0)" v-on:click.stop.prevent='onItem(props.item)'>{{props.item.name || props.item.title || props.item.id || props.item.key}}</a>
                </template>     
            </ui-data-grid>
        </div>
    `
}