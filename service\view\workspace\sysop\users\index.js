import { DataSource, RemoteStore } from '@iac/data'
import { Http } from '@iac/core'
import SetRoleDlg from './_dlg_set_role';

import { UserDevelop } from '@iac/kernel'

const is_trader_test_accepted = context => {
    const face_meta = context?.face_meta;

    if (!face_meta) return false;

    return face_meta?.trader_test && face_meta?.trader_test?.status == true;
}

const is_trader = context => {
    return context?.is_trader == true;
}

export default {
    data: function () {
        return {
            dataSource: new DataSource({
                store: new RemoteStore({
                    method: 'get_user_list',
                    injectQuery: params => {
                        params.query = params.queryText
                        delete params.queryText
                        return params
                    },
                    context: (context) => {
                        
                        // Для Vue реактивности
                        context.face_meta = context.face_meta || {};
                        context.face_meta.trader_test = context.face_meta.trader_test || {};
                        context.face_meta.trader_test.status = context.face_meta.trader_test.status;// || undefined;
                        context.face_meta.trader_test.test_date = context.face_meta.trader_test.test_date;// || undefined;
                        context.face_meta.trader_test.accepted_at = context.face_meta.trader_test.accepted_at;// || undefined;
                        context.face_meta.trader_test.rejected_at = context.face_meta.trader_test.rejected_at;// || undefined;
                        context.face_meta.trader_test.reason = context.face_meta.trader_test.reason;// || undefined;

                        context.email = context.email
                        context.meta = context.meta || {
                            is_ecp_required: true
                        };
                        delete context.login

                        Object.defineProperty(context, "status_type", {
                            configurable: true,
                            enumerable: true,
                            get: () => {
                                if (context?.meta?.force_request_change_password == true) {
                                    return 'danger';
                                }

                                if (context.blocked) {
                                    return 'warning';
                                }

                                if (!context.meta.is_ecp_required) {
                                    return 'info';
                                }
                            }
                        })

                        context.actions = [
                            {
                                label: 'nav.profile',
                                handler: async () => {
                                    this.$router.push({ path: `/workspace/user/${context.id}` })
                                }
                            },
                            {
                                type: 'sep',
                                hidden: () => {
                                    return context.blocked
                                },
                            },
                            {
                                label: 'set_role',
                                hidden: () => {
                                    return context.blocked
                                },
                                handler: async () => {
                                    await SetRoleDlg.Modal({
                                        size: 'right',
                                        role: true,
                                        user_id: context.id
                                    })
                                }
                            },
                            {
                                label: 'set_policies',
                                hidden: () => {
                                    return context.blocked
                                },
                                handler: async () => {
                                    await SetRoleDlg.Modal({
                                        size: 'right',
                                        user_id: context.id
                                    })
                                }
                            },
                            {
                                label: "access.additional_func",
                                handler: async () => {

                                    await UserDevelop.edit(context,"user")
                                }
                            },
                            {
                                type: 'sep',
                                hidden: () => {
                                    return context.blocked
                                },
                            },
                            {
                                label: 'exchange.set_trader_test_date',
                                hidden: () => {
                                  return !is_trader(context) || context.face_meta?.trader_test?.test_date || context.face_meta?.trader_test?.status
                                },
                                handler: async () => {
                                    let { error, data } = await Http.api.rpc("change_trader_test_status", {
                                        action: "request_question",
                                        face_id: context.face_id,
                                    })

                                    if (error) {
                                        Vue.Dialog.MessageBox.Error(error);
                                    } else  {
                                        context.face_meta.trader_test.status = data?.trader_test?.status;
                                        context.face_meta.trader_test.test_date = data?.trader_test?.test_date;
                                        context.face_meta.trader_test.accepted_at = data?.trader_test?.accepted_at;
                                        context.face_meta.trader_test.rejected_at = data?.trader_test?.rejected_at;
                                        context.face_meta.trader_test.reason = data?.trader_test?.reason;
                                    }
                                }
                            },
                            {
                                label: 'exchange.accept_trader_test',
                                hidden: () => {
                                    return !is_trader(context) || !context.face_meta?.trader_test?.test_date
                                },
                                handler: async () => {
                                    let { error, data } = await Http.api.rpc("change_trader_test_status", {
                                        action: "accept_trader_test",
                                        face_id: context.face_id,
                                    })

                                    if (error) {
                                        Vue.Dialog.MessageBox.Error(error);
                                    } else {
                                        context.face_meta.trader_test.status = data?.trader_test?.status;
                                        context.face_meta.trader_test.test_date = data?.trader_test?.test_date;
                                        context.face_meta.trader_test.accepted_at = data?.trader_test?.accepted_at;
                                        context.face_meta.trader_test.rejected_at = data?.trader_test?.rejected_at;
                                        context.face_meta.trader_test.reason = data?.trader_test?.reason;
                                    }
                                }
                            },
                            {
                                label: 'exchange.decline_trader_test',
                                hidden: () => {
                                    return !is_trader(context) || (!context.face_meta?.trader_test?.test_date && !context.face_meta?.trader_test?.status)
                                },
                                handler: async () => {
                                    if (await Vue.Dialog.MessageBox.Question("Внимание! Вы действительно хотите отозвать лицензию?") == Vue.Dialog.MessageBox.Result.Yes) {
                                        let { error, data } = await Http.api.rpc("change_trader_test_status", {
                                            action: "decline_trader_test",
                                            face_id: context.face_id,
                                        })

                                        if (error) {
                                            Vue.Dialog.MessageBox.Error(error);
                                        } else {
                                            context.face_meta.trader_test.status = data?.trader_test?.status;
                                            context.face_meta.trader_test.test_date = data?.trader_test?.test_date;
                                            context.face_meta.trader_test.accepted_at = data?.trader_test?.accepted_at;
                                            context.face_meta.trader_test.rejected_at = data?.trader_test?.rejected_at;
                                            context.face_meta.trader_test.reason = data?.trader_test?.reason;
                                        }
                                    }
                                }
                            },
                            {
                                type: 'sep',
                                hidden: () => {
                                    return context.blocked
                                },
                            },
                            {
                                label: 'delete',
                                hidden: () => {
                                    return context.blocked
                                },
                                handler: async () => {
                                    if (await Vue.Dialog.MessageBox.Question("Внимание! Вы действительно хотите удалить данного пользователя") == Vue.Dialog.MessageBox.Result.Yes) {
                                        let { error, data } = await Http.api.rpc("user_block", {
                                            user_id: context.id
                                        })
                                        if (error) {
                                            //Vue.Dialog.MessageBox.Error(error)
                                        } else {
                                            //context.blocked = true;
                                        }
                                    }
                                }
                            },
                            {
                                label: 'toggle_require_ecp',
                                hidden: () => {
                                    //return context.blocked
                                },
                                handler: async () => {
                                    if (await Vue.Dialog.MessageBox.Question("toggle_require_ecp") == Vue.Dialog.MessageBox.Result.Yes) {
                                        let { error, data } = await Http.api.rpc("toggle_require_ecp", {
                                            user_id: context.id,
                                            value: !context.meta.is_ecp_required
                                        })
                                        if (error) {
                                            Vue.Dialog.MessageBox.Error(error)
                                        } else {
                                            context.meta.is_ecp_required = !context.meta.is_ecp_required;
                                        }
                                    }
                                }
                            },
                            {
                                label: 'force_request_change_password',
                                hidden: () => {
                                    return context?.face_meta?.force_request_change_password === true
                                },
                                handler: async () => {
                                    let { error, data } = await Http.api.rpc("force_request_change_password", {
                                        user_id: context.id
                                    })

                                    if (error) {
                                        Vue.Dialog.MessageBox.Error(error)
                                    } else {
                                        const face_meta = context.face_meta || {};
                                        context.face_meta = {face_meta, ...{force_request_change_password: true}};
                                    }
                                }
                            }
                        ]
                        return context
                    }
                }),
                template: 'template-user'
            })
        }
    },
    template: `
    <iac-access :access='$policy.system_user_list' class='page-report'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.user_list')}}</li>
            </ol>
            <div class='title'>
                <h1 style='margin: 0;'>{{$t('nav.user_list')}}</h1>
            </div>
        </iac-section>

        <iac-section>
            <ui-layout-group>
                <ui-data-view :dataSource='dataSource' />
            </ui-layout-group>
        </iac-section>
    </iac-access>
    `
}
