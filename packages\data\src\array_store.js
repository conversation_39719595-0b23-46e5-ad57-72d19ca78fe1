import Store from './store'


export default class ArrayStore extends Store {
    constructor(options) {
        if (Array.isArray(options)) {
            options = {
                data: options
            }
        } else {
            options = options || {}
        }
        super(options);

        this.filter = options.filter

        this._array = undefined;

        this._onSetDataPromise = new Promise((onSetDataResolve) => {
            this._index = undefined;
            this._onSetDataResolve = onSetDataResolve;
        })

        this.setData(options)
    }

    onSetData() {
        return this._onSetDataPromise
    }

    async setData(options) {
        let { data } = options;

        let result = undefined;
        if (typeof data == 'function') {
            result = await data();
        } else if (data.then) {
            result = await data;
        }

        data = (result && !Array.isArray(result)) ? result.data : (result || data);


        if (data && !Array.isArray(data)) {
            console.log("ERROR")
            this._array = [];
        } else {
            this._array = data.filter((item) => {
                return item
            }).map((context) => {
                return this.context(context);
            });
        }
        this._onSetDataResolve();
    }

    async index() {
        await this.onSetData();
        if (!this._index) {
            this._index = this._array.reduce((prev, curr, index) => {
                prev[curr[this.key]] = curr;
                return prev;
            }, {})
        }
        return this._index;
    }

    set_item(data) {


        this._array = this._array || [];
        this._index = this._index || {};

        for (let i = 0; i < this._array.length; i++) {
            if (data[this.key] == this._array[i][this.key]) {
                this._array[i] = data;
                this._index[this.key] = data;
                return
            }
        }

        this._array.push(data)
        this._index[this.key] = data;

    }

    delete(key) {
        if (!key) {
            this._array = [];
            this._index = undefined
            return;
        }
        if (this._array) {
            let index = -1;
            for (let i = 0; i < this._array.length; i++) {
                let item = this._array[i]
                if (key == item[this.key]) {
                    index = i;
                    break
                }
            }
            if (index >= 0) {
                this._array.splice(index, 1)
            }
        }
        if (this._index) {
            this._index[this.key] = undefined
        }
    }

    async byKeys(keys) {

    }


    async byKey(key) {
        if (!key && key !== 0 && key !== false)
            return;
        return (await this.index())[key];
    }

    async queryByOptions(options) {
        options = options || {};
        let { take = 10, skip = 0, search } = options || {};
        await this.onSetData();
        let search_data = !search ? this._array : this._array.filter((item)=>{
            let text = item[this.key]+"_"+item.name
            return text.indexOf(search) >=0;
        })

        search_data = search_data.filter((item)=>{
            return this.filter ? this.filter(item) : true;
        })

        return {
            data: search_data.slice(skip, skip + take)
        };
    }
}
