import { Entity, DataSource, RefStore, ArrayStore } from '@iac/data'

class Model extends Entity {
    constructor(context = {}) {
        super(context)
        this.actions = context.actions;

        this.preconditions = context.preconditions;
        this.action = context.action;
    }
    props() {
        return {
            preconditions: {
                label: "!preconditions",
                group: '!-group',
                type: "entity",
                multiple: true,
                dataSource: "contract_preconditions",
            },
            action: {
                label: "!action",
                group: '!-group/<t>',
                type: "entity",
                has_del: true,
                dataSource: "contract_actions",
            },
            action_order: {
                group: '!-group/<t>',
                type: "action",
                buttons: true,
                label: "!Order",
            },
        }
    }
    get status() {
        let preconditions = this.preconditions || [];
        if (preconditions.length <= 0 && !this.action)
            return "error"
        if (preconditions.length <= 0 || !this.action)
            return "warning"
        return "success"
    }
    source() {
        let params = this.fields.map((field) => {
            let value = field.value;
            if (field.value && field.value.exp && field.value.exp.value != undefined) {
                value = field.value.exp.value
            }

            if (Array.isArray(value) && value.length <= 0)
                value = undefined;

            if (Array.isArray(value)) {
                value = value.map((item)=>{
                    if (item && item.exp && item.exp.value != undefined) {
                        return item.exp.value
                    }
                    return item;
                })
            }

            return {
                name: field.name,
                value: value
            }
        }).reduce((prev, curr) => {
            prev[curr.name] = curr.value
            return prev;
        }, {})

        return params
    }
}

export default {
    data: function () {
        return {
            model: new Model(),
            dataSource: new DataSource({
                store: new ArrayStore({
                    data: [

                    ],
                    context: (context) => {
                        var item = new Model(context);

                        item.properties.action_order.actions = [
                            {
                                label: "Up",
                                btn_type: "secondary",
                                handler: () => {
                                    let items = this.dataSource.items;
                                    let index = items.indexOf(item);
                                    items.splice(index, 1);
                                    items.splice(index - 1, 0, item);
                                }
                            },
                            {
                                label: "Down",
                                btn_type: "secondary",
                                handler: () => {
                                    let items = this.dataSource.items;
                                    let index = items.indexOf(item);
                                    items.splice(index, 1);
                                    items.splice(index + 1, 0, item);
                                }
                            },
                            {
                                label: "Delete",
                                btn_type: "danger",
                                handler: () => {
                                    let items = this.dataSource.items;
                                    let index = items.indexOf(item);
                                    items.splice(index, 1);
                                }
                            }
                        ]
                        return item
                    }
                }),
                template: {
                    props: ["model"],
                    template: `
                        <div>
                            <ui-layout :fields='model.fields' />
                        </div>
                    `
                }
            })
        }
    },
    methods: {
        add_item() {
            this.dataSource.push_item();
        },
        clear() {
            this.dataSource._items = [];
        },
        async source() {
            //this.dataSource.set_items([{action: "block_all_org", preconditions:["contract_deduce?","fine?"]},{},{}]);

            let source = this.dataSource.items.map((item) => {
                return item.source();
            })

            let items = await Vue.Dialog({
                props: ["source"],
                data: function () {
                    return {
                        error: undefined,
                        fields: [
                            {
                                name: "source",
                                type: "text",
                                label: "!source",
                                value: this.source
                            }
                        ]
                    }
                },
                methods: {
                    async save() {
                        this.error = undefined;
                        try {
                            let value = JSON.parse(this.fields[0].value)
                            if (!Array.isArray(value)) {
                                throw "Должен быть массивом";
                            }
                            this.Close(value);
                        } catch (error) {
                            this.error = error.message || error
                        }
                    }
                },
                template: `
                    <div>
                        <header>Исходник</header>
                        <main v-if='error' class='error'>
                            {{error}}
                        </main>
                        <main>
                            <ui-layout :fields='fields' />
                        </main>
                        <footer>
                            <ui-btn type='secondary' v-on:click.native='Close()'>{{$t("close")}}</ui-btn>
                            <ui-btn type='primary' v-on:click.native='save'>{{$t("apply")}}</ui-btn>
                        </footer>
                    </div>
                `
            }).Modal({
                size: "lg",
                source: JSON.stringify(source, null, '    ')
            })

            if (items)
                this.dataSource.set_items(items);


        }
    },
    template: `
        <div>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('contract_setting')}}</li>
                </ol>
                <h1>{{$t('contract_setting')}}</h1>
            </iac-section>
            <iac-section>
                <ui-list :dataSource='dataSource'>
                    <div slot='items' slot-scope='props'>
                        <transition-group name="list" tag="div" :css='false'>
                            <div :key='item.key' v-for='(item,position) in props.items'  
                            class='data-view-item' style='display: flex'>
                                <div :class='"index " + item.status'>{{position+1}}</div>
                                <div style='border-bottom: 1px solid #999; margin-bottom: 15px; flex: 1 1 auto'>
                                <component v-if='dataSource.template' 
                                            :is='dataSource.template' 
                                            :model='item' />
                                </div>
                            </div>
                        </transition-group>
                    </div>
                    <template slot='not-found'>
                        <ui-alert type='warning' style='margin-left: 60px; margin-right: -10px'>Нет данных для редактирования</ui-alert>
                    </template>
                </ui-list>

                <div style='margin-left: 70px; display: flex'>
                <ui-btn-group style='flex: 1 1 auto;'>
                    <ui-btn type='primary' v-on:click.native='add_item'>{{$t("Добавить")}}</ui-btn>   
                    <ui-btn type='secondary' v-on:click.native='clear'>{{$t("Очистить")}}</ui-btn> 
                </ui-btn-group>
                <ui-btn type='info' v-on:click.native='source' >{{$t("Исходник")}}</ui-btn>
                </div>
            </iac-section>
        </div>
    `
}