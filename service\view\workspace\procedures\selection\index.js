import { DataSource, RemoteStore, RefStore, Query } from '@iac/data'
import { Language } from '@iac/core'
import { Settings } from '@iac/kernel'

var multilot_query = new Query({
  multilot: {
    type: "entity",
    label: "!multipos_procedures",
    group: "multipos_procedures",
    has_del: true,
    dataSource: [{ id: true, name: "yes" }, { id: false, name: "no" }]
  },
  is_new_multilot: {
    type: "entity",
    label: "!multilot_procedures",
    group: "multilot_procedures",
    has_del: true,
    hidden: ()=>!Settings.procedures?._multilot,
    dataSource: [{ id: true, name: "yes" }, { id: false, name: "no" }]
  }
})

let ktru_product = new Query({
    product_id: {
      type: "entity",
      group: "choose_product",
      label: "!choose_product",
      has_del: true,
      dataSource: new DataSource({
        valueExp: "product_id",
        displayExp: "product_name",
        search: true,
        store: new RefStore({
          ref: "ref_enkt_products",
          key: "product_id",
        }),
      }),
      multiple: true,
      hidden: () => {
        return !Settings.procedures?._filter_product
      }
    },
  });
  
var query = new Query({
    green: {
        type: "entity",
        label: "!green_procedures",
        group: "green_procedures",
        has_del: true,
        dataSource: [{id: true,name: "yes"},{id: false,name: "no"}],
        hidden: ()=>!Settings?.procedures?._green
    }
})

export default {
    data: function(){
        return {
            selection_own: new DataSource({
                query: new Query({
                    status: {
                        type: 'enum',
                        dataSource: "ref_selection_status_private",
                        value: [],
                    },
                    relation:{
                        value: "owner",
                        type: "hidden",
                        sync: false,
                    },
                },[multilot_query, ktru_product,query]),
                store: new RefStore({
                    ref: "ref_selection_private",
                    injectQuery: (params) => {
                        params.fields = ["green","id","publicated_at","status","name","good_count","close_at","totalcost","currency", "lang","part_count","meta","remain_time","lot_count"]
                        return params;
                    },
                }),
                template: 'template-selection'
            }),
            selection_party: new DataSource({
                query: new Query({
                    status: {
                        type: 'enum',
                        dataSource: "ref_selection_status_participant",
                        value: [],
                    },
                    relation:{
                        value: "participant",
                        type: "hidden",
                        sync: false,
                    },
                    company_id: {
                        type: "entity",
                        label: "!company",
                        group: "company",
                        has_del: true,
                        dataSource: {
                          search: true,
                          displayExp: "title",
                          store: {
                            method: "company_ref",
                            ref: "companies",
                          }
                        },
                      }
                },[multilot_query, ktru_product,query]),
                store: new RefStore({
                    ref: "ref_selection_private",
                    injectQuery: (params) => {
                        params.fields = ["green","id","publicated_at","status","name","good_count","close_at","totalcost","currency", "lang","part_count","meta","remain_time","lot_count"]
                        return params;
                    },
                }),
                template: 'template-selection'
            })
        }
    },
    template: `
        <iac-access :access='$policy.contest_list_own || $policy.contest_list_participant || $policy.contest_create || $policy.contest_offer_create | $policy.procedures_selections_view'>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('selection')}}</li>
                </ol>
                <div class='title'>
                    <h1>{{$t('selection')}}</h1>
                </div>
            </iac-section>
            <iac-section>
                <ui-layout-tab :clear='true'>

                    <ui-layout-group v-if='$policy.contest_list_own || $policy.procedures_selections_view' key='Organize' label='Organize'>
                        <ui-data-view :dataSource='selection_own' />
                    </ui-layout-group>

                    <ui-layout-group v-if='$policy.contest_list_participant' key='Participate' label='Participate'>
                        <ui-data-view :dataSource='selection_party' />
                    </ui-layout-group>

                </ui-layout-tab>
            </iac-section>
        </iac-access>
    `
}
