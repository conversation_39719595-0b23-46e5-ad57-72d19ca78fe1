
import { DataSource, RefStore, Query } from '@iac/data';
import { Http, Language } from "@iac/core"

export default [
    {
        path: "/cargo_procedure",
        component: {
            data() {
                let transportation_price_group_name = `${Language.t('transportation_price')}, UZS`;
                let distance_group_name = `${Language.t('distance')}, km`;
                return {
                    source: new DataSource({

                        query: {
                            status: {
                                group: 'status',
                                label: '!',
                                type: 'enum',
                                dataSource: 'ref_cargo_procedure_status_public',

                            },

                            min_price: {
                                group: transportation_price_group_name + '/<price>',
                                label: '! ',
                                type: 'float',
                                prefix: this.$t("from"),
                                min: 0,
                                bind: {
                                    status: `price_error && {"type":"error"}`
                                },


                            },
                            max_price: {
                                group: transportation_price_group_name + '/<price>',
                                label: '! ',
                                type: 'float',
                                prefix: this.$t("to"),
                                min: 0,
                                bind: {
                                    status: `price_error && {"type":"error"}`
                                },
                            },

                            price_error: {

                                sync: false,
                                group: transportation_price_group_name,
                                type: "model",
                                label: "!",
                                bind: {
                                    value: "min_price > max_price",
                                    status: `price_error && {"type":"error","message":"${this.$t('to_from_error')}"}`
                                },
                            },

                            min_distance: {
                                group: distance_group_name + '/<distance>',
                                label: '! ',
                                type: 'float',
                                prefix: this.$t("from"),
                                min: 0,
                                bind: {
                                    status: `distance_error && {"type":"error"}`
                                },


                            },

                            max_distance: {
                                group: distance_group_name + '/<distance>',
                                label: '! ',
                                type: 'float',
                                prefix: this.$t("to"),
                                min: 0,
                                bind: {
                                    status: `distance_error && {"type":"error"}`
                                },


                            },

                            distance_error: {
                                sync: false,
                                group: distance_group_name,
                                type: "model",
                                label: "!",
                                bind: {
                                    value: "min_distance > max_distance",
                                    status: `distance_error && {"type":"error","message":"${this.$t('to_from_error')}"}`
                                },
                            },


                            shipper_region: {
                                group: 'region_of_shipment',

                                label: '!region_of_shipment',
                                has_delete: true,
                                type: 'entity',
                                dataSource: 'ref_area_lv4_select',
                                multiple: 'true',
                            },

                            recipient_stock: {
                                group: 'region_of_delivery',

                                label: '!region_of_delivery',
                                has_delete: true,
                                type: 'entity',
                                dataSource: 'ref_area_lv4_select',
                                multiple: 'true',
                            },

                            cargo_type_of_transport: {
                                group: 'transport_type',
                                multiple: true,

                                label: '!transport_type',
                                has_delete: true,
                                type: 'entity',
                                dataSource: {
                                    store: {
                                        ref: 'ref_cargo_type_of_transport',
                                    }
                                },

                            },



                        },
                        store: {
                            ref: 'ref_cargo_procedure_public',
                            injectQuery: (params) => {
                                params.filters = params.filters || {};
                                params.filters.distance_error = undefined;
                                params.filters.price_error = undefined;
                                return params;
                            }
                        },
                    }),
                }
            },
            template: `
                <div>
                    <iac-section type='header'>
                        <ol class='breadcrumb'>
                            <li><router-link to='/'>{{$t('home')}}</router-link></li>
                            <li>{{$t("cargo_procedures")}}</li>
                        </ol>
                        <div class='title'>
                            <h1>{{$t("cargo_procedures")}}</h1>

                        </div>
                    </iac-section>
                    <iac-section>
                    <ui-data-view  :dataSource='source'/>
                    
                    </iac-section>
                </div>           
            `
        }
    }
]