const Component = {
    props: ['model'],
    computed: {

    },
    methods: {
        openContract(){

        }
    },
    template: `
        <ui-data-view-item :model='model'>
        <template slot='header'>
            <iac-date :date="model.created_at"  withoutTime withMonthName/>
            <div>{{ model.status}}</div>
        </template>
        <template slot='title'>
            {{model.id}}
        </template>
         <template slot='sub_title'>
                <a @click.prevent='openContract' href='#' >{{model.count}} {{ $t('contracts',{count: model.count}) }}</a>
        </template> 
        <template slot='description'>
            <div>
                <label>{{ $t('contract.organizer')}}:</label>
                <span>{{ model.company_id }}</span>
            </div>
        </template>
        </ui-data-view-item>
    `
}

Vue.component('template-reassign', Component);