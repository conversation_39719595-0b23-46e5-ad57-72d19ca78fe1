import Vue from 'vue'
import { Entity } from '@iac/data'
import { Http } from '@iac/core'
import { Develop } from '@iac/kernel'

export default class Invite extends Entity {
    constructor(context = {}) {
        super(context)
        this.email = context.email;
        this.name = context.name;
        this.created_at = context.created_at;
    }

    props() {
        return {
            email: {
                required: true,
                type: 'email'
            },
            name: {
                required: true,
            },
            created_at: {
                hidden: true
            }
        }
    }

    async invite_user_to_company() {
        const { error, data } = await Http.api.rpc("invite_face", {
            email: this.email,
            name: this.name
        })

        if (error) {
            if (!this.setError(error)) {
                await Vue.Dialog.MessageBox.Error(error);
            }
        } else {
            Vue.Dialog.MessageBox.Success(data.message);
        }

        return { error, data }
    }

    async invite_user_again() {
        const { error, data } = await Http.api.rpc( "invite_face", {
            email: this.email,
            name: this.name,
        })

        if (error) {
            await Vue.Dialog.MessageBox.Error(error);
        } else {
            Vue.Dialog.MessageBox.Success(data.message);
        }

        return { error, data }
    }

    async delete_invite() {
        const { error, data } = await Http.api.rpc("delete_company_invites", {
            email: this.email
        })

        if (error) {
            await Vue.Dialog.MessageBox.Error(error);
        } else {
            Vue.Dialog.MessageBox.Success(data.message);
        }

        return { error, data }
    }
}