import Model from './model'
import AddExtraDlg from './_dlg_add_extra';
import { Http, Language } from '@iac/core';
import { DataSource } from '@iac/data';
const linkBase = '/workspace/contract/';
import { Develop } from '@iac/kernel';

export default {
  data() {
    return {
      model: undefined,
      error: undefined,
      contract: undefined,
      extraContracts: [],
      moreContracts: undefined,
      moreDropdown: false,
    };
  },
  mounted: async function () {
    await this.update(this.$route.params.id);
  },
  async beforeRouteUpdate(to, from, next) {
    await this.update(to.params.id);
    next();
  },
  computed: {
    linkBase() {
      return linkBase;
    },
    coreLink() {
      return `${linkBase}${this.$route.params.id}/core`;
    },
    executionLink() {
      return `${linkBase}${this.$route.params.id}/execution`;
    },
    logLink() {
      return `${linkBase}${this.$route.params.id}/log`;
    },
    contractsListLink() {
      let link = `${linkBase}`;
      if (this.model.proc_type === 'request') {
        link += '?tabid_tab=1';
      }
      return link;
    },
    initiator() {
      const { org_company, initiator } = this.model;
      return initiator.company_details.title;
    },
  },
  methods: {
    async show_participants() {
      let { error, data } = await this.model.action("__show_participants", {});
      if (error && error.code != 'AbortError') {
        Vue.Dialog.MessageBox.Error(error);
      }
    },
    async chat() {
      await Vue.Dialog.Chat.Modal({
        size: 'full',
        object: {
          type: 'contract',
          id: this.model.number.split('.').slice(0, -1).join('.'),
          number: this.model.number,
          hideGroups: true,
        }
      })
    },
    async addExtra() {
      const { model } = this;
      const number = await AddExtraDlg.Modal({
        model,
        title: this.$t('contract.add_extra_title'),
        text: this.$t('contract.add_extra_text'),
        fields: [{
          name: 'version_number',
          type: 'input',
          label: 'contract.extra_number',
          status: undefined,
          value: undefined,
        }],
        button: this.$t('create'),
        method: 'create_new_version',
      });
      if (number) {
        this.$router.push(`${linkBase}${number}/core`);
      }
    },
    create_new_version(part){
      if(this.model){
        this.$wait(async () => {
          const number = await this.model.create_new_version(part)

          if (number) {
            this.$router.push(`${linkBase}${number}/core`);
          }
        });
      }

    },
    reassign_trader(){
      if(this.model){
        this.$wait(async () => {
          const number = await this.model.reassign_trader()

          if (number) {
            this.$router.push(`${linkBase}${number}/core`);
          }
        });
      }

    },
    async update(id) {
      await this.$wait(async () => {
        let { error, data } = await Model.get(id);
        this.model = data;
        this.error = error;
        const res = await Http.api.rpc('contract_ref', {
          'ref': 'related_contracts',
          'op': 'read',
          'filters': {
            'number': id,
          },
        });
        if (res.data && res.data.length) {
          if (res.data.length > 5) {
            this.extraContracts = res.data.slice(0, 4);
            this.moreContracts = res.data.slice(4, -1);
          } else {
            this.extraContracts = res.data.slice(0, -1);
          }
          this.contract = res.data[res.data.length - 1];
        }
        this.moreDropdown = false;
      });
    },
    toggleMore() {
      this.moreDropdown = !this.moreDropdown;
    },
    update_data() {
      this.$wait(async () => {
        await this.model.update_contract_detailed([
          // "other_requests"
        ]);
      })
    },
    async transfer_contract() {

      let send = await Vue.Dialog({
        props: ["model", "time_count"],
        data() {
          return {
            timeoutID: undefined,
            timer: undefined,
            fields: [
              {
                name: "__marker",
                type: "hidden",
                value: true
              },
              {
                name: "user",
                label: "member",
                type: "entity",
                required: true,
                status: undefined,
                //description: 'После передачи прав, вы потеряете все что у вас есть, жену, детей, собаку',
                dataSource: new DataSource({
                  store: ({
                    method: "get_company_users",
                    context: (context) => {
                      if (Develop.content_debug)
                        context.desc = `id: ${context.id}`
                      return context;
                    }
                  })
                }),
                value: undefined
              }
            ]
          }
        },
        mounted() {

        },
        destroyed: function () {
          if (this.timeoutID) {
            clearTimeout(this.timeoutID);
          }
        },
        computed: {
          btn_active() {
            let value = this.fields[1].value;
            return value && value.id
          }
        },
        methods: {
          time() {
            this.timeoutID = setTimeout(() => {
              this.timer--

              if (this.timer > 0)
                this.time();
              else {
                this.$wait(async () => {
                  let value = this.fields[1].value;
                  if (value && value.id) {

                    let { error, data } = await this.model.action("transfer_contract", {
                      __marker: true,
                      user: value.id
                    })
                    if (!error) {
                      this.Close(true);
                    } else {
                      Vue.Dialog.MessageBox.Error(error);
                    }
                  }
                })
                //this.Close(true);
              }
            }, 1000)
          },
          async send() {
            let value = this.fields[1].value;
            if (!value || !value.id) {
              this.fields[1].status = {
                type: "error",
                message: Language.t('required_field')
              }
            } else {
              //this.timer = this.time_count;
              //this.time();

              if (await Vue.Dialog.MessageBox.Question(`${Language.t('question_transfer_the_authority').replace('____',value.name)} ?`, Language.t('transfer_the_authority')) == Vue.Dialog.MessageBox.Result.Yes) {
                this.$wait(async () => {


                  let { error, data } = await this.model.action("transfer_contract", {
                    __marker: true,
                    user: value.id
                  })
                  if (!error) {
                    this.Close(true);
                  } else {
                    Vue.Dialog.MessageBox.Error(error);
                  }

                })
              }

              /*
              this.$wait(async () => {

                
                let { error, data } = await this.model.action("transfer_contract", {
                  __marker: true,
                  user: value.id
                })
                if (!error) {
                  this.Close(true);
                } else {
                  Vue.Dialog.MessageBox.Error(error);
                }

              })*/
            }
          }
        },
        template: `
          <div>
          <div  class='iac-dialog-header'>
          {{$t('transfer_the_authority')}}
        </div>
            <main>
              <ui-layout :fields='fields' />
            </main>
            <main v-if='this.timer' class='success'>
              Передача состоится через: {{timer}} сек.
            </main>
            <footer>
              <ui-btn type='secondary' v-if='!this.timer' v-on:click.native='Close()'>{{$t('Cancel')}}</ui-btn>
              <ui-btn type='primary' :disabled='!btn_active' v-if='!this.timer' v-on:click.native='send()'>{{$t('to_transfer_the_authority')}}</ui-btn>
              <ui-btn type='secondary' v-if='this.timer' v-on:click.native='Close()'>{{$t('cancel_transfer_the_authority')}}</ui-btn>
            </footer>
          </div>
        `
      }).Modal({
        model: this.model,
        time_count: 20
      })

      if (send) {
        this.update_data()
      }
    }
  },
  template: `
    <div v-if='model' class='page-contract'>
      <iac-section type='header'>
        <ol class='breadcrumb'>
          <li><router-link to='/'>{{ $t('home') }}</router-link></li>
          <li><router-link :to='contractsListLink'>{{ $t('nav.contracts') }}</router-link></li>
          <li>{{ $t('contract.page_title') }}</li>
        </ol>
        <div class='title'>
          <div style='margin: 25px 0;'>
            <h1>{{ $t('contract.page_title') }}</h1>
            <div class='contract-info'>
              <div v-if='model.number'>№ {{ model.number }}</div>
              <div v-if='model.contract_close_at || model.inserted_at'>
                <iac-date :date='model.contract_close_at || model.inserted_at' withoutTime />
              </div>
              <div v-if='model.initiator && model.initiator.company_details'>{{ model.initiator.company_details.title }}</div>
              <div v-if='model.contragent && model.contragent.company_details'>{{ model.contragent.company_details.title }}</div>
            </div>
          </div>

          <ui-btn-group>
            
            <ui-btn v-if='model.rights.transfer_contract' @click.native='transfer_contract' type='primary'>{{ $t('to_transfer_the_authority') }} </ui-btn>
            
            <ui-btn v-if='model.rights.__show_participants' class='page-contract__chat-btn' type='info' @click.native='show_participants'>{{$t('contract.show_participants')}}</ui-btn>
            <ui-btn type='success' @click.native='update_data'>{{$t('balance.refresh')}}</ui-btn>
            <ui-btn class='page-contract__chat-btn' @click.native='chat' type='info'>{{ $t('chat') }}</ui-btn>

            <ui-btn v-if='model.rights.reassign_trader' @click.native='reassign_trader()' type='primary'> {{ $t('contract.reassign_trader') }} </ui-btn>
            <ui-btn v-if='model.rights.create_new_version' @click.native='addExtra' type='primary'>{{ $t('contract.add_extra') }}</ui-btn>
            <ui-btn v-if='model.rights.create_new_version_org' @click.native='create_new_version("org")' type='primary'>{{ $t('contract.add_extra') }}</ui-btn>
            <ui-btn v-if='model.rights.create_new_version_winner' @click.native='create_new_version("winner")' type='primary'>{{ $t('contract.add_extra') }}</ui-btn>

          </ui-btn-group>

        </div>
        <ul v-if='extraContracts || contract' class='extra-contracts'>
          <li v-if='contract' class='extra-contracts__item'>
            <router-link class='extra-contracts__link' :to='linkBase + contract.number'>{{ $t('contract') }}</router-link>
          </li>
          <li v-for='extraContract in extraContracts' :key='extraContract.number' class='extra-contracts__item'>
            <router-link class='extra-contracts__link' :to='linkBase + extraContract.number'>{{ $t('contract.extra') }}&nbsp;{{ extraContract.version - 1 }}</router-link>
          </li>
          <li v-if='moreContracts' class='dropdown extra-contracts__item' :class='{ "dropdown--opened": moreDropdown }'>
            <a href='#' class='dropdown__link dropdown__link--arrow-static extra-contracts__link' @click.prevent='toggleMore'>{{ $t('contract.more') }}</a>
            <ul class='dropdown-menu dropdown-menu--right' style='z-index:1;    box-shadow: 0 6px 18px 0 rgba(14, 21, 47, 0.13), 0 -2px 6px rgba(14, 21, 47, 0.03);'>
              <li v-for='extraContract in moreContracts'>
                <router-link :to='linkBase + extraContract.number + "/core"'>{{ $t('contract.extra') }}&nbsp;{{ extraContract.version - 1 }}</router-link>
              </li>
            </ul>
          </li>
        </ul>
        <div class='links'>
          <router-link :to='coreLink'>{{ $t('contract.data') }}</router-link>
          <router-link v-if='model.rights.execution_show_tab' :to='executionLink'>{{ $t('contract.execution') }}</router-link>
          <router-link v-if='$policy.system_contract_logs_read || model.rights.show_log' :to='logLink'>{{ $t('logs') }}</router-link>
        </div>
      </iac-section>
      
      <iac-section><router-view :model='model'/></iac-section>

    </div>
    <div v-else>
      <iac-section v-if='error'>
        <ui-error style='margin: 10px' :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
      </iac-section>
    </div>
  `,
};
