import Event from './../../core/src/event'

const Key = {
    Context: Symbol('Context'),
}

export default class IacObject {
    @Event onUpdate;
    @Event onChangeProperty() {
        if (!this.lock)
            this.onUpdate();
    };

    constructor(context = {}, ext) {
        this[Key.Context] = context;
        this._own_properties = undefined;
        this._properties = undefined;
        this._fields = undefined;
        this.ext = ext;
        this.lock = false;
        if (this.ext) {
            this.ext.forEach((ext) => {
                if (!ext)
                    return;
                ext.onUpdate.bind(this.onUpdate)
            })
        }

    }

    get propertyModel() {

    }

    get own_properties() {
        if (!this._own_properties) {
            let _own_properties = {};
            // получаем свойства из метода props
            let proto = this.__proto__;
            let props = {};
            while (proto) {
                if (proto.hasOwnProperty('props')) {
                    let protoProps = proto.props.call(this);
                    for (let prop in protoProps) {
                        protoProps[prop].member = true;
                    }
                    props = { ...protoProps, ...props }
                }
                proto = proto.__proto__;
            }
            // Дополняем или переопределяем из контекста 
            props = { ...props, ...this[Key.Context].props }

            // Добавляем все необходимые метаданные
            for (let prop in props) {
                if (["_fields", "fields", "props", "_properties", "properties", "_own_properties", "own_properties"].indexOf(prop) >= 0)
                    continue
                
                if(props[prop] == null)
                    continue 

                if(props[prop] instanceof this.propertyModel){
                    _own_properties[prop] = props[prop];
                    continue
                    
                }else{
                    
                }

                let attributes = props[prop];
                if (typeof attributes == 'string' || typeof attributes == 'number') {
                    attributes = {
                        value: attributes,
                        hidden: true,
                    }
                }

                if (attributes.value || attributes.value == 0 || this[prop] == undefined || this[prop] == null) {
                    this[prop] = attributes.value;
                }

                let property = new this.propertyModel({
                    name: prop,
                    attributes: attributes,
                    model: this
                });

                _own_properties[prop] = property;
            }
            this._own_properties = _own_properties
        }
        return this._own_properties;
    }

    setProperties(props) {

        for (let prop in this._own_properties) {
            if (this._own_properties[prop].onDestroy)
                this._own_properties[prop].onDestroy();
            this._own_properties[prop] = undefined;
            delete this._own_properties[prop];
        }
        delete this._own_properties;

        this[Key.Context].props = props
        this._own_properties = undefined;
        this._properties = undefined;
        this._fields = undefined;
    }

    addProperty(attributes) {  
        let property = undefined;  
        if(attributes instanceof this.propertyModel){
            property = attributes;        
        }else{
            property = new this.propertyModel({
                name: attributes.name,
                attributes: attributes,
                model: this
            });
        }
        
        this.own_properties[property.name] = property
        this._properties = undefined;
        this._fields = undefined;
        return property;
    }

    get properties() {
        if (!this._properties) {
            this._properties = { ...this.own_properties }
            if (this.ext) {
                this.ext.forEach((model) => {
                    if (!model)
                        return;
                    for (let prop in model.properties) {
                        let property = model.properties[prop];
                        this._properties[prop] = property;
                    }
                })
            }
        }
        return this._properties;
    }

    get fields() {
        if (!this._fields) {
            // Для Vue переводим в массив
            this._fields = Object.keys(this.properties).map((key) => {
                return this.properties[key];
            })
        }
        return this._fields;
    }

    Validate() {

        return true;
    }

    setError(error = {}, prefix, clear = false) {
        if (clear)
            for (let prop in this.properties) {
                this.properties[prop].status = undefined;
            }
        if (!error.data)
            return false;

        let error_struct = error.data.reduce((result, error, index)=>{
            let name = error.name || error.field_id || '';
            let [field, prop] = name.split(/\.(.*)/gm,2)
            result[field] = result[field] || {
                message: prop ? "некоторые поля содержат ошибки" :  error.message,
                data: error.data
            }
            if(prop){
                result[field].data = result[field].data || [];
                result[field].data.push({
                    name: prop,
                    message: error.message
                })
            }
            return result;
        },{})
        let result = false;
        for(let name in error_struct){
            let property = this.properties[name];
            let property_error = error_struct[name];
            if (property) {
                if(property.type == 'model'){
                    if(property.setError(property_error,'',clear)){
                        result = true; 
                    }
                }else{
                    result = true;
                    property.status = {
                        type: 'error',
                        message: property_error.message,
                        data: property_error.data
                    }
                }
            }
        }
        return result
    }
}