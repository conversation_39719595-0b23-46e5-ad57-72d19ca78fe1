export default `
## Action
> Декоратор который позволяет регистрировать статический метод с последующим вызовом этого метода с любой точки программного продукта (включая вызов по требованию бэкэнда)



Пример объявления:

    import {Action} from '@iac/core'

    class Object {

        @Action("contract.create")
        static async create(params = {}) {
            return await Http.api.rpc("contract_create", params)
        }
    }


Вызов метода:

    import {Action} from '@iac/core'

    let response = await Action["contract.create"](procedure_params)

`