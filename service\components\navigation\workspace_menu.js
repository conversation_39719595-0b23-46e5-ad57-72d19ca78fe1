import { Language } from '@iac/core'
import Socket from './socket'
import { Context } from '@iac/kernel'

export default {
    props: ['items'],
    data: function () {
        return {
            opened: {},
            keys: {
                "nav.agreements_procedures": "count_votes_user",
                "nav.agreements": "count_votes_user",
                "nav.notification": "unread_notifications"
            }
        }
    },
    computed: {
        menu() {
            let root = {};

            this.items.forEach((item, key) => {
                let current_group = root;

                let groups = item.group && item.group.split("/");
                if (groups && groups.length > 0) {
                    for (let key of groups) {
                        key = key.replace("\\", '/');
                        current_group = current_group.child = current_group.child || {};
                        current_group = current_group[key] = current_group[key] || {
                            label: key,
                        };
                        //current_group.opened = true;
                        //this.$set(current_group, 'opened', false);
                    }

                }
                current_group = current_group.child = current_group.child || {};
                current_group[`Item:${key}`] = item

                if ((this.$route.path || "").indexOf(item.path) == 0) {
                    let group = item.group.split('/')[0]
                    group = group.replace('\\', '/');

                    if (typeof this.opened[group] == "undefined")
                        this.$set(this.opened, group, true);
                }
            });

            return root;
        }
    },
    render: function (c) {
        
        let renderItem = (item) => {
            let title = JSON.stringify({
                item
            }, null, '\t');

            return c(item.label ? 'li' : 'nav', {
                class: {
                    'left-nav': !item.label,
                    'left-nav__item': item.label,
                    'dropdown': item.label && item.child,
                    'dropdown--opened': this.opened[item.label],
                },
            }, [
                (() => {
                    if (!item.label)
                        return;
                    if (item.component) {
                        return c('div', {
                            is: item.component
                        })
                    } else if (!item.path)
                        return c('label', {
                            class: {
                                'left-nav__link': true,
                                'dropdown__link': item.child,
                            },
                            style: {
                                display: "flex",
                                justifyContent: "space-between",
                            },
                            on: {
                                click: () => {
                                    this.$set(this.opened, item.label, !this.opened[item.label]);
                                },
                            },
                        }, this.keys[item.label] ? [
                            Language.t(item.label),
                            c(Socket, {
                                style: {
                                    paddingRight: "25px",
                                },
                                props: {
                                    key: this.keys[item.label]
                                }
                            })
                        ] : Language.t(item.label))
                    return c('router-link', {
                        class: {
                            'left-nav__link': true,
                            disabled: item.disabled
                        },
                        domProps: {
                            title: this.access.debug ? title : ''
                        },
                        props: {
                            to: item.path
                        }
                    },
                        this.keys[item.label] ?
                            [
                                Language.t(item.label),
                                c(Socket, {
                                    props: {
                                        key: this.keys[item.label]
                                    }
                                })
                            ] : [c('div', {
                                class: { 'left-nav__link-content': true, }
                            }, item.label[0] != '!' ? Language.t(item.label) : item.label.substring(1))]
                    )
                })(),
                item.child && c("ul", {
                    class: {
                        'left-nav__list': true,
                        'dropdown-menu': item.label && item.child
                    },
                }, Object.keys(item.child).map((key) => {
                    return renderItem(item.child[key]);
                }))
            ])

        }

        return renderItem(this.menu);
    }
}