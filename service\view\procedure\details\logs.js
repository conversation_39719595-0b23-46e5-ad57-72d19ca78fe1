import { DataSource, RemoteStore, Query } from '@iac/data';

export default {
    props: ["model"],
    data() {
      return {
        logs: new DataSource({
          store: new RemoteStore({
            method: 'get_logs'
          }),
          query: new Query({
            object_type: this.model.procedure,
            object_id: this.model.id,
            order: 'desc'
          })
        }),
        logColumns: [
          {field: 'date', style: 'text-align: left;'},
          {field: 'user_name', label: 'name', style: 'white-space: nowrap;'},
          {field: 'company', label: 'company', style: 'white-space: nowrap; max-width: 150px; overflow: hidden;'},          
          {field: 'message', label: 'description', style: 'width: 100%;'}
        ]
      }
    },
    methods: {

    },
    template: `
        <ui-layout-group>
          <ui-data-grid raw :dataSource='logs' :columns='logColumns'>
            <template slot='date' slot-scope='props'><div  style='text-align: left;'><iac-date :date='props.item.date' full/></div></template>  
            <template slot='user_name' slot-scope='props'>
              <div>{{props.item.user_name}}</div>
              <div style='font-size: 12px; color: #666;'>{{props.item.ip}}</div>
            </template>
            <template slot='company' slot-scope='props'>
              <div style="text-overflow: ellipsis; overflow: hidden;" :title="props.item.company_name">{{props.item.company_name}}</div>
              <div style='font-size: 12px; color: #666;'>{{props.item.company_inn}}</div>
            </template>
          </ui-data-grid>
        </ui-layout-group>
    `
}
