export default {
  props: ['model'],
  template: `
  <div class='grid' v-if='model.isugf && model.isugf.invoices.length'>
    <div class='row'>
      <h2>{{ $t('contract.isugf_invoices') }}</h2>
    </div>
    <div class='row' v-for='(invoice, index) in model.isugf.invoices'>
      <div class='col-md-4'>{{$t('contract.isugf_invoice')}} #{{ invoice.NUMBINVOICE }}</div>  
      <div class='col-md-4'>
        <div class='row' v-if='invoice.DATEINVOICE'>
          <label class='col-sm-6'>{{ $t('contract.isugf_invoice_date') }}:</label>
          <iac-date :date="invoice.DATEINVOICE"  withoutTime withMonthName/>
        </div>
        <div class='row' v-if='invoice.DATEDOC'>
          <label class='col-sm-6'>{{ $t('contract.isugf_invoice_datedoc') }}:</label>
          <iac-date :date="invoice.DATEDOC"  withoutTime withMonthName/>
        </div>
        <div class='row'>
          <label class='col-sm-6'>{{ $t('contract.isugf_invoice_sumpay') }}:</label>
          <div class='col-sm-6'><iac-number :value='invoice.SUMPAY' delimiter=' ' part='2' /> {{model.base_currency}}</div>
        </div>
        <div class='row'>
          <label class='col-sm-6'>{{ $t('contract.isugf_invoice_state') }}:</label>
          <div class='col-sm-6'>{{$t('contract.isugf_invoice_state_'+invoice.STATE)}}</div>
        </div>
      </div>
    </div>
    <div class='row' v-if="model.isugf.invoices_price">
      <div class='col-md-4'></div>
      <div class='col-md-4'>
        <div class='row'>
          <div class='col-sm-6'>{{ $t('contract.isugf_invoices_price') }}:</div>
          <div class='col-sm-6'>
            <div class='total'><iac-number :value='model.isugf.invoices_price' delimiter=' ' part='2' /> {{model.base_currency}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>    
  `,
};
