import {Context} from '@iac/kernel'

let TimerComponent = {
    props: ["model"],
    data: function () {
        return {
            tid: undefined,
            sysStat: Context.SysStat,
            serverDate: undefined,
            localDate: undefined,

            format_time: new Intl.DateTimeFormat("ru-RU", {
                hour: "2-digit",
                minute: "2-digit",
                second: "2-digit",

                //timeZone: "asia/tashkent",
            }),

            format_millisecond: new Intl.DateTimeFormat("ru-RU", {
                //timeZone: "asia/tashkent",
                timeZoneName: "short",
                fractionalSecondDigits: 3,
            })

        }
    },
    mounted(){
        this.tid = setInterval(()=>{
            this.localDate = Date.now()
            this.serverDate = this.localDate - this.sysStat.delta;
            
        },100)
    },
    beforeDestroy() {
        if(this.tid){
            clearInterval(this.tid);
        }
    },
    computed:{

    },
    template: `
        <div class='iac-exchange-timer-tile thin-scroll'>
            <div v-if='serverDate'>
                <div class='time danger'>
                    <span>{{format_time.format(serverDate)}}</span>
                    <span>{{format_millisecond.format(serverDate)}}</span>
                </div>
                <div class='desc'>{{$t('tile.timer.server')}}</div>
            </div>

            <div v-if='localDate'>
                <div class='time'>
                    <span>{{format_time.format(localDate)}}</span>
                    <span>{{format_millisecond.format(localDate)}}</span>
                </div>
                <div class='desc'>{{$t('tile.timer.local')}}</div>
            </div>

            <div style='display: flex; flex: 1 1 100%; justify-content: space-evenly;'>
                <div>
                    <div class='delta'><span style='font-size: 20px'>{{sysStat.delta/1000}}</span> <span>сек.</span></div>
                    <div class='desc'>{{$t('tile.timer.diff')}}</div>
                </div>   

                <div>
                    <div class='ping'><span style='font-size: 20px'>{{sysStat.ping/1000}}</span> <span>сек.</span></div>
                    <div class='desc'>{{$t('tile.timer.ping')}}</div>
                </div> 
            </div>


            <span :title='$t("tile.timer.last")' class='date'>{{format_time.format(sysStat.last_sync)}}</span>
        </div>

    `
}

Vue.Tiling("timer", {
    component: TimerComponent,
    select_contract: false,
    select_group: false
})