export var Text = {
    name: "ui-text",
    props: {
        icon: String,
        label: String,
        status: String,
        value: String,
        readonly: <PERSON><PERSON><PERSON>,
        disabled: <PERSON><PERSON><PERSON>,
        name: String,
        type: String,
        wait: <PERSON><PERSON><PERSON>,
        actions: Array,
        //has_del: <PERSON><PERSON><PERSON>,
        required: <PERSON><PERSON><PERSON>,
        //min: {
        //    default: -100000000000000
        //},
        //max: {
        //    default: 100000000000000
        //},
        //prefix: String,
        //suffix: String,
        trim: {
            default: true
        },
    },
    data: function(){
        return {
            shake: false
        }
    },
    computed: {
        classes() {
            return [
                {
                    "apply-shake": this.shake,
                    "input-error": this.shake
                }
            ]
        },
        opened() {
            if (this.value || this.value === 0) {
                return true
            }
            return false;
        },
        inputListeners: function () {
            var vm = this
            return Object.assign({},
                this.$listeners,
                {
                    input: function (event) {
                        let value = event.target.value;

                        if(vm.trim && value && (value.charAt(0) == ' ' || value.charAt(0) == '\n' || value.charAt(0) == '\t')) {
                            value = value.trim();
                            vm.shake = true;
                            setTimeout(()=>{vm.shake = false},500)
                            vm.$refs.input.value = value
                        }

                        if (value == "")
                            value = undefined;
                        vm.value = value
                        vm.$emit('input', value)
                    },
                    change: function (event) {
                        let value = event.target.value;
                        if (value == "")
                            value = undefined;
                        vm.value = value
                        vm.$emit('change', value)
                    }
                }
            )
        }
    },
    methods: {
        action(event) {
            if (event == 'clear') {
                this.value = undefined;
                this.$emit('input', undefined)
                this.$emit('change', undefined)
            }
        }
    },
    template: `<ui-control class='ui-text' v-bind:class="classes" 
    :icon='icon' :label='label' :opened='opened' :status='status' v-on:action='action' :wait='wait' :readonly='readonly' :disabled='disabled' :actions='actions' :required='required'>
        
        <ui-markdown-view v-if='value && (readonly || disabled)' :content='value'/>
        <textarea  ref='input' v-else class='control thin-scroll' :name="name" :type="type"
            v-bind="$attrs"
            v-bind:value="value"
            v-on="inputListeners"
            :disabled="disabled"
            :readonly="readonly"
        ></textarea>
        

    </ui-control>`
}
