.ui-entity{

    background: linear-gradient(180deg, #fff 70%, #f6f6f6);
    &.readonly,
    &.disabled,
    &.dropdown{
        background: #fff
    }

    &.disabled >.container >.control,
    &.readonly >.container >.control{
        cursor: unset;
    }
    
    >.container >.control {
        cursor: pointer;
        >div{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
    >.dropdown{
        >.entity-dropdown{
            display: flex;
            flex-direction: column;
            >.search {
                flex: 0 0 auto;
                padding: 10px;
                border-bottom: 1px solid #ccc;
                //margin: -1px;

                >input{
                    padding: 10px;
                    border-radius: 5px;
                    width: 100%;
                    border: 1px solid @brand-primary;
                    outline: none;
                }
            }
            >.drop {
                flex: 1 1 auto;
            }
        }        
    } 
    

    &.dropdown-top{
        >.dropdown .entity-dropdown{
            flex-direction: column-reverse;
            >.search {
                border-top: 1px solid #ccc;
                border-bottom: unset;
            }
        }
    }
}