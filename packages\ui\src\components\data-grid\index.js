import Util from "../../../../core/src/util";

export var DataGrid = {
    name: "ui-data-grid",
    props: {
        dataSource: Object,
        tile: <PERSON>ole<PERSON>,
        check: {
            type: Boolean,
            default: false
        },
        header:{
            type: Boolean,
            default: true
        },
        search: String,
        columns: Array,
        filters: {
            type: Boolean,
            default: true           
        },
        sync: {
            type: Boolean,
            default: true           
        },
        buttons: {
            type: Boolean,
            default: false 
        },
        action_name: {
            type: String,
            default: undefined
        },
        action_style: {
            type: String,
            default: "white-space: nowrap;"
        },
        summary: {
            type: Boolean,
            default: false 
        },
        readonly: {
            type: <PERSON>olean,
            default: false            
        },
        not_found: {
            type: String,
            default: ""
        },
        scroller:{
            type: Boolean,
            default: true            
        },
        _class: {
            type: String,
            default: ""
        }
    },
    data: function () {
        return {

        }
    },
    computed: {
        classes(){
            return [
                "ui-data-grid",
                this._class
            ]
        },
        list_classes() {
            return [
                "data-list-list",
                {
                    "tile": this.tile
                }
            ]
        },
        query() {
            return this.dataSource.query;
        },
        fields() {
            return this.query && this.query.fields
        },
        cols(){
            return this.columns.filter((column)=>{
                return column
            }).map((column)=>{
                if(typeof column == "string"){
                    column = {
                        field: column,
                    }
                }
                if(column.summary && typeof column.summary != 'function'){
                    let value = column.summary;
                    column.summary = (items = [])=>{
                        if(value == 'SUM'){
                            let summa = items.reduce((summa, item) => {
                                summa += Number(item[column.field])
                                return summa;
                            },0);

                            return column.display(summa,items[0]);
                        }
                        return value
                    }
                }
                column.label = column.label || column.field;
                column.display = column.display || function(value, item={}){
                    let slice = (v)=>{
                        return ('00'+v).slice(-2)
                    }
                    let display = undefined;
                    switch(column.type){
                        case "number":
                            display = Util.Number(value,' '); break;
                        case "float":
                            display = Util.Number(value,' ',2); break;
                        case "date":
                            let d = new Date(value);
                            display = `${slice(d.getDate())}.${slice(d.getMonth()+1)}.${d.getFullYear()}\u00A0${slice(d.getHours())}:${slice(d.getMinutes())}`; break;
                        default:
                            display = value;
                    }
                    if(column.suffix){
                        return `${display} ${item[column.suffix] || column.suffix}`
                    }
                    return display;
                };

                return column;
            })
        }
    },
    methods: {
        onItem(item,position){
            this.$emit("item",item)
        }
    },
    template: `
    <ui-data-list :sync='sync' :filters='filters' v-bind:class='classes' :dataSource='dataSource' :search='search' :readonly='readonly'>
        <ui-list :sync='sync' slot='items' v-bind:class='list_classes' :dataSource='dataSource' :check='check' :not_found='not_found'>
            <components :is='scroller ? "ui-scroller" : "div" '  class='table-wrapper' slot='items' slot-scope='props'>
                <table>
                    <thead>
                        <tr v-if='header'>
                            <th v-if='check'></th>
                            <th v-for='col in cols' :style='col.style'>{{$t(col.label)}}</th>
                            <th :style='action_style' v-if='!readonly'>{{$t(action_name)}}</th>
                        </tr>
                        <tr v-else>
                            <td v-for='col in cols' :style='col.style'/>
                            <td  v-if='!readonly' />
                        </tr>                        
                    </thead>
                    <tbody>
                        <tr v-on:click='onItem(item,position)' :key='item.key' v-for='(item,position) in props.items' v-bind:class='item.bindClass'>
                            <td v-if='check'>
                                <input type='checkbox' :id="item.id" :value="item.id" v-model="dataSource.checkedItems" />
                            </td>
                            <td v-for='col in cols' :style='col.style'>
                                <slot :name='col.field' :item='item' >
                                    <template v-if='col.component'>
                                        <component :is='col.component' :item='item'/>
                                    </template>
                                    <template v-else>{{col.display(item[col.field], item)}}</template>
                                </slot>
                            </td>
                            <td style='' v-if='!readonly'>
                                <ui-action :style='action_style' class='control' :buttons='buttons' :actions='item.actions'/>
                            </td>
                        </tr>
                    </tbody>
                    <tfoot v-if='summary && props.items && props.items.length>0'>
                        <tr>
                            <td v-if='check'></td>
                            <td v-for='col in cols' :style='col.style' v-html='col.summary && col.summary(props.items)'/>
                            <td :style='action_style' v-if='!readonly'></td>
                        </tr>
                    </tfoot>
                </table>
            </components >
        </ui-list>
    </ui-data-list>`
}