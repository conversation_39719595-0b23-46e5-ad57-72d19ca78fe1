//import { Http } from '@iac/core'
import Http from "../../../../core/src/http"

var Badge = {
    props: ["badge"],
    computed: {
        classes() {
            return [
                "badge",
                this.model.type && `badge-${this.model.type}`,
                {

                }]
        },
        model() {
            if (this.badge.value != undefined)
                return this.badge
            return {
                value: this.badge
            }
        }
    },
    template: `
        <span v-if='model.value' v-bind:class='classes' :title='model.title'>{{model.value}}</span>
    `
}

var TabAction = {
    props: {
        actions: {
            type: Object
        }
    },
    computed: {
        groups() {
            if (!this.actions || !this.actions.filter)
                return;
            let groups = {};
            this.actions.filter((action) => {
                if (!action)
                    return false;
                if (typeof action.hidden == 'function')
                    return !action.hidden();
                return !action.hidden;
            }).forEach(action => {
                let group = action.group || 'general'
                groups[group] = groups[group] || [];
                groups[group].push(action)
            });

            return groups;
        }
    },
    methods: {
        async handler(item) {
            if(item.question){
                if (await Vue.Dialog.MessageBox.Question(item.question) != Vue.Dialog.MessageBox.Result.Yes) {
                    return;
                }
            }
            this.$wait(async () => {
                if (item.handler) {
                    await item.handler();
                    this.dropdown = false;
                } else if (item.type == 'request') {
                    let host = item.host ? Http[item.host] : Http.default;
                    if (host) {
                        await host.rpc(item.method, item.params);
                    }
                }
            })

        }
    },
    components: {
        Badge
    },
    template: `
            <div class='sticky'>
                <div class='action-group' v-for='group in groups'>
                    <template v-for='(item,index) in group'>
                        <div v-if='item.component'>
                            <component :is='item.component' />
                        </div>
                        <ui-btn v-else  :title='$t(item.label)' :key='index' class='item' :type='item.type || "primary"' @click.native='handler(item)'>
                            <icon v-if='item.icon'>{{item.icon || ''}}</icon>
                            <span>{{$t(item.label)}}</span>
                            <badge v-if='item.badge' :badge='item.badge'/>
                        </ui-btn>
                    </template>
                </div>
            </div>
    `
}

export var LayoutTab = {
    name: "ui-layout-tab",
    props: {
        button: {
            default: false,
        },
        direction: {
            type: String,
            default: "top"
        },
        show_all: {
            type: Boolean,
            default: false
        },
        name: {
            type: String,
            default: 'tab'
        },
        clear: {
            type: Boolean,
            default: false
        },
        sync: {
            type: Boolean,
            default: true
        },
        router: {
            type: Boolean,
            default: true
        },
        actions: {
            type: Object
        },
        toolbar: {
            type: Array,
            default: []
        },
        sticky: {
            type: Boolean,
            default: false
        },
        active: {
            type: Number,
            default: undefined
        },
        title: {
            default: undefined
        },
        prefix: String,
    },
    data: function () {
        return {
            items: [],
            curr: this.active
        }
    },
    watch: {
        active: {
            immediate: true,
            async handler(value) {
                this.setCurrent(value);
            }
        }
    },
    computed: {
        classes() {
            return []
        },
        slot() {
            return [this.$slots.default];
        },
        current() {
            if (this.curr == undefined) {

                let tabs = this.tabs();
                let curr = this.router ? this.$router.currentRoute.query[`tabid_${this.name}`] : undefined

                if (curr == undefined) {

                    if (tabs.length <= 0)
                        return 0;
                    for (let tab in tabs) {
                        if (tabs[tab].componentOptions.propsData.active) {
                            curr = tab;
                            break
                        }
                    }
                }

                curr = curr || 0;
                this.curr = curr < tabs.length ? curr : 0;
                this.onTab(this.curr)

            }
            return this.curr;
        }
    },
    components: {
        tabAction: TabAction,
    },
    methods: {
        tabs() {
            if (!this.$slots.default)
                return [];
            return this.$slots.default.filter(
                component => component.componentOptions
            );
        },
        onTab(index) {
            this.$emit("tab", index);
        },
        setCurrent(index) {
            if (this.curr == index) {

                return;
            }
            this.curr = index;
            let query = { ...this.$router.currentRoute.query } || {}
            if (this.sync && this.router && query[`tabid_${this.name}`] != this.current) {
                query[`tabid_${this.name}`] = this.current;
                if (!this.clear)
                    this.$router.replace({ query: query })
                else {
                    this.$router.replace({ query: { [`tabid_${this.name}`]: this.current } })
                }
            }
            this.onTab(index)
        }
    },
    render: function (createElement) {
        let tabs = this.tabs();

        if (tabs.length <= 0)
            return;

        var items = tabs.map((tab, index) => {
            let { label, status, actions, icon, toolbar, type } = tab.componentOptions.propsData;
            tab.componentOptions.propsData.showLabel = false;

            label = label ? label.split(":") : [];

            let title = label[1] || label[0];
            label = label[0];

            return {
                label: label,
                clickLabel: tab.componentOptions.listeners?.clickLabel && true,
                status: status,
                title: title,
                icon: icon,
                actions: actions,
                toolbar: toolbar,
                type: type

            }
        });

        let $this = this;

        let nav_items = createElement(
            'div', { class: 'tab-nav-items' }, [
                this.$slots.prefix && createElement('div', {class: "prefix", style: "display: inline-block;"}, [this.$slots.prefix]),

            items.map((item, index) => {
                let item_class = this.button ? `ui-btn ui-btn-empty ui-btn-${item.type}` : "item";
                //item_class = "item";
                return createElement('div', {
                    class: `${item_class} ${(index == this.current && items.length > 1) ? 'active' : ''}`,
                    attrs: {
                        "data-index": index
                    },
                    domProps: {
                        title: this.$t(item.title),
                        draggable: this.$listeners.dragstart ? true : false,
                    },
                    on: {
                        dragstart: (e) => { e.index = index; this.$emit("dragstart", e) },
                        dragend: (e) => { e.index = index; this.$emit("dragend", e) },
                        click: () => {

                            if (this.curr == index && item.clickLabel) {
                                tabs[index].componentInstance.$emit("clickLabel", index + "")
                            }

                            this.setCurrent(index);
                        }
                    }
                }, [
                    item.icon && createElement('icon', item.icon),
                    createElement('span', {}, this.$t(item.label)),
                    item.status && createElement('ui-badge', { props: { model: item.status } }),

                    this.$listeners.closeItem ? createElement('span', {
                        class: "close",
                        on: {
                            click: (event) => {
                                event.stopPropagation();
                                event.preventDefault();
                                //this.setCurrent(Math.max(Math.min(this.curr, items.length - 2), 0))
                                this.$emit("closeItem", index);
                            }
                        }
                    }, [createElement('icon', "delete")]) : undefined
                ])
            },
            ),
            this.$listeners.addItem ? createElement('span',
                {
                    class: "item",
                    on: {
                        click: (event) => {
                            this.$emit("addItem");
                            //this.setCurrent(items.length);
                        }
                    }
                }, [createElement('icon', { style: "transform: rotateZ(45deg);font-size: 10px;" }, "delete")]) : undefined,
                this.$slots.suffix && createElement('div', {class: "suffix", style: "display: inline-block;"}, [this.$slots.suffix]),
        ])

        let actions = items[this.current] && items[this.current].actions && createElement('ui-action', {
            class: 'tab-nav-action',
            props: {
                //top: false,
                //right: true,
                icon: items[this.current].icon,
                actions: items[this.current].actions,
            }
        })

        let toolbar_items = [];
        if (items[this.current] && items[this.current].toolbar) {
            toolbar_items = toolbar_items.concat(items[this.current].toolbar)
        }
        if (this.toolbar) {
            toolbar_items = toolbar_items.concat(this.toolbar)
        }

        let toolbar = toolbar_items.map((item) => {
            return createElement('ui-action', {
                class: 'tab-nav-action',
                props: {
                    //top: false,
                    //right: true,
                    handler: item.handler,
                    icon: item.icon,
                    actions: item.actions
                }
            })
        })

        let content_actions = this.actions && createElement('div', {
            class: 'tab-content-action',
        }, [
            createElement('tab-action', {

                props: {
                    actions: this.actions,
                    buttons: true
                }
            })
        ])

        let nav = (this.show_all || tabs.length > 1) ? createElement(
            'div', { class: ['tab-nav', { sticky: this.sticky }] }, [nav_items, createElement('div', { class: 'tab-nav-action-group' }, [...toolbar, actions])]
        ) : undefined;

        let title = createElement('div', {
            class: 'tab-nav-title',
            domProps: {
                draggable: this.$listeners.dragstart ? true : false
            },
            on: {
                dragstart: (e) => { this.$emit("dragstart", e) },
                dragend: (e) => { this.$emit("dragend", e) },
            }
        }, [this.title || items[this.current].label])

        let prefix_title = createElement('div', {
            class: 'prefix',
        },this.$slots.before_title);

        let header = (this.show_all || tabs.length > 1) ? createElement(
            'div', {
            class: ['tab-nav tab-header', { sticky: this.sticky }],
        }, [this.direction == 'top' ? nav_items : [prefix_title, title], createElement('div', { class: 'tab-nav-action-group' }, [
            ...toolbar,
            actions,
            this.$listeners.closeItem && this.direction == 'bottom' ? createElement('icon', {
                class: "close",
                on: {
                    click: (event) => {
                        event.stopPropagation();
                        event.preventDefault();
                        this.$emit("closeItem");
                    }
                }
            }, "delete") : undefined
        ])]
        ) : undefined;

        let footer = (this.direction == 'bottom' && tabs.length > 1) ? createElement(
            'div', { class: ['tab-nav tab-footer', { sticky: this.sticky }] }, [nav_items, createElement('div', { class: 'tab-nav-action-group' })]
        ) : undefined;

        let content = createElement(
            'div', { key: `tab_content_${this.current}`, class: 'tab-content' }, [tabs[this.current], content_actions]
        )

        return createElement(
            'div', { class: 'ui-layout-tab' }, [header, this.$slots.header, content,this.$slots.footer, footer]
        )
    }
}