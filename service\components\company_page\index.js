import Vue from "vue"
import { DataSource } from '@iac/data'
import { Context, ecp } from '@iac/kernel'
import { Http } from '@iac/core'

var Component = {
    props: ["item"],
    watch: {
        $route(to, from) {
          this.is_broker = Boolean(this.$route.query.is_broker);
        }
    },
    data() {
      return {
          is_broker: Boolean(this.$route.query.is_broker),
          dataSourceUsers: new DataSource({
            query: { company_id: parseInt(this.item.id) },
            store: {
                method: "get_company_users",
                context: (context) => {
                    context.roles = context.roles || []
                    context.actions = [
                        {
                            label: "set_roles",
                            hidden: () => !Context.Access.policy['user_change_role'],
                            handler: async () => {
                                let result = await Vue.Dialog.set_user_role.Modal({
                                    id: context.id
                                })
                                if (result)
                                    this.dataSourceUsers.reload();
                            }
                        }
                    ]
                    return context;
                },
            },
            template: 'template-user'
          })
      }
    },
    methods: {
      async brokerRequest() {
        const dialog_result = await Vue.Dialog.MessageBox.Question({
          message: this.$t("broker_request.accept_form")
        });
        if (dialog_result != Vue.Dialog.MessageBox.Result.Yes) return;

        let { error: ecp_error, data: ecp_data } = await ecp.subscribe("Hello");
        if (ecp_error) {
          Vue.Dialog.MessageBox.Error(ecp_error);
          return;
        }

        let { error, data } = await Http.api.rpc("broker_request", {
          company_id: this.item.id,
          pkcs7B64: ecp_data,
        });

        if (error) {
          Vue.Dialog.MessageBox.Error(error);
          return;
        }
        if (data.message) { Vue.Dialog.MessageBox.Success(data.message); }

        this.$emit('update-company', {});
      },
      arrToString(arr) {
        if (arr === null) {
          return '-'
        }
        return arr.join(', ')
      }
    },
    computed: {
      liquidation_date() {
        return this.item.meta?.status_sync_with_gnk?.update_blocked_from_at
      },


      tags() {
        const model = this.item;
        const tags = [
          (model.meta && model.meta.gup) ? this.$t("ГУП") : null,
          (model.meta && model.meta.ie) ? this.$t("ИП") : null,
          ((!model.meta || !model.meta.gup) && model.buyer_type_id == 1) ? this.$t("Гос") : this.$t("Корп")
        ].filter(x => x).map(x => this.$t(x));

        return tags.length == 0 && null || tags;
      },
      visible_list_user() {
        return (this.$policy.user_list && Context.User.team_id == this.item.id) || this.$policy.system_page_edit;
      },
      is_broker_request_btn() {
        let has_policy = !!this.$policy.exchange_client_can_hire_broker

        return this.item.is_broker // Компания на которую нужно отправить запрос является брокерской компанией
          // Вместо того что ниже, должно быть вот так" && (!this.item.broker_request || this.item.broker_request.meta.status == 'cancel')
          // но на беке есть проблема с повторной отправкой заявки
          && (!["accept", "wait"].includes(this.item.broker_request?.meta?.status))
          && this.settings.ecp
          && !this.item.has_broker // компания не имеет брокеров
          && has_policy
      }
    },
    template: `
      <div class='tile card'>
        <div class='card__top'>
          <div class="card__number">
            <iac-entity-edit v-if='$develop.content_debug && item.id' :value='{id: item.id, type: "company"}' />
            <template v-else>№{{item.id}}</template>
          </div>
        </div>
        <table class="card__table">

  <tr>
    <td style="vertical-align:top" v-if="item.registration_status === 'liquidated' || item.registration_status === 'active' || item.blocked" width="32%" class="card__label">
      {{ $t('status') }}:
    </td>

    <td>

      <div v-if="item.registration_status === 'liquidated'">

        <ui-alert type='danger'>
          {{ $t('liquidated') }}.          
          <div v-if="item.liquidation_reason">
           {{ $t('liquidation_reason') }}: {{ item.liquidation_reason.charAt(0).toLowerCase() + item.liquidation_reason.slice(1) }}.
          </div>
          <div>
          {{ $t('portal_liquidation_date') }}:
          <iac-date :date='liquidation_date' withoutTime/>
          </div>
        </ui-alert>

      </div>

      <div v-else-if="item.blocked">
        <ui-alert type='warning'>
          {{ $t('blocked_funds_short') }}
          <span v-if="item.blocked_from">
            {{ $t('event_from') }}   <iac-date :date='item.blocked_from' withoutTime/>
          </span>
          <span v-if="item.blocked_to">
            {{ $t('event_to') }}  <iac-date :date='item.blocked_to' withoutTime/>.
          </span>
           <span v-if="item.meta && item.meta.blocking_reason">
           {{ $t('blocking_reason') }}: {{ item.meta.blocking_reason.charAt(0).toLowerCase() + item.meta.blocking_reason.slice(1) }}
          </span>
        </ui-alert>
      </div>

      <div v-else>
          {{ $t('active') }}
      </div>
    </td>
  </tr>

          <tr>
            <td width="32%" class="card__label">{{$t('parent_org_name')}}:</td>
            <td><span>{{item.parent_org_name || '-'}}</span></td>
          </tr>
          <tr>
            <td width="32%" class="card__label">{{$t('company_inn')}}:</td>
            <td><span>{{item._inn || '-'}}</span></td>
          </tr>
          <tr>
            <td width="32%" class="card__label">{{$t('parent_org_inn')}}:</td>
            <td><span>{{item.parent_org_inn || '-'}}</span></td>
          </tr>
          <tr>
            <td width="32%" class="card__label">{{$t('oked_code')}}:</td>
            <td><span>{{item.oked ? item.oked.code + ' ' + item.oked.name : '-'}}</span></td>
          </tr>
          <tr>
            <td width="32%" class="card__label">{{$t('address_legal')}}:</td>
            <td>
              {{ arrToString(item.address_legal) }}
            </td>
          </tr>
          <tr>
            <td width="32%" class="card__label">{{$t('phone')}}:</td>
            <td>
              <a  v-if="item.phone":href='"tel:" + item.phone'>{{item.phone}}</a>
              <template v-else>-</template>
            </td>
          </tr>
          <tr>
            <td width="32%" class="card__label">{{$t('email')}}:</td>
            <td>
              <a v-if="item.email" :href='"mailto:" + item.email'>{{item.email}}</a>
              <template v-else>-</template>
            </td>
          </tr>
          <tr>
            <td width="32%" class="card__label">{{$t('director_name')}}:</td>
            <td><span>{{item.director_name || '-'}}</span></td>
          </tr>
          <tr>
            <td width="32%" class="card__label">{{$t('region')}}:</td>
            <td><span>{{item.legal_area || '-'}}</span></td>
          </tr>
          <template v-if="!is_broker_request_btn && item.broker_request">
            <tr>
              <td width="32%" class="card__label">
                <span>{{ $t('broker_request.description') }}:</span>
              </td>
              <td>
                <div v-if="item.broker_request.meta.status == 'accept'"> {{ $t('broker_request.status.accepted') }} </div>
                <div v-else-if="item.broker_request.meta.status == 'wait'"> {{ $t('broker_request.status.waiting') }} </div>
                <div v-else-if="item.broker_request.meta.status == 'torn'">
                  <a :title="item.broker_request.meta.torn_reason"> {{ $t('broker_request.status.torned') }} </a>
                </div>
                <div v-else-if="item.broker_request.meta.status == 'cancel'">
                  <a :title="item.broker_request.meta.cancel_reason"> {{ $t('broker_request.status.canceled') }} </a>
                </div>
                <div v-else-if="!item.broker_request.meta.status == 'reject'">
                  <a :title="item.broker_request.meta.reject_reason"> {{ $t('broker_request.status.rejected') }} </a>
                </div>
              </td>
            </tr>

            <tr width="32%" class="card__label" v-if="item.broker_request.inserted_at">
              <td> {{ $t('broker_request.request_sended') }}: </td>
              <td><iac-date :date='item.broker_request.inserted_at' full /></td>
            </tr>
            <tr width="32%" class="card__label" v-if="item.broker_request.meta.accepted_at">
              <td> {{ $t('broker_request.request_accepted') }}: </td>
              <td><iac-date :date='item.broker_request.meta.accepted_at' full /></td>
            </tr>
            <tr width="32%" class="card__label" v-if="item.broker_request.meta.torned_at">
              <td> {{ $t('broker_request.request_torned') }}: </td>
              <td><iac-date :date='item.broker_request.meta.torned_at' full /></td>
            </tr>
            <tr width="32%" class="card__label" v-if="item.broker_request.meta.canceled_at">
              <td> {{ $t('broker_request.request_canceled') }}: </td>
              <td><iac-date :date='item.broker_request.meta.canceled_at' full /></td>
            </tr>
            <tr width="32%" class="card__label" v-if="item.broker_request.meta.rejected_at">
              <td> {{ $t('broker_request.request_rejected') }}: </td>
              <td><iac-date :date='item.broker_request.meta.rejected_at' full /></td>
            </tr>

          </template>
          <tr v-if="$develop.content_debug || is_broker">
            <td colspan="2">
              <div>
                <span v-for='tag in tags' class="ui-tag-item">{{ tag }}</span>
              </div>
            </td>
          </tr>
          <tr v-if="is_broker_request_btn">
            <td>
              <ui-btn @click.native='brokerRequest()' type='primary'>{{$t("broker_request")}}</ui-btn>
            </td>
          </tr>
        </table>
        <ui-layout-group v-if='visible_list_user' label="nav.members">
          <ui-data-view :dataSource='dataSourceUsers' :search="false"/>
        </ui-layout-group>
      </div>
    `
}

Vue.component("widget-company-page",Component);
