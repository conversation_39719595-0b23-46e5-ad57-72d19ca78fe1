export default class Util {

    static Number(value, delimiter, part=0, round=false) {
        let number = parseFloat(value);

        if (isNaN(number)) {
            return;
        }
        let res = number;
        part = parseInt(part, 10);
        const strNumber = isNaN(part) ? res + '' : (round == true ? ("" + Math.round(res * Math.pow(10, part)) / Math.pow(10, part)) : res.toFixed(part));
        let [whole, fraction] = strNumber.split('.');
        res = whole;

        if (delimiter) {
            let delimited = whole.split('');
            for (let i = delimited.length - 4, j = 0; i >= 0; i--, j++) {
                if (j % 3 === 0) {
                    delimited[i] += delimiter;
                }
            }
            res = delimited.join('');
        }
        if (round === 'up') {
            const numberFraction = number.toString().split('.')[1];
            if (part > 0) {
                if (fraction < numberFraction) {
                    if (fraction[fraction.length - 1] === '9') {
                        if (fraction.length > 1) {
                            fraction = fraction.slice(0, -2) + fraction[fraction.length - 2]++ + '0';
                        } else {
                            fraction = '0'.repeat(part);
                            res++;
                        }
                    } else {
                        fraction = fraction.slice(0, -1) + (+fraction[fraction.length - 1] + 1);
                    }
                }
            }
        }
        if (fraction !== undefined) {
            res += `.${fraction}`;
        }
        return res;
    }

}