import {Entity} from '@iac/data'
import {Http, Language} from '@iac/core'
import {Config} from '@iac/kernel'

class Model extends Entity{
    constructor(context={}){
        super(context)
        this.company_id = context.id;
        this.meta = context.meta || {}
        this.ignore_limits_of_brv_for_selection = this.meta.ignore_limits_of_brv_for_selection
        this.ignore_limits_of_brv_for_selection_from = this.meta.ignore_limits_of_brv_for_selection_from
        this.ignore_limits_of_brv_for_selection_to = this.meta.ignore_limits_of_brv_for_selection_to
        this.ignore_limits_of_brv_for_selection_file = this.meta.ignore_limits_of_brv_for_selection_file

    }



    props(){
        return {
            ignore_limits_of_brv_for_selection: {
                label: 'permission_to_publish_selections_over_25000_brv',
                type: "bool",   
                group: '!bool-',               
            },
            
            
            ignore_limits_of_brv_for_selection_from:{
                required: true,
                label: 'from',
                type: "date",
                group: '!date-',
                has_del: true,              
                hidden: ()=>{
                    return !this.ignore_limits_of_brv_for_selection
                },
                

            },

            ignore_limits_of_brv_for_selection_to:{
                required: true,
                label: 'to',
                type: "date",
                group: '!date-',
                has_del: true,
                hidden: ()=>{
                    return !this.ignore_limits_of_brv_for_selection
                }, 
                
            },
            
            ignore_limits_of_brv_for_selection_file: {
                required: true,
                label: 'permitting_document',
                type: "file",
                multiple: true,
                hidden: ()=>{
                    return !this.ignore_limits_of_brv_for_selection
                }, 
                meta: {
                    url: (value) => {
                        if (!value.id)
                        return;
                        return `${Config.api_server}/file/${value.id}`
                    },
                }
            }
        }
    }


    get save() {
       
        return async () => {
            let params = this.fields.reduce((obj, current) => {
                obj[current.name] = current.value;
                return obj;
            }, {});
    
            if (params.ignore_limits_of_brv_for_selection_file && Array.isArray(params.ignore_limits_of_brv_for_selection_file)) {

                for (let index in params.ignore_limits_of_brv_for_selection_file) {
                    let item = params.ignore_limits_of_brv_for_selection_file[index];
    
                    if (item?.file instanceof File) {
                        const formData = new FormData();
                        formData.append('data', item.file, item.file.name);
    
                        console.log(formData);
    
                        let { error, data } = await Http.upload.form('tender/attach', formData);
                        if (error) {
                            console.error("Ошибка при загрузке файла:", error);
                            continue;
                        } 
                        else {
                            params.ignore_limits_of_brv_for_selection_file[index] = {
                                id: data.uuid,
                                name: data.meta.name,
                                meta: {
                                    type: data.meta.type,
                                    content_type: data.meta.content_type,
                                    type_group: data.meta.group,
                                    size: data.meta.size,
                                },
                            };
                        }
                    } 
                }
                this.ignore_limits_of_brv_for_selection_file = [...params.ignore_limits_of_brv_for_selection_file]
            }
            


                console.log("Fields:", this.ignore_limits_of_brv_for_selection_file);


            if (!params.ignore_limits_of_brv_for_selection ){
                params = {
                    ignore_limits_of_brv_for_selection: false    
                }
            }
            let {error, data} = await Http.api.rpc("special_accesses", {
                company_id: this.company_id, 
                ...params            
            });
            


            if (error) {           
                if (!this.setError(error)) {
                    Vue.Dialog.MessageBox.Error(error);
                }
            } else {           
                Vue.Dialog.MessageBox.Success (Language.t('updated'));
            }

            return {
                error,
                data: params
            };

        }
    }
}

export default Vue.Dialog("IssuePermit", {
    data: function () {
        return {
            model: new Model(this.item), 
        };
    },
    props: ["item"],

    methods: {
        save() {
           this.$wait(async () => {
               let {error, data} = await this.model.save();
                if(data) {
                    this.Close(data);   
                }
            })
        },
    },
    template: `
    <div>
        <main>
            <ui-layout :fields='model.fields' />
        </main>
        <footer>
            <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
            <ui-btn type='primary'  v-on:click.native='save'>{{$t('apply')}}</ui-btn>     
        </footer>
    </div>
    `,
});