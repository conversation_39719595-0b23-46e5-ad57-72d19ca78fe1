import { Language as LngProvider } from '@iac/core'
import settings from '../../settings';

export class Language {
    static request(config) {
        config.headers = config.headers || {};
        config.headers['X-DBRPC-Language'] = config.headers['X-DBRPC-Language'] || LngProvider._local;

        if (settings._country == 'KG') {
            switch (config.headers['X-DBRPC-Language']) {
                case "ru":
                    config.headers['X-DBRPC-Language'] = 'ru_RU@kg'
                    break;
                case "en":
                    config.headers['X-DBRPC-Language'] = 'en_US@kg'
                    break;
            }
        }

        return config;
    };
};