import { Entity } from '@iac/data'
import { Http } from '@iac/core'
export default class Model extends Entity {
    constructor(context, introspect, scheme) {
        super(context)
        this.context = context;
        this.introspect = introspect;
        this.scheme = scheme;

    }

    props() {
        let create_fields = this.introspect.create_fields;
        return Object.keys(create_fields).reduce((props, name, i) => {
            props[name] = {
                value: this.context[name],
                required: true
            }
            switch (create_fields[name]) {
                case "integer":
                    props[name].type = "number"
                    break
                case "decimal":
                    props[name].type = "float"
                    break
                case "date":
                    props[name].type = "date"
                    break
                case "utc_datetime":
                case "utc_datetime_usec":
                    props[name].type = "date-time"
                    break
                case "map":
                    props[name].type = "text"
                    break
                default:
                    props[name].type = "string"
                    break
            }

            return props;
        }, {})
    }
    async save() {
        let params = this.fields.filter((field) => {
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).reduce((prev, curr) => {
            prev[curr.name] = curr.value && curr.value.exp ? curr.value.exp.value : curr.value
            if (prev[curr.name] == undefined)
                prev[curr.name] = null;
            return prev
        }, {})
        params[this.introspect.id] = this[this.introspect.id]


        let { error, data } = await Http.api.rpc(this.scheme.source_method, {
            ref: this.scheme.method,
            filters: this.context[this.introspect.id] ? {
                [this.introspect.id]: this.context[this.introspect.id],
            } : undefined,
            op: this.context[this.introspect.id] ? "update" : "create",
            data: params
        })

        if (error) {
            if (!this.setError(error)) {
                await Vue.Dialog.MessageBox.Error(error);
            }
        }

        if (!error && data && !this.context[this.introspect.id]) {
            params[this.introspect.id] = data[this.introspect.id]
        }

        return {
            error,
            data: params
        };
    }
}