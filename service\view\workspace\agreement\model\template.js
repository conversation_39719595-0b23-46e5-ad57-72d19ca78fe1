import { Http } from '@iac/core'
import { Entity, DataSource, ArrayStore, RemoteStore } from '@iac/data'
import AgreementEntity from './entity';

export default class Template extends AgreementEntity {
    constructor(context) {
        super(context)

        this.title = context.title;
        this.anno = context.anno;
        this.duration = context.duration;

        this.Users = new DataSource({
            store: new ArrayStore({
                data: [
                    { id: 1, name: "Белоусов Андрей Андреевич" },
                    { id: 2, name: "<PERSON>аскевич Иван Константинович " },
                    { id: 3, name: "Григорян Георгий Григорьевич" },
                    { id: 4, name: "фридрих Олег Зурабович" },
                ]
            })
        });
    }

    props() {
        return {
            title: {
                label: "name.template"
            },
            anno: {
                label: "anno.template",
                type: "text"
            },
            duration: {
                label: "duration",
                type: "entity",
                value: 0,
                dataSource: "ref_template_duration"
            }
        }
    }

    async save() {
        const { error, data } = await Http.api.rpc("update_agreement_template", {
            id: this.id,
            title: this.title || '',
            anno: this.anno || '',
            duration: 0//this.duration != undefined ? this.duration.id : null
        });
        if (error) {
            if (!this.setError(error)) {
                await Vue.Dialog.MessageBox.Error(error);
            }
        }
        return { error, data };
    }

    static async get(id) {
        let { error, data } = await Http.api.rpc("get_agreement_template_info", { id: id });
        let agreement;
        if (!error) {
            let response = await AgreementEntity.stage(id)
            if (response.error)
                return {
                    error: response.error
                }
            data.stage = response.data;
            agreement = new Template(data);
        }
        return {
            error,
            data: agreement
        }
    }
}