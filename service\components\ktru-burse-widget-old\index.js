import Vue from "vue"
import renderTable from './table'
import renderTree from './tree'
import specCreator from './spec_creator'
import specEditor from './spec_editor'
import { Http, Language } from '@iac/core'

let searchTimeoutID = undefined

const KtruBurseWidget = {
  props: {
    mode: {
      type: String,
      default: 'select'//'select'||'add'||'merge'
    },
    onSelect: {
      type: Function,
      required: true
    },
    activeItem: {
      type: Object,
      default: undefined
    }
  },
  components: {
    'renderTree': renderTree,
    'renderTable': renderTable,
    'specCreator': specCreator,
    'specEditor': specEditor
  },
  data() {
    return {
      searchQuery: "",
      searchResult: undefined,
      searchLoading: false,
      currentItem: undefined
    }
  },
  watch: {
    searchQuery(_newVal, _oldVal) {
      clearTimeout(searchTimeoutID)
      searchTimeoutID = setTimeout(() => this.search(), 300)
    }
  },
  computed: {
    isShowSearch() {
      return !['show', 'merge'].includes(this.mode) && !(this.mode === 'add' && this.currentItem)
    }
  },
  methods: {
    clearQuery() {
      this.searchQuery = ""
      this.searchResult = undefined
      this.searchLoading = false
      this.currentItem = undefined
    },
    async search() {
      const { searchQuery: query = "" } = this
      if (query.length < 3) {
        return
      }
      this.searchLoading = true
      const { error, data } = await Http.api.rpc("ref", {
        ref: "ref_enkt_burse_product",
        op: "read",
        limit: 15,
        offset: 0,
        query,
        fields: ['id', 'product', 'meta']
      })
      if (!error && data) {
        data.forEach(item => {
          delete item.__schema__
          item.product.temporary = { currentName: item.product.name[Language.local] || item.product.name[Object.keys(item.product.name)[0]] }
          item.product.id = item.id
          delete item.id
        })
        this.searchResult = data
        this.currentItem = undefined
      }

      this.searchLoading = false
    },
    async setCurrentItem(item = undefined) {
      if (!item) {
        this.currentItem = undefined
        return
      }

      const { product } = item
      product.units.forEach(unit => unit.temporary = { currentName: unit.name[Language.local] || unit.name[Object.keys(unit.name)[0]] })
      product.unit = product.units.find(unit => unit.ratio == 1)
      product.master_unit = product.unit
      product.properties.forEach(prop => {
        prop.temporary = { currentName: prop.name[Language.local] || prop.name[Object.keys(prop.name)[0]] }
        prop.value = undefined
        prop.values.forEach(val => val.temporary = { currentName: val.name[Language.local] || val.name[Object.keys(val.name)[0]] })
      })

      product.groups = await this.getGroups(product.group_id)

      this.currentItem = product
    },
    async getGroups(group_id) {
      let groups = group_id ? undefined : []
      while (!groups || (groups.length && groups[0].parent_id)) {
        const { error, data } = await Http.api.rpc("ref", {
          ref: "ref_enkt_burse_product_groups",
          op: "read",
          limit: 1,
          offset: 0,
          filters: { id: groups?.length ? groups[0].parent_id : group_id },
          fields: ['id', 'parent_id', 'name']
        })
        groups = groups ?? []
        !error && data && data?.length && groups.unshift(data[0])
      }
      groups.forEach(item => {
        delete item.__schema__
        item.temporary = {
          currentName: item.name[Language.local] || item.name[Object.keys(item.name)[0]]
        }
      })
      return groups
    },
    getNamesFromOldKTRUItem(oldNames = []) {
      const name = { "ru-RU": "", "en-US": "", "uz-UZ@cyrillic": "", "uz-UZ@latin": "" }
      oldNames.forEach(nameItem => {
        if (nameItem.LOCALE == "ru_RU") {
          name["ru-RU"] = nameItem.VALUE
        } else if (nameItem.LOCALE == "en_US") {
          name["en-US"] = nameItem.VALUE
        } else if (nameItem.LOCALE == "uz_UZ@latin") {
          name["uz-UZ@latin"] = nameItem.VALUE
        } else if (nameItem.LOCALE == "uz_UZ@cyrillic") {
          name["uz-UZ@cyrillic"] = nameItem.VALUE
        }
      })
      return name
    },
    getPropsAndUnitsFromOldKTRU(oldProps) {
      const units = [], properties = []
      const findMarkers = ["Единица измерения", "Бирлик", "Birlik", "Unit"].map((item) => item.toLowerCase());

      oldProps.forEach(prop => {
        if (findMarkers.includes(prop.data[0].VALUE.toLowerCase())) {
          prop.values.forEach(val => {
            const name = this.getNamesFromOldKTRUItem(val.data)
            const temporary = { currentName: name[Language.local] || name[Object.keys(name)[0]], is_new: true }
            units.push({ name: name, ratio: 1, temporary })
          })
        } else {
          const propName = this.getNamesFromOldKTRUItem(prop.data)
          const temporary = { currentName: propName[Language.local] || propName[Object.keys(propName)[0]], is_new: true }
          const values = prop.values.map(val => {
            const name = this.getNamesFromOldKTRUItem(val.data)
            const temporary = { currentName: name[Language.local] || name[Object.keys(name)[0]], is_new: true }
            return { name: name, temporary }
          })
          properties.push({ name: propName, temporary, values, value: undefined })
        }
      })
      return { units, properties }
    },
    async createEmptySpec() {
      const name = this.getNamesFromOldKTRUItem()
      const newSpec = {
        id: "",
        skp: "",
        name,
        tnved: "",
        units: [],
        group_id: null,
        groups: [],
        temporary: { currentName: name[Language.local] || name[Object.keys(name)[0]], is_new: true },
        properties: []
      }
      if (await Vue.Dialog.MessageBox.Question(Language.t("product.create_new.spec.question")) == Vue.Dialog.MessageBox.Result.Yes) {
        const KTRUProduct = await Vue.Dialog.SelectProduct.Modal({ size: 'right', mode: "spec" })

        const { units, properties } = this.getPropsAndUnitsFromOldKTRU(KTRUProduct.properties)
        const name = this.getNamesFromOldKTRUItem(KTRUProduct.name)

        newSpec.name = name
        newSpec.units = units
        newSpec.properties = properties
      }

      newSpec.unit = newSpec.units.find(unit => unit.ratio == 1)
      newSpec.master_unit = newSpec.unit

      this.currentItem = newSpec
    },
    redirectToBurseProductRequest() {
      this.$router.push({ path: '/workspace/exchange/burse_product_request' })
    }
  },
  mounted() {
    if (this.activeItem) {
      this.currentItem = this.activeItem
    } else {
      setTimeout(() => this.$refs?.search?.focus?.(), 300);
    }
  },
  template: `
    <div class='iac--ktru-burse' @keydown.esc="e=>setCurrentItem()">
      <div v-if="isShowSearch" class="iac--ktru-burse__search">
        <div>
          <icon v-if="searchLoading" class="iac--to-spin">spinner</icon>
          <icon v-else>search</icon>
        </div>
        <input ref="search" :placeholder="$t('search_for_product')" v-model="searchQuery" @keydown.enter="search"/>
        <button v-if="searchQuery.length || searchResult || currentItem" @click="clearQuery">
          <icon>delete</icon>
        </button>
      </div>
      <div v-if="mode=='add' && !currentItem" class="iac--ktru-burse__add_new_burse_product">
        <button @click="createEmptySpec">
          {{$t('product.create_new')}}
        </button>
      </div>
      <div v-if="mode=='select' && !currentItem" class="iac--ktru-burse__add_new_burse_product">
        <template v-if='$policy.exchange_burse_product_request_add_new_product'> 
          <button @click="redirectToBurseProductRequest">
            Добавить новый товар
          </button>
        </template>
      </div>
      <specCreator v-if="mode=='select' && currentItem" :currentItem="currentItem" :onSelect="onSelect"/>
      <specEditor v-if="['add','merge','show'].includes(mode) && currentItem" :currentItem="currentItem" :mode="mode" :onSelect="onSelect" :getGroups="getGroups"/>
      <div v-if="currentItem"/>
      <renderTable v-else-if="searchResult" :searchResult="searchResult" @setCurrentItem="setCurrentItem"/>
      <renderTree v-else @setCurrentItem="setCurrentItem"/>
    </div>
  `,
};

Vue.component("ktru-burse-widget", KtruBurseWidget);