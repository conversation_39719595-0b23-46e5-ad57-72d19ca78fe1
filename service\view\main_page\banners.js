import Banner from './banner'
import {Language} from '@iac/core'
import {Settings} from '@iac/kernel'

export default {
    data: function(){
        let lng_1 = ()=>{
            switch(Language._local_socket){
                case 'en':
                case 'ru':
                    return Language._local_socket
                case 'uzl':
                    return 'uz'

                default:
                   return '' 
            }
        }
        return {
            curr: 0,
            tid: undefined,
            arr: [
                {
                    color_icon: "#FF7733",
                    title: Language.t("banner_4_title"),
                    img: "/img/b_5.png",
                    content: Language.t("banner_4_content"),
                    active: false,
                    link: `https://conference.hayotbirja.uz/${lng_1()}`,
                    link_title: Language.t('banner_4_link')
                },
                {
                    color_icon: "#02676C",
                    title: Language.t("banner_0_title"),
                    img: "/img/b_1.png",
                    active: true,
                    content: Language.t("banner_0_content")
                },
                {
                    color_icon: "#FF7733",
                    title: Language.t("banner_1_title"),
                    img: "/img/b_2.png",
                    active: true,
                    content: Language.t("banner_1_content")
                },
                {
                    color_icon: "#FF7733",
                    title: Language.t("banner_2_title"),
                    img: "/img/b_3.png",
                    img_margin: "0 0 -25px 0",
                    active: true,
                    content: Language.t("banner_2_content")
                },
                {
                    color_icon: "#FF7733",
                    title: Language.t("banner_3_title"),
                    img: "/img/b_4.png",
                    active: Settings.exchange && Settings.procedures?.cargo_procedure,
                    content: Language.t("banner_3_content")
                }
                
            ]
        }
    },
    computed: {
        active_banners() {
            return this.arr.filter((banner) => {
                return banner.active
            })               
        }       
    },
    mounted(){
        this.start_scroll();
    },
    components: {
        Banner
    },
    methods: {
        start_scroll(){
            if(this.tid){
                this.mouseover();
            }
            this.tid = setTimeout(()=>{
                if(this.curr == this.active_banners.length-1){
                    this.curr = 0
                }else{
                    this.curr++;
                }
                this.start_scroll();  
            },5000)
        },
        select(index){
            if(this.curr == index)
            return;
        this.curr = index;
        },
        mouseleave(){
            this.start_scroll();
        },
        mouseover(){
            clearTimeout(this.tid) 
            this.tid = undefined;
        }
    },
    beforeDestroy() {
        clearTimeout(this.tid) 
        this.tid = undefined;
    },
    template: `
    <div class='iac-service-banners' v-on:mouseleave='mouseleave' v-on:mouseover='mouseover'>
        <div class='content' :style='"margin-left: -"+curr*100+"%; margin-right: "+curr*100+"%"'>
            <section v-for='banner in active_banners' style='background: #edefef; padding:  0; flex: 0 0 100%;'>
                <div class='iac-container' style='height: 100%'>
                    <banner :model='banner'/>
                </div>
            </section>
        </div>
        <div class='indicator'>
            <span v-on:click='select(index)' :class='index == curr ? "active":""' v-for='i,index in active_banners.length'/>
        </div>
    </div>
    `
}