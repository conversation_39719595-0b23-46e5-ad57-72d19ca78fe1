import { Context, Config } from '@iac/kernel'
import { DataSource, RemoteStore, Query } from '@iac/data'

let Search = new Query({
    text: {
        icon: 'search',
        label: "!Search",
        order: -1,
        hidden: true,
    }
})

let ContractsOut = new DataSource({
    displayExp: 'title',
    query: new Query({
        direction: "outbox",
        status: {
            type: 'enum',
            dataSource: [
                {id: "draft", name: "draft"},
                {id: "active", name: "active"},
                {id: "archive", name: "archive"},
                {id: "agree_with_contragent", name: "agree_with_contragent"}
            ],
            value: []
        },
        contragent_id: {
            label: "Counteragent",
            type: 'entity',
            dataSource: new DataSource({
                displayExp: 'title',
                store: new RemoteStore({
                    method: 'get_contract_contragents'
                })
            })
        }
    }, [Search]),
    store: new RemoteStore({
        method: "search_contract"
    })
});

let ContractsIn = new DataSource({
    displayExp: 'title',
    query: new Query({
        direction: "inbox",
        company_id: {
            label: "Company",
            type: 'entity',
            dataSource: new DataSource({
                displayExp: 'title',
                store: new RemoteStore({
                    method: 'get_contract_contragents'
                })
            })
        }
    }, [Search]),
    store: new RemoteStore({
        method: "search_contract"
    })
});

let ContractsSysop = new DataSource({
    displayExp: 'title',
    query: new Query({
        company_id: {
            label: "Company",
            type: 'entity',
            dataSource: new DataSource({
                displayExp: 'title',
                store: new RemoteStore({
                    method: 'get_contract_contragents'
                })
            })
        },
        contragent_id: {
            label: "Counteragent",
            type: 'entity',
            dataSource: new DataSource({
                displayExp: 'title',
                store: new RemoteStore({
                    method: 'get_contract_contragents'
                })
            })
        }
    }, [Search]),
    store: new RemoteStore({
        method: "search_contract"
    })
});


export default {
    data: function () {
        return {
            user: Context.User,
            ContractsOut: ContractsOut,
            ContractsIn: ContractsIn,
            ContractsSysop: ContractsSysop,
        }
    },
    methods: {

    },
    template: `
    <div>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.contracts')}}</li>
            </ol>
            <h1>{{$t('contracts')}}11</h1>
        </iac-section>
        <iac-section>
            <ui-layout-tab v-if='user.role_code != "sysop"'>
                <ui-layout-group label='outbox'>1
                    <ui-data-list key='ContractsOut' search='text' :dataSource='ContractsOut'>

                    </ui-data-list>
                </ui-layout-group>
                <ui-layout-group label='inbox'>2
                    <ui-data-list key='ContractsIn' search='text' :dataSource='ContractsIn'>

                    </ui-data-list>
                </ui-layout-group>
            </ui-layout-tab>

            <ui-layout-group v-if='user.role_code == "sysop"'>3
                <ui-data-list key='ContractsSysop' search='text' :dataSource='ContractsSysop'>

                 </ui-data-list>
            </ui-layout-group>
        </iac-section>
    </div>
    `
}
