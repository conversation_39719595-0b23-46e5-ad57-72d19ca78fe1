export default Vue.Dialog({
    props: ['invite'],
    methods: {
        async save() {
            await this.wait(async () => {
                const { error, data } = await this.invite.invite_user_to_company();
                if (!error)
                    this.Close(true);
            });
        }
    },
    template: `
        <div>
            <header>{{$t('send_invite')}}</header>
            <main>
                <ui-layout :fields='invite.fields'/>
            </main>
            <footer>
                <ui-btn type='primary' v-on:click.native='save'>{{$t('send')}}</ui-btn>
            </footer>
        </div>
    `
});