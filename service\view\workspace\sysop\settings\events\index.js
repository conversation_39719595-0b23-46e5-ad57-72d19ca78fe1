import Day from './day'
import Month from './month'
import Year from './year'

import { DataSource } from '@iac/data'
import './_edit_dlg'
import { Language } from '@iac/core'

export default {
    data: function () {
        return {

            dataSourceEvents: new DataSource({
                query: {
                    start: {
                        type: "hidden"
                    },
                    end: {
                        type: "hidden"
                    }
                },
                store: {
                    method: "ref",
                    ref: "ref_calendar_events",
                    context: (context)=>{
                        context.start = context.event_start
                        context.end = context.event_end
                        return context;
                    },
                    injectQuery: (params)=>{
                        if(params.filters && params.filters.start && params.filters.end){
                            params.filters.range = {
                                event_start: params.filters.start,
                                event_end: params.filters.end,
                            }
                        }
                        if(params.filters){
                            params.filters.start = params.filters.end = undefined;
                        }
                        params.limit = undefined;
                        params.offset = undefined;
                        return params;
                    },
                }
            }),
            dataSourceCalendar: new DataSource({
                store: {
                    data: [
                        { id: "burse", name: Language.t("event_exchange") },
                        { id: "events", name: Language.t("event_events") },
                        { id: "holidays", name: Language.t("event_holidays") }
                    ]
                }
            }),
            current: undefined,
            //view: 'week',
            calendarIDs: ["burse", "events", "holidays"],
            content: {
                day: {
                    label: Language.t('event_day'),
                    component: Day,
                    params: {
                        count: 1
                    }
                },
                customday: {
                    label: Language.t('event_4_days'),
                    component: Day,
                    params: {
                        count: 4
                    }
                },
                week: {
                    label: Language.t('event_week'),
                    component: Day,
                    params: {
                        count: 7
                    }
                },
                month: {
                    label: Language.t('event_month'),
                    component: Month
                },
                year: {
                    label: Language.t('event_year'),
                    component: Year
                }
            }
        }
    },
    created() {
        let { current_date } = this.$route.query;
        this.current = current_date ? new Date(current_date) : new Date();

    },
    computed: {
        view() {
            return this.$route.params.view || 'week'
        },
        current_day() {
            let month = ('00' + (this.current.getMonth() + 1)).slice(-2);
            let day = ('00' + this.current.getDate()).slice(-2);

            return `${this.current.getFullYear()}-${month}-${day}`
        },
        current_day_display() {
            return this.current_day
        }
    },
    methods: {
        async create() {
            let event = await Vue.Dialog.EditEvent.Modal({
                size: "lg",
            })

            this.dataSourceEvents.push_item(event);
            console.log(event)

        },
        set_view(view) {
            this.view = view
        },
        get_move() {
            let day = undefined;
            let month = undefined;
            let year = undefined;
            switch (this.view) {
                case "customday":
                    day = 4
                    break;
                case "week":
                    day = 7
                    break;
                case "month":
                    month = 1;
                    break;
                case "year":
                    year = 1;
                    break;
                default:
                    day = 1;
            }
            return { day, month, year }

        },
        to_left() {
            let { day, month, year } = this.get_move();
            if (day)
                this.set_current_date(this.current.setDate(this.current.getDate() - day));
            if (month)
                this.set_current_date(new Date(this.current.getFullYear(), this.current.getMonth() - month, 1));
            if (year)
                this.set_current_date(this.current.setFullYear(this.current.getFullYear() - year));
        },
        to_right() {
            let { day, month, year } = this.get_move();
            if (day)
                this.set_current_date(this.current.setDate(this.current.getDate() + day));
            if (month)
                this.set_current_date(new Date(this.current.getFullYear(), this.current.getMonth() + month, 1));
            if (year)
                this.set_current_date(this.current.setFullYear(this.current.getFullYear() + year));
        },
        to_current() {
            this.set_current_date();
        },
        onInputDate(value) {
            this.set_current_date(value)
        },
        set_current_date(date) {
            this.current = date ? new Date(date) : new Date();
            this.$router.replace({ query: { current_date: this.current_day } })
        }
    },
    template: `
    <iac-access :access='$policy.system_page_edit' class='iac-section event_page' style='flex: 1 1 auto; display: flex;'>
        <component :dataSource='dataSourceEvents' v-bind='content[view].params' :is='content[view].component' class='iac-section-container' :current='current' :calendarIDs='calendarIDs'>
            <div slot='header' style='display: flex;align-items: center; justify-content: space-between;padding: 0 8px;height: 80px; background: #fff'>
                <h2 v-if='0'>{{ $t('nav.settings.EVENTS') }}:</h2>
                <div style='display: flex;'>
                    <ui-btn type='secondary empty' v-on:click.native='to_current'>{{$t('event_today')}}</ui-btn>
                    <div class='move'>              
                        <span  v-on:click='to_left' />
                        <span class='value'>{{current_day_display}}</span>    
                        <span  v-on:click='to_right'/> 
                    </div>
                </div>
                <div>
                    
                    <ui-btn-group>
                        <router-link :class='key != view ? "ui-btn ui-btn-secondary ui-btn-empty" : "ui-btn ui-btn-secondary"' :to='{path:"/workspace/settings/events/"+key,query:{current_date: current_day}}'  v-for='item,key in content'>{{item.label}}</router-link>
                        <ui-btn v-if='0' :key='key' :type='key != view ? "secondary empty" : "secondary"' v-for='item,key in content' v-on:click.native='set_view(key)'>{{item.label}}</ui-btn>
                    </ui-btn-group>
                </div>
            </div>
        </component>
        <div class='sidebar' style='background: #fff; margin: 20px 0 0;'>
            <div class='sticky'>
                <div class='create' v-on:click='create'><icon>+</icon><span>{{$t('event_create')}}</span></div>
                <ui-ctrl-date :value='current_day' v-on:input='onInputDate' />
                <ui-layout-group label='event_my_calendars' :expanded='true'>
                    <ui-enum :dataSource='dataSourceCalendar' :value='calendarIDs' v-model='calendarIDs' style='border: none;' />
                </ui-layout-group>
            </div>
        </div>
        
    </iac-access>   
    `
}