import Kernel from "../..";

export class Blackout {
    static get error_code() {
        return 521;
    }
    static sleep(milliseconds) {
        return new Promise(async (resolve, reject) => {
            setTimeout(() => {
                resolve();
            }, milliseconds)
        });
    }

    static async wait() {
        Kernel.Blackout.active = true
        
        await Blackout.sleep(30000)
        let index = 0;
        let item;
        while (item = Blackout.list && Blackout.list[index]) {
            let start = true;
            while (start) {
                let { response, config, handler } = item.rejection;
                let { error, data } = await handler();
                if (!error || error.status != Blackout.error_code) {
                    start = false;
                    item.resolve({ error, data })
                } else {
                    await Blackout.sleep(15000)
                }
            }
            index++;
        }
        Blackout.list = [];
        
        Kernel.Blackout.active = false
    }

    static addRejection(rejection) {
        let promise = new Promise(async (resolve, reject) => {
            //Blackout.addPromise(rejection, resolve)
            Blackout.list = Blackout.list || [];
            Blackout.list.push({ rejection, resolve })

            if (Blackout.list.length == 1)
                Blackout.wait();
        });
        return promise;
    }

    static async response(rejection) {
        // Если ответ от сервера является запросом то снять блокировку
        if (rejection.response && rejection.response.data && rejection.response.data.question){
            Kernel.Blackout.active = false    
        }

        return rejection.response
    }

    static responseError(rejection) {
        let { response, config, handler } = rejection;

        if (!response.error || (response.error && response.error.status != Blackout.error_code))
            return response;

        if (config.isBlackoutDuplicateRequest)
            return response;

        config.isBlackoutDuplicateRequest = true;

        Kernel.Blackout.message = response.error.message;

        return new Promise(async (resolve, reject) => {
            resolve(await Blackout.addRejection(rejection));
        });
    }
}