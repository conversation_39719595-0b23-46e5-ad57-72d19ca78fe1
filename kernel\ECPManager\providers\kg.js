import { Property, DataSource } from "@iac/data"
import { Http } from "@iac/core"

export default class kg<PERSON><PERSON><PERSON> extends Property {
    constructor(context) {
        super(context)
    }
    props() {
        return {
            pinfl: {
                label: "-pinfl",
                //value: "20108199350059",
                attr: {
                    react: true
                },
                onChange: (value) => {
                    console.log(value)
                }
            },
            certificate: {
                type: "entity",
                label: "-certificate",
                // value: "00304202310123",
                dataSource: {
                    //valueExp: "organizationInn",
                    valueExp: "pkcs7",
                    displayExp: "organization",
                    search: "pinfl",
                    query: {
                        pinfl: {
                            label: "!"
                        }
                    },
                    store: {
                        method: "get_signs",
                        injectQuery: (params) => {
                            console.log(params);
                            params.limit = undefined;
                            params.offset = undefined;
                            //params.pinfl = this.pinfl;// || "20108199350059";
                            //if(params.id)
                            return params
                        }
                    }
                },
                hidden: () => {
                    return !this.pinfl
                }
            }
        }
    }

    static async getInfo(data) {
        return {
            
        };
    }


    static async createPkcs7(message, data) {

        let response = await Http.api.rpc("sign_data", {
            //action: "sign_data",
            pinfl: data.pinfl,
            inn: data.certificate?.organizationInn || data.certificate,
            data: message
            //pin_code_method: "email"
        });

        return response;

    }
}