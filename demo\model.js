import { Data, Settings } from '@iac/kernel'
import { Event, Http, Language } from '@iac/core'
import { DataSource, ArrayStore } from '@iac/data'

var DemoDialog = Vue.Dialog({
    props: ["size", "model"],
    template: `
        <div>
            <header>{{$t(size+"_dialog_size")}}</header>
            <main>
                <ui-layout :fields='model.fields' />
            </main>
            <footer>
                <ui-btn type='secondary' v-on:click.native='Close()'>{{$t("close")}}</ui-btn>
            </footer>
        </div>
    `
})

class Property extends Data.Property {
    constructor(context) {
        super(context)

        this.typeName = this.type;
        if (this.status) {
            this.statusType = this.status.type;
            this.statusMessage = this.status.message;
        }
    }

    onChangeProperty(data) {
        if (!data)
            return;
        switch (data.name) {
            case "typeName":
                //this.value = undefined;
                this.type = data.value
                if (data.value == 'entity' || data.value == 'enum' || data.value == 'tag') {
                    this.dataSourceName = "ref_unit"
                    this.dataSource = DataSource.get("ref_unit")
                } else if (data.value == 'widget') {
                    this.widget = {
                        name: "ui-alert",
                        props: {
                            type: "warning",
                        },
                        content: "finish_registration_info"
                    }
                }
                break;
            case "dataSourceName":
                this.dataSource = DataSource.get(data.value)
                break;
            case "statusType":
            case "statusMessage":
                let type = this.statusType && this.statusType.id;
                if (type)
                    this.status = {
                        type: type,
                        message: this.statusMessage,
                    }
                break;
        }
    }

    props() {
        return {
            label: {
                label: "label",
            },
            group: {
                label: "group"
            },
            typeName: {
                label: "type",
                type: "entity",
                dataSource: [
                    "widget", "static", "string", "text", "html", "number", "float", "range", "file", "date", "time", "date-time", "entity", "enum", "tag", "bool"
                ]
            },
            dataSourceName: {
                label: "dataSource",
                type: "entity",
                dataSource: [
                    "ref_unit", "ref_currency", "ref_products", "ref_uz_area_tree"
                ],
                hidden: () => {
                    let typeName = this.typeName && this.typeName.id
                    return typeName != "entity" && typeName != "tag" && typeName != "enum"
                }
            },
            readonly: {
                type: "bool"
            },
            required: {
                type: "bool"
            },
            has_del: {
                type: "bool"
            },
            multiple: {
                type: "bool"
            },
            statusType: {
                type: "entity",
                dataSource: ["error", "success", "warning", "info"],
                has_del: true,
            },
            statusMessage: {
                hidden: () => {
                    return !this.statusType
                }
            }
        }
    }
}


export class Model extends Data.Entity {
    @Event onField;
    constructor(context = {}) {
        super(context)

        this.new_id = 1;

        this.actions = [
            {
                label: "Отобразить в диалоговом окне",
                handler: async () => {
                    this.showModal("sm");
                }
            },
            {
                label: "Отобразить в большом окне",
                handler: async () => {
                    this.showModal("lg");
                }
            },
            {
                label: "Отобразить на веь экран",
                handler: async () => {
                    this.showModal("full");
                }
            },
            {
                label: "Отобразить в правой панели",
                handler: async () => {
                    this.showModal("right");
                }
            }
        ]

    }

    get propertyModel() {
        return Property;
    }

    async showModal(size) {
        return await DemoDialog.Modal({
            size: size,
            model: this
        });
    }
}

export class ComponentsModel extends Model {
    constructor(context = {}) {
        super(context)
    }

    props() {
        return {
        }
    }
}

export class GroupModel extends Model {
    constructor(context = {}) {
        super(context)
    }

    props() {
        return {
            groupInfoWidget: {
                group: '!Группы',
                type: "static",
                label: "!",
                value: `
                    <p>Существуют четыре основных группировок.</p>
                    <ul>
                        <li><b>Табы</b></li>
                        <li><b>Горизонтальная</b> - Элементы данной группы располагаются друг за другом</li>
                        <li><b>Обычная</b> - Элементы данной группы располагаются друг под другом</li>
                        <li><b>Группировка в единый компонент</b></li>
                    </ul>
                    <p>В качестве контента любой группы могут выступать элементы ввода, виджеты, компоненты и группы. Уровень вложенности не ограничен.</p>
                    <p>Обычная и Горизонтальная группировка могут отображаться как с заголовком так и без него.</p>
                `
            },


            groupHorizontalInfoWidget: {
                group: 'Группы/{group}/Горизонтальная',
                type: "static",
                label: "!",
                value: `
                    <p>Элементы данной группы располагаются друг за другом и распределяются равномерно по всей ширине группы. Если элементы не умещаются в одну строку то они переносятся на следующую.</p>
                `
            },
            groupHorizontalInput_1: {
                group: 'Группы/{group}/Горизонтальная/-Группа 1',
                label: "Элемент ввода 1"
            },
            groupHorizontalInput_2: {
                group: 'Группы/{group}/Горизонтальная/-Группа 1',
                label: "Элемент ввода 2"
            },
            groupHorizontalInput_3: {
                group: 'Группы/{group}/Горизонтальная/-Группа 1',
                label: "Элемент ввода 3"
            },
            groupHorizontalInput_4: {
                group: 'Группы/{group}/Горизонтальная/-Группа 1',
                label: "Элемент ввода 4"
            },
            groupHorizontalInput_5: {
                group: 'Группы/{group}/Горизонтальная/-Группа 1',
                label: "Элемент ввода 5"
            },
            groupHorizontalInput_6: {
                group: 'Группы/{group}/Горизонтальная/-Группа 1',
                label: "Элемент ввода 6"
            },

            groupHorizontalInput_7: {
                group: 'Группы/{group}/Горизонтальная/-!Группа 2/-Группа 2',
                label: "Элемент ввода 7"
            },
            groupHorizontalInput_8: {
                group: 'Группы/{group}/Горизонтальная/-!Группа 2/-Группа 2',
                label: "Элемент ввода 8"
            },
            groupHorizontalInput_9: {
                group: 'Группы/{group}/Горизонтальная/-!Группа 2/-Группа 2',
                label: "Элемент ввода 9"
            },
            groupHorizontalInput_10: {
                group: 'Группы/{group}/Горизонтальная/-!Группа 2/-Группа 3',
                label: "Элемент ввода 10"
            },
            groupHorizontalInput_11: {
                group: 'Группы/{group}/Горизонтальная/-!Группа 2/-Группа 3',
                label: "Элемент ввода 11"
            },


            groupCoreInput_1: {
                group: 'Группы/{group}/Обычная',
                label: "Элемент ввода 1"
            },
            groupCoreInput_2: {
                group: 'Группы/{group}/Обычная',
                label: "Элемент ввода 2",
                type: "text"
            },
            groupCoreInfoWidget: {
                group: 'Группы/{group}/Обычная',
                type: "static",
                label: "!",
                value: `
                    <p>Все компоненты данной группы имеют ширину равной ширине всей группы. Не всегда данный подход хорошо смотрится. Поэтому желательно комбинировать обычную группировку с горизонтальной.</p>
                    <p>Но не всегда есть компоненты да заполнения горизонтальной группы. Но даже в этом случае можно воспользоваться пустышкой  </p>
                `
            },
            groupCoreInfoWidget: {
                group: 'Группы/{group}/Обычная',
                type: "widget",
                label: "!",
                widget: {
                    name: "ui-alert",
                    props: {
                        type: "warning",
                    },
                    content: `
                    Все компоненты данной группы имеют ширину равной ширине всей группы. Не всегда данный подход хорошо смотрится. 
                    Поэтому желательно комбинировать обычную группировку с горизонтальной. Но не всегда есть компоненты да заполнения горизонтальной группы. 
                    Но даже в этом случае можно воспользоваться пустышкой.
                    `
                }
            },
            groupCoreInput_3: {
                group: 'Группы/{group}/Обычная/-!horizontal/!left',
                label: "Элемент ввода 1"
            },
            groupCoreInput_4: {
                group: 'Группы/{group}/Обычная/-!horizontal/!left',
                label: "Элемент ввода 2",
                type: "text"
            },
            groupCoreInput_5: {
                group: 'Группы/{group}/Обычная/-!horizontal/!right',
                label: "!",
                type: "static"
            },

            groupComponentInput_1: {
                group: 'Группы/{group}/В Компоненты/<group1>',
                label: "Элемент ввода 1",
            },
            groupComponentInput_2: {
                group: 'Группы/{group}/В Компоненты/<group1>',
                label: "Элемент ввода 2",
            },
            groupComponentInput_3: {
                group: 'Группы/{group}/В Компоненты/<group1>',
                label: "Элемент ввода 3",
            },

            groupComponentInput_4: {
                group: 'Группы/{group}/В Компоненты/<group2>',
                label: "Элемент ввода 4",
            },
            groupComponentInput_5: {
                group: 'Группы/{group}/В Компоненты/<group2>',
                label: "!Элемент ввода 5",
            },
            groupComponentInput_6: {
                group: 'Группы/{group}/В Компоненты/<group2>',
                label: "!Элемент ввода 6",
            },

            groupComponentInput_7: {
                group: 'Группы/{group}/В Компоненты/<group3>',
                label: "!Элемент ввода 7",
            },
            groupComponentInput_8: {
                group: 'Группы/{group}/В Компоненты/<group3>',
                label: "!Элемент ввода 8",
            },



            groupComponentInput_9: {
                group: 'Группы/{group}/В Компоненты/-Группа 1/!left/<group4>',
                label: "Элемент ввода 9",
            },
            groupComponentInput_10: {
                group: 'Группы/{group}/В Компоненты/-Группа 1/!left/<group4>',
                label: "Элемент ввода 10",
            },
            groupComponentInput_11: {
                group: 'Группы/{group}/В Компоненты/-Группа 1/!right',
                label: "Элемент ввода 11",
            },



            groupComponentInput_12: {
                group: 'Группы/{group}/В Компоненты/-Группа 2/!left/<group4>',
                label: "Элемент ввода 12",
            },
            groupComponentInput_13: {
                group: 'Группы/{group}/В Компоненты/-Группа 2/!left/<group4>',
                label: "Элемент ввода 13",
            },
            groupComponentInput_14: {
                group: 'Группы/{group}/В Компоненты/-Группа 2/!right',
                label: "!",
                type: "static"
            },
        }
    }
}

export class DemoModel extends Model {
    constructor(context = {}) {
        super(context)

        this.actions.push(...[
            {
                type: 'sep'
            },
            {
                label: "add_field",
                handler: async () => {
                    let id = "field_" + this.new_id++;
                    let attributes = {
                        id: id,
                        label: id,
                        type: "string",
                        actions: [
                            {
                                label: "delete",
                                handler: () => {
                                    delete this[id];
                                    delete this._own_properties[id];// = undefined;
                                    this._properties = undefined;
                                    this._fields = undefined;
                                    this.onField()
                                }
                            }
                        ]
                    }
                    let prop = new this.propertyModel({
                        model: this,
                        name: attributes.id,

                        attributes: attributes
                    });

                    this._own_properties[prop.name] = prop;
                    this._properties = undefined;
                    this._fields = undefined;
                    this.onField()
                }
            }
        ])
    }

    async onChangeProperty(event) {


    }

    props() {

        return {
            
            _f_1: {
                readonly: true,
                "value": [
                  {
                    "name": "Арбуз.jpg",
                    "meta": {
                      "type": "jpeg",
                      "size": 4368,
                      "content_type": "image/jpeg"
                    },
                    "id": "76d9a35b-67b0-4560-a692-24f05ea38b22"
                  }
                ],
                "type": "file",
                "system": true,
                "prev_values": [
                  [
                    {
                      "name": "Арбуз.jpg",
                      "meta": {
                        "type": "jpeg",
                        "size": 4368,
                        "content_type": "image/jpeg"
                      },
                      "id": "76d9a35b-67b0-4560-a692-24f05ea38b22"
                    }
                  ]
                ],
                "multiple": true,
                "label": "-Наличие лицензий",
                "group": "general/!Информация о товаре",
                "attr": {
                  "title": "Предыдущее значение: [%{\"id\" => \"76d9a35b-67b0-4560-a692-24f05ea38b22\", \"meta\" => %{\"content_type\" => \"image/jpeg\", \"size\" => 4368, \"type\" => \"jpeg\"}, \"name\" => \"Арбуз.jpg\"}]"
                },
                "actions": [
                  {
                    "type": "request",
                    "params": {
                      "proc_id": 1004893,
                      "field_id": "licenses",
                      "allowed_value": null
                    },
                    "method": "leave_comment",
                    "label": "Добавить комментарий",
                    "host": "proc",
                    "btn_type": "warning empty"
                  },
                  {
                    "type": "request",
                    "params": {
                      "proc_id": 1004893,
                      "field_id": "licenses",
                      "comment": "solved",
                      "allowed_value": [
                        {
                          "name": "Арбуз.jpg",
                          "meta": {
                            "type": "jpeg",
                            "size": 4368,
                            "content_type": "image/jpeg"
                          },
                          "id": "76d9a35b-67b0-4560-a692-24f05ea38b22"
                        }
                      ]
                    },
                    "method": "leave_comment",
                    "label": "Решено ✅",
                    "host": "proc",
                    "btn_type": "success empty"
                  }
                ]
              },
              _f_2: {
                readonly: true,
                "type": "file",
                "system": true,
                "status": {
                  "type": "error",
                  "message": "хрень"
                },
                "prev_values": [
                  null
                ],
                "multiple": true,
                "label": "-Наличие сертификатов",
                "group": "general/!Информация о товаре",
                "comment": "хрень",
                "attr": {
                  "title": "Предыдущее значение: nil"
                },
                "allowed_value": null,
                "actions": [
                  {
                    "type": "request",
                    "params": {
                      "proc_id": 1004893,
                      "field_id": "certificates",
                      "allowed_value": null
                    },
                    "method": "leave_comment",
                    "label": "Добавить комментарий",
                    "host": "proc",
                    "btn_type": "warning empty"
                  },
                  {
                    "type": "request",
                    "params": {
                      "proc_id": 1004893,
                      "field_id": "certificates",
                      "comment": "solved",
                      "allowed_value": null
                    },
                    "method": "leave_comment",
                    "label": "Решено ✅",
                    "host": "proc",
                    "btn_type": "success empty"
                  }
                ]
              },

            phone_test: {
                label: "-phone_test11111111111",
                group: "<test11111111>",
                attr: {
                    react: true,
                },
                onChange: (value) => {
                    this.phone = value
                }
            },
            phone_test_action: {
                label: "!",
                type: "action",
                buttons: true,
                group: "<test11111111>",
                actions: [
                    {label: "AAA",handler: ()=>{}}
                ]
            },


            phone: {
                label: "-phone",
                type: "phone",
                attr: {
                    react: false,
                },
                onChange(value) {
                    this.description = value
                }

            },


            desc_demo_1: {
                type: "static",
                label: "-Опыт оказания услуг",
                description: "-(баллы по данному требованию от '0' до '5')",
                value: `
Государственные закупки посредством рамочного соглашения осуществляются при одновременном выполнении следующих условий:
Критерии определения победителя включают не только ценовую оценку товаров (работ, услуг), но и количественную и качественную оценку;
стоимость товаров (работ, услуг) по одному рамочному соглашению составляет более двадцати пяти тысяч размеров базовой расчетной величины.
`
            },
            desc_demo_2: {
                type: "static",
                label: "-!Опыт оказания услуг",
                description: "_(На дополнительном языке):",
                value: `
Государственные закупки посредством рамочного соглашения осуществляются при одновременном выполнении следующих условий:
Критерии определения победителя включают не только ценовую оценку товаров (работ, услуг), но и количественную и качественную оценку;
стоимость товаров (работ, услуг) по одному рамочному соглашению составляет более двадцати пяти тысяч размеров базовой расчетной величины.
`
            },

            banking_details: {
                type: 'entity',
                label: "-requisites_title",
                description: "Необходимо выбрать реквизит",
                dataSource: 'get_company_bank_accounts',
                has_del: true,
                attr: {
                    details: [
                        "bank_name", 
                        {label: Language.t([Settings._country+".bank_mfo_code","bank_mfo_code"]),field: "bank_mfo_code"}, 
                        "bank_account"]
                }
            },


            currency: {
                type: "entity",
                label: "-currency",
                dataSource: ["USZ", "RUB", "USD"]
            },


            amount: {
                label: "-amount",
                description: "-(suffix)",
                type: "float",
                dataBind: {
                    property: 'suffix',
                    name: 'currency',
                    field: 'name',
                },
            },
            amount2: {
                label: "-amount",
                description: "-(prefix)",
                type: "float",
                dataBind: {
                    property: 'prefix',
                    name: 'currency',
                    field: 'name',
                },
            },

            input_range: {
                type: "number",
                min: "!0",
                max: 10,
                label: "-number",
                description: "$Больше 0 или меньше и равно 10",
            },

            grid_10: {
                type: "data-grid",
                dataSource: {
                    store: [
                        {
                            source: "Источник_1",
                            price: 5000,
                            currency: Settings._default_currency
                        },
                        {
                            source: "Источник_2",
                            price: 15000,
                            currency: Settings._default_currency
                        }
                    ]
                },
                attr: {
                    summary: true,
                    columns: [
                        { field: "source", label: "Источник", style: "text-align:left; width: 100%", summary: "Итого:" },
                        { field: "price", label: "Сумма", style: "text-align:right; white-space: nowrap;", type: "float", suffix: "currency", summary: "SUM" },
                    ]
                },
            },


            grid_11: {
                type: "data-grid",
                label: "Расходы",
                description: "^Пример таблицы",
                dataSource3: {
                    store: {
                        data: async () => {
                            let { error, data } = await Http.api.rpc("contract_ref", { ref: 'cat_sum', op: "read" })
                            if (error)
                                return []
                            data = data && Object.keys(data).map((index) => {
                                return data[index]
                            })
                            return data
                        }
                    }
                },
                dataSource2: {
                    store: {
                        method: 'contract_ref',
                        ref: 'cat_sum',
                    }
                },
                dataSource: {
                    actions: [
                        {
                            label: "Добавить",
                            type: "request",
                            method: "add_items",
                        },
                    ],
                    store: [
                        {
                            col1: "bla-bla",
                            col2: "prc",
                            col3: "500000.1",
                            actions: [
                                {
                                    label: "Edit",
                                    type: "request",
                                    btn_type: "warning",
                                    method: "edit_item",
                                    host: "api",
                                    params: {
                                        id: 1,

                                    }
                                },
                                {
                                    label: "delete",
                                    icon: "delete",
                                    type: "request",
                                    btn_type: "danger",
                                    method: "delete_item",
                                    host: "api",
                                    question: "Вы действительно хотите удалить данную запись",
                                    params: {
                                        id: 1,

                                    }
                                }
                            ]
                        }]
                },
                attr: {
                    action_name: "Действия",
                    //action_style: "text-align:left;white-space: nowrap;",
                    buttons: true, // Отображает в виде кнопок - по умолчанию выпадашка
                    summary: true,
                    columns: [
                        { field: "col1", label: "Причина расхода", style: "text-align:left; width: 100%" },
                        { field: "col2", label: "SUM OR PROCENT", style: "text-align:right; white-space: nowrap;" },
                        { field: "col3", label: "Объем", style: "text-align:right; white-space: nowrap;", type: 'float', suffix: 'col2', summary: "11" }
                    ]
                },
            },


            edit_user_1: {
                type: "entity-edit",
                label: "-edit_user",
                value: {
                    id: 367,
                    type: 'user',
                },
                order: 0,
            },
            edit_user_2: {
                type: "entity-edit",
                label: "-edit_user",
                value: {
                    id: 367,
                    type: 'user',
                    title: 'Дубина Дмитрий Евгеньевич'
                },
                order: 0,
            },
            grid: {
                type: "data-grid",
                label: "-grid",
                dataSource: {
                    actions: [
                        {
                            label: "Добавить",
                            type: "request",
                            method: "add_items",
                        },
                        {
                            label: "Удалить все",
                            type: "request",
                            btn_type: "danger",
                            method: "delete_items",
                        }
                    ],
                    store: [{
                        col1: "asdawdawd",
                        col2: "asdawdawd"
                    },
                    {
                        col1: "asdawdawd",
                        col2: "asdawdawd",
                        actions: [
                            {
                                label: "delete",
                                icon: "delete",
                                type: "request",
                                btn_type: "danger",
                                method: "delete_item",
                                host: "api",
                                params: {
                                    id: 1,

                                }
                            }
                        ]
                    }]
                },
                attr: {
                    action_name: "Действия",
                    buttons: true, // Отображает в виде кнопок - по умолчанию выпадашка
                    columns: [
                        "col1", { field: "col2", label: "Кол2", style: "width: 100%" }
                    ]
                },
            },
            markdown: {
                type: "markdown",
                label: "-markdown",
                value: `
|awdawd|awdawd|awdawd|
|--|--|--|
|awdawd|awdawd|awdawd|
|awdawd|awdawd|awdawd|
                `
            },
            table: {
                type: "widget",
                label: "-table",
                widget: {
                    name: {
                        template: `
                            <table class='table table-bordered table-striped table-hover' width="100%" cellspacing="0" cellpadding="0">
                                <thead>
                                    <tr><th>#</th><th>col_1</th><th>col_2</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>1</td><td>var1_1</td><td>var1_2</td></tr>
                                    <tr><td>2</td><td>var2_1</td><td>var2_2</td></tr>
                                    <tr><td>3</td><td>var3_1</td><td>var3_2</td></tr>
                                    <tr><td>4</td><td>var4_1</td><td>var4_2</td></tr>
                                </tbody>
                            </table>
                        `
                    }
                },
                order: 0,
            },

            product: {
                label: '-product',
                type: 'product',
                description: "Из основного каталога",
                //readonly: true,
                attr: {
                    source: "main"
                },
                has_del: true
            },

            product1: {
                label: '-product',
                description: "Из каталога КТРУ",
                type: 'product',
                //readonly: true,
                has_del: true,
                attr: {
                    eye: true,
                    source: "ktru"
                }
            },

            product2: {
                label: '-product',
                description: "Из БК",
                type: 'product',
                //readonly: true,
                has_del: true,
                attr: {
                    eye: true,
                    source: "bk"
                }
            },

            product_info: {
                label: '!',
                type: 'layout-static',
                group: "{layout-static}/Вариант с привязкой",
                dataBind: { // Забрать у филда product массив product_properties и поместить в value
                    property: 'value',
                    name: 'product',
                    field: 'product_properties',
                },
            },

            statics: {
                // hidden: true,
                group: "{layout-static}/Вариант с текстом",
                label: '!',
                type: 'layout-static',
                value: `
[Описание товара]
Марка: выавпвапв
Производитель: впвапаавпвапр
Страна производства: УЗБЕКИСТАН
Срок годности: 2030-02-28
Год производства: 2011
Описание: пририипраоалпрмимоа пропраола м
Лицензия: yes
Срок гарантии: 365 дней

[Сроки]
Срок торга: 
     tel:+79184666667
     04.03.2023
     04.03.2023
     04.03.2023
     04.03.2023
     04.03.2023
Срок доставки: 15 дней

              
                `
            },
            statics2: {
                label: '!',
                group: "{layout-static}/Вариант с массивом",
                type: 'layout-static',
                value: [
                    {
                        "val_name": "шт",
                        "prop_name": "Единица измерения11111111"
                    },
                    {
                        "val_name": "А4",
                        "prop_name": "формат22222"
                    },
                    {
                        "val_name": "Журнал учета отправляемой служебной информации средствами факсимильной связи",
                        "prop_name": "Вид33333"
                    },
                    {
                        "val_name": "твердая",
                        "prop_name": "тип обложки4444444"
                    },
                    {
                        "val_name": "офсетная",
                        "prop_name": "бумага внутреннего блока"
                    },
                ]
            },

            file: {
                label: "field_setting.add_file",
                group: 'field_setting.general',
                type: "file",
                multiple: true,
                //max: 100,
            },

            checkbox_1: {
                label: "Некий чекбокс",
                type: 'bool'
            },
            textarea_1: {
                label: "Некий textarea",
                type: 'text',
                hidden: "!checkbox_1"
            },
            "entity.1": {
                group: '!range-',
                label: 'entity1',
                type: 'entity',
                dataSource: "ref_products",
                has_del: true,
                required: true,
                value: "8771aff1-8aac-437e-bef9-af3428f36341",
                actions: [
                    {
                        label: "Поле не прошло модерацию",
                    },
                    {
                        type: "sep"
                    },
                    {
                        label: "delete",
                        handler: () => {
                            Vue.Dialog.MessageBox.Question("Вы действительно хотите удалить?");
                        }
                    }
                ]
            },
            fieldname: {
                type: 'data-view',
                label: '!',
                order: 0,
                attr: {
                    search: false,
                    //toolbar: false, 
                    //type: 'row',   
                },
                dataSource: {
                    actions: [
                        { label: "в БАН только выбранных" },
                        { label: "Всех нах в БАН" }
                    ],
                    store: [
                        {
                            id: 1,
                            header: ["Жалоба #1", "от 10.10.2020"],
                            title: { text: 'Отбор № 5801', link: '/procedure/5801/core?tabid_tab=1' },
                            description: [
                                { label: 'текст жалобы', text: 'Ну пачему?' },
                                { label: 'файл', text: 'Ош20.png', link: 'http://api.test.xt-xarid.uz/file/0ea32d19-58fd-4623-85a2-a293fe74d61d' }
                            ],
                            props: [
                                { label: 'Свойство 1', text: 'Значение свойства 1' },
                                { label: 'Свойство 2', text: 'Тарифы', link: '/info/tariffs' },
                                { label: 'Свойство 3', text: 'Значение свойства 3', link: 'http://api.test.xt-xarid.uz/file/0ea32d19-58fd-4623-85a2-a293fe74d61d' },
                            ],
                            actions: [
                                {
                                    label: 'tribunal_cp',
                                    type: 'request',
                                    method: "copy_group",
                                    host: "proc",
                                    params: {
                                        proc_id: 5801,
                                        num: 1,
                                        marker: 'tribunal_cp',
                                        group: "tech_fields/tribunal/<tribunal.0>"
                                    }

                                },
                                {
                                    type: 'sep'
                                },
                                {
                                    label: 'Отправить в БАН',
                                }
                            ],
                        },
                        {
                            id: 2,
                            header: ["Жалоба #2", "от 11.10.2020"],
                            title: { text: 'Ирманова Холида', link: () => { alert(1) } },
                            description: [
                                { label: 'текст жалобы', text: 'возражаю' }
                            ],
                            checkbox: true,
                        },
                        {
                            id: 3,
                            header: ["Жалоба #3", "от 10.10.2020"],
                            title: 'Ирманова Холида ',
                            description: [
                                { label: 'текст жалобы', text: 'против' },
                                { label: 'файл', text: 'Ош20.png', link: 'http://api.test.xt-xarid.uz/file/0ea32d19-58fd-4623-85a2-a293fe74d61d' }
                            ],
                            props: [
                                { label: 'Свойство 1', text: 'Значение свойства 1' },
                                { label: 'Свойство 2', text: 'Значение свойства 2' },
                                { label: 'Свойство 3', text: 'Значение свойства 3', link: 'http://api.test.xt-xarid.uz/file/0ea32d19-58fd-4623-85a2-a293fe74d61d' },
                            ]
                        }
                    ]
                }
            },
            range1: {
                group: '!range-',
                label: 'range1',
                type: 'range',
                min: 1,
                max: 5,
                value: 0,
            },
            widget1: {
                group: '{tab1}/Профиль1',
                type: "widget",
                label: "!",
                widget: {
                    name: "ui-alert",
                    props: {
                        type: "warning",
                    },
                    content: "finish_registration_info"
                }

            },
            login1: {
                group: '{tab1}/Профиль1',
                label: '-login1',
                type: 'static',
                value: '<EMAIL>'
            },
            first_name1: {
                group: '{tab1}/Профиль1',
                label: '-first_name1',
                type: 'string',
                value: 'Verisoko',
                has_del: true
            },
            name1: {
                group: '{tab1}/Профиль1',
                label: '-name1',
                type: 'string',
                value: 'Konstantin'
            },
            date_time1: {
                group: '{tab1}/Профиль1/!date-',
                label: '-date_time1',
                type: 'date-time',
            },
            date1: {
                group: '{tab1}/Профиль1/!date-/<time>',
                label: '!date1',
                type: 'date',
            },
            time1: {
                group: '{tab1}/Профиль1/!date-/<time>',
                label: '!time1',
                type: 'time',
            },
            comment1: {
                group: '{tab1}/Профиль1',
                type: "text",
                readonly: true,
                actions: [{ label: "TEST" }],
                value: `
### Заголовок

[ССЫЛКА](/link)
                `
            },


            widget2: {
                group: '{tab1}/Профиль2',
                type: "widget",
                label: "!",
                widget: {
                    name: "ui-alert",
                    props: {
                        type: "danger",
                    },
                    content: "finish_registration_info"
                }

            },
            login2: {
                group: '{tab1}/Профиль2/!top-',
                value: "<EMAIL>",
                readonly: true
            },
            first_name2: {
                group: '{tab1}/Профиль2/!top-/<name>',
            },
            name2: {
                group: '{tab1}/Профиль2/!top-/<name>',
            },
            comment2: {
                group: '{tab1}/Профиль2',
                type: "text"
            },
            string_1: {
                group: "-!group1/Левая группа"
            },
            string_2: {
                group: "-!group1/!right/{tab2}/tab1"
            },
            string_3: {
                group: "-!group1/!right/{tab2}/tab2",
                label: "!string_3"
            },
            string_4: {
                group: "-!group1/!right2",
                label: "!string_4"
            },
            string_5: {
                group: "-!group1/!right2",
                label: "!string_5"
            }

        }
    }
}

export class ClearModel extends Model {
    constructor(context = {}) {
        super(context)

        this.actions.push(...[
            {
                type: 'sep'
            },
            {
                label: "add_field",
                handler: async () => {
                    let id = "field_" + this.new_id++;
                    let attributes = {
                        id: id,
                        label: id,
                        type: "string",
                        actions: [
                            {
                                label: "delete",
                                handler: () => {
                                    delete this[id];
                                    delete this._own_properties[id];// = undefined;
                                    this._properties = undefined;
                                    this._fields = undefined;
                                    this.onField()
                                }
                            }
                        ]
                    }
                    let prop = new this.propertyModel({
                        model: this,
                        name: attributes.id,

                        attributes: attributes
                    });

                    this._own_properties[prop.name] = prop;
                    this._properties = undefined;
                    this._fields = undefined;
                    this.onField()
                }
            }
        ])
    }
}