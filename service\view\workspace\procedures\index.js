import ProcedureContestList from './contest'
import ProcedureTenderList from './tender'
import ProcedureSelectionList from './selection'
import cargo_procedures from './cargo_procedures'
import ProcedureReductionList from './reduction'

import ProcedureMasterAgreementList from './master_agreement'
import ProcedureMasterAgreementRequestList from './master_agreement/request'

import ProcedureAdList from './electronic_shop/ad'
import ProcedureRequestList from './electronic_shop/request'
import Cart from './electronic_shop/cart';

export default [
    { path: 'tender', component: ProcedureTenderList },
    { path: 'contest', component: ProcedureContestList },
    { path: 'selection', component: ProcedureSelectionList },
    { path: 'cargo_procedures', component: cargo_procedures },
    { path: 'reduction', component: ProcedureReductionList },

    // Рамочные соглашения
    { path: 'master_agreement', component: ProcedureMasterAgreementList },
    { path: 'ma_request', component: ProcedureMasterAgreementRequestList },

    // Магазин
    { path: 'ad', component: ProcedureAdList },
    { path: 'request', component: ProcedureRequestList, props: { nad: false } },
    { path: 'nad_request', component: ProcedureRequestList, props: { nad: true } },
    ...Cart


]