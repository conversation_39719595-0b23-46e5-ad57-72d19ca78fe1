export default {
    template: `
<div style='display: flex;flex-direction: column;'>
    <div class='sticky' style='background: #fff;'>
        <slot name='header' />
    </div>
    <div style='flex: 1 1 auto;border-top: 1px solid  #ccc;  border-bottom: 1px solid #ccc;background: #fff; display: flex; flex-direction: column;'>
        <div v-for='h in [1,2,3,4,5]' style='display: flex; flex: 1 1 20%; min-height: 50px;'>
            <div v-for='d in [1,2,3,4,5,6,7]' style='border-left: 1px solid #ccc;border-bottom: 1px solid #ccc;flex: 0 0 14.28%;'>
            &nbsp;
            </div>
        </div>
    </div>
</div> 
    `
}
