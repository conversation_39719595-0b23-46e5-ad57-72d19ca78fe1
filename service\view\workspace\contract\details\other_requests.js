export default {
    props: ["model"],
    computed: {
        needToAdvancePaymentAmount() {
            return (this.model.graphic || []).some(item => item.advance_payment != undefined)
        }
    },
    methods: {
        update_data(){
            this.$wait(async ()=>{
                await this.model.update_contract_detailed([
                   // "other_requests"
                ]);
            })
        }
    },
    template: `
    <div class='grid'>
        <div class='row' style='display: flex;'>
            <h2 style='flex: 1 1 auto;'>{{$t('contract.other_requests')}}</h2>
            <ui-btn v-if='0' type='success' v-on:click.native='update_data'>{{$t('balance.refresh')}}</ui-btn>
        </div>
        <div class='row graphic'>
            <table>
                <thead>    
                    <tr>
                        <th>№</th>
                        <th>{{$t('request')}}</th>
                        <th>{{$t('ad')}}</th>
                        <th>{{$t('contract.amount')}}</th>
                        <th style='width: 100%; text-align: left'>{{$t('status')}}</th>
                        <th>{{$t('contract')}}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for='(request,index) in model.other_requests'>
                        <td>{{index+1}}</td>
                        <td><router-link :to='"/procedure/"+request.id'>{{request.id}}</router-link></td>
                        <td><router-link :to='"/procedure/"+request.ad'>{{request.ad}}</router-link></td>
                        <td><iac-number :value='request.amount' delimiter=' ' part='2'/> <ui-ref source='ref_unit' :value='request.unit' /></td>
                        <td style='width: 100%; text-align: left'><ui-ref source='status_request' :value='request.status'/></td>
                        <td>
                            <template v-if='request.winner'>
                                <span v-if='request.winner.contract_number == model.number'>{{$t('this')}}&nbsp;{{$t('contract')}}</span>
                                <router-link v-else :to='"/workspace/contract/"+request.winner.contract_number'>{{request.winner.contract_number}}</router-link>
                            </template>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>    
    `
}