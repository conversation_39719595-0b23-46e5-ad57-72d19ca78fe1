#! /usr/bin/env python3

# Example usage: cat ./service/view/workspace/exchange/arbitration_complaint_details/model.js | ./extract_gettext.py .

import os
import sys
import re

lines = sys.stdin.readlines()

root_path = sys.argv[1]

def find(s):
    res = os.popen(f"""rg 'msgid "{s}"' {root_path}""")
    content = res.read()
    if res.close() is None:
        lines = []
        for line in content.split('\n'):
            lines.append(line.split(':msgid')[0])

        return ", ".join(lines)
    else:
        return None

ss = set()
for line in lines:
    res = re.findall(r'''Language\.t\(['|"](.*?)['|"]\)|\$t\(['|"](.*?)['|"]\)|label: ['|"][-]*[!]*(\w+|_?)['|"]''', line)
    groups = res if res is not None else None
    if groups == []: continue

    for group in groups:
        group = list(group)
        group.reverse()
        for match_ in group:
            if match_ != '':
                ss.add(match_)

pretty_lines = []

for s in ss:
    finded_files = find(s)

    if finded_files is not None:
        print(f'#: DECLARE IN OTHER FILES\n{finded_files}\n#: msgid "{s}"\n#: msgstr "{s}"\n')
    else:
        pretty_lines.append(f'msgid "{s}"\nmsgstr "{s}"\n')

print("====== NEW STRINGS ======\n")
for s in pretty_lines:
    print(s)

