import { Http, Language } from '@iac/core'
import { Settings } from '@iac/kernel'
import Vue from "vue"

export const findCurrentocaleItem = struct => struct.find(item => item.LOCALE.replace(/[-_]*/ig, '') == Language.local.replace(/[-_]*/ig, '')) ?? struct[0]

const blockedItemsUZ = []

export const ktruSearch = async (query) => {
    const { error, data } = await Http.api.rpc("ref", {
        ref: "ref_enkt_products",
        op: "read",
        "limit": 15,
        "offset": 0,
        query: query.replace("-0", "_0"),
        fields: ['id', 'name', 'code', 'type', 'category_id']
    })
    if (!error && data) {
        const blockedItems = (Settings._country == 'KG') ? [] : blockedItemsUZ
        data.forEach(item => {
            item.currentName = findCurrentocaleItem(item.name)

            if (item?.code && blockedItems.includes(item.code.replace("_", "-"))) {
                item.blocked = true
            }
            delete item.__schema__
        })
    }
    return { error, data }
}

export const ktruGetAdditionalSpecData = async (itemData) => {
    if (!itemData) {
        return
    }

    const { category_id: id = "", code: productCode = "" } = itemData
    const firstCategoryCode = productCode.split('_')[0]
    const secondCategoryCode = productCode.split('.').slice(0, -1).join('.')

    const { error: err, data: data } = await Http.api.rpc("ref", {
        ref: "ref_enkt_categories",
        op: "read",
        "limit": 100,
        "offset": 0,
        filters: { code: [firstCategoryCode, secondCategoryCode] },
        fields: ['id', 'data', 'code']
    })

    if (err || !data) {
        Vue.Dialog.MessageBox.Error("Данный товар не заполнен полностью и его не получится использовать. Обратитесь в тех.поддержку. Приносим извинения.")
        return
    }

    const data_sorted = data.sort((l, r) => r.code.length - l.code.length)?.[0];

    itemData.category = data_sorted
    itemData.category.currentName = findCurrentocaleItem(itemData.category.data)

    const propsLimit = 90
    const tempProperties = []
    let needMoreProps = true

    while (needMoreProps) {
        const { error: errorProps, data: dataProps } = await Http.api.rpc("ref", {
            ref: "ref_enkt_properties",
            op: "read",
            limit: propsLimit,
            offset: tempProperties.length,
            filters: { code: productCode },
            fields: ['required', 'number', 'id', 'data']
        })
        if (!errorProps && dataProps) {
            tempProperties.push(...dataProps.map(item => ({ ...item, currentName: findCurrentocaleItem(item.data), values: [], currentValue: undefined })))
        }
        needMoreProps = dataProps?.length == propsLimit
    }
    itemData.properties = tempProperties

    const valuesLimit = 90
    const tempValues = []
    let needMoreValues = true

    while (needMoreValues) {
        const { error: errorVals, data: dataVals } = await Http.api.rpc("ref", {
            ref: "ref_enkt_values",
            op: "read",
            limit: valuesLimit,
            offset: tempValues.length,
            filters: { code: productCode },
            fields: ['property_id', 'number', 'id', 'data']
        })
        if (!errorVals && dataVals) {
            tempValues.push(...dataVals)
        }
        needMoreValues = dataVals?.length == valuesLimit
    }

    tempValues.forEach(item => {
        const prop = itemData.properties.find(prop => prop.id == item.property_id)
        if (!prop) {
            return
        }
        item.currentName = findCurrentocaleItem(item.data)
        prop.values.push(item)
    })

    delete itemData.category.__schema__
    itemData.properties.forEach(prop => {
        delete prop.__schema__
        prop.values.forEach(val => delete val.__schema__)
        prop.values = prop.values.sort((valA, valB) => valA.number - valB.number)
    })

    itemData.properties = itemData.properties.filter(prop => prop.values.length)

    return { ...itemData }
}
