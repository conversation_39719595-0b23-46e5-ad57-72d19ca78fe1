import { Context, Config } from '@iac/kernel'
import { Http } from '@iac/core'

var dlg = Vue.Dialog({
    data: function () {
        return {
            error: undefined,
            data: undefined
        }
    },
    mounted() {
        this.$wait(async () => {
            let { error, data } = await Http.api.rpc("open_user_sockets")
            this.error = error;
            this.data = data;
        })
    },
    computed: {
        model() {
            if (!this.data)
                return;
            return this.data.reduce((acc, curr) => {
                acc[curr.peer_data_address] = acc[curr.peer_data_address] || [];
                acc[curr.peer_data_address].push(curr)

                return acc;
            }, {})
        }
    },
    template: `
<div>
    <header>{{$t('active_sessions')}}</header>
    <main class='error' v-if='error'>{{error.message}}</main>
    <main v-if='model'>
        <table style='font-size: 14px; color: #444;'>
        <template v-for='items,key of model'>
            <tr  v-for='item,index of items'>
            <td style='vertical-align: top;' v-if='!index' :rowspan='items.length'>{{key}}</td>
            <td style='vertical-align: top;'><iac-date :date='item.join_time' full icon /></td>
            <td>{{item.user_agent}}</td>
            </tr>
        </template>
        </table>
    </main>
    <footer>
        <ui-btn type='secondary' v-on:click.native='Close'>{{$t('close')}}</ui-btn>
    </footer>
</div>                
    `
})

export default {
    data: function () {
        return {
            data: undefined
        }
    },
    computed: {
        count() {
            return Context.User.user_statistics.count_active_session;
        }
    },
    methods: {
        details() {
            dlg.Modal({
                size: "lg"
            })
        }
    },
    template: `
        <div style='cursor: pointer; font-size: 14px;' v-on:click="details" v-if='$develop.content_debug && count'>{{$t('count_active_session')}}: {{count}}</div>
    `
}