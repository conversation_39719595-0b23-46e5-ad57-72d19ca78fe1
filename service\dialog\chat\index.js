import Phoenix from 'phoenix_js';
import {Http} from '@iac/core';
import { Config, Context } from '@iac/kernel';
import { addNull } from '../../components/date';
import ChatGroups from './chat-group';
import ChatMessages from './chat-messages';
import ChatForm from './chat-form';

const channelParam = {
  token: Context.User.access_token,
}

let socket;

const channels = [];

Vue.Dialog('Chat', {
  props: ['object'],
  components: {
    ChatGroups,
    ChatMessages,
    ChatForm,
  },
  data() {
    return {
      chats: {},
      message: {},
      messages: {},
      currentChatId: 0,
    }
  },
  computed: {
    groupedMessages() {
      if (this.currentChatId === 0) {
        return;
      }
      const messages = this.messages[this.currentChatId];
      if (messages === null) {
        return;
      }
      return Object.values(messages);
    }
  },
  created() {
    this.wait(async () => {
      const { id, type } = this.object;
      const { error, data } = await Http.api.rpc('get_chats', {
        'object_type':  type,
        'object_id': id,
      });

      if (error !== undefined) {
        return;
      } else if (data.length === 0) {
        return;
      }
      for (let i = 0, length = data.length; i < length; i++) {
        if(data[i].last_message){
          data[i].last_message.text_without_tags = (data[i].last_message.text || "").replace(/(\<(\/?[^>]+)>)/g, '')
        }
        this.$set(this.chats, data[i].id, { ...data[i], wasOpen: false });
      }

      let host =  Config.pp_server.replace('http://','ws://')    
      host = host.replace('https://','wss://')  

      socket = new Phoenix.Socket(host + '/socket', {
        timeout: 10000000000,
      });
      socket.connect();

      const chatsIds = Object.keys(this.chats);
      
      for (let i = 0, { length } = chatsIds; i < length; i++) {
        const id = chatsIds[i];
        this.channelJoin(id);
      }
      if (this.object.hideGroups) {
        this.switchChat(chatsIds[0]);
      }
    })
  },
  destroyed() {
    while (channels.length) {
      channels[channels.length - 1].leave(0);
      channels.pop();
    }
    if (socket) {
      socket.disconnect();
    }
  },
  methods: {
    channelJoin(id) {
      channelParam.token = Context.User.access_token;
      let channel = socket.channel(`chats:${id}`, channelParam);

      channel.join()
        .receive('ok', (data) => console.log(data, 'ok'))
        .receive('error', async ({code, reason }) => {
          if (code === 'access_denied' || reason === 'User is not authorized') {
            await Context.User.refreshToken();
            channelParam.token = Context.User.access_token;
          }
        })
        .receive('timeout', (data) => console.log(data, 'timeout'));

      channel.on('send_chat_message', async ({ id }) => {
        let { error, data } = await Http.api.rpc('get_chat_message',  {
          id
        });

        if (error !== undefined) {
          return;
        }
        if (this.chats[data.chat_id].wasOpen) {
          this.addMessage(data);
        }
        if (!this.object.hideGroups) {
          if (!this.chats[data.chat_id].last_message) {
            this.chats[data.chat_id].last_message = {};
          }
          this.chats[data.chat_id].last_message.text =  data.text;
          this.chats[data.chat_id].last_message.sent_at = data.sent_at;
        }
        if (data.chat_id !== this.currentChatId) {
          this.chats[data.chat_id].unread_messages_count++;
        } else {
          Http.api.rpc('read_all_messages', {
            id: data.chat_id,
          });
        }
      });

      channels.push(channel);
    },
    updateMessage(message) {
      this.message[this.currentChatId] = message;
    },
    async switchChat(id) {
      if (!this.chats[id].wasOpen) {
        await this.getChatHistory(id);
      }
      Http.api.rpc('read_all_messages', {
        id
      });
      this.currentChatId = id;
      const messages = this.groupedMessages.flat();
      const lastUnread = messages[messages.length - (this.chats[id].unread_messages_count || 1)];
      this.chats[id].unread_messages_count = 0;
      this.$nextTick(() => {
        if (lastUnread !== undefined && lastUnread.id !== undefined) {
          document.querySelector(`#m-${lastUnread.id}`).scrollIntoView();
        }
      })
    },
    async getChatHistory(chat_id) {
      this.$set(this.messages, chat_id, {});
      this.$set(this.message, chat_id, '');

      let { error, data } = await Http.api.rpc('get_chat_history', {
        chat_id,
        limit: -1,
      });

      if (error === undefined) {
        for (let j = data.length - 1; j >= 0; j--) {
          data[j].chat_id = chat_id;
          this.addMessage(data[j]);
        }
      }
      this.chats[chat_id].wasOpen = true;
    },
    addMessage(data) {
      if (data.user_id === Context.User.id) {
        data.own = true;
      }
      const sentDate = new Date(data.sent_at);
      const dataKey = `${sentDate.getFullYear()}${addNull(sentDate.getMonth())}${addNull(sentDate.getDate())}`;
      if (this.messages[data.chat_id][dataKey] === undefined) {
        this.$set(this.messages[data.chat_id], dataKey, []);
      }
      this.messages[data.chat_id][dataKey].push(data);
    },
  },
  template: `
    <div class='iac-dialog_modal_box-content--tender-chat'>
      <main class='iac-dialog-body--tender-chat'>
        <div class='tender-chat'>
          <chat-groups v-if='!object.hideGroups'
            :chats='chats' :current='currentChatId'
            @switch-chat='switchChat' class='tender-chat__left' />
          <div class='tender-chat__right'>
            <div class='chat-header'>
              <div class='chat-header__title'>{{ $t(object.type) }}</div>
              <div class='chat-header__number'>№{{ object.number || object.id }}</div>
            </div>
            <template v-if='currentChatId'>
              <chat-messages :groupedMessages='groupedMessages' />
              <chat-form :currentChatId='currentChatId' :message='message[currentChatId]'
                @update-message='updateMessage' />
            </template>
            <div v-else class='greeting'>
              <p class='greeting__text'>{{ $t('chat.choose') }}</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  `,
});