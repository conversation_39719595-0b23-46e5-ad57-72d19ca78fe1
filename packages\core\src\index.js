import Guid from './guid'
import Event from './event'
import Util from './util'
import Http from './http'
import Language from './language'
import Action from './action'
import Assets from './assets'
import Marked from './marked'
import ModelProvider from './model_provider'

var Core = {
    install: (Vue, args) => {
        Vue.prototype.$t = Language.t;
    }
}

Core.Assets = Assets;
Core.Util = Util;
Core.Guid = Guid;
Core.Event = Event;
Core.Http = Http;
Core.Language = Language;
Core.Action = Action;
Core.Marked = Marked;
Core.ModelProvider = ModelProvider;

export default Core;

if (typeof window !== 'undefined' && window.Vue) {
    window.Vue.use(Core)
}