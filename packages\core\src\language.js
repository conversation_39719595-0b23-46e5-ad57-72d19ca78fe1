import Event from './event'

class Dictionary {

    constructor(dictionary, provider) {
        this.provider = provider;
        this.dictionary = dictionary;
        this.lang = dictionary[""].lang;
        this.plural = new Function("n", `
            let nplurals,plural;
            ${dictionary[""].plural_forms} 
            return Number(plural)
        `);
        //this.debug = localStorage.getItem("lng_debug") == 'true' ? true : false;
    }

    get debug() {
        return this.provider.debug;
    }

    get debug_raw() {
        return this.provider.debug_raw;
    }

    t(keys, options) {
        if (!keys)
            return "";

        if (!Array.isArray(keys)) {
            keys = [keys];
        }
        let key = undefined;

        if (this.dictionary) {

            let val = undefined;// this.dictionary[key.toLocaleLowerCase()];
            for (key of keys) {
                val = this.dictionary[key.toString().toLocaleLowerCase()];
                if (val)
                    break;
            }

            let plural = 0
            if (this.plural) {
                let count = options?.count ?? 1;
                plural = this.plural(count);
            }

            let value = val?.[plural];
            if (value) {
                if(options){
                    value = value.replace(/{{(\w*?)\}}/gm,(math,p1)=>{   
                        return options[p1] || `{{${p1}}}`
                    })
                }
                if (this.debug_raw) {
                    return `[${value} | ${key}]`
                } else if (this.debug) {
                    return `_${value}_`;
                } else {
                    return value;
                }
            }
        }

        return this.debug ? `{${key}}` : key;
    }

    static async get(lng, provider) {
        lng = lng.replace('-', '_');
        if (lng == 'ru_RU@kg')
            lng = 'ru_KG'
        if (lng == 'en_US@kg')
            lng = 'en_KG'
        let response = undefined;
        try {
            // Грузим локализацию интерфейса
            response = await fetch(`${window.location.origin}/lng/${lng}.json?d=${new Date().toLocaleString()}`, {
                method: "GET",
            });
        } catch (e) {

        }
        if (!response)
            return;

        let body = await response.json();
        let dictionary = body.locale_data[body.domain]

        for (let key in dictionary) {
            if (typeof dictionary[key] == 'string')
                dictionary[key] = [dictionary[key]]
        }

        return new Dictionary(dictionary, provider);
    }
}

class Language {
    @Event onUpdate() {
        if (document) {
            document.title = this.t('hp.info.title')
        }
    }
    constructor() {
        this.local = undefined;
        this.t = this.t.bind(this);
        this.debug = false;
        this.debug_raw = false;
    }

    get dictionary() {
        return this[this.local];
    }

    get list() {
        return {
            'ru-RU': {
                title: "Ру",
                short: "ru_RU",
                socket: 'ru'
            },
            'ru-RU@kg': {
                title: "Ру",
                short: "ru_RU@kg",
                socket: 'ru_kg'
            },
            'ky-KG': {
                title: "KG",
                short: "ky_KG",
                socket: 'kg'
            },
            'en-US': {
                title: "En",
                short: "en_US",
                socket: 'en'
            },
            'en-US@kg': {
                title: "En",
                short: "en_US@kg",
                socket: 'en_kg'
            },
            'uz-UZ@cyrillic': {
                title: "Уз",
                short: "uz_UZ@cyrillic",
                socket: 'uz'
            },
            'uz-UZ@latin': {
                title: "Uz",
                short: "uz_UZ@latin",
                socket: 'uzl'
            }
        }
    }

    get _local() {
        return this.list[this.local] ? this.list[this.local].short : "ru"

    }
    get _local_socket() {
        return this.list[this.local] ? this.list[this.local].socket : "ru"

    }
    t(key, options) {
        if (this.dictionary)
            return this.dictionary.t(key, options);

        return `-${key}-`;
    }

    async set(lng) {
        this[lng] = this[lng] || await Dictionary.get(lng, this);
        this.local = lng;
        localStorage.setItem("language", this.local)
        this.onUpdate();
    }
}

let language = new Language();

export default language;