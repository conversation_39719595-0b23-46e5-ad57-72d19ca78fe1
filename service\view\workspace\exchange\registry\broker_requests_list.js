import { DataSource, RemoteStore, Query } from '@iac/data';
import { Http, Language } from '@iac/core';
import { UserDevelop, Context, ecp } from '@iac/kernel';
import { HttpExtensions } from '@iac/kernel'

const $p = Context.Access.policy;
const $t = Language.t;

const props_gen = (ctx) => (label, fetch_field_func, text_func) => {
    let r = null;

    if (typeof fetch_field_func === 'function') r = fetch_field_func(ctx)
    else r = fetch_field_func

    let text = null;
    text_func = text_func || (id => id)

    if (r) {
        text = text_func(r)
    } else {
        return null
    }

    return {
        label: $t(label),
        text: $t(text)
    }
}

const reject_dialog = async (on_close_f) => {
    return await Vue.Dialog.MessageBox.Form({
        caption: $t('reject_reason'),
        onClose: on_close_f,
        fields: [
            {
                name: "reject_reason",
                label: "!comment",
                type: "text",
                required: true,
                status: undefined,
                value: undefined
            }
        ]
    })
}

export default {
    data: function () {
        return {
            brokersDS: new DataSource({
                store: {
                    method: "company_ref",
                    ref: "broker_request_scrubbed",
                    op: "read",
                    inject: async items => {
                        if (!items) return;

                        return items.map(it => {
                            const prop = props_gen(it);

                            it['footer'] = [
                                it.broker && {
                                    computed: {
                                        model() {
                                            return { ...it.broker, title_text: `${$t('real_broker')}: ${it.broker.public_id}`};
                                        }
                                    },
                                    template: `<template-company :model='model' />`
                                },
                                it.exchanger && {
                                    computed: {
                                        model() {
                                            return { ...it.exchanger, title_text: `${$t('company')}: ${it.exchanger.company_id}`};
                                        }
                                    },
                                    template: `<template-company :model='model' />`
                                }
                            ];

                            const r = {
                                header: [$t('status'), it?.meta?.status_name || $t('deal_is_canceled')],
                                props: [
                                    {
                                        props: ["model"],
                                        template: `<div v-if='model.meta'>
                                            <label>{{ $t('broker_request.request_sended') }}</label>
                                            <div><iac-date :date="model.inserted_at" full/></div>
                                        </div>`
                                    },
                                    {
                                        props: ["model"],
                                        template: `<div v-if='model.meta && model.meta.status == "accept"'>
                                            <label>{{ model.meta.status_name }}</label>
                                            <div><iac-date :date="model.meta.accepted_at" full/></div>
                                        </div>`
                                    },
                                    {
                                        props: ["model"],
                                        template: `<div v-if='model.meta && model.meta.status == "cancel"'>
                                            <label>{{ model.meta.status_name }}</label>
                                            <div><iac-date :date="model.meta.canceled_at" full/></div>
                                        </div>`
                                    },
                                    {
                                        props: ["model"],
                                        template: `<div v-if='model.meta && model.meta.status == "torn"'>
                                            <label>{{ model.meta.status_name }}</label>
                                            <div><iac-date :date="model.meta.torned_at" full/></div>
                                        </div>`
                                    },
                                    {
                                        props: ["model"],
                                        template: `<div v-if='model.meta && model.meta.status && model.meta.status == "reject"'>
                                            <label>{{ model.meta.status_name }}</label>
                                            <div><iac-date :date="model.meta.rejected_at" full/></div>
                                        </div>`
                                    },
                                    prop('reject_reason', c => it?.meta?.reject_reason),
                                    //prop('reject_reason', c => it?.meta?.cancel_reason),
                                    //prop('reject_reason', c => it?.meta?.torn_reason),
                                ].filter(it => it),
                                ...it,
                                title: `${Language.t("request_for_broker")} №${it.id}`,
                            };

                            Object.defineProperty(r, "status_type", {
                                configurable: true,
                                enumerable: true,
                                get: () => {

                                    if (it?.meta?.status == "wait")
                                        return "warning";

                                    if (it?.meta?.status == "accept")
                                        return "success";

                                },
                            });

                            return r;
                        });
                    },
                    context: item => {
                        if (item?.meta?.status == 'wait') {
                            item['actions'] = [{
                                icon: "accept",
                                label: $t('broker_request.action.accept'),
                                hidden: () => !Context.Access.policy['exchange_broker_broker_request_list_panel_admit'],
                                handler: async () => {
                                    const dialog_result = await Vue.Dialog.MessageBox.Question({
                                        message: Language.t("question_contragent_agreement")
                                    });
                                    if (dialog_result != Vue.Dialog.MessageBox.Result.Yes) return;

                                    const ecp_res = await ecp.subscribe(JSON.stringify(item));
                                    if (!ecp_res) return;

                                    if (ecp_res.error) {
                                        return Vue.Dialog.MessageBox.Error(ecp_res.error);
                                    }

                                    let { error, data } = await Http.api.rpc("company_ref", {
                                        ref: "broker_request",
                                        op: "accept_request",
                                        pkcs7B64: ecp_res.data,
                                        request_id: item.id,
                                        company_id: item.company_id
                                    });

                                    if (error) {
                                        await Vue.Dialog.MessageBox.Error(error);
                                        return;
                                    }

                                    await Vue.Dialog.MessageBox.Success($t("bid_completed"));
                                    this.brokersDS.reload();
                                }
                            }, {
                                icon: "reject",
                                label: $t('broker_request.action.reject'),
                                hidden: () => !Context.Access.policy['exchange_broker_broker_request_list_panel_reject'],
                                handler: async () => {
                                    let { error, data } = await reject_dialog(async question_data => {
                                        const { error, data } = await ecp.subscribe(JSON.stringify(question_data));

                                        if (error) {
                                            return Vue.Dialog.MessageBox.Error(ecp_res.error);
                                        }

                                        return await Http.api.rpc("company_ref", {
                                            ref: "broker_request",
                                            op: "reject_request",
                                            request_id: item.id,
                                            company_id: item.company_id,
                                            reject_reason: question_data.reject_reason,
                                            pkcs7B64: data
                                        });
                                    });

                                    if (error) return;
                                    this.brokersDS.reload();
                                }
                            }, {
                                icon: "cancel",
                                label: $t('broker_request.action.cancel'),
                                hidden: () => !Context.Access.policy['exchange_client_broker_request_list_panel_cancel'],
                                handler: async () => {
                                    const dialog_result = await Vue.Dialog.MessageBox.Question({
                                        message: Language.t("question_cancel_deal")
                                    });
                                    if (dialog_result != Vue.Dialog.MessageBox.Result.Yes) return;

                                    let { error, data } = await Http.api.rpc("company_ref", {
                                        ref: "broker_request",
                                        op: "cancel_request",
                                        request_id: item.id,
                                        broker_company_id: item.broker_company_id,
                                        cancel_reason: "not_specified",
                                    });

                                    if (error) {
                                        await Vue.Dialog.MessageBox.Error(error);
                                        return;
                                    }

                                    await Vue.Dialog.MessageBox.Success($t("deal_canceled"));
                                    this.brokersDS.reload();
                                }
                            }];
                        }

                        return item;
                    }
                }
            })
        }
    },
    template: `
    <iac-access :access='$policy.exchange_client_broker_list_panel || $policy.exchange_broker_broker_list_panel'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.broker_requests_list')}}</li>
            </ol>
            <div class='title'>
                <h1 style='margin: 0;'>{{$t('nav.broker_requests_list')}}</h1>
            </div>
        </iac-section>

        <iac-section>
            <ui-data-view :dataSource='brokersDS'/>
        </iac-section>
    </iac-access>
    `
}
