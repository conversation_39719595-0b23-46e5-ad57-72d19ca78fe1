import ebpLangSelect from '../components/ebp_lang_select'
import ebpMultilangInput from '../components/ebp_multilang_input'
import ebpInput from '../components/ebp_input'
import ebpMultilangTreeSelect from '../components/ebp_multilang_tree_select'
import ebpMultilangTable from '../components/ebp_multilang_table'
import ebpTextarea from '../components/ebp_textarea'

import BurseProductRequestShowModel from '../models/burse_product_request.show.model'

export default Vue.Dialog({
  props: {
    currentItem: {
      type: Object,
      required: true
    }
  },
  components: {
    'ebpLangSelect': ebpLangSelect,
    'ebpMultilangInput': ebpMultilangInput,
    'ebpInput': ebpInput,
    'ebpMultilangTreeSelect': ebpMultilangTreeSelect,
    'ebpMultilangTable': ebpMultilangTable,
    'ebpTextarea': ebpTextarea
  },
  data() {
    return {
      model: new BurseProductRequestShowModel(this.currentItem)
     }
  },
  template: `
  <div>
    <header>{{$t("ebp_specification_show")}}</header>
    <main>
      <div v-if="model.motherland">{{$t('ebp_created_from')}} {{model.motherland.source}}({{model.motherland.id}})</div>
      <ebpTextarea v-if="model.message" v-model="model.message" :label="$t('ebp_comment')" :disabled="true"/>
      <ebp-lang-select v-model="model.langs" :label="$t('ebp_select_languages')" :disabled="true"/>
      <ebp-multilang-input v-model="model.name" :langs="model.langs" :label="$t('product_name')" :placeholder="$t('product_name')" :disabled="true"/>
      <div class="iac--ebp-row">
        <ebp-input v-model="model.skp" :label="$t('skp')" :placeholder="$t('skp')" :disabled="true"/>
        <ebp-input v-model="model.tnved" :label="$t('tnved')" :placeholder="$t('tnved')" :disabled="true"/>
      </div>
      <ebp-multilang-tree-select :data="model.groups" :langs="model.langs" :label="$t('group')"
       :placeholder="$t('group')" :request="model.getGroupsQuery"/>
      <ebpMultilangTable v-model="model.units" :columns="model.unitsColumnsWithoutMU"
       :langs="model.langs" :label="$t('units')"/>

      <h3>{{$t("properties")}}:</h3>
      <ebpMultilangTable v-for="(prop,index) in model.props" v-model="prop.values" :columns="model.propsColumns"
       :langs="model.langs" :label="prop.name"/>
    </main>
    <footer>
    </footer>
  </div>
    `
})
