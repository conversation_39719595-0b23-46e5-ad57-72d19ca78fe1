import { DataSource, RemoteStore, Query } from '@iac/data';
import { Http, Language } from '@iac/core';
import { UserDevelop, Context, ecp } from '@iac/kernel';

const $p = Context.Access.policy;
const $t = Language.t;

const torn_dialog = async (on_close_f) => {
    return await Vue.Dialog.MessageBox.Form({
        caption: $t('reject_reason'),
        onClose: on_close_f,
        fields: [
            {
                name: "!_",
                label: "!",
                type: "text",
                readonly: true,
                value: $t("torn_warning")
            },
            {
                name: "torn_reason",
                label: "torn_reason",
                type: "text",
                required: true,
                value: undefined,
                status: undefined
            },
        ]
    })
}

const broker_request_decline = async (id) => {
    let result = await torn_dialog(async question_data => {
        const dialog_result = await Vue.Dialog.MessageBox.Question({
          message: $t("torn_question")
        });
        if (dialog_result != Vue.Dialog.MessageBox.Result.Yes) return {};

        const ecp_res = await ecp.subscribe(JSON.stringify(question_data.torn_reason));
        if (!ecp_res) return {};

        if (ecp_res.error) {
          return {
            error: ecp_res.error
          }
        }

        const pkcs7B64 = ecp_res.data;

        return await Http.api.rpc("company_ref", {
            ref: "broker_request",
            op: "torn_request",
            request_id: id,
            pkcs7B64: pkcs7B64,
            torn_reason: question_data.torn_reason
        });
    });
    if(result != Vue.Dialog.MessageBox.Result.No)
        return result;

}

export default {
    data: function () {
        return {
            hasAccepted: false,
            brokersDS: new DataSource({
                store: {
                    method: "company_ref",
                    ref: "scrubbed_exchange_brokers",
                    inject: async items => {
                        if (!Context.User.team_id || !items) return items;

                        const result = items.map(it => {
                            return {
                                ...it,
                                actions: [
                                    // action для 'разорвать сделку'
                                    ...(it?.meta?.status === 'accept' ? [{
                                        label: "broker_list.torn",
                                        handler: async ()=>{ 
                                            let result = await broker_request_decline(it.request_id)
                                            if(result){
                                                Vue.Dialog.MessageBox.Success($t("deal_is_torn"));
                                                this.hasAccepted = false;
                                                this.brokersDS.reload();
                                            }
                                        }
                                    }] : []),
                                    // для отправки запроса брокеру:
                                    ...(!this.hasAccepted && (!it.broker_public_id || it?.meta?.status !== 'wait') && 
                                        Context.Access.policy['exchange_exchange_deal_execute'] ? [{
                                        label: "broker_list.send_request",
                                        handler: async () => {
                                            const dialog_result = await Vue.Dialog.MessageBox.Question({
                                                message: $t("broker_request.accept_form")
                                            });
                                            if (dialog_result != Vue.Dialog.MessageBox.Result.Yes) return;

                                            let { error: ecp_error, data: ecp_data } = await ecp.subscribe("Hello");
                                            if (ecp_error) {
                                                Vue.Dialog.MessageBox.Error(ecp_error);
                                                return;
                                            }

                                            let { error, data } = await Http.api.rpc("broker_request", {
                                                broker_public_id: it.broker_public_id,
                                                // broker_request: it.request_id,
                                                pkcs7B64: ecp_data,
                                            });

                                            if (error) {
                                                Vue.Dialog.MessageBox.Error(error);
                                                return;
                                            }
                                            if (data.message) { 
                                                Vue.Dialog.MessageBox.Success(data.message); 
                                            }

                                            this.brokersDS.reload();
                                        }
                                    }] : [])
                                ]
                            }
                        });

                        return result;
                    },
                    injectQuery: (params) => {
                        return params;
                    },
                    context: (context) => {
                        context.meta = context.meta || {};
                        context.is_broker = true;
                        // Проверка на наличие принятых заявок
                        if(context?.meta?.status === 'accept')
                            this.hasAccepted = true;
                        return context;
                    }
                },
                template: "template-company-brokers"
            })
        }
    },
    template: `
    <iac-access :access='$policy.exchange_broker_broker_list_panel || $policy.exchange_client_broker_list_panel'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.brokers_list')}}</li>
            </ol>
            <div class='title'>
                <h1 style='margin: 0;'>{{$t('nav.brokers_list')}}</h1>
            </div>
        </iac-section>

        <iac-section>
            <ui-data-view :dataSource='brokersDS'/>
        </iac-section>
    </iac-access>
    `
}
