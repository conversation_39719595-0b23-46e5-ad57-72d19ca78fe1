let SearchInput = {
    template: `
        <div></div>
    `
}

export var DataList = {
    name: "ui-data-list",
    props: {
        dataSource: Object,
        tile: <PERSON>olean,
        raw: <PERSON>olean,
        check: {
            type: Boolean,
            default: false
        },
        search: String,
        sync: {
            type: <PERSON>olean,
            default: true
        },
        filters: {
            type: Boolean,
            default: true
        },
        readonly: {
            type: Boolean,
            default: false            
        }
    },
    data: function () {
        return {

        }
    },
    computed: {
        list_classes() {
            return [
                "data-list-list",
                {
                    "tile": this.tile
                }
            ]
        },
        query() {
            return this.dataSource.query;
        },
        fields() {
            return this.query && this.query.fields && this.query.fields.filter((field) => {
                if (typeof field.hidden == 'function')
                    return !field.hidden();
                return !field.hidden
            })
        },
        search_fields() {
            if (this.fields) {
                return this.fields.filter(() => {
                    return true;
                })
            }
        },
        search_text() {

        }
    },
    components: {
        SearchInput: SearchInput
    },
    methods: {
        onFilter() {
            Vue.Dialog({
                props: ["fields"],
                template: `
                    <div>
                        <main>
                            <ui-layout :fields='fields' />
                        </main>
                        <footer style='background: linear-gradient(0, #FFF, #FFF0); position: sticky;bottom: 0;margin: 0;padding: 24px;pointer-events: none;'>
                            <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('Close')}}</ui-btn>
                        </footer>
                    </div>
                `
            }).Modal({
                size: "right",
                fields: this.fields
            })
        }
    },
    template: `
        <div class='ui-data-list'>
            <ui-layout-group class='data-list_content'>
                <ui-layout-group horizontal v-if='fields && fields.length>0'>

                    <ui-field v-if='fields && search' :model='query.properties[search]' />
                    <ui-btn v-if='filters && fields && fields.length>0' class='data-list_filter_button' type='secondary' v-on:click.native='onFilter'>{{$t("request.filter")}}</ui-btn>

                </ui-layout-group>
                <slot name='items'>
                    <ui-list v-bind:class='list_classes' :dataSource='dataSource' :raw='raw' :check='check' :sync='sync'>
                        <template slot='template' slot-scope='props'>
                            <slot name='template' :item='props.item'>
                                {{props.item.exp.display}}
                            </slot>
                        </template>
                        <template slot='not-found'>
                          <slot name='not-found'></slot>
                        </template>
                    </ui-list>
                </slot>
                <ui-layout-group v-if='dataSource.actions && !readonly' style='margin-top: 8px;'>
                    <ui-action buttons icon='action' :actions='dataSource.actions' />  
                </ui-layout-group>
            </ui-layout-group>

            <ui-layout-group class='data-list_filter' v-if='filters && fields && fields.length>0'>
                <ui-layout :fields='fields' />
                <ui-layout-group horizontal v-if='0'>
                <ui-btn type='secondary' v-on:click.native=''>{{$t('reset')}}</ui-btn>
                <ui-btn type='primary' v-on:click.native='dataSource.query.apply'>{{$t('apply')}}</ui-btn>
                </ui-layout-group>
                <div/>
            </ui-layout-group>
        </div>`
}