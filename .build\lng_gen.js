const fs = require('fs');
const path = require('path');
const execSync = require("child_process").execSync;

var Block = function (lines) {

    var parseForLine = function (lines, id) {
        var res = [];
        var startParse = false;
        for (var i = 0; i < lines.length; i++) {
            if (lines[i].indexOf(id) !== -1 || (startParse && lines[i].indexOf('"') == 0)) {
                res.push(lines[i]);
                startParse = true;
            } else {
                startParse = false;
            }
        }
        return res;
    }

    this.msgid = parseForLine(lines, 'msgid');
    this.msgstr = parseForLine(lines, 'msgstr');
    this.msgctx = parseForLine(lines, 'msgctx');
    this.occ = parseForLine(lines, '#:');
    this.com = parseForLine(lines, '#.');
}

Block.prototype.toStr = function () {
    var res = [];
    if (this.com.length > 0)
        res.push(this.com.join('\n'));

    if (this.occ.length > 0)
        res.push(this.occ.join('\n'));

    if (this.msgctx.length > 0)
        res.push(this.msgctx.join('\n'));

    if (this.msgid.length > 0)
        res.push(this.msgid.join('\n'));

    if (this.msgstr.length > 0)
        res.push(this.msgstr.join('\n'));

    res.push('');
    res.push('');

    return res.join('\n');
}

Block.prototype.hash = function () {

    var buildMsgid = function (arr) {
        var msgid = '';
        var record = false;
        for (var i = 0; i < arr.length; i++) {
            if (arr[i].indexOf('msgid ') >= 0) {
                record = true;
            } else if (arr[i].indexOf('msgid_plural') >= 0) {
                record = false;
            }

            if (record) {
                msgid += arr[i].replace(/\"/g, '');
            }
        }
        return msgid;
    };
    // In case of a plural block, `this.msgid` will contain both msgid and
    // msgid_plural. Extract only msgid, since it's the unique identifier.
    var msgid = this.msgid.length > 0 ? buildMsgid(this.msgid) : [];

    var strToHash = msgid + this.msgctx;

    var hash = 0, i, chr, len;
    if (strToHash.length == 0) return hash;
    for (i = 0, len = strToHash.length; i < len; i++) {
        chr = strToHash.charCodeAt(i);
        hash = ((hash << 5) - hash) + chr;
        hash |= 0; // Convert to 32bit integer
    }
    return hash;
}

Block.prototype.eq = function (b) {
    return this.hash() === b.hash();
}

Block.prototype.merge = function (other) {
    var origOccs = this.occ;
    other.occ.forEach(function (o) {
        if (origOccs.indexOf(o) === -1)
            origOccs.push(o);
    });
}

var SetOfBlocks = function (arr) {
    this.blocks = arr || [];
}

SetOfBlocks.prototype.add = function (block) {
    var duplicate = this.getDuplicate(block.hash());
    if (duplicate)
        duplicate.merge(block);
    else {
        try {
            this.blocks.push(block);
        } catch (e) {
            console.log(e);
        }
    }
}

SetOfBlocks.prototype.getDuplicate = function (hash) {
    for (var i = 0; i < this.blocks.length; i++)
        if (this.blocks[i].hash() === hash)
            return this.blocks[i];

    return;
}

SetOfBlocks.prototype.toStr = function () {
    return this.blocks.reduce(function (prev, curr) {
        return prev + curr.toStr();
    }, "");
}

SetOfBlocks.prototype.addArray = function (arr) {
    for (var i = 0; i < arr.length; i++)
        this.add(arr[i]);
}

var readBlock = function (lines) {
    var block = [];
    var line = lines.pop();
    while (line) {
        block.push(line);
        line = lines.pop();
    }
    return [block, lines];
}

var readBlocks = function (lines, blocks) {
    var blocks = [];
    while (lines.length > 0) {
        var res = readBlock(lines);
        var b = new Block(res[0]);
        blocks.push(b);
        lines = res[1];
    }
    return blocks;
}

var printBlock = function (b) {
    console.log(b.hash());
    console.log(b.toStr());
    console.log();
}

var hashCompare = function (a, b) {
    return a.hash() - b.hash();
}

var msgIdCompare = function (a, b) {
    return a.msgid.localeCompare(b.msgid);
}

var parseFile = function (data) {
    var lines = data.split(/\r?\n/).reverse();
    var blocks = readBlocks(lines).sort(hashCompare);
    return blocks;
}

var merge = function (arrays) {
    var set = new SetOfBlocks(arrays[0]);
    set.addArray(arrays[1]);

    return set;
}

var mergePotContents = function (a, b) {
    var outputData = merge([parseFile(a), parseFile(b)]);
    return outputData.toStr();
}

var writeFile = function (blocks, output) {
    console.log('');
    console.log('writefile: ' + blocks.toStr());
    var text = blocks.toStr();
    fs.writeFile(output, text);
}

var set = new SetOfBlocks();

function fromDir(startPath, filter, callback) {
    if (!fs.existsSync(startPath)) {
        return;
    }

    var files = fs.readdirSync(startPath);
    for (var i = 0; i < files.length; i++) {
        var filename = path.join(startPath, files[i]);
        var stat = fs.lstatSync(filename);
        if (stat.isDirectory()) {
            fromDir(filename, filter, callback); //recurse
        }
        else if (filter.test(filename)) callback(filename);
    };
};

let dirs = ["kernel", "packages", "service"].map((name) => {
    return path.resolve(__dirname, '..', name)
}).forEach((dir) => {
    fromDir(dir, /\.pot$/, function (file) {
        let data = fs.readFileSync(file, 'utf8')
        let blocks = parseFile(data);
        set.addArray(blocks);
    })
})

var dir = path.resolve(__dirname, '..', '.temp')
if (!fs.existsSync(dir)){
    fs.mkdirSync(dir);
}

let template = path.resolve(dir, '_template.pot')
fs.writeFileSync(template, set.toStr())

fromDir(path.join(__dirname, '..', 'language'), /\.po$/, function (poFile) {
    console.log("Making PO: " + poFile);
    execSync("msgmerge -v --backup=none --no-fuzzy-matching --update " + poFile + " " + template,
        function (err, stdout, stderr) {
            console.log(stdout);
            console.log(stderr);
            cb(err);
        });
});
