.iac-exchange-proposal-tile{
    display: flex;
    flex-direction: column;
    height: 100%;
    table {
        width: 100%;
        font-size: 14px;
        thead {
            z-index: 1;
            height: 40px;
            position: sticky;
            top: 0;
            tr {
                background: #dfdfdf;
    
                th {
                    font-weight: normal;
                    text-align: left;
                    padding: 2px 6px;
                    white-space: nowrap;
                    border-left: 1px solid #fff;
                    border-right: 1px solid #a9a9a9;
    
                    &.number {
                        text-align: right; 
                    }
    
                }
            }
        }
        tbody {
            tr {
                background: #fff;
                color: #444;
                td {
                    vertical-align: top;
                    //border-top: 1px solid #eee;
                    border-bottom: 1px solid #eee;
                    border-right: 1px solid #eee;
                    min-height: 26px;
                    height: 1px;
                    padding: 0 5px;
                    line-height: 26px;
                    text-align: left;
                    &.number {
                        text-align: right; 
                    }
                }
            }
        }
        &.striped{
            tbody tr{
                &:nth-of-type(2n+1)>* {
                    box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.05);
                    color: #212529;
                }
            }
        }
    }
}