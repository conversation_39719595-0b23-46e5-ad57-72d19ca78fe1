import { Guid, Language } from '@iac/core'

export default class Item {
    constructor(context) {
        context.contract = context.contract;
        this.context = context;
        this.class = undefined;

        this.tile = context.tile;
        this.space = context.space;

        this.group_id = context.group;

        this.path = context.path
        this.key = context.key || Guid._newGuid();

        let params = (this.meta.params) || {};
        this.params = { ...params, ...(context.params || {}) };
    }

    get required_contract(){
        if(this.group_id && this.tile == 'proposal')
        return true;
    }

    get group() {
        return this.space && this.space.groups[this.group_id - 1];
    }

    get contract() {
        if(this.group && this.group.id){
            return {
                id: this.group.id,
                product_name: this.group.product_name
            }
        }else{
            return this.context.contract;
        }
    }

    get meta() {
        return Vue.Tiling[this.tile];
    }

    get label() {
        return `${this.tile}`
    }

    get title() {
        let title = Language.t(`tile.${this.tile}`)
        if(!this.select_contract)
            return title

        let name = this.contract?.product_name && (this.contract?.product_name[Language.local] || this.contract?.product_name["ru-RU"])

        return `${title} ${this.contract?.id || ''} ${name || ''}`
    }

    get component() {
        return this.meta.component
    }

    get setting() {
        return this.meta.setting
    }

    get toolbar() {
        let general_setting = Vue.Tiling["settings"].general_setting
        return (this.setting && general_setting.show_settings) && [{
            icon: "settings", handler: async () => {
                let tile_info = this.space.node.search('settings')

                if (tile_info) {
                    tile_info.item.params = tile_info.item.params || {};
                    tile_info.item.params.active = this.tile;
                    if (!tile_info.node.setActive(tile_info.index))
                        this.space.save();
                } else {
                    let keys = Object.keys(this.space.node.child);
                    if (keys) {
                        tile_info = this.space.node.insert(keys[keys.length - 1], Item.get({
                            tile: "settings", params: { active: this.tile }
                        }), false)
                        this.space.save();
                    }
                }
            }
        }]
    }

    get set_group() {
        return this.meta.select_group && ((group,save = true) => {

            if (!group && this.group && this.group.id) {
                this.context.contract = {
                    id: this.group.id,
                    product_name: this.group.product_name
                }
            }
            if(group && !group.id && this.context.contract){
                group.product_name = this.context.contract.product_name;
                group.id = this.context.contract.id;
            }

            if(group && !group.id && !this.context.contract){
                group.id = 0;
            }

            console.log(group,this.context.contract);
            
            let search_item_group = this.group_id;
            this.group_id = group?.index;

            if(!group && search_item_group){

                if(!this.space.node.search_item_group(search_item_group)){
                    let _group = this.space.groups[search_item_group-1];  
                    _group.id = undefined;
                    _group.product_name = undefined;
                }
            }
            if(save)
            this.space.save();
        })
    }

    get select_contract() {
        return this.meta.select_contract && (async () => {
            let contract = await Vue.Dialog.SearchContract.Modal({
                size: "lg"
            })
            if (contract) {
                this.set_contract({ ...contract, product_name: contract.$product_name })
            }
        })
    }

    get set_contract(){
        return (contract) =>{
            if (this.group) {
                this.group.product_name = contract.product_name;
                this.group.id = contract.id;
            } else {
                this.context.contract = {
                    id: contract.id,
                    product_name: contract.product_name
                }
            }
            this.space.save();
        }
    }

    get set_params(){
        return (params)=>{
            this.params = this.params || {}
            for (let prop in params) {
                this.params[prop] = params[prop]
            }
            this.space.save();
        }
    }

    getStruct(args = {}) {
        return {
            tile: this.tile,
            group: this.group_id,
            contract: this.contract,
            params: this.params,
            ...args
        }
    }

    static get(context) {
        return new Item(context);
    }
}