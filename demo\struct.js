
import {Marked} from '@iac/core'
import {Entity} from '@iac/data'


var component = {
    props: ["value"],
    data: function(){
        return {
            span_value: this.value
        }
    },
    watch: {
        value: {
            immediate: true,
            async handler(value, oldValue) {
                let cur_val = this.$refs?.content?.innerText
                if(value != cur_val){
                    this.span_value = value;
                }
            }
        }
    },
    methods: {
        onInput(e){
            //this.span_value = e.target.innerText
            this.$emit("input", e.target.innerText)
        }
    },
    template: `
        <span ref='content' style='outline: none; background: rgba(254, 202, 154, 0.4); padding: 0 6px; border-bottom: 1px solid;' contentEditable='true' 
        v-on:input='onInput'>{{span_value}}</span>
    `
}

Vue.component("inline-input",component)



Marked.use({extensions:[{
    name: 'iac-field',
    level: 'inline',
    start(src){
        return src.match(/:/)?.index;
    },
    tokenizer(src, tokens){
        const rule = /^:([^:\n]+):/;
        const match = rule.exec(src);
        if (match) {
            return {                                         
              type: 'iac-field',                           
              raw: match[0],                                 
              field: this.lexer.inlineTokens(match[1].trim()),
            };
          }
    },
    renderer(token){

        return `
            <inline-input :value='model.properties.test.value' v-on:input='value=>model.properties.test.value=value' />
        `
        return `
            <ui-field :model='model.properties.test' />
        `
    }
}]})



class Model extends Entity {
    constructor(context){
        super(context)

    }
    props(){
        return {
            test: {
                has_del: true,
                icon: "search",
                value: "Верисоко Константин Тимофеевич",
                attr: {
                    react: true
                }
            }
        }
    }

    get template(){
        return `
## Test

**test**: {{model.test}}

        import {Blabla} from '@test/test'

        console.log(Blabla);

<ui-btn type='primary'>sss</ui-btn>

В процессе оценки :test: тендерных предложений секретарь тендерной комиссии вправе направлять участникам запросы через функционал системы для подтверждения или разъяснения той или иной информации, указанной в тендерном предложении. При получении таких запросов участникам необходимо ответить секретарю и предоставить запрашиваемую информацию
Если участники тендера представят предложения в разных валютах, суммы предложений при оценке будут пересчитаны в единую валюту по курсу Центрального банка Республики Узбекистан на момент создания процедуры и остаются неизменными до определения победителей
    

        `
    }

}


export default {
    data: function () {
        return {
            model: new Model(),
            flip: false
        }
    },
    computed: {
        component(){
            return {
                props: ["model"],
                template: `<div>${Marked.parse(this.model.template)}</div>`
            }
        }
    },
    template: `
        <div class='ui-markdown-view'>
            <ui-btn type='info' v-on:click.native='flip = !flip'>flip</ui-btn>
            <ui-layout v-if='flip' :fields='model.fields' />
            <component v-else :is='component' :model='model' :fields='model.properies' />
        </div>
    `
}