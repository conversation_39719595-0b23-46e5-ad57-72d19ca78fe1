
import { Model } from '@iac/data'
import { Event } from '@iac/core'

class Config extends Model {
    constructor(){
        super()
    }

    get client_id() {
        return this._client_id || "af36f6cbc";
    }

    get mocrm_server() {
        return localStorage.getItem("mocrm_server") || this._mocrm_server;
    }

    get api_server() {
        return localStorage.getItem("api_server") || this._api_server;
    }

    get oauth_server() {
        return localStorage.getItem("oauth_server") || this._oauth_server || this.api_server;
    }

    get auth_server() {
        return localStorage.getItem("auth_server") || this._auth_server || this.api_server;
    }

    get upload_server() {
        return localStorage.getItem("upload_server") || this._upload_server || this.api_server;
    }

    get pp_server() {
        return localStorage.getItem("pp_server") || this._pp_server || this.api_server;
    }

    set(config) {
        for (let prop in config) {
            this[`_${prop}`] = config[prop];
            if (!this[prop])
                this[prop] = config[prop];
        }
    }
}

export default new Config();