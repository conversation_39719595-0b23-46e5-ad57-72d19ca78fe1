import { DataSource, Query, ArrayStore, RemoteStore, RefStore } from '@iac/data'
import Ref from './../../ref'
import settings from '../../settings';
import { Language } from '@iac/core';

import './ref'

let area_level = () => {
    switch (settings._country) {
        case "KG":
            return 4
        default:
            return 4
    }
}

let country_code = () => {
    switch (settings._country) {
        case "KG":
            return "417"
        default:
            return "33"
    }
}

DataSource.reg("ref_template_duration", () => {
    return [0, 24, 48, 72, 120, 168, 360, 720].map((hour) => {
        let sec = hour * 60 * 60;
        if (hour == 0)
            return { id: 0, name: "unlimited" }
        if (hour <= 12)
            return { id: sec, name: hour + " " + Language.t("hour", { count: hour }) }
        return { id: sec, name: (hour / 24) + " " + Language.t("day", { count: (hour / 24) }) }
    })
});

DataSource.reg("ref_months", () => {
    return {
        store: {
            keyType: 'number', 
            data: ["Январь", "Февраль", "Март", "Апрель", "Май", "Июнь", "Июль", "Август", "Сентябрь", "Октябрь", "Ноябрь", "Декабрь"].map((month, index) => {
                return {
                    id: index,
                    name: "month_" + index
                }
            })
        }
    }
});

DataSource.reg("ref_contract_type", () => {
    return [
        { id: "1", name: "сontract_with_a_single_supplier", desc: "must_exceed_50_bcv" },
        { id: "2", name: "contract_less_than_50_bcv" },
        { id: "4", name: "other" },
    ]
})


DataSource.reg("get_roles", () => {
    return new DataSource({
        valueExp: 'id',
        store: new RemoteStore({
            key: 'id',
            method: 'get_roles',
        })
    })
});



DataSource.reg("status_tender", () => {
    return new DataSource({
        valueExp: 'code',
        store: new ArrayStore({
            key: 'code',
            data: Ref.get('status_tender')
        })
    })
});

DataSource.reg("public_status_tender", () => {
    return new DataSource({
        valueExp: 'code',
        store: new ArrayStore({
            key: 'code',
            data: Ref.get('status_tender', {
                key: "public",
                params: {
                    scope: "public"
                }
            })
        })
    })
});

DataSource.reg("status_multilot_tender", () => {
    return new DataSource({
        valueExp: 'code',
        store: new ArrayStore({
            key: 'code',
            data: Ref.get('status_tender')
        })
    })
});

DataSource.reg("participant_status_tender", () => {
    return new DataSource({
        valueExp: 'code',
        store: new ArrayStore({
            key: 'code',
            data: Ref.get('status_tender', {
                key: "participant",
                params: {
                    scope: "participant"
                }
            })
        })
    })
});



DataSource.reg("ref_insurance", () => {
    return new DataSource({
        valueExp: 'id',
        store: new ArrayStore({
            key: 'id',
            data: Ref.get('insurance')
        })
    })
});

DataSource.reg("ref_type_periodicity", () => {
    return new DataSource({
        valueExp: 'id',
        store: new ArrayStore({
            key: 'id',
            data: Ref.get('type_periodicity')
        })
    })
});

DataSource.reg("ref_type_deposit", () => {
    return new DataSource({
        valueExp: 'id',
        store: new ArrayStore({
            key: 'id',
            data: Ref.get('type_deposit')
        })
    })
});

DataSource.reg("ref_type_business", () => {
    return new DataSource({
        valueExp: 'id',
        store: new ArrayStore({
            key: 'id',
            data: Ref.get('type_business')
        })
    })
});

DataSource.reg("ref_method_marks", () => {
    return new DataSource({
        valueExp: 'id',
        store: new ArrayStore({
            key: 'id',
            data: Ref.get('method_marks')
        })
    })
});

DataSource.reg("ref_status_agreement_procedure", () => {
    return new DataSource({
        store: new ArrayStore({
            data: Ref.get('status_agreement_procedure')
        })
    })
});


DataSource.reg("ref_locale", () => {
    return new DataSource({
        valueExp: 'code',
        store: new ArrayStore({
            key: 'code',
            data: Ref.get('locale')
        })
    })
});


// Таможенный категории
DataSource.reg('get_company_bank_accounts', () => {
    return new DataSource({
        valueExp: 'id',
        store: new RemoteStore({
            key: 'id',
            method: 'get_company_bank_accounts',
        }),
        template: "template-company_bank_account",
    });
});

DataSource.reg('get_company_bank_accounts_struct', () => {
    return new DataSource({
        valueExp: ['id', 'name', 'bank_account', 'bank_mfo_code', 'bank_name'],
        store: new RemoteStore({
            key: 'id',
            method: 'get_company_bank_accounts',
        }),
        template: "template-company_bank_account",
    });
});

DataSource.reg('get_company_bank_accounts_billing', () => {
    return new DataSource({
        valueExp: 'id',
        store: new RemoteStore({
            key: 'id',
            method: 'get_company_bank_accounts',
            inject: (items, { skip = 0 }) => {
                if (skip == 0)
                    items.unshift({
                        id: '00000',
                        name: Language.t('no_budget'),
                        bank_account: '-',
                        bank_name: '-',
                    });
                return items;
            },
            injectQuery: (params) => {
                if (params.offset > 0) {
                    params.offset--;
                } else {
                    params.limit--;
                }
                return params;
            }
        }),
        template: "template-company_bank_account"
    });
});

DataSource.reg("ref_product", () => {
    return new DataSource({
        valueExp: 'id',
        search: 'a_q',
        query: new Query({
            a_q: {

            }
        }),
        store: new RemoteStore({
            key: 'id',
            method: 'search_light',
        })
    })
});

DataSource.reg("ref_currency", () => {
    return new DataSource({
        valueExp: 'id',
        //displayExp: 'name',
        iconExp: 'symbol',
        store: new ArrayStore({
            key: 'id',
            data: Ref.get('currency_full')
        })
        /*store: new RefStore({
            key: 'id',
            ref: 'ref_currency_full',
        })*/
    })
});



// Таможенный категории
DataSource.reg("ref_tnved", () => {
    return new DataSource({
        valueExp: 'id',
        store: new RemoteStore({
            key: 'id',
            method: 'ref_tnved',
        })
    })
});

// Категории
DataSource.reg("ref_catalog_category", () => {
    return new DataSource({
        valueExp: 'id',
        store: new RemoteStore({
            key: 'id',
            method: 'ref_catalog_category',
        })
    })
});

// Регионы
DataSource.reg("ref_country_area", () => {
    return new DataSource({
        valueExp: 'id',
        query: new Query({
            id: country_code()
        }),
        store: new RemoteStore({
            key: 'id',
            method: 'ref_area',
        })
    })
});

DataSource.reg("ref_area", () => {
    return new DataSource({
        valueExp: 'id',
        store: new RemoteStore({
            key: 'id',
            method: 'ref_area',
        })
    })
});

DataSource.reg("ref_area_", () => {
    return new DataSource({
        valueExp: ['id', 'name'],
        store: new RemoteStore({
            key: 'id',
            method: 'ref_area',
        })
    })
});

// UZ Регионы
DataSource.reg("ref_uz_area", () => {
    return new DataSource({
        valueExp: 'id',
        query: new Query({
            parent_id: country_code()
        }),
        store: new RemoteStore({
            key: 'id',
            method: 'ref_area',
            context: (context) => {
                context.has_children = false;
                return context
            }
        })
    })
});

DataSource.reg("ref_uz_area_", () => {
    return new DataSource({
        valueExp: ['id', 'name'],
        query: new Query({
            parent_id: country_code()
        }),
        store: new RemoteStore({
            key: 'id',
            method: 'ref_area',
            context: (context) => {
                context.has_children = false;
                return context
            }
        })
    })
});

DataSource.reg("ref_uz_area_tree", () => {
    return new DataSource({
        valueExp: 'id',
        query: new Query({
            parent_id: country_code()
        }),
        store: new RemoteStore({
            key: 'id',
            method: 'ref_area'
        })
    })
});

let uzAreaRemoteSore = (select = false)=>{
    return new RemoteStore({
        key: 'id',
        method: 'ref_area',
        context: (context) => {
            context.select_btn = select;
            let levels = (context.id + "").split(".");
            if (levels.length >= area_level())
                context.has_children = false;
    
            return context
        }
    })
}

DataSource.reg("ref_uz_area_lv4", () => {
    return new DataSource({
        valueExp: 'id',
        query: new Query({
            id: {
                value: country_code()
            }
        }),
        store: uzAreaRemoteSore()
    })
});

DataSource.reg("ref_area_lv4_select", () => {
    return new DataSource({
        valueExp: 'id',
        query: new Query({
            id: {
                value: country_code()
            }
        }),
        store: uzAreaRemoteSore(true)
    })
});

DataSource.reg("ref_uz_region_lv4", () => {
    return new DataSource({
        valueExp: 'id',
        query: new Query({
            parent_id: {
                value: country_code()
            }
        }),
        store: uzAreaRemoteSore()
    })
});


// Статьи расходов
DataSource.reg("ref_expense_item", () => {
    return new DataSource({
        valueExp: 'code',
        displayExp: function (item) {
            return `${item.code}: ${item.name}`;
        },
        search: true,
        store: new RefStore({
            key: 'code',
            ref: 'ref_expense_item',
        })
    })
});


// Статьи расходов
DataSource.reg("ref_expense_item_struct", () => {
    return new DataSource({
        valueExp: ['code', "name"],
        displayExp: function (item) {
            return `${item.code}: ${item.name}`;
        },
        search: true,
        store: new RefStore({
            key: 'code',
            ref: 'ref_expense_item',
        })
    })
});

DataSource.reg("ref_incoterms", () => {
    return new DataSource({
        valueExp: 'id',
        displayExp: function (item) {
            return `${item.code}: ${item.name}`;
        },
        store: new RemoteStore({
            key: 'id',
            method: 'ref_incoterms',
        })
    })
});

[{
    ref: 'get_agreement_templates',
    label: 'name'
}].forEach((item) => {
    DataSource.reg(item.ref, () => {
        return new DataSource({
            valueExp: item.key || 'id',
            displayExp: item.label || 'title',
            store: new RemoteStore({
                key: item.key || 'id',
                method: item.ref,
            })
        })
    });
})


DataSource.reg("status_contest", () => {
    return new DataSource({
        valueExp: 'code',
        store: new ArrayStore({
            key: 'code',
            data: Ref.get('status', {
                key: 'all_contest',
                params: {
                    type: "contest"
                }
            })
        })
    })
});

DataSource.reg("public_status_contest", () => {
    return new DataSource({
        valueExp: 'code',
        store: new ArrayStore({
            key: 'code',
            data: Ref.get('status', {
                key: "public_contest",
                params: {
                    scope: "public",
                    type: "contest"
                }
            })
        })
    })
});

DataSource.reg("participant_status_contest", () => {
    return new DataSource({
        valueExp: 'code',
        store: new ArrayStore({
            key: 'code',
            data: Ref.get('status', {
                key: "participant_contest",
                params: {
                    scope: "participant",
                    type: "contest"
                }
            })
        })
    })
});


// Категории товаров
DataSource.reg("ref_categories", () => {
    return new DataSource({
        search: true,
        store: new RefStore({
            ref: "ref_categories",
            context: (context) => {
                context.products = new DataSource({
                    search: true,
                    query: new Query({
                        category_id: context.id
                    }),
                    store: new RefStore({
                        ref: "ref_products",
                    })
                });
                return context;
            }
        })
    })
});

// Список товаров
DataSource.reg("ref_products", () => {
    return new DataSource({
        search: true,
        store: new RefStore({
            ref: "ref_products",
        })
    })
});

DataSource.reg("ref_country", () => {
    return new DataSource({
        search: true,
        valueExp: 'code',
        store: new RefStore({
            key: 'code',
            ref: "ref_country",
        })
    })
});

DataSource.reg("ref_country_", () => {
    return new DataSource({
        search: true,
        valueExp: ['code', 'name'],
        store: new RefStore({
            key: 'code',
            ref: "ref_country",
        })
    })
});