import { Context } from '@iac/kernel'
import { Language } from '@iac/core'

import faqBlock from './../home_page/faq'
import registriesBlock from './../home_page/registries'
import manager from "./manager"
import service from "./service"
import helpline from "./helpline"

export default [
    {
        path: '/support',
        component: {
            components: {
                faqBlock,
                registriesBlock,
                manager,
                service,
                helpline
            },
            data() {
                return {
                    user: Context.User,
                    services: [
                        {
                            title: Language.t('support_service'),
                            phoneText: '+998 78-150-0314',
                            phoneLink: '+998 78-150-0314',
                            email: '<EMAIL>'
                        },
                        {
                            title: Language.t('commission_list'),
                            phoneText: `+998 78-148-0314 ${Language.t("phone_ext")} 111`,
                            phoneLink: '+998 78-150-0314',
                            email: '<EMAIL>'
                        },
                        {
                            title: Language.t('exchange_contracts_registration'),
                            phoneText: `+998 78-148-0314 ${Language.t("phone_ext")} 112`,
                            phoneLink: '+998 78-150-0314',
                            email: '<EMAIL>'                            
                        },
                        {
                            title: Language.t('classifier_of_goods_works_services_burse'),
                            phoneText: `+998 78-148-0314 ${Language.t("phone_ext")} 113`,
                            phoneLink: '+998 78-150-0314',
                            email: '<EMAIL>'
                        },
                        {
                            title: Language.t('exchange_membership'),
                            phoneText: `+998 78-148-0314 ${Language.t("phone_ext")} 114`,
                            phoneLink: '+998 78-150-0314',
                            email: '<EMAIL>'
                        }                        
                    ]
                }
            },
            template: `
                <div>
                    <iac-section type='header'>
                        <ol class='breadcrumb'>
                            <li><router-link to='/'>{{$t('home')}}</router-link></li>
                            <li>{{$t('nav.support')}}</li>
                        </ol>
                        <div class='title'>
                            <h1>{{$t('nav.support')}}</h1>
                        </div>
                    </iac-section>
                    <iac-section class='section-block'>
                        <div class="iac-support-grid">
                            <manager v-if="user.team_id"/>
                            <service v-for="item in services" :model="item"/>
                            <!-- <helpline/> -->
                        </div>
                    </iac-section>
                    <registries-block :reg='false'/>
                    <faq-block style='background: #fff'/>
                </div>
            `
        },

    },
]