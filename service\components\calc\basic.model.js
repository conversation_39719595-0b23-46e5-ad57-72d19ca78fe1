import { Entity } from '@iac/data';
import { Language } from '@iac/core'

export default class BasicCalc extends Entity {
  constructor(currentBRV, showResult) {
    super()
    this.currentBRV = currentBRV
    this.showResult = showResult
    this.onChangeTimeout = undefined
  }

  maxCommission = { buyer: 10000, seller: 4000000 }

  uzRound(val, part = 2) {
    return Math.round(val * Math.pow(10, part)) / Math.pow(10, part)
  }

  calcTotal(...params) {
    let sum = 0
    params.forEach(val => sum += (val ?? 0))
    return sum
  }

  onChangeProperty(_data) {
    clearTimeout(this.onChangeTimeout)
    this.onChangeTimeout = setTimeout(() => {
      this.showResult()
      this.fields.forEach(field => field.status = undefined)
      this.calcResult?.()
    }, 300)
  }

  prevalidate(result) {
    const error = {
      code: 'bad_params',
      message: Language.t('some_fields_contain_errors'),
      data: [],
    }
    result.fields.forEach(({ required, value, name, type }) => {
      let message;
      if (required && value === undefined) {
        message = Language.t('required_field');
      } else if (type === 'float' && value <= 0) {
        message = Language.t('must_greater_zero');
      }
      message && error.data.push({ name, message });
    })

    return error.data.length ? error : undefined
  }

  addErrorToResult(result, name, message) {
    result.error = result.error ?? { code: 'bad_params', message: Language.t('some_fields_contain_errors'), data: [] }
    result.error.data.push({ name, message })
  }
}