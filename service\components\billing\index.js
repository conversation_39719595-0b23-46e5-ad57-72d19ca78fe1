import { Context } from '@iac/kernel';

export default {
  data() {
    return {
      user: Context.User,
    }
  },
  computed:{
    company_balance(){
      return this.user && this.user.company_balance
    }
  },
  template: `
    <div v-if='user && user.team_id && company_balance && ($policy.balance_view || $policy.balance_view_exchange)' class='account-summary'>
      
      <ui-layout-group>
          <div class='account-summary_name'>{{ $t('free_funds_short') }}:</div>
          <router-link to='/workspace/billing' class='account-summary_value link-inherit' v-for='balance in company_balance'>
            <iac-number style='color: #009AB8;' :value='balance.free_funds || 0' delimiter=' ' part='2' />  <span style='font-size: 14px; font-weight: normal; color: #989898'>{{balance.currency}}</span>
          </router-link>
          
          <div class='account-summary_name'>{{ $t('blocked_funds_short') }}:</div>
          <router-link to='/workspace/billing' class='account-summary_value link-inherit' v-for='balance in company_balance'>
            <iac-number style='color: #f73;' :value='balance.blocked_funds || 0' delimiter=' ' part='2' /> <span style='font-size: 14px; font-weight: normal; color: #989898'>{{balance.currency}}</span>
          </router-link>
          <template v-if='$settings.exchange &&  $policy.balance_debt_view'>
            <div class='account-summary_name'>{{ $t('debt_amount_short') }}:</div>
            <router-link to='/workspace/billing' class='account-summary_value link-inherit' v-for='balance in company_balance'>
              <iac-number style='color: #CC3C3C;' :value='balance.debt_amount || 0' delimiter=' ' part='2' /> <span style='font-size: 14px; font-weight: normal; color: #989898'>{{balance.currency}}</span>
            </router-link>
          </template>
    </ui-layout-group>

    </div>
  `,
};
