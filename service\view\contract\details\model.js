import { Entity } from '@iac/data';
import { Http, Event, Language } from '@iac/core';
import { Settings } from '@iac/kernel';

import RejectDlg from './_reject_dlg'
import TerminateDlg from './_terminate_dlg'
import ExecutionDlg from './_execution_dlg'
export default class Contract extends Entity {
    constructor(context = {}) {
        super(context);
        this.rights = {};

        this.update_context(context);
    }

    async update_rights(rights) {
        if (!rights) {
            const { error, data } = await this.action('get_rights');
            if (!error && data) {
                rights = data
            }
        }
        if (rights) {
            for (let name in rights) {
                if (rights[name] == 'grant') {
                    rights[name] = true;
                } else {
                    rights[name] = false;
                }
            }
        }
        this.rights = rights || {};
    }

    async update_context(context) {
        this.number = context.number;
        this.proc_type = context.proc_type;
        this.contract_name = context.contract_name;
        this.base = context.base || {};

        this.finances = context.finances;
        this.graphic = context.graphic;
        this.buyer = context.initiator || {};
        this.seller = context.contragent || {};
        this.execution = context.execution || {};

        await this.update_rights(context.rights);
    }

    async action(name, params) {
        return Contract.Action(name, this.number, params)
    }

    static async Action(name, number, params) {
        return await Http.api.rpc('contract_action', {
            action: name,
            number,
            params,
        });
    }

    props() {
        return {

            8706: {
                group: '8706',
                type: 'model',
                label: "!",
                fields: {
                    count: {
                        label: "-Количество"
                    }
                }
            },
            8703: {
                group: '8703',
                type: 'model',
                label: "!",
                fields: {
                    count: {
                        label: "-Количество"
                    }
                }
            },

            contract_name: {
                group: 'contract.page_title',
                label: '-contract.contract_name',
                type: 'static'
            },
            base: {
                group: 'contract.base',
                label: '!',
                type: "model",
                fields: {
                    procedure_link: {
                        group: '!group-/!left',
                        label: `-${this.proc_type}`,
                        type: 'link',
                        attr: {
                            text: `№ ${this.base.tender_id}`,
                            to: `/procedure/${this.base.tender_id}`
                        }
                    },
                    currency: {
                        group: '!group-/!left',
                        type: "static",
                        label: "-currency"
                    },
                    start_cost: {
                        group: '!group-/!left',
                        type: "float",
                        label: "-start_cost",
                        readonly: true
                    },
                    winner_totalcost: {
                        group: '!group-/!left',
                        type: "float",
                        label: "-winner_totalcost",
                        readonly: true
                    },
                    difference: {
                        group: '!group-/!left',
                        type: "float",
                        label: "-difference",
                        readonly: true
                    },
                    empty: {
                        group: '!group-/!right',
                        type: "static",
                        label: "!"
                    }
                }
            },
            finances: {
                label: '!',
                type: 'widget',
                group: '-contract.finance',
                widget: {
                    name: {
                        props: ['items'],
                        template: `
                            <div class='finance'>
                                <table>
                                    <tbody>
                                        <tr v-key='index' v-for='item,index in items'>
                                            <td>{{index+1}}</td>
                                            <td>{{item.name}}</td>
                                            <td><iac-number :value='item.sum' delimiter=' ' part='2'/></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        `
                    },
                    props: {
                        items: this.finances,
                    }
                }
            },
            finances_empty: {
                group: '-contract.finance',
                type: 'static',
                label: "!"
            },
            graphic: {
                label: '!',
                type: 'widget',
                group: '-contract.graphic',
                widget: {
                    name: {
                        props: ['items'],
                        template: `
                            <div class='grid'><div class='graphic'>
                                <table>
                                    <thead>
                                    <tr>
                                        <th>№</th>
                                        <th>{{$t('month')}}, {{$t('year')}}</th>
                                        <th>{{$t('summa')}}</th>
                                        <th>{{$t('expense_item_code')}}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for='(graphic,index) in items'>
                                            <td>{{index+1}}</td>
                                            <td>{{$t("month_"+graphic.month)}}, {{graphic.year}}</td>
                                            <td><iac-number :value='graphic.sum' delimiter=' ' part='2'/></td>
                                            <td>{{graphic.base}}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div></div>
                        `
                    },
                    props: {
                        items: this.graphic,
                    }

                }
            },
            spec: {
                group: '-contract.spec',
                type: 'widget',
                label: '!'
            },
            buyer: {
                type: "model",
                group: "-requisites/company.buyer",
                label: "!",
                fields: {
                    company_details: {
                        label: "!",
                        type: 'model',
                        fields: {
                            company_link: {
                                label: "!",
                                type: 'link',
                                attr: {
                                    text: this.buyer.company_details.title,
                                    to: `/company/${this.buyer.company_details.id}`,
                                    style: '-webkit-line-clamp: 3; overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; line-height: 20px; height: 60px;'
                                },
                            },
                            inn: {
                                label: '-inn',
                                type: 'static'
                            },
                            postal_address: {
                                label: '-postal_address',
                                type: 'static'
                            }
                        }
                    },
                    banking_details: {
                        type: 'entity',
                        label: "requisites_title",
                        description: "$requisites_title",
                        dataSource: 'get_company_bank_accounts',
                        has_del: true,
                        readonly: () => {
                            return !this.rights.edit_org_banking_details
                        }

                    },
                    banking_details_info: {
                        label: "!",
                        type: "model",
                        dataBind: {
                            property: 'value',
                            name: 'banking_details',
                        },
                        fields: {
                            bank_name: {
                                label: '-bank_name',
                                type: 'static'
                            },
                            bank_mfo_code: {
                                label: `-${Language.t([Settings._country+".bank_mfo_code","bank_mfo_code"])}`,
                                type: 'static'
                            },
                            bank_account: {
                                label: '-bank_account',
                                type: 'static'
                            }
                        }
                    },
                    actions: {
                        type: "action",
                        label: "!",
                        buttons: true,
                        //hidden: true,
                        actions: [
                            {
                                label: "org_confirm_income",
                                btn_type: 'primary',
                                hidden: () => {
                                    //return !this.rights.org_confirm_income 
                                }
                            },
                            {
                                label: "accept_delivery_timeout",
                                btn_type: 'secondary',
                                hidden: () => {
                                    //return !this.rights.accept_delivery_timeout 
                                }
                            },
                            {
                                label: "subscribe",
                                btn_type: 'primary',
                                hidden: () => {
                                    //return !this.rights.org_sign 
                                }
                            },
                            {
                                label: "reject_sign",
                                btn_type: 'secondary',
                                hidden: () => {
                                    //return !this.rights.org_reject_sign 
                                }
                            },
                            {
                                label: "reject",
                                btn_type: 'danger',
                                hidden: () => {
                                    //return !this.rights.org_reject 
                                }
                            }
                        ]
                    }
                }
            },
            seller: {
                type: "model",
                group: "-requisites/company.seller",
                label: "!",
                fields: {
                    company_details: {
                        label: "!",
                        type: 'model',
                        fields: {
                            company_link: {
                                label: "!",
                                type: 'widget',
                                widget: {
                                    name: 'router-link',
                                    content: this.seller.company_details.title,
                                    props: {
                                        to: `/company/${this.seller.company_details.id}`,
                                        style: '-webkit-line-clamp: 2; overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; line-height: 20px; height: 60px;'
                                    },
                                }
                            },
                            inn: {
                                label: '-inn',
                                type: 'static'
                            },
                            postal_address: {
                                label: '-postal_address',
                                type: 'static'
                            }
                        }
                    },
                    banking_details: {
                        type: 'entity',
                        label: "requisites_title",
                        description: "$requisites_title",
                        dataSource: 'get_company_bank_accounts',
                        readonly: () => {
                            return !this.rights.edit_contragent_banking_details
                        }
                    },
                    banking_details_info: {
                        label: "!",
                        type: "model",
                        dataBind: {
                            property: 'value',
                            name: 'banking_details',
                        },
                        fields: {
                            bank_name: {
                                label: '-bank_name',
                                type: 'static'
                            },
                            bank_mfo_code: {
                                label: `-${Language.t([Settings._country+".bank_mfo_code","bank_mfo_code"])}`,
                                type: 'static'
                            },
                            bank_account: {
                                label: '-bank_account',
                                type: 'static'
                            }
                        }
                    },
                    actions: {
                        type: "action",
                        label: "!",
                        buttons: true,
                        //hidden: true,
                        actions: [
                            {
                                label: "confirm_income_timeout_expired",
                                btn_type: 'secondary',
                                hidden: () => {
                                    //return !this.rights.confirm_income_timeout_expired 
                                }
                            },
                            {
                                label: "reject_without_fine",
                                btn_type: 'secondary',
                                hidden: () => {
                                    //return !this.rights.reject_without_fine 
                                }
                            },
                            {
                                label: "subscribe",
                                btn_type: 'primary',
                                hidden: () => {
                                    //return !this.rights.winner_sign 
                                }
                            },
                            {
                                label: "reject_sign",
                                btn_type: 'secondary',
                                hidden: () => {
                                    //return !this.rights.winner_reject_sign 
                                }
                            },
                            {
                                label: "reject",
                                btn_type: 'danger',
                                hidden: () => {
                                    //return !this.rights.winner_reject 
                                }
                            }
                        ]
                    }
                }
            },

            execution: {
                type: "model",
                group: "contract.execution",
                label: "!",
                fields: {
                    who: {
                        label: '-contract.initiator',
                        type: 'static',

                    }
                }
            },

            contract_actions: {
                //group: "contract.execution",
                type: "action",
                label: "!",
                buttons: true,
                actions: [
                    {
                        label: "subscribe",
                        btn_type: 'primary',
                        hidden: () => {
                            //return !this.rights.winner_sign 
                        }
                    },
                    {
                        label: "reject_sign",
                        btn_type: 'secondary',
                        hidden: () => {
                            //return !this.rights.winner_reject_sign 
                        }
                    },
                    {
                        label: "terminate_contractual_obligations",
                        btn_type: 'secondary',
                        handler: async ()=>{
                            await RejectDlg.Modal({
                                model: this
                            })
                        }
                    },
                    {
                        label: "terminate_contractual_obligations",
                        btn_type: 'secondary',
                        handler: async ()=>{
                            await TerminateDlg.Modal({
                                model: this
                            })
                        }
                    },
                    {
                        label: "complete_contractual_obligations",
                        btn_type: 'success',
                        handler: async ()=>{
                            await ExecutionDlg.Modal({
                                model: this
                            })
                        }
                    }
                ]
            },
        }
    }

    static async get(id) {
        let { error, data } = await Http.api.rpc('contract_detailed', {
            number: id,
        })
        if (error) {
            return {
                error,
            };
        }
        data = new Contract(data);
        return {
            data,
        };
    }
}