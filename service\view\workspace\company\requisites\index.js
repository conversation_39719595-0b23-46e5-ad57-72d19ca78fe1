
import {ModelProvider} from '@iac/core'

import Bank from './bank';

export default {
    data: function(){
        return {
            model: undefined,
            error: undefined
        }
    },
    computed:{
        has_edit(){
            if(!this.model)
                return false
            if(this.$settings.external_company  && this.model.external_company){
                return false
            }
            return this.$policy.company_edit;
        }
    },
    components: {
        Bank,
    },
    mounted() {
        this.$wait(async () => {
            let { data, error } = await ModelProvider['company_model'].get(this.$route.params.id)
            this.model = data;
            this.error = error

            console.log("ERROR",error)
        });
    },
    methods: {
        save() {
            this.$wait(async () => {
                let {error,data} = await this.model.save();
                if(error){
                    await Vue.Dialog.MessageBox.Error(error);
                }else{
                    //this.change_user(data.user_id)
                    //this.Close();
                    //this.$router.push('/workspace')
                }
            })
        }
    },
    template: `
        <iac-access :access='$policy.company_edit || $policy.company_view_own_requisites'>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('requisites')}}</li>
                </ol>
                <h1>{{$t('requisites')}}</h1>
            </iac-section>
            <template v-if='model'>
                <iac-section>
                    <ui-layout :readonly='!$policy.company_edit' :fields='model.fields' v-if='model' style='max-width: 750px' />
                    <ui-btn v-if='has_edit' type='primary' v-on:click.native='save'>{{$t('save')}}</ui-btn>
                </iac-section>
                <iac-section><Bank :company='model'><Bank/></iac-section>
            </template>
            <iac-section v-if='error'>
                <ui-error class='page' :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
            </iac-section>
        </iac-access>
    `
}