import User from './user'
import Access from './access'
import SysStat from './sys_stat'

export default class Context {

    static get User() {
        if (!this._user) {
            this._user = new User(this)
        }
        return this._user;
    }

    static get Access() {
        if (!this._access) {
            this._access = new Access(this)
        }
        return this._access;
    } 

    static get SysStat() {
        if (!this._sys_stat) {
            this._sys_stat = new SysStat()
        }
        return this._sys_stat;
    } 
}