import OrderBookSetting from './setting'
import Contract from '../../model/contract'


let contractBidIndicators = {
    props: ["item"],
    template: `
        <div class='indicators'>
            <template v-if='item.distribution && item.distribution.length > 0'>
                <span :class='"indicator indicator_"+distribution.start' :style='"flex: 1 1 "+(distribution.amount*100)/item.amount+"%;"' v-for='distribution in item.distribution'>
                    &nbsp
                </span>
                <span v-if='item.remains' :style='"flex: 1 1 "+(item.remains*100)/item.amount+"%;"'></span>
            </template>
            <span v-else>&nbsp;</span>
        </div>
    `
}

let contractBid = {
    props: ["bid"],
    computed: {
        start() {
            return this.bid?.list[0]?.distribution[0]?.start
        },
        title() {
            let info = this.bid.info;
            if(info && !Array.isArray(info))
                info = [info]
            if(!info)
                return;

            let result = info.map(item => {
                let _success = (item._0 || 0)+(item._1 || 0)+(item._2 || 0)+(item._3 || 0);
                return `
id: ${item.bid_id}
    _success: ${_success}
    amount: ${item.amount}`;
            });  
            
            return  `
Всего ставок: ${this.bid.list?.length || 0}
Из них ваших: ${info.length || 0}
${result.join('\n')}`
        }
    },
    components: {
        contractBidIndicators
    },
    template: `
        <div :title='title' class='bid' style='line-height: 18px;'>

            <div v-if='bid.type > 0 && bid.amount' style='flex: 0 0 20px; padding: 3px;color: rgb(25 111 50);'>
                <icon v-if='bid.type == 2'>check2</icon>
            </div>

            <div :class='"amount indicator_"+start' v-if='bid.type > 0 && bid.amount' >
                {{bid.amount}}
            </div>

            <div :class='"item item_"+item.type' :style='"flex-basis: "+(item.amount*100)/bid.amount+"%"' v-for='item,index in bid.list'>
                <contractBidIndicators :item='item' />
            </div>
        </div>
    `
}

let OrderBookComponent = {
    props: ["model"],
    data: function () {
        return {
            order_setting: Vue.Tiling["order_book"].setting,
            general_setting: Vue.Tiling["settings"].general_setting,
            contract: undefined
        }
    },
    watch: {
        "model.contract": {
            immediate: true,
            async handler(contract, oldVal) {
                if (contract && this.contract && contract.id == this.contract.id)
                    return;

                if (this.contract) {
                    this.contract.unwatch(Contract.Event_ALL)
                    this.contract = undefined
                }
                if (contract && contract.id) {
                    this.contract = Contract.get(contract)
                    this.contract.watch(Contract.Event_ALL)
                }

            },
        }
    },
    beforeDestroy() {
        setTimeout(() => {
            this.contract.unwatch(Contract.Event_ALL);
        }, 200)
    },
    computed: {
        $order_setting() {
            return this.order_setting.params
        },
        list_classes() {
            return [
                "iac-order-book",
                {
                    "show_my": this.$order_setting.zip == 3 && this.$order_setting.show_my,
                    "indicator": this.$order_setting.indicator,
                    [`indicator_${this.$order_setting.indicator}`]: this.$order_setting.indicator,
                    "indicator_start": this.$order_setting.indicator && this.$order_setting.indicator_start,
                    "start_top_fix": this.$order_setting.start_top_fix,
                }
            ]
        },
        $general_setting() {
            return this.general_setting.params
        },
        $bids() {
            return this.contract.bids;
        },
        bids() {
            if (this.$order_setting.zip == 1) {
                let current = undefined;
                return this.$bids.reduce((acc, bid) => {
                    if (!current ||
                        current.price != bid.price ||
                        (bid.type != current.type) ||
                        (bid.type == 2 && current.type == 2)) {
                        current = {
                            amount: 0,
                            price: bid.price,
                            type: bid.type,
                            list: [],
                            info: []
                        }
                        acc.push(current)
                    }
                    if (bid.type){
                        current.list.push(bid);
                        if (bid.type == 2) {
                            if (bid.info)
                                current.info.push(bid.info)
                        }
                    }
                    current.amount += bid.amount;
                    current.start_color = current.start_color || bid.color;
                    return acc;
                }, [])
            }
            if (this.$order_setting.zip == 2) {
                let current = undefined;
                return this.$bids.reduce((acc, bid) => {
                    if (!current ||
                        current.price != bid.price ||
                        (bid.type != current.type)) {
                        current = {
                            amount: 0,
                            price: bid.price,
                            type: bid.type,
                            list: [],
                            info: []
                        }
                        acc.push(current)
                    }
                    if (bid.type){
                        current.list.push(bid);
                        if (bid.type == 2) {
                            if (bid.info)
                                current.info.push(bid.info)
                        }
                    }
                    current.amount += bid.amount;
                    current.start_color = current.start_color || bid.color;
                    return acc;
                }, [])
            }
            if (this.$order_setting.zip == 3) {
                let current = undefined;
                return this.$bids.reduce((acc, bid) => {
                    if (!current || current.price != bid.price) {
                        current = {
                            amount: 0,
                            price: bid.price,
                            type: 3,
                            start_amount: 0,
                            start_color: bid.color,
                            list: [],
                            info: []
                        }
                        acc.push(current)
                    }
                    if (bid.type) {
                        if (bid.type == 2) {
                            current.type = 2
                            if (bid.info)
                                current.info.push(bid.info)
                        }

                        current.list.push(bid);
                        current.amount += bid.amount;
                    }
                    else {
                        current.start_color = current.start_color || bid.color,
                        current.start_amount += bid.amount;
                    }
                    return acc;
                }, [])
            }
            return this.$bids.reduce((acc, bid) => {
                if (bid.type) {
                    bid.list = [bid]
                    if (bid.type != 2) {
                        bid.info = [];// [bid.info]
                        
                    }
                }else{
                    bid.start_color = bid.color;    
                }
                acc.push(bid)
                return acc;

            }, []);
        }
    },
    methods: {
        dev(type) {
            switch (type) {
                case "commited":
                    this.contract.status = "commited"
                    if (!this.contract.start || this.contract.start.length <= 0)
                        this.contract.set_start([
                            { amount: 100, price: 100 }
                        ])
                    break;
                case "on_sale":
                    this.contract.status = "on_sale"
                    if (!this.contract.start || this.contract.start.length <= 0)
                        this.contract.set_start([
                            { amount: 100, price: 100 }
                        ])
                    break;
                case "closed":
                    this.contract.status = "closed"
                    //this.contract.set_start([])
                    break;
            }
        }
    },
    components: {
        contractBid,
        contractBidIndicators,
    },
    template: `
        <div title='' v-bind:class='list_classes'>
        <div v-if='$develop.content_debug' class='toolbar' style='background: #cfd6e5;    padding: 4px 8px;'>
            <ui-btn type='sm' v-on:click.native='dev("commited")'>commited</ui-btn>
            <ui-btn type='sm' v-on:click.native='dev("on_sale")'>on_sale</ui-btn>
            <ui-btn type='sm' v-on:click.native='dev("closed")'>closed</ui-btn>
        </div>
        <div v-if='!contract.status || contract.status == "closed"' style='padding: 4px 8px;flex: 1 1 auto;background: #efefef; '>
            <span style='font-size: 16px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;'>{{contract.id}}: {{$t("contract_status.closed")}}</span>
        </div>
        <template v-else-if='contract.status != "init"'>
            <div class='panel' style='display: flex; align-items: end; justify-content: space-between;padding: 4px 8px;'>
                <span style='font-size: 16px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;'>{{$t("contract_status."+contract.status)}}</span>
                <span v-if='contract.timer'><iac-timer :key='contract.status' :date='contract.timer' /></span>    
            </div>
            <div class='content thin-scroll'>
            <table :class='$general_setting.striped && "striped"'  cellspacing=0 cellpadding=0>
                <thead style='position: sticky; top: 0; z-index: 1'>
                    <tr>
                        <template v-if='contract.status == "commited"'><th style='width: 100%;'>&nbsp;&nbsp;</th></template>
                        <template v-else>
                            <th style='font-weight: normal;width: 100%; padding: 3px 3px 3px 20px; text-align: left;'>{{$t('exchange.buy')}}</th>
                        </template>
                        <th style='font-weight: normal;padding: 3px;'>{{$t('price')}}</th>
                        <th style='font-weight: normal; padding: 3px;'>{{$t('exchange.sale')}}</th>
                    </tr>
                </thead>
                <tbody class='start_info' v-if='$order_setting.start_top'>
                    <tr v-for='start in contract.start'>
                        <td style='padding: 3px; text-align: right;padding: 3px;'>{{Math.round(start.reserved*100/start.amount)}}%</td>
                        <td :class='"start_amount start_color start_color_"+start.color' style='padding: 3px;'>{{start.price}}</td>
                        <td :class='"start_amount start_color start_color_"+start.color' style='padding: 3px;'>{{start.amount}}</td>
                    </tr>
                </tbody>
                <tbody>
                <tr v-if='!$order_setting.start_top || (bid.type && bid.list && bid.list.length > 0)' v-for='bid in bids'>
                    <template v-if='contract.status == "commited"'><td style='width: 100%;'>&nbsp;&nbsp;</td></template>
                    <template v-else>
                        <td style='position: relative;'><contractBid :bid='bid' /></td>
                    </template>
                    <td style='padding: 3px;'>{{bid.price}}</td>
                    <td v-if='$order_setting.start_top'>&nbsp;</td>
                    <td v-else style='padding: 3px;' :class='"start_color start_color_"+bid.start_color'>
                        <span v-if='!bid.type'>{{bid.amount}}</span>
                        <span v-else-if='bid.start_amount'>{{bid.start_amount}}</span>
                    </td>
                </tr>
                </tbody>
            </table>
            </div>
        </template>
        </div>
    `
}

Vue.Tiling("order_book", {
    component: OrderBookComponent,
    setting: OrderBookSetting,
    select_contract: true,
    select_group: true,
    required_contract: true
})