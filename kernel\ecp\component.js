var ecpComponent = {
    name: "iac-ecp",
    props: {
        label: String,
        status: Object,
        value: Object,
        readonly: <PERSON><PERSON><PERSON>,
        disabled: <PERSON><PERSON><PERSON>,
        wait: <PERSON><PERSON><PERSON>,
        actions: Array,
        has_del: <PERSON><PERSON><PERSON>,
        required: <PERSON><PERSON><PERSON>,
        createPkcs7: {
            type: Boolean,
            default: true
        }
    },
    computed: {
        listeners: function () {
            var vm = this
            return Object.assign({},
                this.$listeners,
                {
                    input: function (value) {
                        vm.$emit('input',value);
                        vm.$emit('change',value);
                    },
                    change: function (value) {
                        vm.$emit('input',value);
                        vm.$emit('change',value);
                    }
                }
            )
        }
    },
    template: `
    <iac-ecp-manager v-if='$develop.new_ecp_develop'
        :label='label'
        :status='status'
        :value='value'
        :readonly='readonly'
        :disabled='disabled'
        :wait='wait'
        :actions='actions'
        :has_del='has_del'
        :required='required'
        :createPkcs7='createPkcs7'
        v-on="listeners"
        />
    <ui-eimzo v-else
        :label='label'
        :status='status'
        :value='value'
        :readonly='readonly'
        :disabled='disabled'
        :wait='wait'
        :actions='actions'
        :has_del='has_del'
        :required='required'
        :createPkcs7='createPkcs7'
        v-on="listeners"
        />

    `
}



Vue.component('iac-ecp', ecpComponent);

Vue.Fields.ecp = { is: 'iac-ecp' }