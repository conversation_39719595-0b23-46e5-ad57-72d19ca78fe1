import TerminalModel from './model'
import ProposalModel from './model/proposal'
import './provider'

import './tiles'

import terminalWindow from './components/terminalWindow'

var Terminal = {
    props: {

    },
    data: function () {
        return {
            maximize: false,
            proposal: ProposalModel,
            model: TerminalModel.get()
        }
    },
    computed: {
        closeSpace() {
            if (this.model.visible_items.length < 2)
                return;
            return (index) => {
                this.model.delete(index);
            }
        },
        prefix_actions(){
            let $this = this;
            return $this.model.items.map((item, index) => {
                let manager = item.manager
                return {
                    label: item.label,
                    checked: item.visible,
                    disabled: item.visible && (manager.visible_items.length <= 1),
                    on_check: (check) => {
                        manager.toggle_visible(index);
                    },
                }
            })
        },
        toolbar() {
            return [
                {
                    icon: "vscode",
                    actions: [
                        {
                            label: "Light",
                            handler: () => {
                                this.model.theme = "light"
                                this.model.save()
                            }
                        },
                        {
                            label: "Visual Studio 2022",
                            handler: () => {
                                this.model.theme = "vs_2022"
                                this.model.save()
                            }
                        }
                    ]
                },
                {
                    icon: !this.maximize ? "maximize" : "minimize",
                    handler: () => {
                        this.maximize = !this.maximize
                    }
                },

            ]
        }
    },
    mounted(){
        this.proposal.watch();
    },
    beforeDestroy() {
        setTimeout(() => {
            this.proposal.unwatch();
        }, 200)

    },
    methods: {
        onTab(index) {
            this.model.setActive(index)
        },
        addSpace() {
            this.model.add();
        },
    },
    components: {
        terminalWindow
    },
    template: `
        <iac-maximize :maximize='maximize' :class="'iac-terminal '+model.theme" style='user-select: none;'>

            <ui-layout-tab  v-if='model' name="space" v-on:tab='onTab' :active='model.active_item' :show_all='true' :toolbar='toolbar' v-on:closeItem='closeSpace' v-on:addItem='addSpace'>
                <ui-action  slot='prefix' icon='action' :actions='prefix_actions' />
                <ui-layout-group v-on:clickLabel='space.setting()'  :toolbar='space.toolbar' :status='space.status' :label='space.label' v-for='space in model.visible_items' style='filter: url();'>
                    <terminalWindow :allow='3' :window='space.node' :horizontal='true'  style='height: 100%;position: relative;' />
                </ui-layout-group>                   
            </ui-layout-tab>

        </iac-maximize>
    `
}

Vue.component("iac-terminal", Terminal);