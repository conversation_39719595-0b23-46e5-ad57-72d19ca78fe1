.ui-data-view-item {
    padding: 8px 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    >.header{
        font-size: 13px;
        display: flex;
        //justify-content: space-between;
        color: #969595;
        padding: 0px 16px;
        flex: 0 0 auto;
        min-height: 1px;
        position: relative;
        >.info{
            flex: 1 1 auto;
            display: flex;
            justify-content: space-between;
            a{
                color: #888;
                &:hover{
                    color: #666;
                }
            }
            >*:last-child{
                text-align: right;
            }
        }
        >.ui-action{
            flex: 0 0 auto;
            margin-top: -5px;
            margin-right: -16px;
        }
    }
    >.content {
        display: flex;
        //flex-wrap: wrap;
        //overflow: hidden;
        flex: 1 1 100%;
        flex-direction: column;
        
        flex-direction: row;
        align-items: flex-start;
        align-content: space-between;
        flex-wrap: wrap;

        >.body{
            //border: 1px solid #eee;
            flex: 1 1 700px;
            padding: 0px 16px;
            max-width: 100%;
            >.title{
                margin-block: 8px;
                color: #222;
                max-width: 100%;
                a{
                    color: #222;
                    &:hover {
                        color: @primary-link;
                    }
                }
                display: flex;
                >label{
                    flex: 1 1 auto;
                    word-break: break-word;
                    max-width: 100%;
                }
                >span{
                    flex:0 0 auto;
                    font-size: 13px;
                    
                    a{
                        color: @primary-link;    
                    }
                }
            }
            >.description{
                font-size: 13px;
                color: #555;
                word-break: break-word;
                >div{
                    margin-block: 8px;
                    border-left: 2px solid #F3F3F3;
                    padding-left: 8px;
                    label{
                        color: #969595;
                    }    
                }
            }
        }
        >.props{
            border-top: 1px solid #f5f5f5;
            border-left: 1px solid #f5f5f5;
            flex: 1 1 350px;
            margin: -1px 0 0 -1px;
            padding: 8px 16px;

            //display: flex;
            //flex-wrap: wrap;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            grid-gap: 8px;
            >div:not(.ui-layout-group){
                flex: 1 1 180px;
                display: flex;
                flex-wrap: wrap;
                //padding: 4px 0;
                >label{
                    font-size: 14px;
                    color: #969595;
                    flex: 1 1 150px;
                }
                >div{
                    font-size: 14px;
                    color: #333;
                    flex: 1 1 150px;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    .sub{
                        font-size: 13px;
                        color: #777;
                    }
                }
            }
        }
    }
    >.footer{
        >* {
            border-top: 1px solid #f5f5f5;
        }
    }

    border-radius: 4px;

    transition: background-color 0.2s ease-out;
    >.header{
        transition: background-color 0.2s ease-out;
    }
    >.status_text{
        transition: color 0.2s ease-out;
    }

    &.status_success{
        background-color: @alert-success-bg;
        border: 1px solid @alert-success-border;
        >.header{
            background-color: @alert-success-bg;    
        }
        .status_text{
            color: @alert-success-text;
        }
    }
    &.status_info{
        background-color: @alert-info-bg;
        border: 1px solid @alert-info-border;
        >.header{
            background-color: @alert-info-bg;    
        }
        .status_text{
            color: @alert-info-text;
        }
    }
    &.status_warning{
        background-color: @alert-warning-bg;
        border: 1px solid @alert-warning-border;
        >.header{
            background-color: @alert-warning-bg;    
        }
        .status_text{
            color: @alert-warning-text;
        }
    }
    &.status_danger{
        background-color: @alert-danger-bg;
        border: 1px solid @alert-danger-border;
        >.header{
            background-color: @alert-danger-bg;    
        }
        .status_text{
            color: @alert-danger-text;
        }
    }


}

.ui-data-view{
    display: flex;
    margin: 0 -8px;
    >.ui-data_filter{
        flex: 0 0 280px;
        min-height: 500px;
        >.content {
            //border: 1px solid #F3F3F3;
            //border-radius: 4px;
            //background: #fff;
            //height: 500px;
            //padding: 8px;
            //color: #999;
        }
        margin: 0 8px;
    }   

    >.ui-data_content{
        display: flex;
        flex-direction: column;
        flex: 1 1 auto;
        margin: 0 8px;
        >.search{
            .sliders.up{
                display: none;
            }
        }
        >.toolbar{
            display: flex;
            justify-content: space-between;
            align-items: center;
            >.sort{
                display: flex;
                margin: 0 -4px;
                flex: 1 1 100%;
                >*{
                    min-width: auto;
                    margin: 0 4px;
                }
                margin-bottom: 16px;
                align-items: center;
            }
            >.view {
                margin-bottom: 15px;
                line-height: 45px;
                flex: 0 0 auto;
                icon {
                    cursor: pointer;
                    color: #969595;

                    width: 32px;
                    height: 32px;
                    line-height: 32px;
                    text-align: center;
                    font-size: 16px;
                    border-radius: 4px;
                    &:hover{
                        background: #eee;
                    }
                    &.active {
                        background: #eee;
                        color: #666;
                    }
                }
            }
            >.search_action{
                flex: 0 0 auto; 
                margin-bottom: 16px;
                border: 1px solid #ccc;
                border-radius: 5px;
                margin-left: 5px;

                .sliders.up{
                    display: none;
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                    -webkit-user-select: none;
                    -moz-user-select: none;
                    -ms-user-select: none;
                    user-select: none;
                    line-height: 30px;
                    text-align: center;
                    color: #868e96;
                    cursor: pointer;
                    &:hover{
                        background: #eeeeee;
                        border-radius: 5px;
                    }
                }
            }
        }
        >.content >.items{
            flex: 1 1 auto;
            display: grid;
            //grid-template-columns: repeat(auto-fit);
            grid-gap: 16px;
            >.data-view-item{
                border: 1px solid #F3F3F3;
                //padding: 10px;
                box-shadow: 0px 0px 24px rgba(219, 219, 219, 0.25);
                border-radius: 4px;
                background: #fff;
                white-space:normal;
                display: block;
            }
        }
    }

    /*&.view_type_row {
        >.ui-data_content >.content >.items{
            //grid-template-columns: repeat(auto-fit);
            //grid-gap: 8px;

            .ui-data-view-item{
                >.content {
                    flex-direction: row;
                    align-items: flex-start;
                    flex-wrap: wrap;
                    >.body{
                        flex: 1 1 700px;
                    }
                    >.props{
                        flex: 1 1 350px;
                        //max-width: 500px;
                    }
                }
            }            
        } 
    } */
    &.view_type_tile {
        >.ui-data_content >.content >.items{
            grid-template-columns: repeat(auto-fill, minmax(312px, 1fr));
        } 
    }
    &.view_type_grid {
        >.ui-data_content >.content >.items{
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
        } 
    }    
    
}



.ui-data_filter{
    flex: 0 0 340px !important;
    //margin-bottom: 30px;
    //border: 1px solid #ccc;
    //padding: 10px;
    margin-left: 8px;
    margin-right: 8px;

    border-radius: 5px;
    box-sizing: border-box;

    &.border{
        max-width: 340px;
        >.content >.ui-layout-group{
            padding: 20px;
            border: 1px solid #F3F3F3;  
            border-radius: 4px;  
        }
    }

    >.content >.ui-layout-group{

        background: #fff;
        
        >.content{
            >.ui-layout-group{
                margin: 0 -20px;
                &:first-child{
                    >.label{
                        //margin-top: 0;
                    }
                }
                &:not(:last-child){
                    border-bottom: 1px solid #F3F3F3;
                }
                >.label{
                    padding: 0 20px;
                    border: none;
                    color: #969595;
                    //margin: 20px 0 8px;
                    >.title{
                        font-weight: 500;
                        font-size: 13px;
                        line-height: 16px;

                        /* identical to box height, or 123% */
                        letter-spacing: 0.05em;
                        text-transform: uppercase;
                    }
                }
                >.content{
                    padding: 0 20px;
                }
            }
        }
    }
    
    .ui-enum,
    .ui-tag{
        &.ui-control {
            border: none;
            >.container{
                margin: 0;
            }
        }
    }

    .ui-enum-tree {
        border: none;
        min-width: 0;
        >.container{
            margin: 0;
        }
        .ui-control {
            border: none;
            margin-bottom: 0;
            border-left: 1px solid #eee;
            border-radius: 0;
            margin-left: 8px;
            >.container{
                margin: 0 15px;
            }
        }
    }
}

@media (max-width:1023px) {
    .ui-data-view{
        >.ui-data_filter{
            display: none;
        }
        .search_action,
        .search {
            .sliders{
               display: none;
               &.up{
                   display: block!important;
               } 
            }
        }
    }
}


