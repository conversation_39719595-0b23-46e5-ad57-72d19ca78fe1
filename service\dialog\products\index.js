import {Guid} from '@iac/core';
import {DataSource} from '@iac/data';
Vue.Dialog('products', {
  props: {
    model: {
      title: String,
      count: Number,
      items: Object,
    },
  },
  data() {
    return {
      modelItems: undefined,
      searchText: '',
    }
  },
  mounted(){
    this.$wait(async ()=>{
      let items = DataSource.get(this.model.items);
      await items.load();
      this.modelItems = items.items.map((item)=>{
        item.key = item.key || Guid.newGuid();
        return item;
      })
    })
  },
  computed: {
    products() {
      let { searchText, modelItems: items } = this;
      if (searchText) {
        searchText = searchText.toLowerCase();
        return items.filter(({ name, product_name }) => {
          const value = name || product_name;
          return value.toLowerCase().includes(searchText);
        });
      }
      return items;
    },
  },
  methods: {
    properties(product){
      Vue.Dialog({
        props: ['model'],
        template: `
          <div>
            <header>{{model.product_name || model.name}}</header>
            <main>
              <iac-layout-static :value='model.product_properties' />
            </main>
            <footer>
              <ui-btn type='secondary' v-on:click.native='Close'>{{$t('Close')}}</ui-btn>
            </footer>
          </div>
        `
      }).Modal({
        model: product
      })
    }
  },
  template: `
    <div class='just-text just-text--sm'>
      <header>
        <h2 class='section-title mb-0'>{{ model.title }}</h2>
      </header>
      <main class='mt-4'>
        <div v-if='model.count' class='mb-16 text-gray'>{{ model.count }} {{ $t('product', { count: model.count }) }}</div>
        <ui-input v-model='searchText' label='product_name' class='mb-16 search-input' />
        <ul v-if='products' class='list'>
          <li v-for='product of products' :key='product.key'
            class='list__item mb-12 text-black'>
            <div>
              <a href='' v-if='product.product_properties' v-on:click.prevent='properties(product)'>{{ product.product_name || product.name}}</a>
              <div v-else>{{ product.product_name || product.name}}</div>

              <div v-if='product.desc' style='color: #777; font-size: 13px;'>{{ product.desc }}</div>
              <div v-if='product.price != undefined && product.amount != undefined'>
                <iac-number :value='product.price' delimiter=' ' part='0' />&nbsp;{{ product.currency }} &nbsp;&nbsp;
                <iac-number :value='product.amount' delimiter=' ' part='0' />&nbsp;<ui-ref source='ref_unit' :value='product.unit' />
              </div>
            </div>
          </li>
        </ul>
      </main>
    </div>
  `,
});
