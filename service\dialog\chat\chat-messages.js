export default {
  props: ['groupedMessages'],
  beforeUpdate() {
    const { groupedMessages } = this;
    if (groupedMessages.length === 0) {
      return;
    }
    const { messages } = this.$refs;
    const lastGroup = groupedMessages[groupedMessages.length - 1];
    const { own } = lastGroup[lastGroup.length - 1];
    if (Math.floor(messages.scrollTop + messages.clientHeight) === messages.scrollHeight || own) {
      this.$nextTick(() => {
        messages.scrollTop = messages.scrollHeight;
      });
    }
  },
  template: `
    <div class='tender-chat__messages' ref='messages'>
      <div v-for='(messages, i) in groupedMessages' :key='i'
        class='tender-chat__messages-block'>
        <div class='tender-chat__messages-date'>
          <iac-date :date='messages[0].sent_at' forGroup></iac-date>
        </div>
        <div v-for='message in messages' :key='message.id' :id='"m-" + message.id'
          class='message' :class='{"message--own": message.own}' >
          <h4 class='message__author'>{{ message.user_name }}</h4>
          <div class="message__text" v-html='message.text'></div>
          <div class="message__time">
          <iac-date :date='message.sent_at' alwaysHour></iac-date>
          </div>
        </div>
      </div>
    </div>
  `,
}
