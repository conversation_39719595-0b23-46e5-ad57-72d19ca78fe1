import Model from './../model/index'
import Core from './core'

Vue.Dialog("Procedure",{
    props: ["proc_id","actions"],
    data: function(){
        return {
            model: undefined,
            error: undefined,
        }
    },
    mounted: async function () {
        await this.update_model()
    },
    methods: {
        update_model() {
            this.$wait(async ()=>{

                let { error, data } = await Model.get(this.proc_id+"");
                this.error = error;
                this.model = data;

                if(!data)
                    return;

                if(this.actions && Array.isArray(this.actions)){
                    data.actions.forEach(action => {
                        if(this.actions.includes(action.name)){
                            let _handler = action.handler || (async ()=>{return {}});   
                            action.handler = async ()=>{
                                let response = await _handler({redirect: false});
                                if(response && response.data)
                                    this.Close();
                            } 
                        }
                    });
                }

                data.actions.push({
                    name: "close_dlg",
                    group: "close",
                    type: "secondary",
                    label: "close",
                    handler: async ()=>{
                        this.Close();
                    }    
                })

            });
        }
    },
    components: {
        Core
    },
    template: `
        <div>
            <main v-if='model'>
                <core v-if='model' :model='model' />
            </main>
            <main v-else-if='error'>
                <ui-error :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
            </main>
            <footer>

            </footer>
        </div>
    `
})