const path = require('path')
const resolve = require('rollup-plugin-node-resolve')
const babel = require('rollup-plugin-babel')
const { terser } = require('rollup-plugin-terser')
const css = require('rollup-plugin-postcss')
import image from '@rollup/plugin-image';

import serve from 'rollup-plugin-serve'
import livereload from 'rollup-plugin-livereload';

import copy from 'rollup-plugin-copy'
import html from '@open-wc/rollup-plugin-html';

import vue from 'rollup-plugin-vue';
import commonjs from 'rollup-plugin-commonjs';
import modify from 'rollup-plugin-modify'

const env = process.env.NODE_ENV;
var title = process.env.TITLE || '...';
var build_number = process.env.BUILD_NUMBER || 0;
var build_version = `${require('./../package.json').version}${build_number ? ("_" + build_number) : ''}`;
var theme_name = process.env.THEME_NAME || "default";

var wait_visibility = process.env.WAIT_VISIBILITY || false;


var build_dir = /*process.env.BUILD_DIR || */'_build'

const plugins = [
    /*modify({
        find: /template: ([`])(?:(?=(\\?))\2.)*?\1/gms,
        replace: (match, path) => {
            return match.replace(/\s+/gm,' ')
        }
    }), */
    vue(),
    css(),
    babel({
        exclude: '**/node_modules/**',
        presets: [
            ["@babel/preset-env", {
                "modules": false,
                "targets": {
                    "browsers": ["Firefox > 30"],
                    "node": "current"
                },
                "useBuiltIns": "usage",
                "corejs": 2
            }]
        ],
        "plugins": [
            //["@babel/plugin-syntax-dynamic-import"],
            ["@babel/plugin-proposal-decorators", { "legacy": true }],
            ["@babel/plugin-proposal-class-properties", { "loose": true }]
        ]
    }), commonjs(),
    resolve(),
    env === 'production' && terser(),
    //terser(),
    image()
]



export default [
    // Сервис
    {
        input: path.resolve(__dirname, '../service'),
        output: {
            file: path.resolve(__dirname, `../${build_dir}`, `js/iac.service.js`),
            format: 'umd',
            name: `iac.Service`,

            globals: {
                'vue': "Vue",
                'vue-router': "VueRouter",
                '@iac/data': "iac.Data",
                '@iac/core': "iac.Core",
                '@iac/kernel': "iac.Kernel",
                '@iac/eimzo': "iac.EImzo",
                'echarts': "echarts",
                'tns': "tns",
            }
        },
        external: ['vue', 'vue-router', '@iac/core', '@iac/data', '@iac/kernel', '@iac/eimzo', "echarts", "tns"],
        plugins,
    },
    // Ядро системы
    {
        input: path.resolve(__dirname, '../kernel'),
        output: {
            file: path.resolve(__dirname, `../${build_dir}`, `js/iac.kernel.js`),
            format: 'umd',
            name: 'iac.Kernel',
            globals: {
                'vue': "Vue",
                'vue-router': "VueRouter",
                '@iac/data': "iac.Data",
                '@iac/core': "iac.Core",
                '@iac/eimzo': "iac.EImzo",
            }
        },
        external: ['vue', 'vue-router', '@iac/core', '@iac/data', '@iac/eimzo'],
        plugins,
    },
    // Основные пакеты системы
    {
        input: path.resolve(__dirname, '../packages'),
        output: {
            intro: `const build_version = "${build_version}";`,
            file: path.resolve(__dirname, `../${build_dir}`, `js/iac.packages.js`),
            format: 'umd',
            name: 'iac',
            globals: {
                'vue': "Vue",
                'vue-router': "VueRouter",
                'marked': "marked",
            }
        },
        external: ['vue', 'vue-router', 'marked'],
        plugins,
    },
    // DEMO
    {
        input: path.resolve(__dirname, '../demo/index.js'),
        output: {
            file: path.resolve(__dirname, `../${build_dir}`, `js/iac.demo.js`),
            format: 'umd',
            name: 'iac.demo',
            globals: {
                'vue': "Vue",
                'vue-router': "VueRouter",
                '@iac/data': "iac.Data",
                '@iac/core': "iac.Core",
                '@iac/kernel': "iac.Kernel",
                '@iac/eimzo': "iac.EImzo",
                'echarts': "echarts",
                'tns': "tns",
            }
        },
        external: ['vue', 'vue-router', '@iac/core', '@iac/data', '@iac/kernel', '@iac/eimzo', "echarts", "tns"],
        plugins,
    },
    {
        input: path.resolve(__dirname, '../doc/index.js'),
        output: {
            file: path.resolve(__dirname, `../${build_dir}`, `js/iac.doc.js`),
            format: 'umd',
            name: 'iac.demo',
            globals: {
                'vue': "Vue",
                'vue-router': "VueRouter",
                '@iac/data': "iac.Data",
                '@iac/core': "iac.Core",
                '@iac/kernel': "iac.Kernel",
                '@iac/eimzo': "iac.EImzo",
                'echarts': "echarts",
                'tns': "tns",
            }
        },
        external: ['vue', 'vue-router', '@iac/core', '@iac/data', '@iac/kernel', '@iac/eimzo', "echarts", "tns"],
        plugins,
    },
    {
        input: path.resolve(__dirname, '../practice/index.js'),
        output: {
            file: path.resolve(__dirname, `../${build_dir}`, `js/iac.practice.js`),
            format: 'umd',
            name: 'iac.demo',
            globals: {
                'vue': "Vue",
                'vue-router': "VueRouter",
                '@iac/data': "iac.Data",
                '@iac/core': "iac.Core",
                '@iac/kernel': "iac.Kernel",
                '@iac/eimzo': "iac.EImzo",
                'echarts': "echarts",
                'tns': "tns",
            }
        },
        external: ['vue', 'vue-router', '@iac/core', '@iac/data', '@iac/kernel', '@iac/eimzo', "echarts", "tns"],
        plugins,
    },
    // EDIT
    {
        input: path.resolve(__dirname, '../edit/index.js'),
        output: {
            file: path.resolve(__dirname, `../${build_dir}`, `js/iac.edit.js`),
            format: 'umd',
            name: 'iac.edit',
            globals: {
                'vue': "Vue",
                'vue-router': "VueRouter",
                '@iac/data': "iac.Data",
                '@iac/core': "iac.Core",
                '@iac/kernel': "iac.Kernel",
                '@iac/eimzo': "iac.EImzo",
                'echarts': "echarts",
                'tns': "tns",
            }
        },
        external: ['vue', 'vue-router', '@iac/core', '@iac/data', '@iac/kernel', '@iac/eimzo', "echarts", "tns"],
        plugins,
    },
    // Пусковой файл
    {
        input: path.resolve(__dirname, '../service/launcher'),
        output: {
            intro: `const build_version = "${build_version}";const theme_name = "${theme_name}"; const wait_visibility = ${wait_visibility}`,
            //file: path.resolve(__dirname, `../${build_dir}`, `js/iac.launcher.js`),
            dir: path.resolve(__dirname, `../${build_dir}`),
            format: 'iife',
            exports: 'named',
            globals: {
                
            }
        },
        external: [],
        plugins: [
            copy({
                targets: [
                    { src: 'assets/js/**/*', dest: `${build_dir}/js` },
                    { src: 'assets/css/**/*', dest: `${build_dir}/css` },
                    { src: 'assets/img/**/*', dest: `${build_dir}/img` },
                    { src: 'assets/favicon.ico', dest: `${build_dir}` },
                ]
            }),
            html({
                name: 'index.html',
                inject: false,
                template: () => {
                    return `
<!DOCTYPE html>
<html>
<head>
    <base href="/" />
    <title>${title}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
</head>
<body>
    <div id="app">
        <img style="max-width: 320px;margin: 50px auto; display: none;" src="/img/logotype.svg" alt="logo" class="logo"/>            
        <div id='iac-page-preloader' class='active'></div>
        <div id='preloader-info'/>
    </div>
    
    <script src="./js/vue.min.js"></script>
    <script src="./js/vue-router.min.js"></script>
    <script src="./js/tiny-slider.min.js"></script>
    <script src="./js/marked.min.js?v=1"></script>
    <script src="./js/iac.packages.js?v=${build_version}"></script>
    <script src="./launcher.js?v=${build_version}"></script>
</body>
</html>                    
                    `
                }
            }),
            env === 'ddev' && livereload('_build'),
            ['dev','ddev'].includes(env)&&serve({
                open: env=='ddev',
                contentBase:'_build',
                historyApiFallback: true,
                host:'127.0.0.1',
                port:8080,
                onListening(server) {
                    const address = server.address()
                    const host = address.address === '::' ? 'localhost' : address.address
                    const protocol = this.https ? 'https' : 'http'
                    console.log(`Server listening at ${protocol}://${host}:${address.port}/`)
                  }
            })
        ]
    }
]