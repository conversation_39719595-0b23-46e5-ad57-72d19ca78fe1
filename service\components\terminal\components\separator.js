import Item from "../model/item";
import selectTileDlg from './_selectTile'

export default {
    props: ["index", "window"],
    data: function () {
        return {
            onDrag: false,
        }
    },
    methods: {
        dragover(event) {
            this.onDrag = true;
        },
        dragenter(event) {
            this.onDrag = true;
            return true;
        },
        async drop(event) {
            this.onDrag = false;

            let item = undefined;
            try {

                let text = event.dataTransfer.getData("text/plain");
                if (text) {
                    item = Item.get({
                        tile: "note", params: { text: text }
                    })
                }
                let tile = event.dataTransfer.getData("tile")
                if (tile) {
                    item = JSON.parse(tile);
                    if(item){
                        if(!Array.isArray(item)){
                            item = [item];
                        }
                        if(item.length > 1){
                            //item = await selectTileDlg.Modal({
                            //    tiles: item
                            //})
                        }
                        if(!item)
                            return;

                        item = item.map((i)=>{
                            return Item.get(i)
                        })

                        let _group = undefined;
                        for (let _g of this.window.space.groups) {
                            if (!_g.id && _g.id != 0){
                                _group = _g;
                                break;
                            }
                        }
                        if (_group) {
                            item.forEach((_item) => {
                                _item.set_group(_group, false);
                            })
                        }
                    }
                }

                
                let moveTail = this.window.space.manager.moveTail;
                if (!item && moveTail) {
                    
                    if(moveTail.index){
                        item = moveTail.window.items[moveTail.index]
                    }else{
                        item = moveTail.window.items;
                        item.forEach((itm,index)=>{
                            itm.active = (index == moveTail.window.active_item)
                        })
                    }
                }

            } catch (e) {
                console.log(e);
            }
            if (!item)
                return;

            this.window.insert(this.index, item, true);
        },
        dragleave(event) {
            this.onDrag = false;
        }
    },
    template: `
        <div :class='"sep "+(onDrag && "drag")' 
            v-on:drop.prevent="drop" 
            v-on:dragover.prevent="dragover" 
            v-on:dragenter.prevent='dragenter'  
            v-on:dragleave.prevent='dragleave'
            
            ></div>
    `
}