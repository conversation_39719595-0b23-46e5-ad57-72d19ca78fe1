import { DataSource, RemoteStore, Entity } from '@iac/data'
import { Http } from '@iac/core'
import { Context } from '@iac/kernel'

let hidden_edit = ()=>{
    return !Context.Access.policy['agreement_create_template']; 
}

class Template extends Entity {
    props() {
        return {
            title: {
                label: 'name.template',
                required: true,
                attr:{
                    react:true
                }
            },
        }
    }
}

export default {
    data: function () {
        return {
            dataSource: new DataSource({
                valueExp: 'id',
                displayExp: 'title',
                store: new RemoteStore({
                    method: "get_agreement_templates",
                    context: (context) => {
                        context.actions = [
                            {
                                label: 'edit',
                                hidden: hidden_edit,
                                handler: () => {
                                    this.$router.push({ path: `/workspace/agreement/template/${context.id}` })
                                }
                            },
                            {
                                label: 'delete',
                                hidden: hidden_edit,
                                handler: async () => {
                                    if (await Vue.Dialog.MessageBox.Question(this.$t('question_delete_this_record')) != Vue.Dialog.MessageBox.Result.Yes) {
                                        return
                                    }
                                    let { error, data } = await Http.api.rpc('delete_agreement_template', {
                                        id: context.id
                                    });
                                    if (error) {
                                        await Vue.Dialog.MessageBox.Error(error);
                                    } else {
                                        if (data.message)
                                            await Vue.Dialog.MessageBox.Success(data.message);
                                        this.dataSource.reload();
                                    }

                                    return {
                                        error, data
                                    }
                                }
                            }
                        ]
                        return context;
                    }
                })
            })
        }
    },
    methods: {
        edit(item) {
            this.$router.push({ path: `/workspace/agreement/template/${item.id}` })
        },
        async create() {
            await Vue.Dialog({
                data: function () {
                    return {
                        model: new Template()
                    }
                },
                methods: {
                    async create() {
                        await this.wait(async () => {
                            const { error, data } = await Http.api.rpc('create_agreement_template', {
                                title: this.model.title
                            });
                            if (error) {
                                await Vue.Dialog.MessageBox.Error(error)
                                this.model.setError(error)
                            } else {
                                this.$router.push({ path: `/workspace/agreement/template/${data.id}` })
                                this.Close()
                            }
                        })
                    }
                },
                template: `
                    <div>
                        <header>{{$t('template')}}</header>
                        <main>
                            <ui-layout :fields='model.fields'/>
                        </main>
                        <footer>
                            <ui-btn :disabled='!model.title' type='primary' v-on:click.native='create'>{{$t('Create')}}</ui-btn>    
                        </footer>
                    </div>
                `
            }).Modal();
        }
    },
    template: `
    <iac-access :access='$policy.agreement_list_templates || $policy.agreement_create_template' class='iac-templates-list'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.templates')}}</li>
            </ol>
            <div class='title'>
                <h1>{{$t('nav.templates')}}</h1>
                <div v-if='$policy.agreement_create_template'><ui-btn type='primary' v-on:click.native='create'>{{$t('Create')}}</ui-btn></div>
            </div>
        </iac-section>
        <iac-section v-if='$policy.agreement_list_templates'>
            <ui-data-tile :dataSource='dataSource'>
                <widget-template slot='template' slot-scope='props' :item='props.item' />
            </ui-data-tile>
        </iac-section>
        </iac-access>
    `
}