.iac-support-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 360px));
    grid-gap: 15px;
}

.iac-support-manager {
    background: #fff;
    padding: 16px;
    position: relative;

    >.about {
        text-align: center;

        >div:first-child {
            height: 100px;
            width: 100px;
            border-radius: 50%;
            background: #eee;
            margin: 0 auto;

            >* {
                line-height: 100px;
                vertical-align: middle;
                font-size: 40px;
                color: #009AB8;
            }
        }
    }

    >.actions {
        display: flex;

        >* {
            flex: 1 1 auto;
            margin: 8px;
        }
    }

    >.rate {
        position: absolute;
        right: 10px;
        top: 10px;
        font-size: 14px;
        line-height: 20px;
        color: #009AB8;
        cursor: pointer;
    }

}