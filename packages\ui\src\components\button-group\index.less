.ui-btn-group {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    >.ui-btn {
        position: relative;
        //float: left;
    }
}

.ui-btn-group {
    .ui-btn + .ui-btn,
    .ui-btn + .ui-btn-group,
    .ui-btn-group + .ui-btn,
    .ui-btn-group + .ui-btn-group {
      margin-left: -1px;
    }
}

.ui-btn-group > .ui-btn:not(:first-child):not(:last-child) {
    border-radius: 0;
}

.ui-btn-group > .ui-btn:first-child {
    margin-left: 0;
    &:not(:last-child) {
      .border-right-radius(0);
    }
}
.ui-btn-group > .ui-btn:last-child:not(:first-child){
  .border-left-radius(0);
}

.ui-btn-group-xs > .ui-btn { &:extend(.ui-btn-xs); }
.ui-btn-group-sm > .ui-btn { &:extend(.ui-btn-sm); }
.ui-btn-group-lg > .ui-btn { &:extend(.ui-btn-lg); }
.ui-btn-group-big > .ui-btn { &:extend(.ui-btn-big); }


.ui-btn-group-empty > .ui-btn { &:extend(.ui-btn-empty); }