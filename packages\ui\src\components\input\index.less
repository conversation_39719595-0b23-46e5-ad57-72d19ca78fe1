.ui-input{
    .sliders,
    .eye,
    .eye-off{
        font-size: 20px!important;
        cursor: pointer;
        &:hover{
            color: @brand-primary!important;
        }
    }
    &.input-error{
        border-color: #eea236;
    }
}

input[type=password]::-ms-reveal,
input[type=password]::-ms-clear
{
    display: none;
}

@keyframes shake {
    10%,
    90% {
      transform: translate3d(-1px, 0, 0);
    }
  
    20%,
    80% {
      transform: translate3d(2px, 0, 0);
    }
  
    30%,
    50%,
    70% {
      transform: translate3d(-4px, 0, 0);
    }
  
    40%,
    60% {
      transform: translate3d(4px, 0, 0);
    }
  }

  @keyframes shake_color {
      0% {
        background: #ffff;
      }
      100% {
        background: #fff0;
      }
  }
  
  .apply-shake {
    animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
  }