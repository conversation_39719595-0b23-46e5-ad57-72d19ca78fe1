export default {
  data() {
    return {
      opened: false,
    };
  },
  mounted() {
    const theme = localStorage.getItem('theme');
    if (theme !== null) {
      this.toggleTheme(theme);
    }
  },
  methods: {
    toggleOpen() {
      this.opened = !this.opened;
    },
    toggleTheme(theme) {
      const removeClass = {
        'invert': 'theme-gray',
        'gray': 'theme-invert',
      };
      if (theme === 'clear') {
        document.documentElement.classList.remove('theme-invert', 'theme-gray');
        localStorage.removeItem('theme');
        return;
      }
      document.documentElement.classList.remove(removeClass[theme]);
      document.documentElement.classList.add(`theme-${theme}`);
      localStorage.setItem('theme', theme);
    },
  },
  template: `
    <div class='visual-impared'>
        <button :title='$t("desc.visual")' class='visual-impared__dropdown' @click='toggleOpen'>
          <span class='sr-only'>{{ $t('open_visual_impared') }}</span>
          <svg xmlns='http://www.w3.org/2000/svg' width='22' height='15' viewBox='0 0 22 15'>
          <path d='M12,4.5A11.827,11.827,0,0,0,1,12a11.817,11.817,0,0,0,22,0A11.827,
          11.827,0,0,0,12,4.5ZM12,17a5,5,0,1,1,5-5A5,5,0,0,1,12,17Zm0-8a3,3,
          0,1,0,3,3A3,3,0,0,0,12,9Z'
          transform='translate(-1 -4.5)'></path>
          </svg>
        </button>
        <div class='visual-impared__dropdown-menu' v-if='opened' v-on-clickaway='toggleOpen'>
          <div class='visual-impared__overflow'>
            <div class="visual-impared__header">Visual effects</div>
              <ul class='visual-impared__colors'>
                <li>
                  <button @click='toggleTheme("clear")' class='default'>A</button>
                </li>
                <li>
                  <button @click='toggleTheme("gray")' class='gray'>A</button>
                </li>
                <li>
                  <button @click='toggleTheme("invert")' class='invert'>A</button>
              </li>
              </ul>
          </div>
        </div>
    </div>
  `,
};
