import { Http, Language } from '@iac/core'
import { Model } from '@iac/data'

const alphabetRegExpsStrings = [
    "a-zA-Z",
    "0-9",
    ".,:;?!*+%\\-<>@\\[\\]\\{\\}\\/\\\\_\\$\\#"
]
const allAlphabetRegExps = alphabetRegExpsStrings.map(string => new RegExp(`[${string}]`))//all groups needed
allAlphabetRegExps.push(new RegExp(`^[${alphabetRegExpsStrings.join('')}]+$`)) //only this symbols available

class Registration extends Model {
    props() {
        return {
            email: {
                group: "!l-/!gr1/!email",
                required: true,
                type: 'email',
                attr: {
                    autocomplete: 'off',
                    react: true
                }
            },
            password: {
                group: "!l-/!gr1/!password",
                type: "password",
                required: true,
                attr: {
                    autocomplete: 'new-password',
                    react: true
                },
                onChange(val = "") {
                    let conditions = []
                    const valLength = val?.length ?? 0
                    if (8 <= valLength && valLength <= 30) {
                        conditions.push('length')
                    }
                    
                    if(/^(?=.*[a-zA-Z])(?=.*[0-9])(?=.*[\.\,\:\;\?\!\+\%\-\<\>\@\[\]\{\}\/\_\$\#])[0-9a-zA-Z\.\,\:\;\?\!\+\%\-\<\>\@\[\]\{\}\/\_\$\#]+$/g.test(val)){
                        conditions.push('alphabet')
                    }
                    //if (allAlphabetRegExps.every(re => re.test(val))) {
                    //    conditions.push('alphabet')
                    //}
                    if (val.toUpperCase() !== val && val.toLowerCase() !== val) {
                        conditions.push('registr')
                    }

                    this.model.conditions = conditions
                    if (conditions.length === 3) {
                        this.status = { type: 'success' }
                    }
                }
            },
            confirmPassword: {
                group: "!l-/!gr1/!password",
                type: "password",
                confirm: ['Password', Language.t('repeated_password_does_not_match')],
                required: true,
                attr: {
                    react: true
                },
            },
            empty1: {
                group: "!l-",
                type: "info",
                label: " "
            },
            empty2: {
                group: "!l-",
                type: "info",
                label: " "
            },
            conditions: {
                type: "enum",
                label: "!",
                readonly: true,
                dataSource: [
                    { id: 'length', name: Language.t('the_length_of_password_should_not_exceed_30_characters_or_be_less_than_8_characters') },
                    { id: "alphabet", name: Language.t('password_must_consist_of_latin_letters_arabic_numerals_and_special_characters') },
                    { id: "registr", name: Language.t('literal_part_of_the_password_must_contain_both_lowercase_and_uppercase_capital_letters') }
                ]
            },
            agree_to_terms: {
                type: 'bool',
                value: false
            }
        }
    }

    async send() {
        let formError = { data: [] }
        if (!this.email) {
            formError.data.push({ name: "email", message: Language.t('required_field') });
        }
        if (!this.password) {
            formError.data.push({ name: "password", message: Language.t('required_field') });
        }
        if (!this.confirmPassword) {
            formError.data.push({ name: "confirmPassword", message: Language.t('required_field') });
        }
        if (this.password && this.confirmPassword != this.password) {
            formError.data.push({ name: "confirmPassword", message: Language.t('confirm_password_does_not_match') });
        }
        if (formError.data.length > 0)
            return this.setError(formError);

        const { error, data } = await Http.api.rpc("register_user", {
            email: this.email,
            password: this.password,
            agree_to_terms: this.agree_to_terms,
        });
        if (error) {
            if (!this.setError(error)) {
                await Vue.Dialog.MessageBox.Error(error);
            }
        }
        return { error, data };
    }
}

export default {
    data: function () {
        return {
            error: undefined,
            model: new Registration(),
            //message: '',
        }
    },
    computed: {
        message() {
            return sessionStorage.getItem('reg-message');
        },
        signOnDisabled() {
            const { conditions = [], email = "", password = "", confirmPassword = "", agree_to_terms = false } = this.model
            return conditions.length !== 3 || !email || !password || !agree_to_terms
        }
    },
    methods: {
        async next() {
            try {
                this.$wait(async () => {
                    let { data } = await this.model.send();
                    if (data) {
                        this.$router.push({ query: { email: 'send' } });
                        sessionStorage.setItem('reg-message', data.message);
                    }
                })
            } catch (error) {
                this.error = error;
            }
        }
    },
    template: `
        <div>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('SignOn.title')}}</li>
                </ol>
                <h1>{{$t('SignOn.title')}}</h1>
            </iac-section>
            <iac-section>
                <ui-alert v-if='$route.query.email && message' type="success">
                    {{ message }}
                </ui-alert>
                <template horizontal v-else>
                    <ui-layout-group>
                        <ui-layout-group horizontal>
                            <ui-layout class="iac-register-fields" :fields='model.fields' />
                        </ui-layout-group>
                    </ui-layout-group>
                    <ui-btn type='big primary' :disabled="signOnDisabled" v-on:click.native='next'>{{$t('SignOn')}}</ui-btn>
                </template>
            </iac-section>
        </div>
    `
}