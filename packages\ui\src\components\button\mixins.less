.button-variant(@color; @background; @border) {
    color: @color;
    background-color: @background;
    border-color: @border;
    text-decoration: none;
    &:any-link{
      color: @color;
    }
    &:hover {
      text-decoration: none;
      color: @color;
      background-color: darken(@background, 5%);
          border-color: darken(@border, 5%);
    }

    &.active{
      background-color: darken(@background, 20%);
    }

    &.disabled,
    &[disabled],
    fieldset[disabled] & {
      &:hover,
      &:focus,
      &.focus {
        background-color: @background;
            border-color: @border;
      }
    }
  
    .badge {
      color: @background;
      background-color: @color;
      padding: 1px 3px;
      border-radius: 5px;
    }
    &.ui-btn-empty{
        background: transparent;
        color: darken(@border, 5%);
        &.active{
          color: @color;
          background-color: darken(@background, 10%);
        }
        &:hover {
            color: @color;
            background-color: darken(@background, 5%);
                border-color: darken(@border, 5%);
        }
    }
}

.button-size(@padding-vertical; @padding-horizontal; @font-size; @line-height; @border-radius) {
    padding: @padding-vertical @padding-horizontal;
    font-size: @font-size;
    line-height: @line-height;
    border-radius: @border-radius;
}