import { Http, Language, ModelProvider } from '@iac/core'

import { Config,Context } from '@iac/kernel'

const alphabetRegExpsStrings = [
    "a-zA-Z",
    "0-9",
    ".,:;?!*+%\\-<>@\\[\\]\\{\\}\\/\\\\_\\$\\#"
]
const allAlphabetRegExps = alphabetRegExpsStrings.map(string => new RegExp(`[${string}]`))//all groups needed
allAlphabetRegExps.push(new RegExp(`^[${alphabetRegExpsStrings.join('')}]+$`)) //only this symbols available

class AcceptFaceInvitationModel extends ModelProvider['registration_face'] {

    constructor(context){
        super(context)

    }

    props() {
        return {
            email: {
                label: "-email",
                type: "static",
                readonly: true,
                order: -1,
            },
            code: {
                label: "-code",
                type: "static",
                readonly: true,
                order: -1,
            },
        }
    }

    async send() {
        if (this.validate())
            return false

        let params = this.init_params();
        let {error: error_pkcs, data: pkcs7B64} = await this.pkcs7B64(params)
        
        if (error_pkcs) {
            await Vue.Dialog.MessageBox.Error(error_pkcs.message)
            return { error: error_pkcs }
        } else {
            params.pkcs7B64 = pkcs7B64;
            params.ecp = undefined;
        }

        let { error, data } = await Http.api.rpc("accept_face_invitation", params);
        if (error) {
            if (!this.setError(error)) {
                await Vue.Dialog.MessageBox.Error(error);
            }
        }else{
           await Context.User.set_tokens(data);
           data.message = Language.t('you_have_successfully_registered')
        }
        return { error, data };
    }
}

export default {
    data() {
        return {
            error: undefined,
            model: undefined
        }
    },
    mounted() {
        this.$wait(async ()=> {
            let { error, data } = await Http.api.rpc("validate_code", {
                email: this.$route.query.email,
                code: this.$route.query.code
            });

            if(data){
                this.model =  new AcceptFaceInvitationModel()
                this.model.email = this.$route.query.email;
                this.model.code = this.$route.query.code;
            }else {
                this.error = error;
            }

            Context.User.logOut();
    })
    },
    computed: {
        sendDisabled() {
            if (!this.model.conditions || this.model.conditions.length !== 3 || !this.model.agree_to_terms)
                return true;
            return false;
        }
    },
    methods: {
        async send() {
            this.$wait(async () => {
                const { error, data } = await this.model.send();
                if (data) {

                    Vue.Dialog.MessageBox.Success(data.message);
                    this.$router.push({
                        path: '/'
                    });
                }
            })
        },
    },
    template: `
        <div>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('accept_invitation')}}</li>
                </ol>
                <h1>{{$t('accept_invitation')}}</h1>
            </iac-section>
            <iac-section>
                <template v-if='model' horizontal>
                    <ui-layout-group>
                        <ui-layout-group horizontal style='max-width: 800px;'>
                            <ui-layout class="iac-invite-fields" :fields='model.fields' />
                        </ui-layout-group>
                    </ui-layout-group>
                    <ui-btn type='big primary' :disabled="sendDisabled" v-on:click.native='send'>{{$t('send')}}</ui-btn>
                </template>
                <ui-error v-else-if='error' :code='error.status' :message='error.message' :details='error.details' :number='error.number' /> 
            </iac-section>
        </div>
    `
}

/*
https://localhost:8080/account/accept_invitation?email=verisokokt%2B1234%40yandex.ru&code=5YFj1csvzmrLZQQMbT0FlEds8lESqTQhL0VERDW
*/