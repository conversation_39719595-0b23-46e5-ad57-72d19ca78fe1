import EditSetting from './edit'
import CalendarSetting from './calendar'
import BcvSetting from './bcv'
import Reference from './reference'
import FaqSetting from './faq'
import Banners from './banners'
import EventsSetting from './events'
import Content from './content'
import Doc from './doc'
import LocalPermissions from './local_permissions'

export default [
    {
        path: 'settings', 
        component: {
            template: `
            
                <iac-page :title='$t("nav.settings")' style='display: flex;flex-direction: column;'>

                    <iac-section v-if='0' style='flex: 0 0 auto;'>
                        <h1 style='font-size: 44px; font-weight: 500; line-height: 52px; margin: 0;'>{{ $t('nav.settings') }}</h1>
                    </iac-section>
                    <router-view  style='flex: 1 1 auto;' />
                </iac-page>
            `
        },
        children: [
            { path: 'edit', component: EditSetting },
            { path: 'calendar', component: CalendarSetting },
            { path: 'events/:view?', component: EventsSetting},
            { path: 'bcv', component: BcvSetting },
            { path: 'faq', component: FaqSetting},
            { path: 'banners', component: Banners},
            { path: 'content', component: Content},
            { path: 'local_permissions', component: LocalPermissions},
            ...Reference,
            ...Doc,
        ]
    }
]