import { Http, Action } from "@iac/core"
import { DataSource, Query, RefStore } from "@iac/data"

export default [
    {
        path: 'cart',
        component: {
            data: function () {
                return {
                    source: new DataSource({
                        query: new Query({

                        }),
                        store: new RefStore({
                            ref: 'ref_cart',
                            key: 'cart_id',
                            context: (context) => {
                                context = {id: context.ad, ...context.ad_full, cart_id: context.id, product_name: context.product_name}
                                //context.checkbox = true

                                context.actions = [
                                    {
                                        label: "delete",
                                        handler: async ()=>{
                                            let {error, data} = await Http.api.rpc("ref",{
                                                op: "delete_many",
                                                ref: "ref_cart",
                                                data: {
                                                  id: [context.cart_id]
                                                }
                                              });
                                              this.source.reload();
                                        }
                                    }
                                ]

                                return context;
                            },
                            inject: async (items) => {
                                if(!items)
                                    return items;
                                let ad_ids = items.reduce((prev, curr)=>{
                                    prev[curr.id] = prev[curr.id] || [];
                                    prev[curr.id].push(curr)
                                    return prev;
                                },{})

                                let {error, data} = await Http.api.rpc("ref",{
                                    op: "read",
                                    ref: "ref_online_shop_public",
                                    filters: {
                                      id: Object.keys(ad_ids).map((item)=>{
                                          return Number(item)
                                      })
                                    },
                                    limit: 100
                                  });
                                if(data){
                                    data.forEach(item => {
                                        let ads = ad_ids[item.id];
                                        if(ads){
                                            ads.forEach(ad_item => {
                                                for(let prop in item){
                                                    ad_item[prop] = item[prop];
                                                }
                                            });
                                        }
                                    });
                                }
                                
                                return items;
                            }
                        }),
                        actions: [
                            {
                              label: "request_price",
                              hidden: () => {
                                  if(this.source.items && this.source.items.length > 0)
                                    return false;
                                  return true;
                                //if (!this.source.checkedItems || this.source.checkedItems.length <= 0)
                                //  return true;
                              },
                              handler: async () => {
                                let response = await Action["procedure.create"]({
                                    object: {
                                        type: "cart"
                                    },
                                    type: "request"
                                });

                                this.source.reload();

                              }
                            },
                            {
                                label: "Удалить",
                                hidden: () => {
                                  //if (!this.source.checkedItems || this.source.checkedItems.length <= 0)
                                    return true;
                                },
                                handler: async () => {
                                  let {error, data} = await Http.api.rpc("ref",{
                                      op: "delete_many",
                                      ref: "ref_cart",
                                      data: {
                                        id: this.source.checkedItems
                                      }
                                    });
                                }
                              }
                          ]

                    })
                }
            },
            template: `
                <div class='page-cart'>
                    <iac-section type='header'>
                        <ol class='breadcrumb'>
                            <li><router-link to='/'>{{$t('home')}}</router-link></li>
                            <li>{{$t('nav.cart')}}</li>
                        </ol>
                        <div class='title'>
                            <h1 style='margin: 0;'>{{$t('nav.cart')}}</h1>
                        </div>
                    </iac-section>
                    <iac-section v-if='$policy.request_list_own'>
                        <ui-data-view type='tile' :dataSource='source'>
                            <widget-shop key_id='cart_id' :type='props.item.proc_key' slot='item' slot-scope='props' :item='props.item' />
                        </ui-data-view>  
                    </iac-section>
                </div>            
            `
        }
    }
]