import { Http, Language } from '@iac/core'

const loadMoreAndMore = async (method, params, callback) => {
    const limit = 90
    const tempItems = []
    let needMoreItems = true
    while (needMoreItems) {
        const { error, data = [] } = await Http.api.rpc(method, { ...params, limit, offset: tempItems.length })
        !error && data.length && tempItems.push(...(callback ? callback(data) : data))
        needMoreItems = data.length == params.limit
    }
    return tempItems
}

const ebpSearchTreeNode = {
    name: "ebpSearchTreeNode",
    components: {
        'ebpSearchTreeNode': ebpSearchTreeNode
    },
    props: {
        data: {
            type: Object,
            default: {}
        },
        openGroupEditor: {
            type: Function
        },
        emptyRootMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loadingGroups: false,
            loadingProducts: false,
            groups: [],
            products: []
        }
    },
    methods: {
        getCurrentName(name) {
            let currentName = name[Language.local]
            if (!currentName) {
                const existedNameLang = Object.keys(name).find(lang => name[lang])
                currentName = name[existedNameLang] || ""
            }
            return currentName
        },
        async getGroups(rootGroup = {}) {
            const { id: parent_id = null } = rootGroup
            this.loadingGroups = true
            this.groups = await loadMoreAndMore(
                "ref",
                {
                    ref: "ref_enkt_burse_product_groups",
                    op: "read",
                    filters: { parent_id },
                    fields: ['id', 'parent_id', 'name', 'meta', 'has_children', 'has_children_prod']
                },
                items => {
                    items.forEach(item => {
                        const { name, has_children = 0, has_children_prod = 0 } = item
                        delete item.__schema__
                        item.temporary = {
                            currentName: this.getCurrentName(name),
                            isShowable: Boolean(has_children + has_children_prod),
                            isShow: false
                        }
                    })
                    return items
                }
            )
            this.loadingGroups = false
        },
        async getProducts(rootGroup = {}) {
            const { id: group_id = null } = rootGroup
            if (this.emptyRootMode && !group_id) {
                return
            }
            this.loadingProducts = true
            this.products = await loadMoreAndMore(
                "ref",
                {
                    ref: "ref_enkt_burse_product",
                    op: "read",
                    filters: { group_id },
                    fields: ['id', 'product', 'meta']
                },
                items => {
                    items.forEach(item => {
                        delete item.__schema__
                        item.product.temporary = { currentName: this.getCurrentName(item.product.name) }
                        item.product.id = item.id
                        item.product.meta = item.meta
                        delete item.id
                        delete item.meta

                    })
                    return items
                }
            )
            this.loadingProducts = false
        },
        openEditor(group) {
            const { id: parent_id = null } = this.data
            let editedGroup = { parent_id }
            if (group) {
                editedGroup = { parent_id: group.parent_id, id: group.id, name: group.name }
            }

            const result = this.openGroupEditor?.(editedGroup)
            if (result) {
                this.loadData()
            }
        },
        setCurrentItem(item) {
            this.$emit('setCurrentItem', item)
        },
        loadData() {
            this.getGroups(this.data)
            this.getProducts(this.data)
        },
        onGroupClick(item) {
            const handler = this.openGroupEditor || this.showGroup
            handler(item)
        },
        showGroup(item) {
            item.temporary.isShowable && (item.temporary.isShow = !item.temporary.isShow)
        }
    },
    computed: {
        showGroupEdit() {
            return Boolean(this.openGroupEditor)
        }
    },
    mounted() {
        this.loadData()
    },
    template: `
    <icon v-if="loadingGroups || loadingProducts" class="iac--to-spin">spinner</icon>
    <div v-else-if="!groups.length && !products.length && !showGroupEdit">
        Nothing
    </div>
    <div class="iac--ebp-search-tree-node" v-else>
      <div class="iac--ebp-search-tree-category" v-for="(item,index) in groups" :key="index">
        <div @click="e=>showGroup(item)" :title="item.temporary.currentName">
            <span class="showable" :class="{'show': item.temporary.isShow}" v-if="item.temporary.isShowable">></span>
            <span @click.stop.prevent="e=>onGroupClick(item)">{{item.temporary.currentName}}</span>
        </div>
        <ebpSearchTreeNode v-if="item.temporary.isShow" :data="item"  @setCurrentItem="setCurrentItem" :openGroupEditor="openGroupEditor"/>
      </div>
      <div class="iac--ebp-search-tree-product" v-for="(item,index) in products" :key="index" @click="e=>setCurrentItem(item)" :title="item.product.temporary.currentName">
        <span>{{item.product.temporary.currentName}}</span>
      </div>
    </div>
    `
}

export default ebpSearchTreeNode