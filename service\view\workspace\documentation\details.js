import {Entity} from '@iac/data'
import { Http, Language } from '@iac/core'
import { Context } from '@iac/kernel'
import Model from './model'

class ModelItem extends Entity{
    constructor(context={}){
        super(context)
        this.type = context.type || 'text'
        this.uz = context.uz
        this.ru = context.ru
        this.en = context.en
    }

    props(){
        return {
            uz: {
                label: "uz",
                group: '<uz>',
                type: this.type,
                required: true,
                attr: {
                    react: true,
                    style: 'height: 100%'
                }
            },
            ru: {
                label: "ru",
                group: '<ru>',
                type: this.type,
                attr: {
                    react: true,
                    style: 'height: 100%'
                }
            },
        }
    }
}

let EditItemDlg = Vue.Dialog({
    props: ["item"],
    methods: {
        apply() {
            if(this.item.validate())
                return;
            let props = this.item.fields.reduce((arr, cur) => {
                arr[cur.name] = cur.value;
                return arr
            }, {})

            this.Close(props);
        }
    },
    template: `
<div>
    <main>
        <ui-layout :fields='item.fields' />
    </main>
    <footer>
        <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
        <ui-btn type='primary' v-on:click.native='apply()'>{{$t('apply')}}</ui-btn>
    </footer>
</div>`
})


let Documentation = {
    props: ['model'],
    methods: {  

        async edit_section(section){
            
            if(this.model.readonly)
                return;

            let _content = this.model.content[section];
            let params = await EditItemDlg.Modal({
                size: 'lg',
                item: new ModelItem({..._content?.title, type: "string"})
            })
            if(!params)
                return;

            if(!_content){
                this.model.content.push({
                    title: {...params},
                    items: []
                })
            }else{
                for(let prop in params){
                    Vue.set(_content.title,prop,params[prop])
                }
            }

            return;

        },
        async edit_item(section,item){
            if(this.model.readonly)
                return;

            let _item = this.model.content[section]?.items[item];


            let params = await EditItemDlg.Modal({
                size: 'full',
                item: new ModelItem(_item)
            })
            if(!params)
                return;

            if(!_item){
                if(!this.model.content[section].items){
                    Vue.set(this.model.content[section],"items",[])
                }  
                this.model.content[section].items.push(params)          
            }else{
                for(let prop in params){
                    Vue.set(_item,prop,params[prop])
                }
            }
        },
        async deleteItem(section, item=undefined) {

            if(!this.model.content[section])
                return;

            if (await Vue.Dialog.MessageBox.Question(Language.t("question.delete_section"), Language.t("question.title_warning")) != Vue.Dialog.MessageBox.Result.Yes) {
                return;
            }
            if(item==undefined){
                this.model.content.splice(section, 1);
                return;
            }

            this.model.content[section].items.splice(item, 1);

            

        },
        save() {
            this.$wait(async () => {
                await this.model.save();
            });
        },

    },
    template: `
        <ui-scroller  :class='model.readonly ? "readonly" : ""'>
            <table cellpadding=0 border=0 cellspacing=0>
                <tbody :key='index' v-for='section,index in model.content'>
                    
                    <tr style='font-weight: bold; text-transform: uppercase; font-size: 13px;' v-on:click='edit_section(index)'>
                        <td>{{index+1}}</td>
                        <td style='width: 50%;word-break: break-word;'>{{section.title.uz}}</td>
                        <td style='width: 50%;word-break: break-word;'>{{section.title.ru}}</td>
                        <td  v-if='!model.readonly'><ui-btn type='xs danger' v-on:click.native.stop.prevent='deleteItem(index)'><icon>trash</icon></ui-btn></td>
                    </tr>

                    <tr :key='index+"_"+sub_index' v-for='item,sub_index in section.items' v-on:click='edit_item(index,sub_index)'>
                        <td>{{index+1}}.{{sub_index+1}}</td>
                        <td style='width: 50%;word-break: break-word; position: relative;padding: 0px 2px;'>
                            <ui-markdown-view :content='item.uz || "" ' />
                        </td>
                        <td style='width: 50%;word-break: break-word; position: relative;padding: 0px 2px;'>
                            <ui-markdown-view :content='item.ru || "" ' />
                        </td>
                        <td  v-if='!model.readonly'><ui-btn type='xs danger' v-on:click.native.stop.prevent='deleteItem(index,sub_index)'><icon>trash</icon></ui-btn></td>

                    </tr>

                    <tr v-if='!model.readonly'>
                        <td></td>
                        <td colspan=3 style='background: #fff;text-align: left'><ui-btn type='xs info' v-on:click.native='edit_item(index)'>Добавить</ui-btn></td>
                    </tr>
                </tbody>
            </table>

            <template slot='footer'  style='bottom: 20px; position: sticky;' v-if='!model.readonly'>
                <ui-btn type='primary' v-on:click.native='edit_section()'>Добавить раздел</ui-btn>
                <ui-btn type='primary' v-on:click.native='save'>{{$t("save")}}</ui-btn>
            </template>
        </ui-scroller>
    `
}

export default {
    data: function () {
        return {
            version_dropdown: undefined,
            model: undefined,
            error: undefined,
        }
    },
    mounted() {
        this.update();
    },
    watch: {
        $route(to, from) {
            this.update();
        }
    },
    methods: {
        createVersion() {
            this.$wait(async () => {
                let { error, data } = await Http.api.rpc("ref", {
                    ref: "ref_documentation_pages",
                    op: "create",
                    data: {
                        type: this.model.type
                    }

                })
                if (error) {
                    return Vue.Dialog.MessageBox.Error(error);
                }
                let { id } = data
                this.$router.push({ path: `/workspace/documentation/${this.model.type}/${id}` })

            });
        },
        public() {
            if (!this.model)
                return;
            this.$wait(async () => {
                if (await this.model.public()) {
                    this.update();
                }
            });
        },
        update() {
            this.$wait(async () => {
                let { error, data } = await Model.get(this.$route.params.id, this.$route.params.version)
                this.model = data;
                this.error = error
            });
        },
        hideSelectVersion() {
            this.version_dropdown = false;
        },
        onSelectVersion({ id }) {
            this.version_dropdown = false;
            if (this.model && this.model.id == id)
                return;
            this.$router.push({ path: `/workspace/documentation/${this.model.type}/${id}`})
        },
    },
    components: {
        Documentation: Documentation
    },
    template: `
       <div>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li><router-link to='/workspace/documentation'>Закупочная документация</router-link></li>
                <li>Документация</li>
            </ol>
            <div class='title'>
                <h1 style='margin: 0;'>Документация</h1>
            </div>
        </iac-section>

        <iac-section class='sticky header' v-if='model' style='background: rgb(251 251 251); border-bottom: 1px solid #eee;'>
<div class='info' style='display: flex; align-items: center; justify-content: space-between;' >
                    <div>
                        <div style='display: inline-block;'>{{$t(model.status)}}</div>
                        <span v-if='0'>версия от:</span>  
                        <div style='display: inline-block;'>
                            <div><iac-date :date='model.updated_at' withoutTime v-on:click.native.stop.prevent='version_dropdown=!version_dropdown' style='color: #2973b3; text-decoration: underline; cursor: pointer;'/></div>

                            <div v-if='version_dropdown' style='    max-width: 500px; z-index: 10;position: absolute; min-width: 200px; max-height: 300px; overflow: auto;background: #fff;    box-shadow: 0 6px 18px 0 rgba(14, 21, 47, 0.13), 0 -2px 6px rgba(14, 21, 47, 0.03);'>
                                <ui-list v-on-clickaway="hideSelectVersion" :dataSource='model.versions' v-on:item='onSelectVersion' />
                            </div>
                        </div>
                        
                    </div>
                    <ui-btn-group>
                        <template v-if='model.status == "draft" && $policy.company_buyer_admin_cm_dp_public'>
                            <ui-btn type='primary sm' v-on:click.native='public'>{{$t("action.publish_procedure")}}</ui-btn>
                        </template>

                        <ui-btn v-else-if='model.status == "public" && $policy.company_buyer_admin_cm_dp_create'' type='primary sm' v-on:click.native='createVersion'>Добавить новую версию</ui-btn>
                    </ui-btn-group>
                    
                </div>
        </iac-section>

        <iac-section v-if='model'>
            <ui-layout :fields='model.fields' />              
            <ui-layout-group><documentation class='iac-procedure-doc' :model='model' /></ui-layout-group >

        </iac-section>

       </div>
    `
}