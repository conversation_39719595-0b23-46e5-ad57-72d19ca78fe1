import { DataSource, RemoteStore, Query } from '@iac/data';

export default Vue.Dialog({
    props: ['item'],
    data: function(){
        return {
            specifications: new DataSource({
                displayExp: 'product_name',
                query: new Query({
                    claim_id: {
                        value: this.item.id
                    }
                }),
                store: new RemoteStore({
                    method: "get_specifications_by_claim_id",
                })
            }),
            graphics: new DataSource({
                displayExp: 'year',
                query: new Query({
                    claim_id: {
                        value: this.item.id
                    }
                }),
                store: new RemoteStore({
                    method: "get_graphics_by_claim_id", 
                })
            })
        }
    },
    template: `
    <div>
        <main>№ {{item.id}}</main>
        <main>
            <ui-layout-tab>
                <ui-layout-group label='specifications'>
                    <ui-list key='specifications' :dataSource='specifications'>
                        <div slot='template' slot-scope='props' class='claim-specs'>
                            <div class='claim-spec-name'>{{props.item.product_code}} {{props.item.product_name}}</div>
                            <div class='claim-spec-desc'>{{props.item.product_description}}</div>
                            <div><span>{{$t('amount')}}</span><span><iac-number :value='props.item.count' delimiter=' ' /> <ui-ref source='ref_unit' :value='props.item.unit' /></span></div>
                            <div><span>{{$t('price')}}</span><span><iac-number :value='props.item.price' delimiter=' ' part='2'/> {{props.item.currency}}</span></div>
                            <div><span>{{$t('total')}}</span><span><iac-number :value='props.item.total_price' delimiter=' ' part='2'/> {{props.item.currency}}</span></div>
                        </div>
                    </ui-list>
                </ui-layout-group>
                <ui-layout-group label='graphics'>
                    <ui-list  key='graphics' :dataSource='graphics'>
                        <div slot='template' slot-scope='props' class='claim-specs'>
                            <div class='claim-spec-name'> <ui-ref source='ref_expense_item' :value='props.item.expense' /></div>
                            <div class='claim-spec-desc'>{{$t("month_"+props.item.month)}} {{props.item.year}}</div>
                            <div><iac-number :value='props.item.price' delimiter=' ' part='2'/>{{props.item.currency}}</div>
                        </div>
                    </ui-list>
                </ui-layout-group>                                            
            </ui-layout-tab>
        </main>
        <footer>
            <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
        </footer>
    </div>
    `
})