import Settings from './settings';
import {Event} from '@iac/core'

class FrameStorage {
    @Event onUpdate;
    constructor() {
        this._onInitResolve = undefined
        this.access_token = undefined;
        this.refresh_token = undefined;
    }
    onMessage(event) {

        try {
            let data = JSON.parse(event.data)
            if (data.type != "update_tokens")
                return;

            if((data.send & 1) != 0){
                this.access_token = data.access_token
                console.log("access_token",this.access_token)
            }
            if((data.send & 2) != 0){
                this.refresh_token = data.refresh_token
                console.log("refresh_token",this.refresh_token)
            }
            
            if((data.send & 1) != 0 && !this._onInitResolve){
                this.onUpdate();
            }

        } catch (e) {

        }

        if (this._onInitResolve) {
            this._onInitResolve();
            this._onInitResolve = undefined;
        }
    }
    init() {
        window.addEventListener("message",this.onMessage.bind(this));
        return new Promise((resolve, reject) => {
            this._onInitResolve = resolve

            var frame = document.createElement("iframe");
            frame.style = "display: none";
            document.body.insertBefore(frame, document.body.firstChild);
            frame.src = 'http://127.0.0.1:8080/auth.html'
        });
    }

}

export default new FrameStorage();