
const Component = {
  props: ["model"],
  template: `
    <ui-data-view-item :model='model'>
      <template slot='title'>
        {{model.name}}
      </template>  
      <template slot='description'>  
        <div>
          <label>{{$t('bank_name')}}:</label>
          <span>{{model.bank_name}}</span>
        </div>
        <div>
          <label>{{$t('bank_account')}}:</label>
          <span>{{model.bank_account}}</span>
        </div>
      </template>
    </ui-data-view-item>
  `
};



Vue.component("template-company_bank_account", Component)
