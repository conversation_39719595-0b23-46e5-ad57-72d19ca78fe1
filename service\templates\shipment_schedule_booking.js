import Vue from "vue";
import { Config } from '@iac/kernel'

const Component = {
    props: ["model"],
    data() {
        return {}
    },
    template: `
        <ui-data-view-item :model='model'>            
            <template slot='header'>
                <div>
                  {{model.id}}
                </div>
                <div>
                  <span><iac-date :date='model.plan_date_release' withoutTime /></span>
                </div>
            </template>
            
            <template slot='title'>
                <div>{{model.exc_pgo_items_product_name}}</div>
            </template>

            <template slot='description'>
                <div class='clamp_2' v-if="model.face_fullname">
                    <label>{{$t("shipment_creator")}}: </label>
                    <span :title='model.face_fullname'>{{ model.face_fullname }}</span>
                </div>
                <div class='clamp_2' v-if="model.face_fulltittle">
                    <label>{{ $t('company') }}: </label>
                    <span :title='model.face_fulltittle'>{{ model.face_fulltittle }}</span>
                </div>
                <div class='clamp_2'>
                    <label>{{$t("ship_booking_contract")}}: </label>
                    <span :title='model.contract_number'>{{ model.contract_number }}</span>
                </div>
                <div class='clamp_2'>
                    <label>{{ $t('amount') }}: </label>
                    <span :title='model.amount'>{{ model.amount}}</span>
                </div>
            </template>
        
        </ui-data-view-item>
    `
}

Vue.component("template-shipment_schedule_booking", Component)