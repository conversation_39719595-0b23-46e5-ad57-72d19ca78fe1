*,
*::before,
*::after {
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    padding: 0;
    margin: 0;
    background: #FCFCFC;
}

[tabindex="-1"]:focus:not(:focus-visible) {
    outline: 0 !important;
}

h1 {
    font-style: normal;
    font-weight: bold;
    font-size: 25px;
    line-height: 34px;
    margin: 25px 0;
    padding: 0;
}

h3 {
    font-size: 16px;
}

a {
    color: @primary-link;
    outline: none;
}


input:-webkit-autofill,
input:-webkit-autofill:focus {
    transition: background-color 600000s 0s, color 600000s 0s;
}

.field-type-markdown table,
.table {

    caption-side: bottom;
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    vertical-align: top;
    border-color: #dee2e6;


    tbody,
    td,
    tfoot,
    th,
    thead,
    tr {
        border-color: inherit;
        border-style: solid;
        border-width: 0;
    }

    >thead {
        vertical-align: bottom;
    }

    > :not(:first-child) {
        border-top: 2px solid currentColor;
    }

    > :not(caption)>*>* {
        padding: .5rem .5rem;
        background-color: transparent;
        border-bottom-width: 1px;
        box-shadow: inset 0 0 0 9999px transparent;
    }

    th {
        text-align: inherit;
    }

    &.table-striped>tbody>tr:nth-of-type(2n+1)>* {
        box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.05);
        color: #212529;
    }

    &.table-bordered> :not(caption)>* {
        border-width: 1px 0;
    }

    &.table-bordered> :not(caption)>*>* {
        border-width: 0 1px;
    }

}