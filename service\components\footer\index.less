.iac-service-footer{
  background-color: @footer-bg;
  font-size: 14px;
  color: @white;
  line-height: 1.43;

  z-index: -1;

  &__item {
    margin-bottom: 16px;
  }

  &__info {
    padding-top: 48px;

    .brand-lockup {
      margin-bottom: 28px;
      padding-left: 0;
    }
  }

  &__requisites-title {
    margin: 0 0 12px;
    font-size: 13px;
    font-weight: 500;
    line-height: 1.25;
    text-transform: uppercase;
    color: #737373;
    a{
      color: #737373;
      &:hover{
        color: #fff;
      }
    }
  }
  
  &__requisites-subtitle {
    margin: 0 0 7px;
    font-size: 13px;
    font-weight: 500;
    line-height: 1;
    text-transform: uppercase;
    color: #ffff00;
    a{
      color: #737373;
      &:hover{
        color: #fff;
      }
    }
  }

  &__requisites-description {
    margin-bottom: 7px;
    font-style: normal;
    white-space: pre-line;
    color: white;
    text-decoration: underline;
  }

  &__requisites-content {
    margin-bottom: 15px;
    font-style: normal;
    white-space: pre-line;
  }

  &__link {
    padding-bottom: 14px;

    &:hover {

      a {
        color: #bbb;
      }
    }

    ul {
      margin: 0;
      padding: 0;
      list-style: none;
    }

    li {
      margin: 0 0 8px;
    }
    
    a {
      display: block;
      color: inherit;
      text-decoration: none;

      &:hover{
        color: inherit;
        text-decoration: underline;
      }
    }
  }

  &__social{
    display: flex;
    margin: 0 -4px;
    padding: 4px 0 0;
    list-style: none;

    li {
      padding: 0 4px;
      margin-bottom: 12px;
    }

    a {
      display: flex;
      background-color: #fff;
      align-items: center;
      text-decoration: none;
      min-width: 28px;
      overflow: hidden;
      border-radius: 50%;
      svg {
        vertical-align: top;
      }

      span{
        text-indent: -9999px;
      }
    }
    &:hover{

      a {
        background-color: #bbb;
        &:hover{
          background-color: #fff;
        }
  
      }

      .insta-circle {
        fill: #bbb;
      }
    }
  }

  &__stores{
    display: flex;
    margin: 0 -4px;
    padding: 4px 0 0;
    list-style: none;

    li {
      padding: 0 4px;
    }

    a {
      display: flex;
      align-items: center;
      text-decoration: none;
      min-width: 28px;
      overflow: hidden;
      svg {
        vertical-align: top;
      }

      span{
        text-indent: -9999px;
      }
    }
  }
}
.copyright {
  padding: 24px 0;
  color: @gray;
}

.footer-title {
  margin: 0 0 12px;
  font-size: 13px;
  font-weight: 500;
  line-height: 1.25;
  text-transform: uppercase;
  color: #737373;
  a{
    color: #737373;
    &:hover{
      color: #fff;
    }
  }
}

.find-error {
  max-width: 220px;
}

.footer-contact {
  line-height: 1.2;

  &__item {
    margin-bottom: 12px;
  }

  &__link {
    display: block;
    font-size: 20px;
    font-weight: 500;
    color: inherit;
    text-decoration: none;
  }
}

.link-inherit {
  color: inherit;
  text-decoration: none;

  &:hover {
    color: inherit;
  }
}
@media (min-width: 992px) {
  .iac-service-footer {
    
    &__item {
      padding-left: 63px;
    }
  }
}
@media (min-width: 768px) {
  .iac-service-footer {
    
    &__item {
      padding-left: 20px;
      border-left: 1px solid @footer-bd;
    }
  }

  .copyright {
    border-top: 1px solid @footer-bd;

    &__text {
      text-align: center;
    }
  }
}

@media (max-width: 767px) {
  .iac-service-footer {

    &__info {
      padding: 28px 0 0;
    }

    &__link {

      li {
        display: inline-block;
        margin-right: 8px;
      }
    }
  }

  .footer-title {
    margin-bottom: 8px;
  }

  .copyright {
    padding-top: 12px;
    
    &__link {
      display: inline-block;
      margin-bottom: 12px;
    }
  }
}