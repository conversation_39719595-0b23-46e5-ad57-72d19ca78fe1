import { DataSource, RemoteStore, RefStore, Query } from "@iac/data"
import { Config, Context, <PERSON><PERSON><PERSON>, Setting<PERSON> } from "@iac/kernel"
import { Http, Action, Language } from "@iac/core"

let select_fields = [
  "number", "id",
  "contract_close_at",
  "status", "contract_name",
  "goods_count",
  "org_company",
  "contragent_company",
  "contract_totalcost",
  "winner_totalcost",
  "currency",
  "initiator",
  "contragent",
  "contragent_currency",
  "file",
  "contract_body",
  "stuck_tries"
];

const $d = Develop;

const org_company = ctx => {
  const org_company_title =
    $d.content_debug
      ? `${ctx?.initiator?.company_details?.title} u${ctx?.initiator?.user_details?.id}c${ctx?.initiator?.company_details?.id}`
      : ctx?.initiator?.company_details?.title;

  return {
    inn: ctx?.initiator?.company_details?.inn,
    company_title: org_company_title,
    company_id: ctx?.initiator?.company_details?.id
  }
};

const contragent_company = ctx => {
  const contragent_company_title =
    $d.content_debug
      ? `${ctx?.contragent?.company_details?.title} u${ctx?.contragent?.user_details?.id}c${ctx?.contragent?.company_details?.id}`
      : ctx?.contragent?.company_details?.title;

  return {
    inn: ctx?.contragent?.company_details?.inn,
    company_title: contragent_company_title,
    company_id: ctx?.contragent?.company_details?.id
  }
};

const company = ctx => {
  return {
    org_company: org_company(ctx),
    contragent_company: contragent_company(ctx)
  }
}

export default {
  props: ["access"],
  data: function () {

    var real_end_confirm_date = new Query({
      real_end_date_gte: {
        group: "contract.real_end_date_period",
        type: "date",
        label: "from",
        has_del: true,
        bind: {
          status: `real_end_date_error && {"type":"error"}`
        }
      },
      real_end_date_lte: {
        group: "contract.real_end_date_period",
        type: "date",
        label: "to",
        has_del: true,
        bind: {
          status: `real_end_date_error && {"type":"error"}`
        }
      },
      real_end_date_error:  {
        sync: false,
        group: "contract.real_end_date_period",
        type: "model",
        label: "!",
        bind: {
          value: "real_end_date_gte > real_end_date_lte",
          status: `real_end_date_error && {"type":"error","message":"${Language.t('to_from_error')}"}`
        },

      },
      confirm_income_date_gte: {
        group: "contract.confirm_income_date_period",
        type: "date",
        label: "from",
        has_del: true,
        bind: {
          status: `confirm_income_date_error && {"type":"error"}`
        }
      },
      confirm_income_date_lte: {
        group: "contract.confirm_income_date_period",
        type: "date",
        label: "to",
        has_del: true,
        bind: {
          status: `confirm_income_date_error && {"type":"error"}`
        },
      },
      confirm_income_date_error:  {
        sync: false,
        group: "contract.confirm_income_date_period",
        type: "model",
        label: "!",
        bind: {
          value: "confirm_income_date_gte > confirm_income_date_lte",
          status: `confirm_income_date_error && {"type":"error","message":"${Language.t('to_from_error')}"}`
        },
      }
    })

    var my_contracts = new Query({
      my_contracts: {
        type: "bool",
        label: "my_contracts",
        title: "Договора где я участвую как пользователь",
        group: "!Develop filters",
        order: -1,
        hidden:()=> {
           return this.access == "public"
        }
      }
    });

    var ktru_product = new Query({
      product_id: {
        type: 'entity',
        group: 'choose_product',
        label: '!choose_product',
        has_del: true,
        dataSource: new DataSource({
          valueExp: 'product_id',
          displayExp: "product_name",
          search: true,
          store: new RefStore({
            ref: "ref_enkt_products",
            key:"product_id"
          })
        }),
        multiple: true,
        hidden: ()=>{
          return !Settings.contract?._filter_product
        }
      }
    })

    var queryShared = new Query({
      queryText: {
        icon: "search",
        label: "!Search",
        hidden: true
      },
      relation: {
        type: "entity",
        group: "role",
        label: "!role",
        dataSource: ["buyer", "seller"],
        has_del: true,
        hidden: function () {
          return this.model.access == "public"
        }
      },
      currency: {
        group: "currency",
        label: "!currency",
        type: "entity",
        dataSource: "ref_currency",
        has_del: true
      },
      contract_totalcost_gte: {
        group: "contract.totalcost/<total>",
        type: "float",
        label: "!from",
        min: 0,
        bind: {
            status: `totalcost_error && {"type":"error"}`
        },
    },
    contract_totalcost_lte: {
        group: "contract.totalcost/<total>",
        type: "float",
        label: "!to",
        min: 0,
        bind: {
            status: `totalcost_error && {"type":"error"}`
        },
    },
    totalcost_error: {
        sync: false,
        group: "contract.totalcost",
        type: "model",
        label: "!",
        bind: {
            value: "contract_totalcost_gte > contract_totalcost_lte",
            status: `totalcost_error && {"type":"error","message":"${this.$t('to_from_error')}"}`
        },
    },
      org_activity_area_id: {
        label: "!org_activity_area_id",
        group: "activity_area_id",
        type: "entity",
        dataSource: "ref_area",
        has_del: true
      },
      contragent_activity_area_id: {
        label: "!contragent_activity_area_id",
        group: "activity_area_id",
        type: "entity",
        dataSource: "ref_area",
        has_del: true
      },
      contract_close_at_gte: {
        group: "contract.close_at_period",
        type: "date",
        label: "from",
        has_del: true,
        bind: {
            status: `close_at_error && {"type":"error"}`
        },
    },
    contract_close_at_lte: {
        group: "contract.close_at_period",
        type: "date",
        label: "to",
        has_del: true,
        bind: {
            status: `close_at_error && {"type":"error"}`
        },
    },
    close_at_error: {
        sync: false,
        group: "contract.close_at_period",
        type: "model",
        label: "!",
        bind: {
            value: "contract_close_at_gte > contract_close_at_lte",
            status: `close_at_error && {"type":"error","message":"${this.$t('to_from_error')}"}`
        },
    }
    }, [my_contracts])

    queryShared.access = this.access

    let outdated_tender = {
      fields: {
        outdated: {
          type: "entity",
          group: "outdated_filter",
          label: "!outdated_default_placeholder",
          dataSource: ["outdated_execution"],
          has_del: true,
          hidden: function () {
            //return queryShared.access == "public"
          }
        }
      }
    }

    let outdated = {
      fields: {
        outdated: {
          type: "entity",
          group: "outdated_filter",
          label: "!outdated_default_placeholder",
          dataSource: [
            "upcoming_payment",
            "outdate_income",
            "upcoming_delivery",
            "outdated_execution"
          ],
          has_del: true,
          hidden: function () {
            //return queryShared.access == "public"
          }
        }
      }
    }


    let who_fine = {
      hidden() {
        if (queryShared.access == "public")
          return true;

        if (this.model.status && Array.isArray(this.model.status)) {
          for (let status of this.model.status) {
            if (["Договор завершен.execution_published"].includes(status))
              return false;
          }
        }

        return true;
      },
      fields: {
        base: {
          group: "execution",
          type: "entity",
          label: "execution.base",
          has_del: true,
          hidden() {
            return (who_fine.hidden.bind(this))()
          },
          dataSource: [{ id: "1", name: "termination" }, { id: "2", name: "execution" }]
        },
        who_pays: {
          group: "execution",
          type: "entity",
          label: "who_pays",
          has_del: true,
          dataSource: [{ id: "1", name: "company.buyer" }, { id: "2", name: "company.seller" }],
          hidden() {
            return (who_fine.hidden.bind(this))()
          },
        }
      }
    }

    return {
      user: Context.User,
      config: Config,
      system_access: Context.Access,
      searchAuctionDataSource: new DataSource({
        //request_count: true,
        query: new Query(
          {
            status: {
              group: "status",
              label: "!",
              type: "enum-tree",

              dataSource: {
                valueExp: "id",
                store: {
                  key: "id",
                  ref: "auction_contract_status", 
                  method: "contract_ref",
                  injectQuery: (params)=>{
                    params.filters = params.filters || {};
                    params.filters.tree  = true;
                    params.filters.scope = this.access
                    return params;
                  }
                }
              },
            },
             ...who_fine.fields,
            ...outdated.fields,
          },
          [ktru_product, queryShared]
        ),
        template: 'template-contract',
        store: new RefStore({
          method: "contract_ref",
          ref:
            this.access == "private"
              ? "auction_private_registry"
              : "auction_public_registry",
          injectQuery: (params) => {
            params.fields = [...select_fields,"real_end_date"];
            params.filters.totalcost_error = undefined;
            params.filters.close_at_error = undefined;
            return params;
          },
          context: (context) => {
            context = {
              ...context,
              ...company(context),
              //tender_totalcost: context.contragent && context.contragent.price,
              winner_totalcost: context.winner_totalcost || (context.contragent && context.contragent.price),
              ref_status: "auction_contract_status"
            }

            return context
          }
        })
      }),
      searchADDataSource: new DataSource({
        // request_count: true,
        query: new Query(
          {
            status: {
              group: "status",
              label: "!",
              type: "enum-tree",
              dataSource: {
                valueExp: "id",
                store: {
                  key: "id",
                  ref: "online_shop_contract_status", 
                  method: "contract_ref",
                  injectQuery: (params)=>{
                    params.filters = params.filters || {};
                    params.filters.tree  = true;
                    params.filters.scope = this.access
                    return params;
                  }
                }
              },
            },
            ...who_fine.fields,
            ...outdated.fields,
            nad: {
              type: "hidden",
              sync: false,
              value: false,
            }
          },
          [ktru_product, queryShared, this.access == "private" && real_end_confirm_date]
        ),
        template: 'template-contract',
        store: new RefStore({
          method: "contract_ref",
          ref:
            this.access == "private"
              ? "online_shop_contract_private_registry"
              : "online_shop_contract_public_registry",
          injectQuery: (params) => {
            params.fields = [...select_fields, "close_count", "total_count", "real_end_date", "confirm_income_date"];
            params.filters.totalcost_error = undefined;
            params.filters.close_at_error = undefined;
            params.filters.real_end_date_error = undefined;
            params.filters.confirm_income_date_error = undefined;
            return params;
          },
          context: (context) => {
            context = {
              ...context,
              ...company(context),
              //tender_totalcost: context.contragent && context.contragent.price,
              winner_totalcost: context.winner_totalcost || (context.contragent && context.contragent.price),
              ref_status: "online_shop_contract_status"
            }
            return context;
          }
        })
      }),
      searchNadADDataSource: new DataSource({
        // request_count: true,
        query: new Query(
          {
            status: {
              group: "status",
              label: "!",
              type: "enum-tree",
              dataSource: {
                valueExp: "id",
                store: {
                  key: "id",
                  ref: "online_shop_contract_status", 
                  method: "contract_ref",
                  injectQuery: (params)=>{
                    params.filters = params.filters || {};
                    params.filters.tree  = true;
                    params.filters.scope = this.access
                    return params;
                  }
                }
              }
            },
            ...who_fine.fields,
            ...outdated.fields,
            nad: {
              type: "hidden",
              sync: false,
              value: true,
            }
          },
          [ktru_product, queryShared, this.access == "private" && real_end_confirm_date]
        ),
        template: 'template-contract',
        store: new RefStore({
          method: "contract_ref",
          ref:
            this.access == "private"
              ? "online_shop_contract_private_registry"
              : "online_shop_contract_public_registry",
          injectQuery: (params) => {
            params.fields = [...select_fields, "close_count", "total_count", "real_end_date", "confirm_income_date"];
            params.filters.totalcost_error = undefined;
            params.filters.close_at_error = undefined;
            params.filters.real_end_date_error = undefined;
            params.filters.confirm_income_date_error = undefined;
            return params;
          },
          context: (context) => {
            context = {
              ...context,
              ...company(context),
              //tender_totalcost: context.contragent && context.contragent.price,
              winner_totalcost: context.winner_totalcost || (context.contragent && context.contragent.price),
              ref_status: "online_shop_contract_status"
            }
            return context;
          }
        })
      }),
      searchDataSourceTender: new DataSource({
        // request_count: true,
        query: new Query(
          {
            status: {
              group: "status",
              label: "!",
              type: "enum-tree",
              dataSource: {
                valueExp: "id",
                store: {
                  key: "id",
                  ref: "contract_status", 
                  method: "contract_ref",
                  injectQuery: (params)=>{
                    params.filters = params.filters || {};
                    params.filters.tree  = true;
                    params.filters.scope = this.access
                    return params;
                  }
                }
              }
            },
            ...who_fine.fields,
            ...outdated_tender.fields,
            is_general: {
              group: "is_general",
              label: "!is_general_all",
              type: "entity",
              has_del: true,
              dataSource: [
                { id: false, name: 'is_general_no' },
                { id: true, name: 'is_general_yes' }
              ]
            },
            proc_type: {
              type: "hidden",
              value: "tender"
            }
          },
          [ktru_product, queryShared]
        ),
        store: new RefStore({
          method: "contract_ref",
          ref:
            this.access == "private"
              ? "contract_private_registry"
              : "contract_public_registry",
          context: (context) => {
            context = {
              ...context,
              ...company(context),
              winner_totalcost: context.winner_totalcost || context.contract_totalcost,
            }
            return context
          },
          injectQuery: (params) => {
            params.fields = [...select_fields,"real_end_date"];
            params.filters.totalcost_error = undefined;
            params.filters.close_at_error = undefined;
            return params;
          }
        }),
        template: "template-contract"
      }),
      searchDataSourceContest: new DataSource({
        // request_count: true,
        query: new Query(
          {
            status: {
              group: "status",
              label: "!",
              type: "enum-tree",
              dataSource: {
                valueExp: "id",
                store: {
                  key: "id",
                  ref: "contract_status", 
                  method: "contract_ref",
                  injectQuery: (params)=>{
                    params.filters = params.filters || {};
                    params.filters.tree  = true;
                    params.filters.scope = this.access
                    return params;
                  }
                }
              },
            },
            ...who_fine.fields,
            ...outdated_tender.fields,
            proc_type: "contest"
          },
          [ktru_product, queryShared]
        ),
        store: new RefStore({
          method: "contract_ref",
          ref:
            this.access == "private"
              ? "contract_private_registry"
              : "contract_public_registry",
          context: (context) => {
            context = {
              ...context,
              ...company(context),
              winner_totalcost: context.winner_totalcost || context.contract_totalcost,
            }
            return context
          },
          injectQuery: (params) => {
            params.fields = [...select_fields,"real_end_date"]
            params.filters.totalcost_error = undefined;
            params.filters.close_at_error = undefined;
            return params;
          }
        }),
        template: "template-contract"
      }),
      searchDataSourceSelection: new DataSource({
        // request_count: true,
        query: new Query(
          {
            status: {
              group: "status",
              label: "!",
              type: "enum-tree",
              dataSource: {
                valueExp: "id",
                store: {
                  key: "id",
                  ref: "contract_status", 
                  method: "contract_ref",
                  injectQuery: (params)=>{
                    params.filters = params.filters || {};
                    params.filters.tree  = true;
                    params.filters.scope = this.access
                    return params;
                  }
                }
              },
            },
            ...who_fine.fields,
            ...outdated_tender.fields,
            is_general: {
              group: "is_general",
              label: "!is_general_all",
              type: "entity",
              has_del: true,
              dataSource: [
                { id: false, name: 'is_general_no' },
                { id: true, name: 'is_general_yes' }
              ]
            },
            proc_type: "selection"
          },
          [ktru_product, queryShared]
        ),
        store: new RefStore({
          method: "contract_ref",
          ref:
            this.access == "private"
              ? "contract_private_registry"
              : "contract_public_registry",
          context: (context) => {
            context = {
              ...context,
              ...company(context),
              winner_totalcost: context.winner_totalcost || context.contract_totalcost,
            }
            return context
          },
          injectQuery: (params) => {
            params.fields = [...select_fields,"real_end_date"]
            params.filters.totalcost_error = undefined;
            params.filters.close_at_error = undefined;
            return params;
          },
        }),
        template: "template-contract"
      }),
      searchDataSourceAgreementRequest: new DataSource({
        // request_count: true,
        query: new Query(
          {
            status: {
              group: "status",
              label: "!",
              type: "enum-tree",
              dataSource: {
                valueExp: "id",
                store: {
                  key: "id",
                  ref: "contract_status", 
                  method: "contract_ref",
                  injectQuery: (params)=>{
                    params.filters = params.filters || {};
                    params.filters.tree  = true;
                    params.filters.scope = this.access
                    return params;
                  }
                }
              },
            },
            ...who_fine.fields,
            ...outdated_tender.fields,
            proc_type: "agreement_request"
          },
          [ktru_product, queryShared]
        ),
        store: new RefStore({
          method: "contract_ref",
          ref:
            this.access == "private"
              ? "contract_private_registry"
              : "contract_public_registry",
          context: (context) => {
            return {
              ...context,
              ...company(context),
            }
          },
          injectQuery: (params) => {
            params.fields = [...select_fields,"real_end_date"]
            params.filters.totalcost_error = undefined;
            params.filters.close_at_error = undefined;
            return params;
          },
        }),
        template: "template-contract"
      }),
      searchDataSourceExchange: new DataSource({
        // request_count: true,
        valueExp: "number",
        query: new Query({
          status: {
            group: "status",
            label: "!",
            type: "enum-tree",
            dataSource: {
              valueExp: "id",
              store: {
                key: "id",
                ref: "exchange_contract_status", 
                method: "contract_ref",
                injectQuery: (params)=>{
                  params.filters = params.filters || {};
                  params.filters.tree  = true;
                  params.filters.scope = this.access
                  return params;
                }
              }
            },
          },
          //proc_type: "exchange"
        },[my_contracts]),
        store: new RefStore({
          method: "contract_ref",
          ref:
            this.access == "private"
              ? "exchange_contract_private_registry"
              : "exchange_contract_public_registry",


              context: (context) => {
                if (this.access == "private" && Context.Access.policy['exchange_contract_reassign_trader'] ) {
                  context.checkbox = true;
                }

                


                context.actions = [
                  {
                    label: "contract.reassign_trader",
                    hidden: () =>  !Context.Access.policy['exchange_contract_reassign_trader'],
                    handler: async () => {
                        const { error, data } = await Http.api.rpc('contract_action', {                          
                          action: 'reassign_trader',
                          number: context.number,
                        });
                        if (error) {
                          Vue.Dialog.MessageBox.Error(error);
                          return;
                        }
                        return data?.number || data;           
                    }
                  }
                ];
                return {
                  proc_type: 'schedule_exchange_contract',
                  ...context,
                  ...company(context),
                  ref_status: "exchange_contract_status",           
                }           
            }, 
            injectQuery: (params) => {
              params.fields = [...select_fields]
              params.filters.totalcost_error = undefined;
              params.filters.close_at_error = undefined;
              return params;
            },

        }),

        actions: [        
          {
            label: "contract.reassign_trader",
            // question: "contract.reassign_trader_question",
            hidden: () => {
              return this.access != "private" || 
              !Context.Access.policy['exchange_contract_reassign_trader'] ||
              !this.searchDataSourceExchange.checkedItems.length > 0
             },            
             handler: async () => {
              const { error, data } = await Http.api.rpc('ref', { 
                ref: "ref_exchange_reassign",                         
                op: 'reassign',
                contract_numbers: this.searchDataSourceExchange.checkedItems,
              });
              if (error) {
                Vue.Dialog.MessageBox.Error(error);
                return;
              }
              Vue.Dialog.MessageBox.Success(this.$t('contract.reassign_trader_success'));
        
              await this.searchDataSourceExchange.reload();
              return data?.number || data;           
          }
          }
        ],
        template: "template-contract",
      }),
      searchDataSourceCustom: new DataSource({
        // request_count: true,
        query: new Query({
          status: {
            group: "status",
            label: "!",
            type: "enum",
            dataSource: {
              valueExp: "code",
              store: {
                key: "code",
                ref: "custom_contract_status", 
                method: "contract_ref",
                injectQuery: (params)=>{
                  params.filters = params.filters || {};
                  params.filters.tree  = true;
                  params.filters.scope = this.access
                  return params;
                }
              }
            },
          },
          contract_type: {
            group: "contract_type",
            type: "entity",
            label: "!contract_type",
            has_del: true,
            dataSource: "ref_contract_type"
          },
          contract_totalcost_gte: {
            group: "contract.totalcost/<total>",
            type: "float",
            label: "!from",
            min: 0,
            bind: {
                status: `totalcost_error && {"type":"error"}`
            },
          },
          contract_totalcost_lte: {
              group: "contract.totalcost/<total>",
              type: "float",
              label: "!to",
              min: 0,
              bind: {
                  status: `totalcost_error && {"type":"error"}`
              },
          },
          totalcost_error: {
              sync: false,
              group: "contract.totalcost",
              type: "model",
              label: "!",
              bind: {
                  value: "contract_totalcost_gte > contract_totalcost_lte",
                  status: `totalcost_error && {"type":"error","message":"${this.$t('to_from_error')}"}`
              },
          },        
          published_at_gte: {
            group: "contract.published_at",
            type: "date",
            label: "!contract.published_at",
            has_del: true,
            prefix: Language.t("from"),
            bind: {
              status: `published_at_error && {"type":"error"}`
            },
          },
          published_at_lte: {
            group: "contract.published_at",
            type: "date",
            label: "!contract.published_at",
            has_del: true,
            prefix: Language.t("to"),
            bind: {
              status: `published_at_error && {"type":"error"}`
            },
          },
          published_at_error: {
            sync: false,
            group: "contract.published_at",
            type: "model",
            label: "!",
            bind: {
              value: "published_at_gte > published_at_lte",
              status: `published_at_error && {"type":"error","message":"${this.$t('to_from_error')}"}`
            },
          },
        },[my_contracts]),
        store: new RefStore({
          method: "contract_ref",
          ref:
            this.access == "private"
              ? "custom_contract_private_registry"
              : "custom_contract_public_registry",

          injectQuery: (params) => {
            params.filters.totalcost_error = undefined;
            params.filters.published_at_error = undefined;
            params.fields = [...select_fields, "lot_id", "public_date", "total_count", "spec_totalcost"]
            return params;
          },
          context: (context) => {
            context = {
              ...context,
              ...company(context),
              //tender_totalcost: context.contragent && context.contragent.price,
              ref_status: "custom_contract_status"
            }

            if (!Array.isArray(context.spec_totalcost)) {
              if (context.spec_totalcost && context.spec_totalcost.amount) {
                context.spec_totalcost = [context.spec_totalcost]
              } else {
                context.spec_totalcost = [{ amount: context.spec_totalcost, currency: Settings._default_currency }]
              }
            }

            return context;
          }
        }),
        actions: [
          {
            label: "contract.create",
            hidden: () => {
              return 1;//(this.access != "private") || !this.system_access.policy.contract_create
            },
            handler: async () => {
              let { error, data: id } = await Action['contract.create']();
              if (id)
                this.$router.push({ path: `/workspace/contract/${id}/core` });
            }
          }
        ],
        template: "template-contract"
      })
    }
  },
  computed: {
    access_policy(){
      if(this.access=='private')
        return this.system_access.policy.contract_list || this.system_access.policy.contract_create

      return true
    }
  },
  template: `
    <iac-access :access='access_policy'>
      <iac-section type='header'>
          <ol class='breadcrumb'>
              <li><router-link to='/'>{{$t('home')}}</router-link></li>
              <li>{{$t('nav.contracts')}}</li>
          </ol>
        <h1>{{$t('contracts')}}</h1>
      </iac-section>
      <iac-section>
        <ui-layout-tab clear>
          
          <ui-layout-group label='tenders' v-if='$settings.procedures && $settings.procedures.tender'>
            <ui-data-view :dataSource='searchDataSourceTender'/>
          </ui-layout-group>

          <ui-layout-group label='selections'>
            <ui-data-view :dataSource='searchDataSourceSelection'/>
          </ui-layout-group>

          <ui-layout-group label='master_agreements' v-if='$settings.procedures && $settings.procedures.master_agreement'>
            <ui-data-view :dataSource='searchDataSourceAgreementRequest'/>
          </ui-layout-group>

          <ui-layout-group label='nav.electronic_shop' v-if='$settings.procedures && $settings.procedures.e_shop'>
            <ui-data-view :dataSource='searchADDataSource' />
          </ui-layout-group>

          <ui-layout-group label='nav.national_shop' v-if='
               $settings.procedures
            && $settings.procedures.e_shop
            && $settings.procedures.e_shop.national_shop
            && ((access != "private" || user.id && user.team_id) || system_access.policy.system_national_shop)
          '>
            <ui-data-view :dataSource='searchNadADDataSource' />
          </ui-layout-group>

          <ui-layout-group label='reductions' v-if='$settings.procedures && $settings.procedures.auction'>
            <ui-data-view :dataSource='searchAuctionDataSource' />
          </ui-layout-group>

          <ui-layout-group label='contests'  v-if='$settings.procedures && $settings.procedures.contest'>
            <ui-data-view :dataSource='searchDataSourceContest'/>
          </ui-layout-group>

          <ui-layout-group label='nav.exchange' v-if='system_access.policy.exchange_contract_list && $settings.exchange && !$settings.exchange._blocked && access != "public"'>
            <ui-data-view :dataSource='searchDataSourceExchange'/>
          </ui-layout-group>

          <ui-layout-group label='custom_contracts'>
            <ui-data-view :dataSource='searchDataSourceCustom'/>
          </ui-layout-group>
          
        </ui-layout-tab>
      </iac-section>
    </iac-access>
  `
}
