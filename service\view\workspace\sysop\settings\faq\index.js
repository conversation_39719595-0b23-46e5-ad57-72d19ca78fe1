import { Language } from '@iac/core';
import { RefStore, RemoteStore, DataSource, Query } from '@iac/data';
import FaqEditDialog from './_dlg_edit_faq';
import Faq from './model';
import FaqItem from './FaqItem';


export default {
  data() {
    return {
      faqList: new DataSource({
        store: new RefStore({
          method: 'common_ref',
          ref: 'ref_faqs',
          params: {
            locale: Language._local_socket
          },
          context: (context) => {
            return new Faq(context);
          }
        })
      }),
      faqList1: new DataSource({
        query: new Query({
          op: "read",
          ref: "ref_faqs"
        }),
        store: new RemoteStore({
          method: 'common_ref',
          context: (context) => {
            return new Faq(context);
          }
        })
      })
    }
  },
  components: {
    FaqItem
  },
  computed: {
    actions() {
      return { editItem: this.edit, deleteItem: this.delete, toggleShow: this.toggleShow }
    }
  },
  created() {
    this.faqList.load();
  },
  methods: {
    async add() {
      const result = await FaqEditDialog.Modal({ model: new Faq });
      if (result) {
        this.faqList.reload();
      }
    },
    async edit(item) {
      const result = await FaqEditDialog.Modal({ model: new Faq(item) });
      if (result) {
        this.faqList.reload();
      }
    },
    async delete(item) {
      if (await Vue.Dialog.MessageBox.Question(Language.t('question_delete_faq_item')) != Vue.Dialog.MessageBox.Result.Yes) {
        return;
      }
      const { error, data } = await item.delete();
      if (error) {
        await Vue.Dialog.MessageBox.Error(error);
      }
      if (data) {
        this.faqList.reload()
      }
    },
    async toggleShow(item) {
      const { error, data } = await item.toggleShow();
      if (error) {
        await Vue.Dialog.MessageBox.Error(error);
      }
      if (data) {
        this.faqList.reload()
      }
    },
  },
  template: `
  <iac-access :access='$policy.system_cm_faq_view'>
    <iac-section>
      <ol class='breadcrumb'>
        <li><router-link to='/'>{{$t('home')}}</router-link></li>
        <li>{{$t('hp.faq')}}</li>
      </ol>
      <div class='title'>
        <h2>{{$t('hp.faq')}}</h2>
        <div ><ui-btn type='primary' v-if='$policy.system_cm_faq_view' v-on:click.native='add'>{{$t('add')}}</ui-btn></div>
      </div>
    </iac-section>
    <iac-section>
      <ui-layout-group v-if='$policy.system_cm_faq_view'>
        <faq-item v-for='item in faqList.items' :key='item.id' :item='item' :actions="actions" :editable="true" />
      </ui-layout-group>
    </iac-section>
  </iac-access>
  `
}
