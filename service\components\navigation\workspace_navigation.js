import WMenu from './workspace_menu'
import IacBilling from './../billing';
import { Context } from '@iac/kernel'

export default {
    props: ['items'],
    data: function () {
        return {
            user: Context.User,
            active: undefined,
            toolbar: [
                {
                    icon: "bulb",
                    handler: () => {
                        this.$router.push({ path: '/contact' })
                    }
                },
                {
                    icon: "grid",
                    actions: [
                        {
                            label: "Администрирование",
                            handler: () => {
                                this.$router.push({ path: '/admin' })
                            }
                        },
                        {
                            label: "Демо страница",
                            handler: () => {
                                this.$router.push({ path: '/demo' })
                            }
                        },
                    ]
                },
                {
                    icon: "action",
                    actions: [{
                        label: "Что нового",
                        handler: () => { }
                    }, {
                        label: "Релизы",
                        handler: () => { }
                    },
                    { type: "sep" },
                    {
                        label: "API Документация",
                        handler: () => { }
                    },
                    { type: "sep" },
                    {
                        label: "О системе",
                        handler: () => {
                            Vue.Dialog.MessageBox.Info("v.2.0.1")
                        }
                    }]
                }
            ]
        }
    },

    computed: {
        groups() {

            let root = {};
            this.items.forEach((item, key) => {
                item = { ...item }
                let groups = item.group && item.group.split("/") || [];
                let group = groups[0] ? groups[0] : 'general'

                root[group] = root[group] || {
                    items: []
                }

                if (groups.length > 0) {
                    groups.shift();
                    item.group = groups.join('/')
                }

                if((this.$route.path || "").indexOf(item.path) == 0){
                    root[group].active = true;
                    this.active = group;
                }

                root[group].items.push(item);

            });
            return root;
        }
    },
    components: {
        WMenu: WMenu,
        IacBilling,
    },
    template: `
        <nav class='tab_menu'>
            <ui-layout-tab :key='active' name='navigation' :router1='false' class='horizontal' style='width: 100%' :toolbar='toolbar' >
                <ui-layout-group :active='group.active'  :icon='key' v-for='group,key in groups' style='min-width: unset; height: 100%; display: flex;'>
                    <WMenu class='compact' style='flex: 1 1 auto' :items='group.items'/>
                    <iac-billing v-if='user && user.team_id' style='padding: 20px;' />
                </ui-layout-group> 
            </ui-layout-tab>   
        </nav>
    `
}