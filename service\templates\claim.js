import Vue from 'vue'

const Component = {
    props: ['model'],
    computed: {

        proc_ids() {
            return this.model.proc_ids || []
        }
    },
    methods: {
        procedure_url(id) {
            return `/procedure/${id}/core`
        },
    },
    template: `
        <ui-data-view-item class='claim-item' :model='model'>
            <template slot='header'>
                <div>
                    <span>{{model.finyear}}</span>
                    <span>{{$t("month_"+model.month)}}</span>
                </div>
                <div>
                    <span class='procedure_link' v-if='proc_ids.length == 1'>
                        <router-link :to='procedure_url(proc_ids[0].proc_id)'> {{ $t(proc_ids[0].proc_type) }} № {{proc_ids[0].proc_id}}</router-link>
                    </span>
                    <span class='procedure_link' v-else-if='proc_ids.length > 1'>
                        <div>{{proc_ids.length}} {{$t('procedures',{count: proc_ids.length})}}
                            <div>
                                <router-link v-key='index' :to='procedure_url(item.proc_id)' v-for='item,index in proc_ids'> {{ $t(item.proc_type) }} № {{item.proc_id}}</router-link>
                            </div>
                        </div>

                    </span>
                </div>
            </template>
            <template slot='title'>
                {{model.tovarname}}
                <span class='status_text' v-if='model.in_update' style='font-size: 13px; white-space: nowrap;text-decoration: underline;'>{{$t('updating')}}</span>
            </template>
            <template slot='props'>
                <div v-if='0'>
                    <label>plantype</label>
                    <div>{{model.plantype}}</div>
                </div>
                <div>
                    <label>{{$t('quantity')}}</label>
                    <div><iac-number :value='model.tovaramount' delimiter=' ' part=''/> <span class='sub'>{{model.unit}}</span></div>
                </div>
                <div>
                    <label>{{$t('price')}}</label>
                    <div><iac-number :value='model.price' delimiter=' ' part='2'/> <span class='sub'>{{$settings._default_currency}}</span></div>
                </div>
                <div>
                    <label>{{$t('total_sum')}}</label>
                    <div><iac-number :value='model.total_sum' delimiter=' ' part='2'/> <span class='sub'>{{$settings._default_currency}}</span></div>
                </div>
                <div :title="model.kls" v-if="model.kls">
                    <label>{{$t('kls')}}</label>
                    <div>{{model.kls}}</div>
                </div>
                <div :title="model.expense" v-if="model.expense">
                    <label>{{$t('expense_item_code')}}</label>
                    <div>{{model.expense.display || model.expense}}</div>
                </div>
            </template>
        </ui-data-view-item>
    `
}

Vue.component('template-claim', Component);