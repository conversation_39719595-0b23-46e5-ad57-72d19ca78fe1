import './components'

import * as iac from './iac'
import * as practice from './practice'
import * as extensions from './extensions'

import {Marked} from "@iac/core";
import {markedHighlight} from "marked-highlight";
import hljs from 'highlight.js';

var docCode = {
    props: {
        code: Object,
        language: {
            type: String,
            default: "json"
        }
    },
    computed:{
        html(){
            if(!this.code)
                return;
            return hljs.highlight(JSON.stringify(this.code, null, '  '), { language: this.language }).value;
        }
    },
    template: `
        <pre><code v-html='html'/></pre>
    `
}
Vue.component("doc-code",docCode)

Marked.use(markedHighlight({
    langPrefix: 'hljs language-',
    highlight(code, lang) {
      const language = hljs.getLanguage(lang) ? lang : 'javascript';
      return hljs.highlight(code, { language }).value;
    }
  }));



let iac_pages = Object.keys(iac).map((package_name) => {
    return Object.keys(iac[package_name]).map((component_name) => {
        return {
            path: `@iac/${package_name.toLocaleLowerCase()}/${component_name.toLocaleLowerCase()}`,
            component: iac[package_name][component_name]
        }
    }).reduce((arr, cur) => {
        arr[cur.path] = cur.component;
        return arr
    }, {})
}).reduce((arr, cur) => {
    arr = {...arr,...cur}
    return arr
},{})

let practice_pages = Object.keys(practice).map((component_name) => {
    return {
        path: `practice/${component_name.toLocaleLowerCase()}`,
        component: practice[component_name]
    }
}).reduce((arr, cur) => {
    arr[cur.path] = cur.component;
    return arr
}, {})

let extensions_pages = Object.keys(extensions).map((component_name) => {
    return {
        path: `extensions/${component_name.toLocaleLowerCase()}`,
        component: extensions[component_name]
    }
}).reduce((arr, cur) => {
    arr[cur.path] = cur.component;
    return arr
}, {})

let pages = {
    ...iac_pages,
    ...practice_pages,
    ...extensions_pages,
}


const DocComponent = {
    props: ["page", "menu"],
    data: function(){
        return {
            pages: pages
        }
    },
    computed: {
        blocks(){
            let blocks = this.pages[this.page];
            
            if(!blocks)
                return;
            if(!Array.isArray(blocks)){
                blocks = [blocks];
            }
            return blocks.map((block)=>{
                if(typeof block == "string"){
                    let content = block;
                    block = {
                        data: function(){
                            return {
                                content: content
                            }
                        },
                        template: `
                            <ui-markdown-view :content='content'>
                        `
                    }
                }else{
                    block.template = `
                    <div class='ui-markdown-view'>${Marked.parse(block.markdown || block.template)}</div>                    
                    `    
                }
                return block
            })
        }
    },
    template: `
        <div>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('doc')}}</li>
                </ol>
                <div class='title'>
                    <h1>Документация</h1>
                </div>
            </iac-section>
            <iac-section>
                <template v-if='blocks'>
                    <component :is='block'  v-for='block in blocks' />    
                </template>
                <ui-error v-else code='404' message='Документация в разработке'/>
            </iac-section>
        </div>
    `
}

Vue.component("doc-component", DocComponent);