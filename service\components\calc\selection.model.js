import { Language } from '@iac/core'
import BasicCalc from './basic.model'
import { Settings } from '@iac/kernel'

export class Seller extends BasicCalc {
  constructor(currentBRV, showResult) {
    super(currentBRV, showResult)
  }

  props() {
    return {
      start_price: {
        description:'$calc.start_price_description',
        required: true,
        label: 'enter_procedure_start_price',
        type: 'float'
      },
      commercial_price: {
        required: true,
        label: 'enter_commercial_price_without_vat',
        type: 'float'
      },
      vat_percent: {
        required: true,
        label: 'vat_percent',
        type: 'number',
        min: 0,
        max: 99,
        suffix: "%"
      },
      deposit_percent: {
        required: true,
        label: 'choose_percent_deposit',
        type: 'range',
        min: 0,
        max: 3,
        attr: { step: 0.1 },
        has_del: true
      }
    }
  }

  calcResult() {
    const result = { data: { deposit_percent: 3, currentBRV: this.currentBRV, price: 0, vat: 0, price_with_vat: 0 }, title: '', total: 0 }
    result.fields = this.fields.map(({ name, required, type, value }) => (result.data[name] = value, { name, required, type, value: value?.exp?.value ?? value }))

    result.error = this.prevalidate(result)
    if (result.data.commercial_price > result.data.start_price) {
      this.addErrorToResult(result, 'commercial_price', Language.t('commercial_price_cannot_be_highter_start'))
      this.addErrorToResult(result, 'start_price', Language.t('commercial_price_cannot_be_highter_start'))
    }
    const maximumOrEqualStartPrice = 25000 * result.data.currentBRV
    if (result.data.start_price > maximumOrEqualStartPrice) {
      this.addErrorToResult(result, 'start_price', `${Language.t('start_price_must_be_lower_or_equal_25000BRV')} (${maximumOrEqualStartPrice} ${Settings._default_currency})`)
    }
    if (result.error) {
      this.setError(result.error)
      return
    }

    const computed = { deposit: 0, commission: 0 }
    result.data.price = result.data.commercial_price
    result.data.vat = (result.data.price / 100) * result.data.vat_percent
    result.data.price_with_vat = result.data.price + result.data.vat
    if (result.data.price_with_vat > result.data.start_price) {
      this.addErrorToResult(result, 'commercial_price', Language.t('commercial_price_with_vat_cannot_be_highter_start'))
      this.addErrorToResult(result, 'start_price', `${Language.t('commercial_price_with_vat_cannot_be_highter_start')}`)
      this.addErrorToResult(result, 'vat_percent', Language.t('commercial_price_with_vat_cannot_be_highter_start'))
      this.setError(result.error)
      this.showResult(result)
      return
    }
    computed.deposit = this.uzRound(result.data.price_with_vat * result.data.deposit_percent * 0.01)
    computed.commission = this.uzRound(Math.min(result.data.price_with_vat * 0.0015, this.maxCommission.seller))
    result.total = this.calcTotal(computed.deposit, computed.commission)

    result.computed = computed
    this.showResult(result)
  }
}

export class Buyer extends BasicCalc {
  constructor(currentBRV, showResult) {
    super(currentBRV, showResult)
  }

  props() {
    return {
      start_price: {
        description:'$calc.start_price_description',
        required: true,
        label: 'enter_procedure_start_price',
        type: 'float',
        attr: { react: true }
      },
      deposit_percent: {
        required: true,
        label: 'choose_percent_deposit',
        type: 'range',
        min: 0,
        max: 3,
        attr: { step: 0.1 },
        has_del: true
      }
    }
  }

  calcResult() {
    const result = { data: { deposit_percent: 3, currentBRV: this.currentBRV, price: 0 }, title: '', total: 0 }
    result.fields = this.fields.map(({ name, required, type, value }) => (result.data[name] = value, { name, required, type, value: value?.exp?.value ?? value }))

    result.error = this.prevalidate(result)
    const maximumOrEqualStartPrice = 25000 * result.data.currentBRV
    if (result.data.start_price > maximumOrEqualStartPrice) {
      this.addErrorToResult(result, 'start_price', `${Language.t('start_price_must_be_lower_or_equal_25000BRV')} (${maximumOrEqualStartPrice} ${Settings._default_currency})`)
    }
    if (result.error) {
      this.setError(result.error)
      return
    }

    const computed = { deposit: 0, commission: 0 }
    result.data.price = result.data.start_price
    computed.deposit = this.uzRound(result.data.price * result.data.deposit_percent * 0.01)
    computed.commission = this.uzRound(Math.min(result.data.price * 0.0015, this.maxCommission.buyer))
    result.total = this.calcTotal(computed.deposit, computed.commission)

    result.computed = computed
    this.showResult(result)
  }
}
