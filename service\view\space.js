import { Config, Context, Develop, Blackout } from '@iac/kernel'
import Experts from '../components/experts';

let secret_code = "000000000000"
let showDevelopSetting = false;

document.addEventListener('keyup', async function ({ ctrlKey, shiftKey, keyCode }) {
	if (!ctrlKey || !shiftKey || showDevelopSetting)
		return;

	let code = ('00' + keyCode).slice(-2);
	secret_code = (secret_code + code).slice(-12);
	if (secret_code == '515351535448') {
		showDevelopSetting = true;
		await Vue.Dialog.DevelopSettingDlg.Modal({
			size: 'right'
		})
		showDevelopSetting = false;
	}
});


export default {
	data: function () {
		return {
			blackout: Blackout,
			display: false
		}
	},
	async created() {
		let query = { ...this.$route.query }
		let { auth } = query;

		if (auth) {
			query.auth = undefined
			this.$router.replace({ query: query })

			if(Develop.disable_authcheck_debug){
				this.display = true
				return;
			}

			let [face_email, user_id] = auth.split(':')

			user_id = user_id && Number(user_id);

			if (!face_email) {
				this.display = true
				return;
			}

			if (Context.User.login != face_email) {
				let result = await Vue.Dialog.SignIn.Modal({
					email: face_email,
					view: 1
				})

				if (!result) {
					Context.User.logOut();
					this.display = true
					return;
				}
			}

			if (user_id /*&& (await Context.User.has_user(user_id))*/) {
				let error = await Context.User.change_user(user_id);
				if (error) {
					await Vue.Dialog.MessageBox.Error(error)
				}
			}

		}
		this.display = true
	},
	computed: {

	},
	methods: {

	},
	components: {
		Experts
	},
	template: `
    <div class='iac-service'>
        <router-view v-if='display' class='iac-service-content'/>
        
        <iac-banners class='float' :style='$settings.experts ? "right: "+(40*($settings.experts._scale || 1)+12)+"px;": ""' />
		<Experts v-if='$settings.experts'/>
        <iac-dialog-container/>
        <div class='blackout' v-if='blackout.active'>
            <div class='content'>{{$t(blackout.message || "blackout")}}</div>
        </div>
    </div>`
}