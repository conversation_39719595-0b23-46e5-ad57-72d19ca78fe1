import { Context, <PERSON>elo<PERSON> } from '@iac/kernel'

export default {
    data: function () {
        return {
            error: undefined,
            user: Context.User
        }
    },
    async created() {
        let { email, code } = this.$route.query;
        let { error, data } = await this.user.activateUser(email, code);
        if (error)
            this.error = error
        else {
            this.$router.push({
                path: '/workspace'
            });
        }
    },
    template: `
        <iac-section>
            <h1>{{$t('activate_user')}}</h1>
            <ui-error v-if='error' :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
        </iac-section>
    `
}