import EIMZOClient from './client'

import DataSource from '../../data/src/data_source';
import ArrayStore from '../../data/src/array_store';

import Language from '../../core/src/language'

var EIMZO_MAJOR = 3;
var EIMZO_MINOR = 37;

export default class EImzoStore {

    static checkVersion() {
        return new Promise((resolve, reject) => {
            EIMZOClient.checkVersion((major, minor) => {
                var newVersion = EIMZO_MAJOR * 100 + EIMZO_MINOR;
                var installedVersion = parseInt(major) * 100 + parseInt(minor);
                if (installedVersion < newVersion) {
                    resolve({ error: { message: Language.t('ecp_error_version') } });
                } else {
                    resolve({ data: { major, minor } });
                }
            }, function (e, r) {
                if (e) {
                    resolve({ error: { message: Language.t('ecp_error_5') } });
                } else {
                    resolve({ error: { message: Language.t(r) } });
                }
            });
        });
    }

    static installApiKeys() {
        return new Promise(async (resolve, reject) => {
    
            if(EImzoStore._installApiKeys)
                return resolve({ data: "ok" })

            let response = await EImzoStore.checkVersion();
            if (response.error)
                return resolve(response)
            EIMZOClient.installApiKeys(async () => {
                EImzoStore._installApiKeys = true;
                resolve({ data: "ok" })
            }, function (e, r) {
                if (e) {
                    resolve({ error: { message: Language.t('ecp_error_3') } });
                } else {
                    resolve({ error: { message: Language.t(r) } });
                }
            })
        });
    }

    static loadKey(vo) {
        return new Promise((resolve, reject) => {
            EIMZOClient.loadKey(vo, (id) => {
                resolve(id)
            }, function (e, r) {
                console.log("ERROR", e, r)
                if (r) {
                    if (r.indexOf("BadPaddingException") != -1) {
                        //uiShowMessage(errorWrongPassword);
                    } else {
                        //uiShowMessage(r);
                    }
                } else {
                    //uiShowMessage(errorBrowserWS);
                }
                resolve()
            });
        });
    }

    static loadKeys() {
        return new Promise(async (resolve, reject) => {
            let response = await EImzoStore.installApiKeys();
            if (response.error)
                return resolve(response)

            EIMZOClient.listAllUserKeys(
                function (o, i) {
                    return i;
                }, function (id, vo) {
                    var now = new Date();
                    vo.expired = EIMZOClient.dates.compare(now, vo.validTo) > 0 ? true : false;
                    var itm = {};
                    itm.id = id;
                    itm.value = "itm-" + vo.serialNumber + "-" + id;
                    itm.text = vo.CN;
                    itm.pkcs7 = undefined;
                    if (!vo.expired) {

                    } else {
                        itm.text = itm.text + ` (${Language.t('expired')})`;
                    }
                    itm.vo = vo;
                    return itm;
                }, function (items, firstId) {
                    if (items && items.length > 0)
                        resolve({ data: items })
                    else
                        resolve({ error: { message: Language.t("ecp_error_0") } })
                }, function (e, r) {
                    resolve({
                        error: {
                            message: Language.t(r)
                        }
                    })
                })

        });
    }

    static createPkcs7(keyId, data, timestamper = null) {
        return new Promise(async (resolve, reject) => {
            EIMZOClient.createPkcs7(keyId, data, timestamper, function (pkcs7) {
                resolve({ data: pkcs7 });
            }, function (e, r) {
                console.log(e, r);
                if (e) {
                    resolve({ error: { message: Language.t('ecp_error_1') } });
                } else {
                    if(r && r.indexOf("BadPaddingException") != -1){
                        resolve({ error: { message: Language.t('ecp_error_2') } });
                    }else{
                        resolve({ error: { message: Language.t(r) } });
                    }
                }
            });
        });
    }

    static async byKey(key) {
        if (key == undefined)
            return;
        let { error, data } = await EImzoStore.loadKeys();

        if (!error)
            return data[key]
    }

    static async load(options = {}) {
        let response = await EImzoStore.loadKeys();
        if (response.error)
            return response

        let { take = 10, skip = 0, search } = options || {};
        let _array = response.data;
        if(search){
            search = new RegExp(`(${search.replace(/\s+/gm,' ').trim().split(' ').join("|")})`, 'igm')
            _array = _array.filter((item)=>{
                if(`${item.vo.CN} ${item.vo.TIN} ${item.vo.O}`.search(search) >= 0){
                    return true
                }else{
                    return false;
                }
            })
        }

        return {
            data: _array.slice(skip, skip + take)
        }
    }

    static async subscribe(message, details) {
        let { error, data: items } = await EImzoStore.load();
        if(error)
            return {
                error
            }
        if (!items || items.length <= 0) {
            return { error: { message: Language.t("ecp_error_0") } };
        }

        let item = undefined;
        if (items.length <= 1 && !details) {
            item = items[0];
        } else {
            item = await Vue.Dialog({
                props: ["items", "title", "message"],
                data: function () {
                    return {
                        item: 0,
                        dataSource: DataSource.get("eimzo")
                    }
                },
                template: `
                <div>
                    <header>{{title}}</header>
                    <main v-if='message'>
                        <ui-text :readonly='true' :value='message' />
                    </main>
                    <main>
                        <ui-entity label='select_key' v-bind:value='item' v-on:input='item = $event' :dataSource='dataSource'/>                        
                    </main>
                    <footer>
                        <ui-btn type='primary' v-on:click.native='Close(item)'>{{$t("subscribe")}}</ui-btn>
                    </footer>
                </div>
            `
            }).Modal({
                items: items,
                title: details && details.title,
                message:  details && details.message
            });
        }
        if (!item)
            return;
        let keyId = await EImzoStore.loadKey(item.vo);

        if (keyId) {
            return await EImzoStore.createPkcs7(keyId, message);
        }
    }
}

DataSource.reg("eimzo", () => {
    return new DataSource({
        limit: 10000,
        search: true,
        displayExp: "text",
        valueExp: "pkcs7",
        store: EImzoStore,
        template: {
            props: ["model"],
            template: `
                <ui-data-view-item :model='model'>
                    <template slot='header'>
                        <div :class='{text_danger:model.vo.expired}'>
                            <iac-date :date='model.vo.validFrom'  /> - <iac-date :date='model.vo.validTo'  />
                        </div>
                        <div :class='{text_danger:model.vo.expired}' v-if='model.vo.expired'>{{$t('ecp.expired')}}</div>
                    </template>
                    <template slot='title'>
                        <img src='/img/pfx.ico' style='vertical-align: middle; float: left; margin-right: 16px;' />
                        <span>
                            <div><b> № {{$t('ecp.sert')}}:</b> {{model.vo.serialNumber}}</div>
                            <div v-if='model.vo.legal'>{{$t('ecp.legal')}}</div>
                            <div v-else>{{$t('ecp.postal')}}</div>
                        </span>
                    </template>
                    <template slot='description'>
                        <div style='white-space: normal;' v-if='model.vo.TIN'><b>{{$t('inn')}}:</b> {{model.vo.TIN}}</div>
                        <div style='white-space: normal;' v-if='model.vo.PINFL'><b>{{$t('pinfl')}}:</b> {{model.vo.PINFL}}</div>
                        <div style='white-space: normal;'><b>{{$t('fio')}}:</b> {{model.vo.CN}}</div>
                        <div style='white-space: normal;' v-if='model.vo.O'><b>{{$t('ecp.company')}}:</b> {{model.vo.O}}</div> 
                    </template>
                    <template slot='props' v-if='0'>
                        <div>
                            <label>{{$t('fio')}}</label>
                            <div>{{model.vo.CN}}</div>
                        </div>
                        <div>
                            <label>{{$t('inn')}}</label>
                            <div>{{model.vo.TIN}}</div>
                        </div>
                        <div>
                            <label>{{$t('ecp.company')}}</label>
                            <div>{{model.vo.O}}</div>
                        </div>
                    </template>
                </ui-data-view-item>
            `
        }
    })
});