.iac--ebp-search-table {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  border: 1px solid #dadada;
  border-radius: 4px;
  overflow: hidden;

  >div {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    justify-content: flex-start;

    &:nth-child(2n+2) {
      background-color: #FBFBFB;
    }

    &:nth-child(2n+1) {
      background-color: #eaeaea;
    }

    >div {
      padding: 5px;

      &:first-child {
        //width: 130px;
        flex: 0 0 auto;
        font-size: 14px;
        line-height: 16px;
      }

      &:not(:first-child) {
        flex: 1 1 auto;
        font-size: 14px;
        line-height: 16px;
      }
    }

    &:not(:first-child) {
      cursor: pointer;

      >div {
        &:first-child {
          font-size: 12px;
          line-height: 16px;
        }

        &:not(:first-child) {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          word-wrap: break-word;
          border-left: 1px solid #dadada;
          font-size: 14px;
          line-height: 16px;
        }
      }
    }

    &.active {
      background-color: #01afe5;
      color: white;
    }
  }
}