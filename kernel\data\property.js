import { Property as IacProperty } from '@iac/data'

export default class Property extends IacProperty {
  constructor(context) {
    super(context)
  }
  setAttributes(attributes) {
    if (attributes.type) {
      switch (attributes.type) {
        case "struct":
          attributes.type = 'model'
          break;
        case "ref":
          attributes.type = 'entity'
          if (attributes.type_def) {
            attributes.dataSource = attributes.type_def;
            delete attributes.type_def;
          }
          break;
        case "enum-tree":
        case "enum":
          if (attributes.type_def) {
            attributes.dataSource = attributes.type_def;
            delete attributes.type_def;
          }
          break;
        case "time":
          if (attributes.type_def && Array.isArray(attributes.type_def)) {
            attributes.range = `${attributes.type_def[0]}-${attributes.type_def[1]}`;
          } else if (attributes.type_def) {
            attributes.range = attributes.type_def;
          }
          delete attributes.type_def;
          break;
        case "range":
          if (attributes.type_def) {
            attributes.min = attributes.type_def[0]
            attributes.max = attributes.type_def[1]
          }
          delete attributes.type_def;
          break;
        case "boolean":
          attributes.type = "entity";
          attributes.has_del = true;
          attributes.dataSource = [
            { id: false, name: "no" },
            { id: true, name: "yes" }
          ]
          delete attributes.type_def;
          break;
      }
    }
    attributes.label = attributes.label || attributes.name;
    super.setAttributes(attributes);
  }
}