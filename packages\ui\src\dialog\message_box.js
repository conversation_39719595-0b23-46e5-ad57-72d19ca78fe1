import Language from "../../../core/src/language"

var MessageBox = {
    props: ["text", "caption", "buttons", "type", "number", "details", "fields", "onClose"],
    get Result() {
        return {
            'Yes': 1,
            'No': 2,
            'Ok': 3,
            'Cancel': undefined
        }
    },
    get Button() {
        return {
            'YesNo': 1,
            'YesNoCancel': 2,
            'Ok': 3,
            'OkCancel': 4
        }
    },
    get Type() {
        return {
            'Error': 1,
            'Info': 2,
            'Success': 3,
            'Question': 4
        }
    },
    async Show(...params) {
        const props = {
            
            //size: this.$develop.content_debug ? "lg" : "sm"
        }
        if (typeof params[0] == "object") {
            props.text = params[0].message;
            props.caption = params[0].caption;
            props.fields = params[0].fields;
            props.onClose = params[0].onClose;
            props.details = params[0].details;
            props.number = params[0].number;
            props.size = params[0].size;

            if (props.details) {

                props.details = {
                    stacktrace: (props.details["stacktrace"] || "").split(/\n/ig),
                    code: props.details["code"],
                }
            }

        } else {
            props.text = params[0]
        }
        params.shift()
        params.forEach(param => {
            if (typeof param === 'string')
                props.caption = props.caption || param
            else if (!props.buttons)
                props.buttons = param
            else
                props.type = param
        })
        props.caption = props.caption || ''
        return await MessageBox.Modal(props)
    },
    __prepareMessageToShow(params, button, type) {
        if (typeof params[params.length - 1] === 'string' || typeof params[params.length - 1] === 'object')
            params.push(button)
        params.push(type)
        return params
    },
    /**
     * text
     * text, buttons
     * text, caption
     * text, caption, buttons
     */
    async Error(...params) {
        if (params?.[0].code == "AbortError") {
            return;
        }
        return await MessageBox.Show(...MessageBox.__prepareMessageToShow(params, MessageBox.Button.Ok, MessageBox.Type.Error))
    },

    /**
     * text
     * text, buttons
     * text, caption
     * text, caption, buttons
     */
    async Info(...params) {
        return await MessageBox.Show(...MessageBox.__prepareMessageToShow(params, MessageBox.Button.Ok, MessageBox.Type.Info))
    },
    /**
     * text
     * text, buttons
     * text, caption
     * text, caption, buttons
     */
    async Question(...params) {
        return await MessageBox.Show(...MessageBox.__prepareMessageToShow(params, MessageBox.Button.YesNo, MessageBox.Type.Question))
    },
    /**
 * text
 * text, buttons
 * text, caption
 * text, caption, buttons
 */
    async Success(...params) {
        return await MessageBox.Show(...MessageBox.__prepareMessageToShow(params, MessageBox.Button.Ok, MessageBox.Type.Success))
    },
    /**
     * 
     */
    async Form(...params) {
        return await MessageBox.Show(...MessageBox.__prepareMessageToShow(params, MessageBox.Button.YesNo, MessageBox.Type.Question))
    },

    computed: {
        textClass() {
            let typeClass = ''
            Object.keys(MessageBox.Type).forEach(key => typeClass = (MessageBox.Type[key] === this.type) ? key.toLowerCase() : typeClass)
            return typeClass;
        },
        btnOk() {
            return this.buttons == MessageBox.Button.Ok || this.buttons == MessageBox.Button.OkCancel
        },
        btnNo() {
            return this.buttons == MessageBox.Button.YesNoCancel || this.buttons == MessageBox.Button.YesNo
        },
        btnCancel() {
            return this.buttons == MessageBox.Button.YesNoCancel || this.buttons == MessageBox.Button.OkCancel
        },
        btnYes() {
            return this.buttons == MessageBox.Button.YesNoCancel || this.buttons == MessageBox.Button.YesNo
        }
    },
    methods: {
        async close(result) {
            if (this.fields && result == 1) {
                let error = false;
                let params = this.fields.filter((field) => {
                    if (field.hidden && typeof field.hidden == 'function') {
                        return !field.hidden();
                    }
                    return !field.hidden;
                }).map((field) => {
                    let value = field.value;
                    if (field.value && field.value.exp && field.value.exp.value != undefined) {
                        value = field.value.exp.value
                    }

                    let _error = field.validate ? field.validate() : undefined;
                    if (_error) {
                        error = true;
                        field.status = {
                            type: "error",
                            message: _error
                        }
                    }

                    if (field.required && (value == null || value == undefined)) {
                        error = true;
                        field.status = {
                            type: "error",
                            message: Language.t("required_field")
                        }
                    }
                    return {
                        name: field.name,
                        value: value
                    }
                }).reduce((prev, curr) => {
                    prev[curr.name] = curr.value
                    return prev;
                }, {})
                if (error)
                    return;
                if (this.onClose) {
                    this.$wait(async () => {
                        let { error, data } = await this.onClose(params)
                        if (error){
                            if(error.data){
                                let errors_fields = error.data.reduce((acc,curr)=>{
                                    acc[curr.name] = curr.message
                                    return acc;
                                },{});

                                this.fields.forEach((field)=>{
                                    if(errors_fields[field.name]){
                                        field.status = {
                                            type: "error",
                                            message: errors_fields[field.name]
                                        }
                                    }
                                })
                            }else{
                                Vue.Dialog.MessageBox.Error(error)   
                            }
                            return 
                        }
                        return data && this.Close(data)
                    })
                } else
                    return this.Close(params)
            } else {
                return this.Close(result)
            }
        }
    },
    template: `
        <div>
            <header>{{$t(caption)}}</header>
            <main v-if='text' v-bind:class='textClass' v-html='text'></main>
            <main v-if='fields'>
                <ui-layout :fields='fields' />
            </main>

            <main v-if='details && $develop.content_debug'>
                <router-link :to='"/error/"+number' target= '_blank'>{{number}}</router-link>
                
                <ui-layout-group v-if='details.stacktrace'>
                    <ul><li v-if='stacktrace' v-for='stacktrace of details.stacktrace'>{{stacktrace}}</li></ul>
                </ui-layout-group>  
                
                <ui-layout-group label='code' v-if='details.code'>
                    <div style='word-break: break-all'>{{details.code}}</div>
                </ui-layout-group>                 
            </main>                
             
            <footer>
                <ui-btn v-if='btnCancel' type='secondary' @click.native='close()'>{{$t("cancel")}}</ui-btn>
                <ui-btn v-if='btnNo' type='secondary' @click.native='close(2)'>{{$t("No")}}</ui-btn>
                <ui-btn v-if='btnYes' type='primary'@click.native='close(1)'>{{$t("Yes")}}</ui-btn>
                <ui-btn v-if='btnOk' type='primary' @click.native='close(3)'>{{$t("Close")}}</ui-btn>
            </footer>
        </div>
    `
}

export default MessageBox;