import { Http } from '@iac/core'
export default class SysStat {
    constructor() {
        document.addEventListener('visibilitychange', this.visibilitychange);
        this.tid = undefined;
        this.channel = undefined;

        this.last_sync = undefined;
        this.ping = undefined;
        this.delta = undefined;

        this.join();
    }

    request() {
        if(this.tid || !this.channel || document.visibilityState != 'visible')
            return;

        let start = Date.now();
        let end,foo;
        this.channel.push("sync", start)
            .receive("ok", (data) => {

                [end,foo] = data;
                this.ping = Date.now() - start;
                this.delta = Math.ceil( (start + this.ping/2)-end );
                //this.delta = (foo - this.ping/2);

                this.last_sync = new Date();


                this.tid = setTimeout(()=>{
                    this.tid = undefined;
                    this.request();
                },10000)

            });
    }

    join() {
        if (!this.channel && document.visibilityState == 'visible') {
            Http.api.socket.join(`time`, (channel) => {
                this.channel = channel;

                channel.joinPush
                    .receive("ok", (data) => {
                        // Запускаем опросы сервера
                        this.request();
                    })
                    .receive("error", (data) => {
                        this.channel = undefined
                    })

            }, true);
        }
    }

    leave() {
        if (document.visibilityState != 'visible') {
            Http.proc.socket.leave_channel(this.channel);
            this.channel = undefined;
        }
    }

    visibilitychange = (data) => {
        if (document.visibilityState == 'visible') {
            this.join();
        } else if (document.visibilityState == 'hidden') {
            this.leave();
        }
    }
}