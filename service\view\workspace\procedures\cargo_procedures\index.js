import { DataSource, RemoteStore, RefStore, Query } from '@iac/data'
import { Language, Http } from '@iac/core'



export default {
    data: function(){
        let transportation_price_group_name = `${Language.t('transportation_price')}, UZS`;
        let distance_group_name = `${Language.t('distance')}, km`;
        var query = new Query({
            min_price: {
                group: transportation_price_group_name + '/<price>',
                label: '! ',
                type: 'float',
                prefix: this.$t("from"),
                bind: {
                    status: `price_error && {"type":"error"}`
                },
                min: 0


            },
            max_price: {
                group: transportation_price_group_name + '/<price>',
                label: '! ',
                type: 'float',
                prefix: this.$t("to"),
                bind: {
                    status: `price_error && {"type":"error"}`
                },
                min: 0
            },

            price_error: {

                sync: false,
                group: transportation_price_group_name,
                type: "model",
                label: "!",
                bind: {
                    value: "min_price > max_price",
                    status: `price_error && {"type":"error","message":"${this.$t('to_from_error')}"}`
                },
            },

            min_distance: {
                group: distance_group_name + '/<distance>',
                label: '! ',
                type: 'float',
                prefix: this.$t("from"),
                bind: {
                    status: `distance_error && {"type":"error"}`
                },
                min: 0


            },

            max_distance: {
                group: distance_group_name + '/<distance>',
                label: '! ',
                type: 'float',
                prefix: this.$t("to"),
                bind: {
                    status: `distance_error && {"type":"error"}`
                },
                min: 0


            },

            distance_error: {
                sync: false,
                group: distance_group_name,
                type: "model",
                label: "!",
                bind: {
                    value: "min_distance > max_distance",
                    status: `distance_error && {"type":"error","message":"${this.$t('to_from_error')}"}`
                },
            },


            shipper_region: {
                group: 'region_of_shipment',

                label: '!region_of_shipment',
                has_delete: true,
                type: 'entity',
                dataSource: 'ref_area_lv4_select',
                multiple: true,
            },

            recipient_stock: {
                group: 'region_of_delivery',

                label: '!region_of_delivery',
                has_delete: true,
                type: 'entity',
                dataSource: 'ref_area_lv4_select',
                multiple: true,
            },

            cargo_type_of_transport: {
                group: 'transport_type',
                multiple: true,

                label: '!transport_type',
                has_delete: true,
                type: 'entity',
                dataSource: {
                    store: {
                        ref: 'ref_cargo_type_of_transport',
                    }
                },

            },

        })
        return {
            cargo_own: new DataSource({
                actions: [
                    {
                        label: "create",
                        question: Language.t("create_cargo.question"),
                        handler: async ()=>{
                            let {error,data} = await Http.proc.rpc("create_procedure",{
                                type: "cargo_procedure"
                            })
                            if(error){
                                return Vue.Dialog.MessageBox.Error(error)
                            }
                            if(data) {
                                let id = data.id || data.proc_id
                                if (id) {
                                    this.$router.push({path: `/procedure/${id}/core`});
                                }
                                
                            }
                        }
                    }
                ],
                query: new Query({
                    status: {
                        type: 'enum',
                        dataSource: "ref_cargo_procedure_status_private",
                        //value: [],
                    },
                    relation:{
                        value: "owner",
                        type: "hidden",
                        sync: false,
                        hidden: true
                    },
                },[query]),
                store: new RefStore({
                    ref: "ref_cargo_procedure_private",
                    //ref: "ref_cargo_type_of_transport",
                    injectQuery: (params) => {
                        params.filters = params.filters || {};
                        params.filters.distance_error = undefined;
                        params.filters.price_error = undefined;
                        return params;
                    },
                }),
            }),
            cargo_party: new DataSource({
                query: new Query({
                    status: {
                        type: 'enum',
                        dataSource: "ref_cargo_procedure_status_participant",
                        //value: [],
                    },
                    relation:{
                        value: "participant",
                        type: "hidden",
                        sync: false,
                        hidden: true
                    },
                    /*company_id: {
                        type: "entity",
                        label: "!company",
                        group: "company",
                        has_del: true,
                        dataSource: {
                          search: true,
                          displayExp: "title",
                          store: {
                            method: "company_ref",
                            ref: "companies",
                          }
                        },
                      }*/
                },[query]),
                store: new RefStore({
                    ref: "ref_cargo_procedure_private",
                    //ref: "ref_cargo_type_of_transport",
                    injectQuery: (params) => {
                        params.filters = params.filters || {};
                        params.filters.distance_error = undefined;
                        params.filters.price_error = undefined;
                        return params;
                    },
                }),
            })
        }
    },
    template: `
        <iac-access :access='$policy.cargo_procedure_my_crud || $policy.cargo_procedure_participate'>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('cargo_procedures')}}</li>
                </ol>
                <div class='title'>
                    <h1>{{$t('cargo_procedures')}}</h1>
                </div>
            </iac-section>
            <iac-section>
                <ui-layout-tab :clear='true'>

                    <ui-layout-group v-if='$policy.cargo_procedure_my_crud' key='Organize' label='Organize'>
                        <ui-data-view :dataSource='cargo_own' />
                    </ui-layout-group>

                    <ui-layout-group v-if='$policy.cargo_procedure_participate' key='Participate' label='Participate'>
                        <ui-data-view :dataSource='cargo_party' />
                    </ui-layout-group>

                </ui-layout-tab>
            </iac-section>
        </iac-access>
    `
}
