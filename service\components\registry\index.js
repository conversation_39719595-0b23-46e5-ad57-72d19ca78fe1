import Vue from 'vue';

const Component = {
    props: ['item'],
    template: `
      <router-link class='widget-registry' :to='item.link'>
        <icon class='widget-registry__icon' aria-hidden='true'>{{ item.icon }}</icon>
        <div class='widget-registry__right'>
          <h3 class='widget-registry__title'>{{ $t(item.name) }}</h3>
          <p class='widget-registry__text'>{{ $t(item.text) }}</p>
        </div>
      </router-link>
    `,
};

Vue.component('widget-registry', Component);
