SHELL               = /bin/bash
CFG                ?= .env
APP_SITE           ?= iac.dev.lan
DOC_SITE           ?= doc.iac.dev.lan
PROJECT_NAME       ?= iac
DCAPE_PROJECT_NAME ?= dcape
DCAPE_NET          ?= $(DCAPE_PROJECT_NAME)_default

define CONFIG_DEF
APP_SITE=$(APP_SITE)
DOC_SITE=$(DOC_SITE)
PROJECT_NAME=$(PROJECT_NAME)
DCAPE_NET=$(DCAPE_NET)
endef
export CONFIG_DEF

-include $(CFG).bak
export

-include $(CFG)
export

.PHONY: all $(CFG) setup start start-hook stop update up reup down dirs build build-deps build-config build-all build-dir dc help

all: help

## настройка контейнера
setup: $(CFG) dirs


start: $(CFG) reup

start-hook: $(CFG) reup

stop: down

update: reup

up:
up: CMD=up -d
up: dc

## рестарт контейнеров
reup:
reup: CMD=up --force-recreate --build -d
reup: dc

## остановка и удаление всех контейнеров
down:
down: CMD=rm -f -s
down: dc

dc: docker-compose.yml
	@docker run --rm  \
	-v /var/run/docker.sock:/var/run/docker.sock \
	-v $$PWD:$$PWD \
	-w $$PWD \
	docker/compose:1.14.0 \
	-p $$PROJECT_NAME \
	$(CMD)

$(CFG):
	@[ -f $@ ] || { echo "$$CONFIG_DEF" > $@ ; echo "Warning: Created default $@" ; }

help:
	@grep -A 1 "^##" Makefile | less
