import { Config, Context } from '@iac/kernel'
import { Entity } from '@iac/data'
import { Language } from '@iac/core'

export default class Manager extends Entity {
    constructor(context = {}) {
        super(context)

        this.age = context.Age
        this.firstName = context.FirstName
        this.gender = context.Gender
        this.id = context.ID
        this.lastName = context.LastName
        this.phone = context.Phone
        this.rate = context.Rate
        this.role = context.Role

        this.comment = ""
        this.new_rate = 0
    }

    props() {
        return {
            new_rate: {
                type: 'rate'
            },
            comment: {
                type: 'text'
            }
        }
    }

    async save() {
        let error;
        try {
            const response = await fetch(`${Config.mocrm_server}/ext-api/employee/set-rate?rate=${this.new_rate}&feedback=${this.comment}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json;charset=utf-8',
                    'Authorization': `Bearer ${Context.User.access_token}`,
                    'X-DBRPC-Language': Language._local
                },
            });

            const body = await response.json();

            if (response.status != 200) {
                error = {
                    status: response.status,
                    message: body?.message
                }
            }
        } catch (e) {
            error = { message: e.toString() }
        }

        if (error) {
            if (!this.setError(error)) {
                await Vue.Dialog.MessageBox.Error(error);
            }
        } else {
            this.comment = ""
            this.new_rate = 0
            this.refresh()
        }
    }

    static async getData() {
        let error
        let data
        try {
            const response = await fetch(`${Config.mocrm_server}/ext-api/company/get-assistent`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json;charset=utf-8',
                    'Authorization': `Bearer ${Context.User.access_token}`,
                    'X-DBRPC-Language': Language._local
                },
            });

            data = await response.json();

            if (response.status != 200) {
                error = {
                    status: response.status,
                    message: data?.message
                }
            }
        } catch (e) {
            error = { message: e.toString() }
        }
        return { error, data }
    }

    async refresh() {
        const { error, data } = await Manager.getData()
        if (error) {
            if (!this.setError(error)) {
                await Vue.Dialog.MessageBox.Error(error);
            }
        } else {
            this.constructor(data)
        }
    }

    static async get() {
        const { error, data } = await Manager.getData()
        return {
            error,
            data: !error ? (new Manager(data)) : undefined
        }
    }
}