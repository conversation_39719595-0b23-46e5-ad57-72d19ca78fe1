import { DataSource, RefStore, Entity } from '@iac/data'
import { Http, Language } from '@iac/core'
import { Context } from '@iac/kernel'
import "@iac/service/templates/company"

class ShipmentItemEditForm extends Entity {
    constructor(context) {
        super(context);

        this.plan_date_release = context.plan_date_release
        this.amount = context.amount ?? 1
        this.files = context.files ?? []
        this.booked = context.booked
    }

    props() {
        return {
            plan_date_release: {
                label: "shipment_plan_schedule_date",
                type: "date",
                readonly: true
            },
            amount: {
                label: "shipment_quantity",
                required: true,
                type: "number",
                onChange: () => {
                    this.properties.amount.status = undefined
                    if (this.amount <= 0) {
                        this.properties.amount.status = {
                            type: "error",
                            message: "Количество товаров должно быть больше нуля"
                        }
                        return true;
                    } else if (this.amount < this.booked) {
                        this.properties.amount.status = {
                            type: "error",
                            message: `Количество товаров должно быть больше текущего резерва (${this.booked})`
                        }
                    }
                }
            },
            files: {
                label: "shipment_files",
                type: "file",
                multiple: true,
                readonly: true
            }
        }
    }
}

const query = () => {
    return {
        plan_date_release_gte: {
            group: "shipment_schedule.plan_date_release",
            type: "date",
            label: "from",
            has_del: true,
            bind: {
                status: `plan_date_release_error && {"type":"error"}`
            },           
        },
        plan_date_release_lte: {
            group: "shipment_schedule.plan_date_release",
            type: "date",
            label: "to",
            has_del: true,
            bind: {
                status: `plan_date_release_error && {"type":"error"}`
            },
        },
        plan_date_release_error:  {
            sync: false,
            group: "shipment_schedule.plan_date_release",
            type: "model",
            label: "!",
            bind: {
                value: "plan_date_release_gte >= plan_date_release_lte",
                status: `plan_date_release_error && {"type":"error","message":"${Language.t('to_from_error')}"}`
            },
        },
    }
}


class Booking extends Entity {
    constructor(context = {}) {
        super(context);
        this.amount = context.amount ?? 0
    }
    
    props() {
        return {
            exc_schedule_pgo_get_own_contracts: {//поиск по полю
                label:'exc_schedule_booking_create_own_contracts',
                type: "entity",
                onChange: () => {
                    if (!this.exc_schedule_pgo_get_own_contracts) {
                        this.claim_id = undefined
                        this.exc_schedule_pgo_for_booking = undefined
                        this.properties.exc_schedule_pgo_for_booking.hidden = true
                    } else {
                        this.properties.exc_schedule_pgo_for_booking.hidden = false
                    }
                },
                dataSource: {
                    store: {
                        method: "ref_schedule",
                        ref: "exc_schedule_pgo_get_own_contracts",
                        context: (context) => {
                            context.name = `${context.number} (${context.good})`
                            context.desc = context.company_fulltitle
                            context.value = context.number
                            return context
                        }
                    }
                }
            },
            exc_schedule_pgo_for_booking: {
                label:'exc_schedule_pgo_create_own_pgo',
                type: "entity",
                hidden: true,
                dataSource: {
                    store: {
                        method: "ref_schedule",
                        ref: "exc_schedule_pgo_for_booking",
                        injectQuery: (params) => (params.filters = { ...params.filters, claim_ids: this.exc_schedule_pgo_get_own_contracts.parent_id }, params),
                        context: (context) => {
                            context.name = `${context.product_name} (${context.amount}) / ${context.plan_date_release}`
                            context.desc = context.company_fulltitle
                            return context
                        }
                    }
                }
            },
            amount: {
                label: "shipment_quantity",
                required: true,
                type: "number",
                onChange: () => {
                    this.properties.amount.status = undefined
                    if (this.amount <= 0) {
                        this.properties.amount.status = {
                            type: "error",
                            message: "Количество товаров должно быть больше нуля"
                        }
                        return true;
                    }
                }
            }
        }
    }

    async create() {
        const { amount, exc_schedule_pgo_get_own_contracts, exc_schedule_pgo_for_booking } = this
        const contract_number = exc_schedule_pgo_get_own_contracts?.value
        const exc_pgo_items_id = exc_schedule_pgo_for_booking?.exp?.value
        if (!amount || !contract_number || !exc_pgo_items_id) {
            return false
        }

        const answer = await Http.api.rpc("ref_schedule", {
            ref: "exc_schedule_pgo_booking",
            op: "create",
            data: { amount, contract_number, exc_pgo_items_id }
        })

        answer.error && Vue.Dialog.MessageBox.Error(answer.error);

        return !answer.error
    }
}


class BookingEditor extends Entity {
    constructor(context = {}) {
        super(context)

        this.id = context.id
        this.exc_pgo_items_id = context.exc_pgo_items_id
        this.exc_pgo_items_product_name = context.exc_pgo_items_product_name
        this.contract_number = context.contract_number
        this.amount = context.amount ?? 0
    }

    props() {
        return {
            exc_pgo_items_id: {
                label: "!",
                type: "static",
                value: `${this.exc_pgo_items_product_name} (ПГО №${this.exc_pgo_items_id})`
            },
            contract_number: {
                label: "contract_number",
                type: "static",
                value: this.contract_number
            },
            amount: {
                label: "shipment_quantity",
                required: true,
                type: "number",
                onChange: () => {
                    this.properties.amount.status = undefined
                    if (this.amount <= 0) {
                        this.properties.amount.status = {
                            type: "error",
                            message: "Количество товаров должно быть больше нуля"
                        }
                        return true;
                    }
                }
            }
        }
    }

    async create() {
        const { amount, id } = this
        if (!amount) {
            return false
        }

        const answer = await Http.api.rpc("ref_schedule", {
            ref: "exc_schedule_pgo_booking",
            op: "update",
            data: { id, amount }
        })

        answer.error && Vue.Dialog.MessageBox.Error(answer.error);

        return !answer.error
    }
}

class ShipmentSchedule extends Entity {
    constructor(context = {}) {
        super(context)
        this.idArray = context.idArray;
        this.amount = context.amount
    }

    props() {
        return {
            shipments_items: {
                label: "shipment_plans",
                type: "data-grid",
                dataSource: new DataSource({
                    actions: [
                        {
                            label: "add", handler: async () => {
                                const datesList = this.properties.shipments_items.dataSource.store._array.map(item => item.plan_date_release)
                                function onClose(result) {
                                    let data = result
                                    if (datesList.includes(result.plan_date_release)) {
                                        return { error: { data: [{ name: 'plan_date_release', message: "на эту дату уже добавлена строка" }] } }
                                    }

                                    if (this._props.fields[0].status) {
                                        data = undefined
                                    }

                                    return { error: null, data }
                                }
                                const newCurrentItem = await Vue.Dialog.MessageBox.Form({ fields: (new ShipmentItemForm({})).fields, onClose }, Language.t("filling_shipment_data"))
                                if (!newCurrentItem || typeof newCurrentItem != 'object') {
                                    return
                                }
                                const uuid = crypto.randomUUID()
                                const actions = [
                                    {
                                        icon: "edit", btn_type: "warning", handler: async () => {
                                            const currentItem = this.properties.shipments_items.dataSource.store._array.find(item => item.uuid == uuid)
                                            const newCurrentItem = await Vue.Dialog.MessageBox.Form({ fields: (new ShipmentItemForm({ ...currentItem })).fields, onClose }, Language.t("filling_shipment_data"))
                                            if (!newCurrentItem || typeof newCurrentItem != 'object') {
                                                return
                                            }
                                            Object.keys(newCurrentItem).forEach(key => currentItem[key] = newCurrentItem[key])
                                            this.properties.shipments_items.dataSource.reload();
                                        }
                                    }
                                ]
                                this.properties.shipments_items.dataSource.store._array.unshift({ uuid, actions, ...newCurrentItem, not_saved: true })
                                this.properties.shipments_items.dataSource.reload();
                            }
                        },
                    ],
                    store: {
                        data: [],
                        context: (context) => {
                            Object.defineProperty(context, "bindClass", {
                                configurable: true,
                                enumerable: true,
                                get: () => {
                                    return context.not_saved ? "ui-alert ui-alert-warning" : "ui-alert ui-alert-success"
                                },
                            });
                            return context
                        }
                    }
                }),
                attr: {
                    buttons: true,
                    summary: false,
                    columns: [
                        { field: "plan_date_release", label: "shipment_plan_date", style: "text-align:left; width: 100%;" },
                        { field: "amount", label: "shipment_quantity", style: "text-align:right; white-space: nowrap;" },
                        { field: "files", label: "shipment_files", type: "file", style: "text-align:right; white-space: nowrap;", display: items => (items?.length ? items.length : 'no') + ' files' }
                    ]
                }
            },
            claim_ids: {
                label: "shipment_requests",
                value: this.idArray,
                type: "static"
            },
            files: {
                label: "shipment_files",
                type: "file",
                multiple: true,
            }
        }
    }

    async loadFile(fieldData) {
        let answer = undefined
        let formData = new FormData();
        formData.append('scope_tender_participant', this.proc_id);
        formData.append('data', fieldData.file, fieldData.file.name);

        let { data, error } = await Http.upload.form('tender/attach', formData);
        if (error) {
            answer = { i: index, message: error.message }
        } else {
            fieldData.id = data.uuid
            fieldData.name = data.meta.name
            fieldData.meta = {
                "type": data.meta.type,
                "content_type": data.meta.content_type,
                "type_group": data.meta.group,
                "size": data.meta.size
            }
        }
        return answer
    }

    async loadFiles(itemsToSave) {
        if (this.files && this.files.length > 0) {
            const errorData = [];
            for (let index in this.files) {
                let fieldData = this.files[index];
                if (!fieldData.file || fieldData.id)
                    continue;
                const error = await this.loadFile(fieldData)
                error && errorData.push(error)
            }

            if (errorData && errorData.length > 0) {
                this.properties.files.status = {
                    type: "error",
                    data: errorData
                }
            }
        }

        await Promise.all(itemsToSave.map(async item => {
            if (item.files && item.files.length > 0) {
                const errorData = [];
                for (let index in item.files) {
                    let fieldData = item.files[index];
                    if (!fieldData.file || fieldData.id)
                        continue;
                    const error = await this.loadFile(fieldData)
                    error && errorData.push(error)
                }
            }
        }))
    }

    async create() {
        const itemsToSave = (this.properties.shipments_items.dataSource.store._array ?? []).filter(item => item.not_saved)
        if (!itemsToSave.length) {
            return
        }

        await this.loadFiles(itemsToSave)

        const dataForSending = []

        itemsToSave.forEach(item => {
            dataForSending.push({
                amount: item.amount,
                plan_date_release: item.plan_date_release,
                claim_ids: this.claim_ids,
                files: [...(item.files ?? []).map(item => item.id), ...(this.files ?? []).map(item => item.id)]
            })
        })

        const answer = await Http.api.rpc("ref_schedule", {
            ref: "exc_schedule_pgo",
            op: "create",
            data: dataForSending
        })

        itemsToSave.forEach(item => item.not_saved = false)
        let messages=''
        if (answer.error?.data?.length) {
            answer.error.data.forEach((item, index) => {
                if (item != 'ok') {
                    this.properties.shipments_items.dataSource.store._array[index].not_saved = true
                    messages+=`\n Строка ${item.row}: ${item.message}`
                }
            })
        }
        messages&&Vue.Dialog.MessageBox.Error(messages)
        return !answer.error
    }
}

function addNull(num) {
    if (num < 10) {
        return `0${num}`;
    }
    return num;
}
class ShipmentItemForm extends Entity {
    constructor(context) {
        super(context);
        let plan_date_release = new Date();
        plan_date_release.setDate(plan_date_release.getDate() + 1);
        plan_date_release = `${plan_date_release.getFullYear()}-${addNull(plan_date_release.getMonth() + 1)}-${addNull(plan_date_release.getDate())}`

        this.plan_date_release = context.plan_date_release ?? plan_date_release
        this.amount = context.amount ?? 1
        this.files = context.files ?? []
    }


    validate_date() {
        if (this.plan_date_release && new Date(this.plan_date_release) > new Date()) {
            this.properties.plan_date_release.status = undefined
        } else if (this.plan_date_release && new Date(this.plan_date_release) <= new Date()) {
            this.properties.plan_date_release.status = {
                type: "error",
                message: Language.t("select_date_not_today")
            }
            return true;
        }
    }

    props() {
        return {
            plan_date_release: {
                label: "shipment_plan_schedule_date",
                required: true,
                type: "date",
                onChange: () => {
                    this.validate_date();
                }
            },
            amount: {
                label: "shipment_quantity",
                required: true,
                type: "number",
                onChange: () => {
                    this.properties.amount.status = undefined
                    if (this.amount <= 0) {
                        this.properties.amount.status = {
                            type: "error",
                            message: "Количество товаров должно быть больше нуля"
                        }
                        return true;
                    }
                }
            },
            files: {
                label: "shipment_files",
                type: "file",
                multiple: true,
            }
        }
    }
}
class ContractsSelector extends Entity {
    constructor(context) {
        super(context);
        this.contract = undefined
    }

    props() {
        return {
            contract: {
                label: "which_contract_for_shipment",
                type: "entity",
                dataSource: new DataSource({
                    valueExp: ["id", "checkGroup", "amount_of_good_in_lot"],
                    store: new RefStore({
                        method: "ref",
                        ref: "ref_exchanges_contracts",
                        injectQuery: (params) => (params.filters = { ...params.filters, status: 'approved' }, params),
                        context: (context) => {
                            context.contracts = context.claim_ids?.length && DataSource.get(context.claim_ids)
                            context.name = `${context.name} (${context.id})`
                            context.desc = `${context.amount_of_good_in_lot} ${context.unit}`
                            return context
                        }
                    })
                })
            }
        }
    }
}

export default {
    data() {
        return {
            ShipmentScheduleSellerBookingDataSource: new DataSource({
                store: new RefStore({
                    method: "ref_schedule",
                    ref: "exc_schedule_pgo_booking",
                    injectQuery: (params) => (params.filters = { ...params.filters, exc_person: 'exc_seller' }, params)
                }),
                query: query(),
                template: "template-shipment_schedule_booking"
            }),
            ShipmentScheduleBuyerBookingDataSource: new DataSource({
                store: new RefStore({
                    method: "ref_schedule",
                    ref: "exc_schedule_pgo_booking",
                    context: context => {
                        context.actions = [
                            {
                                label: "delete",
                                handler: async () => {
                                    const requestData = { ref: "exc_schedule_pgo_booking", op: "delete", data: { id: context.id } }
                                    const { error, data } = await Http.api.rpc('ref_schedule', requestData);
                                    error ? Vue.Dialog.MessageBox.Error(error) : this.ShipmentScheduleBuyerBookingDataSource.reload()
                                }
                            },
                            {
                                label: "edit",
                                handler: async () => {
                                    const model = new BookingEditor(context)
                                    Vue.Dialog({
                                        props: ["model"],
                                        methods: {
                                            async save() {
                                                if (await this.model.create()) {
                                                    Vue.Dialog.MessageBox.Success("Изменения успешно сохранены.")
                                                    this.Close()
                                                    this.ShipmentScheduleBuyerBookingDataSource.reload()
                                                }
                                            }
                                        },
                                        template: `
                                          <div>
                                            <header>{{$t("change_shipment_booking")}}</header>
                                            <main>
                                                <ui-layout :fields='model.fields' />
                                            </main>
                                            <footer>
                                                <ui-btn type='secondary' v-on:click.native='Close'>{{$t('Close')}}</ui-btn>
                                                <ui-btn type='primary' v-on:click.native='save'>{{$t('Save')}}</ui-btn>
                                            </footer>
                                          </div>
                                        `
                                    }).Modal({ model })
                                }
                            }
                        ]
                        return context
                    },
                    injectQuery: (params) => (params.filters = { ...params.filters, exc_person: 'exc_buyer' }, params)
                }),
                query: query(),
                actions: [
                    {
                        label: "add",
                        handler: async () => {
                            const model = new Booking()
                            Vue.Dialog({
                                props: ["model"],
                                methods: {
                                    async save() {
                                        if (await this.model.create()) {
                                            Vue.Dialog.MessageBox.Success(Language.t("new_shipment_reserved"))
                                            this.Close()
                                        }
                                    }
                                },
                                template: `
                                  <div>
                                    <header>{{$t("create_ship_reserve")}}</header>
                                    <main>
                                        <ui-layout :fields='model.fields' />
                                    </main>
                                    <footer>
                                        <ui-btn type='secondary' v-on:click.native='Close'>{{$t('Close')}}</ui-btn>
                                        <ui-btn type='primary' v-on:click.native='save'>{{$t('Create')}}</ui-btn>
                                    </footer>
                                  </div>
                                `
                            }).Modal({ model })
                        },
                        hidden: () => {
                            return !Context.Access.policy.exc_pgo_booking_create;
                        },
                    }
                ],
                template: "template-shipment_schedule_booking"
            }),
            ShipmentScheduleSellerDataSource: new DataSource({
                store: new RefStore({
                    method: "ref_schedule",
                    ref: "exc_schedule_pgo",
                    context: context => {
                        context.actions = [
                            {
                                label: "delete",
                                handler: async () => {
                                    const requestData = { ref: "exc_schedule_pgo", op: "delete", data: { id: context.id } }
                                    const { error, data } = await Http.api.rpc('ref_schedule', requestData);
                                    error ? Vue.Dialog.MessageBox.Error(error) : this.ShipmentScheduleSellerDataSource.reload()
                                },
                                hidden: () => {
                                    return !Context.Access.policy.exc_pgo_delete;
                                },
                            },
                            {
                                label: "edit",
                                handler: async () => {
                                    const { amount } = await Vue.Dialog.MessageBox.Form({ fields: (new ShipmentItemEditForm({ ...context })).fields }, "Редактирование данных ПГО")

                                    if (!context.id || !amount) {
                                        Vue.Dialog.MessageBox.Info("Данные исправлены не верно")
                                        return
                                    }

                                    const { error, answer } = await Http.api.rpc("ref_schedule", {
                                        ref: "exc_schedule_pgo",
                                        op: "update",
                                        data: { id: context.id, amount }
                                    })
                                    if (error) {
                                        Vue.Dialog.MessageBox.Error(error)
                                    } else if (answer.error?.data?.length) {
                                        Vue.Dialog.MessageBox.Info("Данные исправлены не верно")
                                    } else {
                                        Vue.Dialog.MessageBox.Info("Изменения успешно сохранены")
                                        this.ShipmentScheduleSellerDataSource.reload()
                                    }
                                },
                                hidden: () => {
                                    return !Context.Access.policy.exc_pgo_update;
                                },
                            }
                        ]
                        return context
                    },
                    injectQuery: (params) => (params.filters = { ...params.filters, plan_date_release_error: undefined, exc_person: 'exc_seller' },  params)
                }),
                query: query(), 
                actions: [
                    {
                        label: 'add',
                        handler: async () => {
                            const contractSelection = await Vue.Dialog.MessageBox.Form({ fields: (new ContractsSelector()).fields }, Language.t("select_contract_for_shipment"))

                            const { contract } = contractSelection ?? {}
                            if (!contract) {
                                Vue.Dialog.MessageBox.Success(Language.t("ship_contract_not_selected"))
                                return
                            }
                            const idArray = [contract.id]
                            const amount = contract.amount_of_good_in_lot
                            if (!idArray?.length || !amount) {
                                Vue.Dialog.MessageBox.Success("Не выбраны договоры или количество товаров в них не указано")
                                return
                            }

                            const model = new ShipmentSchedule({ idArray, amount })
                            Vue.Dialog({
                                props: ["model"],
                                methods: {
                                    async save() {
                                        if (await this.model.create()) {
                                            Vue.Dialog.MessageBox.Success(Language.t("shipment_plan_created"))
                                            this.Close()
                                        }
                                    }
                                },
                                template: `
                                  <div>
                                    <header>{{$t("create_shipment_plan")}}</header>
                                    <main>
                                        <ui-layout :fields='model.fields' />
                                    </main>
                                    <footer>
                                        <ui-btn type='primary' v-on:click.native='save'>{{$t('Create')}}</ui-btn>
                                        <ui-btn type='secondary' v-on:click.native='Close'>{{$t('Close')}}</ui-btn>
                                    </footer>
                                  </div>
                                `
                            }).Modal({ model })
                        },
                        hidden: () => {
                            return !Context.Access.policy.exc_pgo_create;
                        },
                    },
                ],
                template: "template-shipment_schedule"
            }),
            ShipmentScheduleBuyerDataSource: new DataSource({
                store: new RefStore({
                    method: "ref_schedule",
                    ref: "exc_schedule_pgo",
                    injectQuery: (params) => (params.filters = { ...params.filters, exc_person: 'exc_buyer' }, params)
                }),
                query: query(),
                template: "template-shipment_schedule"
            })
        }
    },
    template: `
    <iac-access :access='$policy.exc_pgo_read'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.shipment_schedule')}}</li>
            </ol>
            <h1>{{$t('nav.shipment_schedule')}}</h1>
        </iac-section>
        <iac-section>
            <ui-layout-tab :clear='true' :sync='false' >
                <ui-layout-group key='Seller' label='Seller'>
                    <ui-layout-tab :clear='true' :sync='false' >
                        <ui-layout-group key='Seller' label='shipment_schedule'>
                            <ui-data-view :dataSource='ShipmentScheduleSellerDataSource'/>
                        </ui-layout-group>
                        <ui-layout-group key='SellerBooking' label='booking'>
                            <ui-data-view :dataSource='ShipmentScheduleSellerBookingDataSource'/>
                        </ui-layout-group>
                    </ui-layout-tab>
                </ui-layout-group>
                <ui-layout-group key='Buyer' label='Buyer'>
                    <ui-layout-tab :clear='true' :sync='false' >
                        <ui-layout-group key='Buyer' label='shipment_schedule'>
                            <ui-data-view :dataSource='ShipmentScheduleBuyerDataSource'/>
                        </ui-layout-group>
                        <ui-layout-group key='BuyerBooking' label='booking'>
                            <ui-data-view :dataSource='ShipmentScheduleBuyerBookingDataSource'/>
                        </ui-layout-group>
                    </ui-layout-tab>
                </ui-layout-group>
            </ui-layout-tab>
        </iac-section>
    </iac-access>
    `
}
