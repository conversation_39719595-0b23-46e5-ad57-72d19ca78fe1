@import './claim/index.less';
@import './contract/index.less';
@import './company/billing/index.less';
@import './purchase/index.less';
@import './sysop/settings/events/index.less';
@import './sysop/settings/doc/index.less';
@import './company/users/ip_access.less';
@import './documentation/index.less';

.page-report {
  .report-wrapper {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;

    >.report {
      padding: 1px;
      overflow: auto;
    }
  }

  .report-frame {
    margin: 0 auto;
    display: block;
    border: none;
    overflow: hidden;
  }
}

.dashboard-section {
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid #F3F3F3;
  padding-bottom: 36px;
  margin-bottom: 36px;

  >div {
    width: 100%;

    .iac-card {}
  }
}

.dropdown-content--mx-width {
  .dropdown {
    >.content {
      max-width: initial;
    }
  }
}


.notification_details {
  .text-content {
    font-family: Helvetica, Arial, sans-serif;
    font-size: 16px;
    padding: 0px 15px;
    text-decoration: none;
    white-space: pre-wrap;
  }

  .button-block {
    font-family: Helvetica, Arial, sans-serif;
    font-size: 16px;
    text-align: center;
    text-decoration: none;

    .button {
      background: #2ba6cb repeat center center;
      color: #ffffff;
      display: inline-block;
      font-family: Helvetica, Arial, sans-serif;
      font-size: 16px;
      font-weight: bold;
      padding: 9px 10px;
      text-decoration: none;
      width: auto;
    }
  }
}