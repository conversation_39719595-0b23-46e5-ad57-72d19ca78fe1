export default {
    name: "ebpLangSelect",
    model: {
        prop: 'name',
        event: 'change'
    },
    props: {
        langs: {
            type: Array,
            required: true
        },
        name: {
            type: Object,
            required: true
        },
        label: String,
        placeholder: String,
        disabled: {
            type: Boolean,
            default: false
        },
        enableShowChanges: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {}
    },
    methods: {
        onChangeInput(langName, newValue) {
            this.change(langName, newValue)
        },
        change(langName, newValue) {
            const newName = { ...this.name, [langName]: newValue }
            this.enableShowChanges && (newName.is_new = true)
            this.$emit('change', newName)
        }
    },
    template: `
    <div class="iac--ebp-multilang-input">
        <label v-if="label">
            {{label}}
        </label>
        <div>
            <b v-if="enableShowChanges && name.is_new">!</b>
            <div v-for="(lang, index) in langs" v-if="lang.active">
                <div>{{lang.title}}</div>
                <input v-model="name[lang.name]"
                    @change="e=>onChangeInput(lang.name, event.target.value)"
                    :placeholder="placeholder"
                    :disabled="disabled"/>
            </div>
        </div>
    </div>
    `
}