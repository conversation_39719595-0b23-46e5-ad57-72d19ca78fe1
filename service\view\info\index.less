.static-page{
  color: #201D1D;

  table {
    width: 100%;
    min-width: 250px;
    border-collapse: collapse;
    background: #fff;
  }

  tr:first-child {

    td {
      padding: 17px 10px;
      font-size: 13px;
      font-weight: 500;
      color: #969595;
      line-height: 1.23;
      letter-spacing: 0.05em;
      text-transform: uppercase;
    }
  }

  td {
    padding: 16px 8px;
    color: #201d1d;
    border: 1px solid #f3f3f3;
    font-size: 14px;
    line-height: 20px;
    vertical-align: top;
  }

  .iac-section:not(.iac-section-header){
    >.iac-section-container{
      padding-top: 12px;
      >div{
        max-width: 800px;
        line-height: 24px;
        font-size: 16px;     
      }      

      h3{
        font-size: 20px;
        line-height: 24px;
        margin: 0;
        padding: 0;
        margin-block-start: 32px;
        margin-block-end: 12px;
        &:first-child{
          margin-block-start: 0;
        }
      }
      p{
        display: block;
        margin-block-start: 8px;
        margin-block-end: 8px;
        &:first-child{
          margin-block-start: 0;
        }
      }

      ul{
        li{
          //list-style-type: none;
          &::marker{
            //content: '\2023';
            color: @primary-link;
            font-size: 18px;
          }
        }
      }
      .quote{
        display: flex;
        align-items: flex-start;
        margin-block-start: 32px;
        margin-block-end: 32px;
        >img{
          flex: 0 0 292px;
          height: 292px;
          width: 292px;
          margin-right: 15px;
        }
        >div{
          background: #EEF6F8;
          border-radius: 8px;
          font-size: 16px;
          line-height: 24px;
          color: #201D1D;
          padding: 24px 32px;
          .sub{
            font-size: 14px;
            line-height: 20px;
          }
        }
      }
    }
  }
  .iac-section.iac-calc-wrapper{
    >.iac-section-container{
      >div{
        max-width: 1200px;
      }      
    }
  }
}