@import './tiles/order_book/index.less';
@import './tiles/exchange_contract/index.less';
@import './tiles/bkl_exchange_contract/index.less';
@import './tiles/proposal/index.less';
@import './tiles/timer/index.less';
@import './tiles/history/index.less';

@keyframes insert {
    10%,
    90% {
        //box-shadow: inset 0 0 5px rgba(0, 0, 128, 0.1);
        border: 5px solid #faa11b44;
    }
  
    20%,
    80% {
        //box-shadow: inset 0 0 100px #667b9f;
        border: 4px solid #faa11b;
    }
  
    30%,
    50%,
    70% {
        //box-shadow: inset 0 0 10px rgba(0, 0, 128, 0.2);
        border: 2px solid #faa11b44;
    }
  
    40%,
    60% {
        //box-shadow: inset 0 0 20px rgba(0, 0, 128, 0.0);
        border: 7px solid #faa11b;
    }
  }


.iac-terminal {
    display: flex;
    flex-direction: column;

    .panel {
        background: #dfdfdf;
        color: #333;
        //border-color: #5d6c8b !important;
    }

    .tail-components {
        border: 1px solid #e5e5e5;

        >.title {
            flex: 0 0 auto;
            background: #eee;
            margin: 0;
            z-index: unset;
            border-left: 1px solid #f9f9f9;
            border-top: 1px solid #f9f9f9;
            border-bottom: 1px solid #cfcfcf;
            font-size: 14px;
            line-height: 30px;
            padding: 0 8px;
            color: #868ea2;
        }

        >.content {
            background: #fff;

            >div {
                cursor: pointer;
                padding: 8px 0;
                color: #666;
                font-size: 14px;
                display: flex;

                &:hover {
                    background: #83a5c412;
                }

                >icon {
                    text-align: center;
                    flex: 0 0 30px;
                    color: #009ab8;
                }

                &:not(:first-child) {
                    border-top: 1px solid #e5e5e5;
                }
            }
        }
    }

    >.ui-layout-tab {
        height: 100%;
        display: flex;
        flex-direction: column;

        >.tab-nav {
            flex: 0 0 auto;
            border: none;
            margin: 0;
            

            >.tab-nav-items {
                >.item.active {
                    border: none;
                }
            }

            >.tab-nav-action-group {
                display: flex;
                flex: 0 0 auto;

                >.tab-nav-action {
                    >.toggle {
                        margin-right: 0;
                    }
                }
            }

        }

        >.tab-content {
            flex: 1 1 0;

            >.tab-content-action {
                .action-group {
                    padding: 0 0 0 8px;
                }
            }

            >.ui-layout-group {
                display: flex;
                overflow: auto;
                border: 1px solid #ccc;
            }
        }
    }

    .ui-control,
    .ui-field {
        min-width: 150px;
    }

    .ui-layout-group {
        padding: 0;
        margin: 0;
        border: none;
        min-width: 150px;

        >.label {
            margin: 0;

            >.title {
                padding: 0;
                font-size: 14px;
                line-height: 20px;
                //font-weight: normal;
                margin: 8px 0;
            }
        }

        &.collapsed {
            >.label {
                padding: 0 4px;
                background: #eeeeee;

                >.title {
                    font-weight: normal;
                }

                &::after {
                    font-size: 7px;
                }
            }

            >.content {
                padding: 5px;
                font-size: 13px;
            }
        }
    }

    .ui-data-list {
        margin: 0;
        padding: 0;

        .table-wrapper {
            border: none;

            thead {
                //z-index: 1;
                top: 0;
                position: sticky;
                height: 36px;
                background: #fcfcfc;
            }

            thead th {
                background: #fcfcfc;
                line-height: normal;
                padding: 6px;
                border-top: none;
            }

            tbody tr.active td {
                background: #a1c8e7;
            }

            tbody td {
                font-size: 13px;
                line-height: normal;
                padding: 6px;
            }
        }
    }

    .terminal-window {
        border: none;
        display: flex;
        flex-direction: column;
        min-width: 100px;
        min-height: 100px;
        flex: 1 1 0;
        flex-shrink: 0;

        &.ui-layout-tab {
            flex-direction: column;
            margin: 0;
            //z-index: 1;

            >.tab-nav {
                flex: 0 0 auto;
                background: #eee;
                margin: 0;
                z-index: unset;

                border-left: 1px solid #f9f9f9;
                border-top: 1px solid #f9f9f9;
                border-right: 1px solid #cfcfcf;
                border-bottom: 1px solid #cfcfcf;

                >.tab-nav-title {
                    line-height: 30px;
                }

                >.prefix {
                    >.group {
                        padding: 0 2px 0 4px;

                        >.icon {
                            width: 12px;
                            height: 12px;
                            border: 1px solid #eeeeee94;
                            //background: #00a9dc;
                            cursor: pointer;
                            border-radius: 50%;
                            display: flex;
                            transform: rotateZ(45deg);
                            align-items: center;
                            justify-content: center;
                            font-size: 6px;
                            color: #000;
                            background: #eeeeee94;
                            >icon{
                                width: 100%;
                                text-align: center;
                            }

                            &.delete {
                                transform: rotateZ(0);

                                >icon {
                                    display: none;
                                }

                                &:hover {
                                    >icon {
                                        display: inline-block;
                                    }
                                }
                            }

                        }

                        >.content {
                            position: absolute;
                            //left: 0;
                            //top: 0;
                            //width: 100%;
                            //min-width: 250px;
                            //max-height: 400px;
                            z-index: 10;
                            background: #fff;
                            box-shadow: 0px -3px 7px rgba(0, 0, 0, 0.1);
                            border: 1px solid #ccc;

                            .ui-list .ui-list-item {
                                span {
                                    width: 10px;
                                    height: 10px;
                                    display: inline-block;
                                    margin-right: 10px;
                                }
                            }

                        }
                        &.dropdown-top{
                            >.content {
                                bottom: 100%;
                            }
                        }
                    }

                    >.search {
                        margin-right: -4px;

                        >icon {
                            cursor: pointer;
                            padding: 4px;
                            font-size: 13px;
                        }

                        >.content {
                            position: absolute;
                            left: 0;
                            top: 0;
                            width: 100%;
                            min-width: 250px;
                            max-height: 400px;
                            z-index: 10;
                            background: #fff;
                            box-shadow: 0px -3px 7px rgba(0, 0, 0, 0.1);
                            border: 1px solid #ccc;
                        }
                    }

                }


                >.tab-nav-items {

                    display: flex;
                    flex: 1 1 0;
                    overflow: hidden;
                    background: transparent;
                    margin-top: -1px;

                    >.item {
                        line-height: 20px;
                        font-size: 11px;
                        padding: 5px 18px 4px 8px;
                        border: none;
                        text-transform: unset;
                        text-overflow: ellipsis;
                        overflow: hidden;

                        >.close {
                            right: 3px;
                        }

                        >span {

                            white-space: nowrap;
                        }

                        &.active {
                            background: #fcfcfc;
                        }
                    }
                }

                >.tab-nav-action-group {
                    display: flex;
                    flex: 0 0 auto;

                    >.tab-nav-action {
                        >.toggle {
                            margin-right: 0;

                            >icon {
                                padding: 4px;
                                font-size: 14px;
                                line-height: 20px;
                            }
                        }
                    }
                }
            }

            >.tab-content {
                flex: 1 1 auto;
                background: #fff;
                min-height: 0;

                >.ui-layout-group {
                    //overflow: auto;
                    min-width: 0;
                    width: 100%;

                    >.content {
                        font-size: 14px;
                        background: #fcfcfc;
                        >.empty {
                            background: #dfdfdf;
                            position: relative; 
                            align-items: center;
                            display: flex;
                            >.arrow{
                                position: absolute;
                                top: 10px;
                                left: 22px;
                                >svg path{
                                    fill: #666;
                                }
                            }
                            >.content{
                                color: #666;
                                text-align: center;
                                width: 100%;
                               
                                >icon{
                                    font-size: 20px;
                                }
                            }
                        }
                    }
                }
            }

            .tab-nav>.prefix>.group .ui-list-item.group_1>.content>.icon,
            &.group_1>.tab-nav>.prefix>.group>.icon {
                background: #ffd450;
            }

            .tab-nav>.prefix>.group .ui-list-item.group_2>.content>.icon,
            &.group_2>.tab-nav>.prefix>.group>.icon {
                background: #ff7b76;
            }

            .tab-nav>.prefix>.group .ui-list-item.group_3>.content>.icon,
            &.group_3>.tab-nav>.prefix>.group>.icon {
                background: #a381ff;
            }

            .tab-nav>.prefix>.group .ui-list-item.group_4>.content>.icon,
            &.group_4>.tab-nav>.prefix>.group>.icon {
                background: #4dc3f7;
            }

            .tab-nav>.prefix>.group .ui-list-item.group_5>.content>.icon,
            &.group_5>.tab-nav>.prefix>.group>.icon {
                background: #aed57f;
            }

            .tab-nav>.prefix>.group .ui-list-item.group_6>.content>.icon,
            &.group_6>.tab-nav>.prefix>.group>.icon {
                background: #4da197;
            }

            .tab-nav>.prefix>.group .ui-list-item.group_7>.content>.icon,
            &.group_7>.tab-nav>.prefix>.group>.icon {
                background: #ffb74c;
            }
        }

        >.sep {
            background: transparent;
            flex: 0 0 5px;
            cursor: s-resize;
            position: relative;

            background: #ededed;
            border-left: 1px solid #f9f9f9;
            border-top: 1px solid #f9f9f9;
            border-right: 1px solid #cfcfcf;
            border-bottom: 1px solid #cfcfcf;

            margin: 0;
            //z-index: 1;

            &:hover {
                background: #a1c8e7;
            }

            &::before {
                content: " ";
                display: none;
                background: #a1c8e765;
                position: absolute;
                right: 0px;
                left: 0px;
                top: -100px;
                bottom: -100px;
                pointer-events: none;
                z-index: 500;
            }

            &.drag {
                &::before {
                    display: block;
                }
            }
        }

        &.document {

            &.insert{
                position: relative;
                &::after{
                    content: " ";
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    top: 0;
                    left: 0;
                    //box-shadow: inset 0 0 50px rgba(0, 0, 127, 0.5);
                    z-index: 100;
                    animation: insert 1s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
                    //border: 2px solid #eea236;
                    pointer-events: none;
                }
            }

            position: relative;

            // z-index: 1;
            >.move {
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
                z-index: 500;

                >div:not(.mask) {
                    left: 0;
                    right: 0;
                    top: 0;
                    bottom: 0;
                    position: absolute;
                    border: 1px dashed #aaa;

                    &[data-ord='top'] {
                        bottom: 80%;
                    }

                    &[data-ord='bottom'] {
                        top: 80%;
                    }

                    &[data-ord='left'] {
                        right: 80%;
                    }

                    &[data-ord='right'] {
                        left: 80%;
                    }
                }

                >.mask {
                    background: #a1c8e765;
                    position: absolute;
                    left: 0;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    pointer-events: none;
                    //transition: all .05s ease-out;
                }

                &.top .mask {
                    bottom: 50%;
                }

                &.bottom .mask {
                    top: 50%;
                }

                &.left .mask {
                    right: 50%;
                }

                &.right .mask {
                    left: 50%;
                }
            }

        }

        &.horizontal {
            flex-direction: row;

            >.sep {
                cursor: e-resize;

                //border-top: unset;
                //border-left: 1px solid rgb(204, 204, 204);
                //margin: 0 -3px 0 0;
                &::before {
                    right: -100px;
                    left: -100px;
                    top: 0;
                    bottom: 0;
                }
            }
        }


    }


    .ui-checkbox {
        margin-block-start: 2px;
        margin-block-end: 2px;

        input[type=checkbox] {
            height: 15px;
            width: 15px;

            &::after {
                left: 4px;
                top: 1px;
            }
        }

        >label {
            line-height: 14px;
            font-size: 13px;
            color: #666;
        }
    }

    .ui-field {
        margin-bottom: 5px;

        >.field-content>.field-label>.label-wrapper>label {
            font-size: 13px;
            line-height: 14px;
        }

        >.field-content>.field-container>.field-description {
            font-size: 12px;
        }
    }

    .ui-control {

        >.toolbar,
        >.suffix,
        >.prefix {
            height: 32px;
        }

        >.container>.control {
            min-height: 32px;
        }

        >.container>label {
            top: 5px;
        }
    }



    &.vs_2022 {
        .panel {
            background: #cfd6e5 !important;
            color: #2f3f5c !important;
            //border-color: #5d6c8b !important;
        }
        .tail-components>.title {
            background: #cfd6e5;
            color: #4d6082;
            border: none;
        }

        .ui-checkbox input[type=checkbox]:checked {
            background: #4d6082;
        }

        >.ui-layout-tab {
            >.tab-nav {
                background: #cfd6e5;

                >.tab-nav-items {
                    >.item {
                        color: #4d60829c;

                        &.active {
                            color: #4d6082;
                        }
                    }
                }

                >.tab-nav-action-group {
                    >.tab-nav-action {
                        >.toggle {
                            color: #4d6082;
                        }
                    }
                }
            }
        }

        .terminal-window {
            &.ui-layout-tab {
                >.tab-nav {
                    &.tab-header {
                        background: #4d6082;
                        border: 1px solid #5d6c8b;
                        color: #cbd1db;

                        >.tab-nav-title {
                            color: #cbd1db;
                            line-height: 30px;
                        }

                        >.tab-nav-action-group {
                            >.tab-nav-action {
                                >.toggle {
                                    color: #cbd1db;

                                    &:hover {
                                        color: #fff;

                                        >icon {
                                            color: #fff;

                                            &.arrow {
                                                border-left: 1px solid #ffffff26;
                                            }
                                        }
                                    }
                                }
                            }

                            >.close {
                                color: #cbd1db;
                                font-size: 8px;

                                &:hover {
                                    color: #fff;
                                }
                            }
                        }
                    }

                    &.tab-footer {
                        border: none;
                        background: #2f3f5c;

                        >.tab-nav-items {
                            background: #4e5f83;
                            flex: unset;
                            margin: 0;

                            >.item {
                                color: #cbd1db;

                                &.active {
                                    color: #2f3f5c;
                                }
                            }
                        }
                    }
                }
            }

            >.sep {
                background: #2f3f5c;
                border: none;
            }
        }

        >.ui-layout-tab>.tab-content>.ui-layout-group {
            border: 5px solid #2f3f5c;

        }

        .window-toolbar {
            background: #cfd6e5;
            color: #4e5f83;

            .ui-action>.toggle {
                color: #4e5f83;

                &:hover {
                    color: #2f3f5c;

                    >icon {
                        color: #2f3f5c;

                        &.arrow {
                            border-left: 1px solid #ffffff26;
                        }
                    }
                }
            }
        }

        thead th {
            background: #cfd6e5 !important;
            color: #2f3f5c !important;
            //border-color: #5d6c8b !important;
        }

        .ui-layout-group {
            &.collapsed {
                >.label {
                    background: #667b9f;

                    >.title {
                        color: #cfd6e5;
                    }

                    &::after {
                        color: #cfd6e5;
                    }
                }
            }
        }
    }

}