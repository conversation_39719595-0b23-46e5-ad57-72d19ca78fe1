import Model from './model/template'

let EditModel = {
    props: ["model"],
    methods: {
        async save() {
            await this.$wait(async ()=>{
                if (!this.model)
                    return;
                const { error } = await this.model.save();
                if(!error){
                    await Vue.Dialog.MessageBox.Success(this.$t('procedure_saved'))
                }
            })

        },
        async set_chairman(item) {
            if (await Vue.Dialog.MessageBox.Question(this.$t('question_set_chairman')) != Vue.Dialog.MessageBox.Result.Yes) {
                return;
            }
            await this.$wait(async () => {
                if (this.model && this.model.stage) {
                    await this.model.stage.set_chairman(item)
                }
            })
        },
        roles(item) {
            if (!item.roles)
                return;
            return item.roles.map((role) => {
                return "- " + role.name
            }).join("\n")
        }
    },
    template: `
             <div>
                <ui-layout-group horizontal=2>
                    <ui-layout-group>
                        <ui-field :model='model.properties.title'/>
                        <ui-field :model='model.properties.anno'/>
                    </ui-layout-group>
                    <ui-layout-group label='committee' class='dropdown-content--mx-width' :actions='model.stage.actions'>
                        <ui-list check :dataSource='model.stage.users'>
                            <template slot='template' slot-scope='props'>
                                <div style='flex: 1 1 auto;'>
                                    <b>{{props.item.name}}</b>
                                    <div style='white-space: pre-line; font-size: 12px;'>{{roles(props.item)}}</div>
                                </div>
                                <ui-btn v-if='props.item.is_chairman' type='primary'>{{ $t('chairman') }}</ui-btn>
                                <ui-btn v-else-if='props.item.can_be_chairman' type='default' @click.native='set_chairman(props.item)'>{{ $t('chairman') }}</ui-btn>
                                <span class='ui-btn ui-btn-link' v-if='props.item.is_sec' type='default'><b>{{ $t('secretary') }}</b></span>
                            </template>
                        </ui-list>
                    </ui-layout-group>                    
                </ui-layout-group>
            </div>

    `
}

export default {
    data: function () {
        return {
            model: undefined,
            error: undefined
        }
    },
    mounted: async function () {
        this.$wait(async ()=>{
            let { error, data } = await Model.get(this.$route.params.id);
            this.model = data;
            this.error = error;            
        })


    },
    components: {
        EditModel
    },
    template: `
    <div>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li><router-link to='/workspace/agreement/template'>{{$t('nav.templates')}}</router-link></li>
                <li>{{$t('template')}}</li>
            </ol>
            <h1>{{$t('template')}}: <span v-if='model'>{{model.title}}</span></h1>
        </iac-section>
        <iac-section>            
            <div v-if='error'>{{error}}</div>
            <EditModel v-if='model' :model='model'/>
        </iac-section>
    </div>
    `
}