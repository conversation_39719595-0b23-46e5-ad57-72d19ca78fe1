export default {
    props: ["model"],
    computed: {
        needToAdvancePaymentAmount() {
            return (this.model.graphic || []).some(item => item.advance_payment != undefined)
        }
    },
    template: `
    <div class='grid'>
        <div class='row'>
            <h2>{{$t('contract.graphic')}}</h2>
        </div>
        <div class='row graphic'>
            <table>
                <thead>    
                    <tr>
                        <th>№</th>
                        <th>{{$t('month')}}, {{$t('year')}}</th>
                        <th>{{$t('summa')}}</th>
                        <th v-if='model.is_gos'>{{$t('kls')}}</th>
                        <th v-if='model.is_gos' style='width: 100%;text-align: left;'>{{$t('expense_item_code')}}</th>
                        <th v-if="needToAdvancePaymentAmount">{{$t('advance_payment_amount')}}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for='(graphic,index) in model.graphic'>
                        <td>{{index+1}}</td>
                        <td>{{$t("month_"+graphic.month)}}, {{graphic.year}}</td>
                        <td><iac-number :value='graphic.sum' delimiter=' ' part='2'/> {{graphic.currency || model.contract_currency}}</td>
                        <td v-if='model.is_gos'><span>{{graphic.details}}</span></td>
                        <td v-if='model.is_gos' style='white-space: normal; text-align: left;'><span v-if='graphic.base'>{{graphic.base.code}} {{graphic.base.name}}</span></td>
                        <td v-if="needToAdvancePaymentAmount"><iac-number :value='graphic.advance_payment||0' delimiter=' ' part='2'/> {{graphic.currency || model.contract_currency}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>    
    `
}