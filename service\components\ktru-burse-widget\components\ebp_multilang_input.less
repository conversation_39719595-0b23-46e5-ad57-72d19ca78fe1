.iac--ebp-multilang-input {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  margin-bottom: 8px;

  >label {
    flex: 0 0 auto;


  }

  >div {
    flex: 1 1 auto;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: stretch;
    position: relative;
    margin-left: -6px;

    >b {
      position: absolute;
      font-size: 24px;
      color: red;
      left: -8px;
      top: 3px;
    }

    >div {
      flex: 1 1 auto;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: stretch;
      border: 1px solid transparent;
      border-radius: 4px;
      border-color: #006f85;
      overflow: hidden;
      height: 35px;
      max-width: 260px;
      margin-left: 6px;

      >div {
        flex: 0 0 auto;
        text-decoration: none;
        display: inline-block;
        font-weight: normal;
        text-align: center;
        vertical-align: middle;
        padding: 6px;
        font-size: 14px;
        line-height: 1.5;
        background-color: #00859f;

      }

      >input {
        flex: 1 1 auto;
        width: 100%;
        border: none;
        outline: none;

        &:disabled {
          background-color: #f6f6f6;
          border-color: #e6e6e6;
          cursor: unset;
          color: #201D1D;
        }
      }
    }
  }
}