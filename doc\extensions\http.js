import { Entity } from '@iac/data'
import { HttpExtensions } from '@iac/kernel'

class QuestionModel extends Entity {
    constructor(context) {
        super(context)
        this.source = localStorage.getItem("extensions_http_source");

        this.source = this.source || `{
\tsize: "sm",
\tsubscribe: true,
\ttitle: "Заголовок окна",
\tfields: {
\t\ttest_field: {
\t\t\t  
\t\t}
\t},
\tsubmit: "Отправить"
}`

        this.update_result(this.source);
    }
    update_result(value) {
        try {
            let question = new Function(`return ${value}`)()
            this.properties.result.setAttributes({
                fields: { ...question.fields }
            })

        } catch (e) {
            this.properties.source.status = {
                type: "error",
                message: e
            }
        }
    }
    props() {
        let $this = this;
        return {
            source: {
                type: "code-editor",
                group: "-!questrion/Исходный код",
                label: "!",
                attr: {
                    react: true,
                    style: "max-width: 500px;",
                    autoclose: localStorage.getItem("extensions_http_source_autoclose")
                },
                onChange: (value) => {
                    localStorage.setItem("extensions_http_source", value)
                    this.update_result(value);

                },
                actions: [
                    {
                        label: "autoclose",
                        get icon() {
                            if ($this.properties.source.attr.autoclose)
                                return "check2"
                        },
                        handler: () => {
                            this.properties.source.attr.autoclose = !this.properties.source.attr.autoclose
                            localStorage.setItem("extensions_http_source_autoclose", this.properties.source.attr.autoclose)
                        }
                    },
                    {
                        type: "sep"
                    },
                    {
                        label: "Сделать запрос Question",
                        handler: async () => {
                            let question = new Function(`return ${this.source}`)()

                            await HttpExtensions.Question.response({
                                response: {
                                    data: {
                                        question: question
                                    }
                                },
                                config: {},
                                handler: (data) => {
                                    return {
                                        error: {
                                            message: data,
                                            details: {
                
                                            }
                                        },
                                    }
                                }
                            })                      
                        }
                    }
                ]
            },
            result: {
                type: "model",
                label: "!",
                group: "-questrion/Предварительный просмотр",
            }
        }
    }
}

export default {
    data: function () {
        return {
            model: new QuestionModel()
        }
    },
    template: `
## Http extensions
> Расширения перехватывают события при работе с http серверами
- request
- requestError *не используется*
- response
- responseError
    
На данный момент реализованы следующие расширения:
- auth - *В заголовок запроса добавляет Authorization а при 401 статусе в ответе делает refreshToken и повторяет запрос*
- blackout - *Блокирует работу портала при 521 статусе*
- language - *В заголовок запроса добавляет X-DBRPC-Language*
- ms_pp_instance  - *При включенном Develop.ms_pp_instance в заголовок запроса добавляет ms_pp-instance (только для ПП)*
- question - *перехватывает 200 ответ. Обрабатывает следующие атрибуты ответа:*
    - do - *действие или массив действий которые необходимо выполнить*
    - question - *Отобразить форму и повторить запрос к серверу*
    

### Question
do

    {
        type: "redirect", // Тип действия
        url: "http://www.domain.uz" // URL для типа redirect
    }

question

    {
        size: "sm", // Тип диалогового окна lg sm full right
        subscribe: true, // требуется подпись или нет - принимает также строку которая будет подписана 
        title: "Заголовок окна",
        fields: {
            test_field: {
                
            }
        },
        submit: "Отправить" // Наименование кнопки для отправки - принимает также true и false
    }


<ui-layout :fields='model.fields'/>
`
}