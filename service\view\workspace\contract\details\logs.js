import { DataSource, RemoteStore, Query } from '@iac/data';
import { Develop, Context } from '@iac/kernel';

export default {
  props: ["model"],
  data: function () {
    return {
      debug_visible: false,
      logs: new DataSource({
        store: {
          method: 'contract_ref',
          ref: 'contract_logs',
          context: (item) => {
            if (this.debug_visible) {
              item = {...item, ...item.data};
              if (item?.data?.auth_data && typeof item?.data?.auth_data == 'object') {
                item.user_info = {
                  text: `u${item?.data?.auth_data?.id || '∅'}c${item?.data?.auth_data?.team_id || '∅'}i${item?.data?.auth_data?.team_inn || '∅'}`,
                  user_id: item?.data?.auth_data?.id
                };
              } else {
                item.user_info = { text: `∅` };
              }

              if (item.success != undefined || item.success != null) item.success = item.success ? '🍏' : '🍎';

              item.title = JSON.stringify(item.data, null, 4);
            } else {
              item.date = item.data.date;
              item.data.message = item.data.message.trim();
              item.error_expand = false;
              item.who = undefined;
              item.repeat = item.data.repeat || 1;
              item.message = item.data.message;

              if (item.data.who && item.data.who !== 'anon') item.who = item.data.who;
              if (item?.data?.error) item.data.error = item.data.error.trim();
            }

            return item;
          }
        },
        query: new Query({ number: this.model.number, }),
      })
    };
  },
  computed: {
    log_columns() {
      return this.debug_visible ? [
        'inserted_at',
        'type',
        'from',
        'to',
        'method',
        'success',
        { field: "user_info", component: {
            props: ['item'],
            methods: {
              debug_login(e, id) {
                e.preventDefault();
                Context.User.authorization_god(id);
              },
            },
            template: `
              <div>
                <a v-if='$develop.content_debug && item.user_info.user_id'
                  title='Телепортироваться'
                  v-on:click='e => debug_login(e, item.user_info.user_id)'
                  href="#">🕳️ </a>
                <span>{{$t(item.user_info.text)}}</span>
              </div>
            `
          }
        },
        'repeat',
      ] : [
        'date',
        { field: 'message', label: 'description', style: "width:100%" }
      ];
    }
  },
  methods: {
    show_error_message(item) {
      item.additional_message = !item.additional_message;

      let base_message = item.data.message;

      if (item.additional_message) {
        if (item?.data?.error) base_message += ("\n" + item.data.error);
        if (item?.data?.additional_message) base_message += ("\n" + item.data.additional_message);
      }

      item.message = base_message;
    },
    toggle_debug_visible() {
      this.debug_visible = !this.debug_visible;
      this.logs.reload();
    }
  },
  template: `
    <div v-if='model'>
      <ui-btn @click.native='toggle_debug_visible' v-if="$develop.content_debug" style="margin-bottom: 5px">Toggle debug visible</ui-btn>
      <ui-data-grid raw :dataSource='logs' :columns='log_columns'>
        <template slot='inserted_at' slot-scope='props' v-if="$develop.content_debug">
          <a :title='props.item.title'>{{ props.item.inserted_at }}</a>
        </template>
        <template slot='date' slot-scope='props'>
            <iac-date style='white-space: nowrap;' :date='props.item.date' fullWithSeconds/>
        </template>
        <template slot='message' slot-scope='props'>
          <div style="
            word-wrap: break-word;
            white-space: pre-wrap;
            display: flex;
            margin: -8px -6px;
            align-items: baseline;
          ">
            <pre style="
              margin: 1px 0px;
              overflow-wrap: break-word;
              white-space: pre-wrap;
              text-align: left;
              width: 100%;
              border-radius: 0;
              border: none;"
              @click="show_error_message(props.item)"
              class="ui-btn"
              :class="{
                'ui-btn-info': (props.item.data.error == null),
                'ui-btn-danger': (props.item.data.error != null),
              }"
            >{{ props.item.message }}</pre>
            <pre
              v-if="props.item.repeat > 1"
              :title="props.item.repeat + ' раз повторено.'"
              style="cursor: pointer; margin: 0px;"
            >⛌{{props.item.repeat}}</pre>
          </div>
        </template>
      </ui-data-grid>
    </div>
  `
}
