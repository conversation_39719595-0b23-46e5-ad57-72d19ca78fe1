import { Guid } from '@iac/core'
import { Entity } from '@iac/data'
import Item from './item';

export default class Node {
    constructor(context = {}) {
        this.space = context.space;

        this.id = context.id || Guid._newGuid();
        this.parent = context.parent;
        this.active_item = context.active_item || 0;
        this.child = {};
        this.size = Number(context.size || 0)
    }

    insert(target, items, start = false) {
        if (!items)
            return;
        if (!Array.isArray(items)) {
            items = [items];
        }
        let active_item = 0;
        items.forEach((item,index)=>{
            if(item.active)
                active_item = index;
            item.space = this.space;
            //item.group = item.group || undefined;
            //item.contract = item.contract || undefined;            
            //item.params = item.params || {}
        })

        let keys = Object.keys(this.child);
        let size = 100 / (keys.length + 1);
        let new_node = new Node({ parent: this, size: size, space: this.space });
        new_node.items = items
        new_node.active_item = active_item;

        this.child = ((trigChild) => {
            var newChild = {};

            keys.forEach((k) => {

                this.child[k].size = this.child[k].size ? this.child[k].size * (100 - size) / 100 : size
                if (k === trigChild) {
                    if (start) {
                        newChild[new_node.id] = new_node;
                        newChild[k] = this.child[k];
                    } else {
                        newChild[k] = this.child[k];
                        newChild[new_node.id] = new_node;
                    }

                } else {
                    newChild[k] = this.child[k];
                }
            });
            return newChild;
        })(target);

        if (this.space.manager.moveTail) {
            let { window, index } = this.space.manager.moveTail
            window.delete_item(index);
            this.space.manager.moveTail = undefined;
        }

        this.space.save();

        return {
            node: new_node,
            item: items,
            index: 0
        }
        
    }
    insert_parent(item, start) {
        if (this.parent) {
            this.parent.insert(this.id, item, start);
        }
    }

    add_item(items) {


        if (this.space.manager.moveTail) {
            if (this.space.manager.moveTail.window == this)
                return;
        }
        if (!items)
            return;

        let active_item = this.items.length;
        if (!Array.isArray(items)) {
            items.active = true;
            items = [items];
        }
        items.forEach((item, index)=>{
            if(item.active){
                active_item = active_item + index;
            }
            item.space = this.space;
            return item;
        })
        this.items = this.items.concat(items)
        if(active_item >= this.items.length){
            active_item = this.items.length - 1;
        }
        this.active_item = active_item;

        if (this.space.manager.moveTail) {
            let { window, index } = this.space.manager.moveTail
            window.delete_item(index);
            this.space.manager.moveTail = undefined;
        }
        this.space.save();
    }

    get_items(){
        let items = [];
        items = items.concat(this.items)

        /*let keys = Object.keys(this.child);
        if (keys.length > 1) {
            keys.forEach((node) => {
                console.log("NODE",node)
            });
        }*/
        return items;
    }


    delete_child(id) {

        // Получить список всех виджетов
        let child = this.child[id];
        let group_ids = child.get_items().map((item)=>{
            return item.group_id
        }).filter((group_id)=>{
            return group_id;
        })


        let keys = Object.keys(this.child);
        if (keys.length > 1) {

            let size_d = child.size / (keys.length - 1);
            keys.forEach((k) => {
                this.child[k].size = this.child[k].size ? this.child[k].size + size_d : size_d
            });
            this.child[id] = undefined;
            delete this.child[id];

            if (keys.length == 2 && this.parent) {

                
                let key = Object.keys(this.child)[0];
                let child_re_split = this.child[key];

                if(child_re_split.items && child_re_split.items.length){
                    child_re_split.parent.items  =  child_re_split.items
                    child_re_split.parent.active_item = child_re_split.active_item;
                    child_re_split.parent.child = {}
                }else if(child_re_split.child ){
                    // перекинуть на про родителя
                    if(child_re_split.parent.parent && child_re_split.parent.parent.child){
                        var newChild = {};
                        let trigChild = child_re_split.parent.id
                        Object.keys(child_re_split.parent.parent.child).forEach((k) => {
                            if (k === trigChild) {
                                let size = child_re_split.parent.parent.child[k].size;

                                let child_re_split_keys = Object.keys(child_re_split.child);
                                for(let i of child_re_split_keys){
                                    let new_id = Guid._newGuid();
                                    newChild[new_id] = child_re_split.child[i]
                                    newChild[new_id].id = new_id;
                                    newChild[new_id].parent = child_re_split.parent.parent
                                    newChild[new_id].size = (size/100)*(newChild[new_id].size / 100)*100
                                }
                            }else{
                                newChild[k] = child_re_split.parent.parent.child[k];
                            }
                        });
                        child_re_split.parent.parent.child = newChild
                    }
                }
            }
        } else if (this.parent) {
            this.parent.delete_child(this.id);
        }

        group_ids.forEach((search_item_group)=>{
            if(search_item_group && !this.space.node.search_item_group(search_item_group)){
                let _group = this.space.groups[search_item_group-1];  
                _group.id = undefined;
                _group.product_name = undefined;
            }
        })

        this.space.save();
    }
    delete_item(index) {
        if (index == undefined && this.parent) {
            return this.parent.delete_child(this.id);
        }

        if (this.items && this.items.length > 1) {

            let del_item = this.items.splice(index, 1);
            let search_item_group = del_item && del_item[0]?.group_id;
            if(search_item_group && !this.space.node.search_item_group(search_item_group)){
                let _group = this.space.groups[search_item_group-1];  
                _group.id = undefined;
                _group.product_name = undefined;
            }

            if (this.active_item > index) {
                this.active_item--;
            } else if (this.active_item == index) {
                this.active_item = Math.max(Math.min(this.active_item, this.items.length - 1), 0)
            }

        } else {
            if (this.parent) {
                this.parent.delete_child(this.id);
            }
        }
        this.space.save();
    }
    split(items, start = false) {
        if (!items)
            return;

        if (!Array.isArray(items)) {
            items = [items];
        }

        let active_item = 0;
        items.forEach((item,index)=>{
            if(item.active)
                active_item = index;
            item.space = this.space;
            return item;
        })

        let new_node_1 = new Node({ parent: this, size: 50, space: this.space });
        new_node_1.items = [...this.items]
        new_node_1.active_item = this.active_item;

        let new_node_2 = new Node({ parent: this, size: 50, space: this.space });
        new_node_2.items = items
        new_node_2.active_item = active_item;

        if (start) {
            this.child = {
                [new_node_2.id]: new_node_2,
                [new_node_1.id]: new_node_1,
            }
        } else {
            this.child = {
                [new_node_1.id]: new_node_1,
                [new_node_2.id]: new_node_2,
            }
        }

        this.active_item = 0;

        this.items = undefined
        delete this.items

        if (this.space.manager.moveTail) {
            let { window, index } = this.space.manager.moveTail
            if (window == this) {
                window = new_node_1;
            }
            window.delete_item(index);
            this.space.manager.moveTail = undefined;
        }

        this.space.save();
    }
    setActive(index) {
        if (this.active_item == index)
            return;
        this.active_item = index
        this.space.save();
        return true
    }

    get tile_items() {
        return this.items
    }

    search_item_group(group_id){
        if (this.items && this.items.length > 0) {
            for (let index in this.items) {
                let item = this.items[index]
                if (item.group_id == group_id) {
                    return true
                }
            }
        }else{
            for (let key in this.child) {
                let result = this.child[key].search_item_group(group_id)
                if (result)
                    return result;
            }
        }
    }

    search(tile) {
        if (this.items && this.items.length > 0) {
            for (let index in this.items) {
                let item = this.items[index]
                if (item.tile == tile) {
                    return {
                        node: this,
                        item: item,
                        index: index
                    }
                }
            }
        } else {
            let keys = Object.keys(this.child);
            for (let key in this.child) {
                let result = this.child[key].search(tile)
                if (result)
                    return result;
            }
        }
    }

    get horizontal(){
        if(!this.parent)
            return true;
        return !this.parent.horizontal
    }

    getStruct() {

    }
}