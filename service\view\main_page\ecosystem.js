import { DataSource, RefStore, Query } from '@iac/data';
import { Language } from '@iac/core';
import { Settings } from '@iac/kernel';
import { Http } from '@iac/core';
import IacCarousel from '../../components/carousel';

// Общая переменная для поиска
var search_val = {
  value: undefined
}

export default {
  data() {
    return {
      search_val: search_val,

      localManufacturers: new DataSource({
        query: new Query({}),
        limit: 9,
        store: {
          method: "company_ref",
          ref: "ecosystem_producers",
          injectQuery: (params) => {
            params.fields = ["company_name","id","products_count","certificate_id","valid_from","expire_at","inn","localization_degree"]
            if (search_val.value) {
              params.query = search_val.value;
            }
            params.limit = 9;
            return params
          },
          context:(context)=>{
            return {
              ...context,
              header: [` `, 
              {
                component: {
                  props: ["products","products_count","company_id"],
                  computed:{
                    count(){
                      return this.products_count;
                    }
                  },
                  methods: {
                    onclick(){
                      Vue.Dialog({
                        props: ["id"],
                        data: function() {
                          return {
                            source: new DataSource({
                              columns: [{field: 'enkt',label:'code', style:"white-space: nowrap;"},{field: "name",label: "direct_contract_comp_name", style:"width: 100%;"}],
                              store: {
                                data: async () =>{
                                  let {error,data} = await Http.api.rpc("company_ref",{
                                    ref: "ecosystem_producers",
                                    op: "read",
                                    filters: {
                                      id: this.id
                                    }
                                  })
                                  return data?.[0].data?.products || []
                                }
                              }
                            }),
                          }
                        },
                        template: `
                          <div>
                            <header>{{$t('link.products')}}</header>
                            <main>
                              <ui-data-grid :dataSource='source' :columns='source.columns' :buttons="true"/>
                            </main>
                            <footer>
                              <ui-btn type='secondary' v-on:click.native='Close'>{{$t('close')}}</ui-btn>
                            </footer>
                            </div>
                      `
                      }).Modal({
                        id: context.id,
                        size: "lg"
                      });
                    }
                  },
                  template: `
                  <div v-if='!count'>{{count}} {{$t("product",{ count: count })}}</div>
                  <a v-else href='javascript:void(0)' v-on:click='onclick'>{{count}} {{$t("product",{ count: count })}}</a>
                  `
                },
                props: {
                  products: context?.data?.products,
                  products_count: context.products_count,
                  company_id: context.id
                },
              }],
              title: {text: context.company_name},
              props: [{
                label: Language.t("inn"),text: context.inn
              },
              {
                label: Language.t("registry.certificate_number"),text: context.certificate_id
              },{
                props: ['model'],
                template: `
                    <div>
                        <label>{{$t("document_expire")}}</label>
                        <div style='text-transform: lowercase;'>
                            <div>{{$t('from')}} <iac-date :date='model.valid_from'/></div>
                            <div>{{$t('to')}} <iac-date :date='model.expire_at'/></div>
                        </div>
                    </div>`
              },
              {
                label: Language.t("localization"),text: Math.round(context.localization_degree)+'%'
              }]
            }
          }
        }
      }),
      
      ecosystemShop: new DataSource({
        store: new RefStore({
          ref: 'ref_online_shop_public',
          injectQuery: (params) => {
            params.fields = ["green", "product", "unit", "id", "publicated_at", "status", "name", "price", "close_at", "totalcost", "currency", "amount", "min_amount", "images", "owner_legal_area_id", "product_name", "remain_time"];
            params.limit = 6;
            params.filters = {
              ecosystem_producer: true,
              status: "publicated",
            }
            return params;
          },
        }),
        template: 'template-shop'
      }),

      carouselOptions: {
        items: 1,
        gutter: 24,
        nav: false,
        loop: false,
        rewind: true,
        autoplayTimeout: 5000,
        responsive: {
          600: {
            items: 2,
          },
          1000: {
            items: 3,
          },
          1300: {
            items: 4,
          },
        }
      }
    };
  },
  mounted() {
    search_val.value = undefined;
  },
  components: {
    IacCarousel,
    ecosystemSearch: {
      props: ["type"],
      data: function() {
        return {
          search_val: search_val
        }
      },
      computed: {
        label() {
          return `search.${this.type}.placeholder`;
        }
      },
      methods: {
        search() {
          let path = '/registry/local_manufacturers';
          let query = {};
          
          if (this.search_val.value) {
            query.queryText = this.search_val.value;
          }
          
          if (this.type === 'local_manufacturers') {
            query.tabid_tab = '0'; 
          } else if (this.type === 'national_shop') {
            query.tabid_tab = '1'; 
          }
          
          this.$router.push({ path, query });
        }
      },
      template: `
        <form @submit.prevent='search' style='max-width: 700px; margin: 0 auto; width: 100%;'>
          <ui-control-group>
            <ui-input icon='search' :label='label' v-model='search_val.value' />
            <ui-btn type='primary'>{{$t('search')}}</ui-btn>
          </ui-control-group>
        </form>
      `
    }
  },
  template: `
    <ui-layout-tab class='proc_tab' name='ecosystem'>
      <ui-layout-group key='local_manufacturers' label='local_manufacturers'>
        <ecosystem-search type='local_manufacturers' />
        <ui-data-view :toolbar='false' :search='false' :showMore='false' :dataSource='localManufacturers' />
        <div style='text-align:center; margin-top: 24px; '>
          <router-link class='ui-btn ui-btn-primary' to='/registry/local_manufacturers'>{{ $t('hp.goto_local_manufacturers') }}</router-link>
        </div>
      </ui-layout-group>
      
      <ui-layout-group key='national_shop' label='offers' v-if='$settings.procedures && $settings.procedures.e_shop && $settings.procedures.e_shop.national_shop'>
        <ecosystem-search type='national_shop' />
        <ui-list :dataSource='ecosystemShop' class='tns-controls-center mb-12'>
          <iac-carousel v-if='props && props.items && props.items.length' slot='items'
            slot-scope='props' :model='carouselOptions'>
            <div v-for='item in props.items' :key='item.id'>
              <widget-shop type='ecosystem' :item='item' class='h-100' />
            </div>
          </iac-carousel>
        </ui-list>
        <div style='text-align:center; '>
          <router-link class='ui-btn ui-btn-primary' to='/registry/local_manufacturers?tabid_tab=1'>{{ $t('hp.goto_national_shop') }}</router-link>
        </div>
      </ui-layout-group>
    </ui-layout-tab>
  `
};











