.doc-code-editor {
    //overflow: hidden;
    >.container {
        margin: 0;

        >.control {
            outline: none;
            resize: vertical;
            padding: 0 0 0 46px;
            min-height: 300px;

            tab-size: 2;
            white-space: pre;
            font-family: monospace;
            color: transparent;
           // background-color: rgb(13, 17, 23);
            //resize: none;
            //height: 229px;
            caret-color: rgb(201, 209, 217);
            font: 400 12px monospace;
            line-height: 20px;
            border: none;
            margin: 0;
            //background: linear-gradient(to right,#eee 40px, white 40px, white);
            
        }

        >.highlighted-code {
            position: absolute;
            overflow: hidden;
            box-sizing: border-box;
            pointer-events: none;
            tab-size: 2;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            font: 400 12px monospace;
            letter-spacing: normal;
            word-spacing: 0px;
            padding: 16px;
            border: 1px solid transparent;
            margin: 0px;
            background: initial;
            padding: 0 0 0 0; 
            line-height: 20px;
            border: none;
            margin: 0;
            >.wrapper{
                overflow: unset;
                margin: 0px;
                padding: 0px 0px 30px;
                display: flex;
                >.line-number{
                    flex: 0 0 40px;
                    //background: #eee;
                    line-height: 20px;
                    max-width: 40px;
                    overflow: hidden;
                    >div{
                        line-height: 20px;
                        //border-bottom: 1px solid #e2e2e2;
                        padding: 0 14px 0 0;
                        text-align: right;
                        color: #9a9a9a
                    }
                }
                >.code{
                    padding-left: 6px;
                    flex: 1 1 auto;
                }
            }

            .hljs-attr{
                color: #0b56bb;
            }
            .hljs-string{
                color: #a82222;
            }
            .hljs-literal{
                color: #161efd;
            }
            .hljs-number{
                color: #10865c;
            }


        }

    }

    >.toolbar {
        position: absolute;
        right: 6px;
    }

    &.readonly {
        >.container {
            margin: 0 15px;
            font-size: 14px;

            pre {
                background: #f4f3f3;
                padding: 15px;
                border-radius: 4px;
                border: 1px solid #eee;
                font-size: 12px;
            }
        }
    }
}