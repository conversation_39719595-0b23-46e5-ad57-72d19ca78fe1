.edit-item {
    .value {
        background: #ffedc4;
        padding: 0 6px;
    }

    &:hover {
        background: #eee;

        .value {
            background: #efddb4;
        }
    }
}

.doc-section-wrapper{
    .ui-btn-group{
        button {
            display: none;
        }
    }
    &:hover{
        .ui-btn-group{
            button {
                display: unset;
            }
        }        
    }
}

.doc-section {
    .title {
        display: flex;
        font-size: 16px;
        font-weight: bold;
        justify-content: center;
        margin: 1em 0;
        position: relative;

        .action {
            position: absolute;
            right: 0;
            display: none;
            cursor: pointer;
            >span{
                padding: 6px;
                background: #fff; 
                border: 1px solid #ccc;
                border-radius: 4px;
                &:hover {
                    background: #eee;
                }
            } 
        }

    }
    .items{
        .item{
            position: relative;
            .action{
                position: absolute;
                right: 0;
                display: none;    
                
                cursor: pointer;
                >span{
                    padding: 6px;
                    background: #fff; 
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    &:hover {
                        background: #eee;
                    }
                }           
            }
            &:hover{
                .action {
                    display: block;
                }
            }
        }
    }

    &:hover {
        .title {
            .action {
                display: block;
            }
        }
    }

}