@import 'components/index.less';

.iac--ebp-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: stretch;

  >*:not(:last-child) {
    margin-right: 6px;
  }
}

.iac--ktru-burse {
  width: 100%;
  padding: 5px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  background-color: #FBFBFB;

  &__add_new_burse_product {
    >button {
      margin: 5px;
      padding: 5px;
      height: 24px;
      font-size: 13px;
      line-height: 14px;
      border: 1px solid #DADADA;
      border-radius: 4px;
      cursor: pointer;
      color: #fff;
      background-color: #009ab8;
      border-color: #00859f;
      text-decoration: none;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }
  }

  &__search {
    position: relative;
    margin-bottom: 10px;

    >div {
      position: absolute;
      user-select: none;
      pointer-events: all;
      z-index: 10;
      left: 15px;
      top: 0;
      bottom: 0;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;

      >icon {
        font-size: 14px;
        color: #7F8182;

        &.iac--to-spin {
          animation-name: spin;
          animation-duration: 4000ms;
          animation-iteration-count: infinite;
          animation-timing-function: linear;
        }
      }
    }

    >input {
      width: 100%;
      padding-left: 44px;
      padding-right: 44px;
      height: 42px;
      border: none;
      background: #fff;
      outline: none;
      border: 1px solid #DADADA;
      border-radius: 4px;

      &::placeholder {
        color: #B9B8B8;
        font-size: 16px;
        line-height: 20px;
      }

      &:focus {
        outline: none;
      }
    }

    >button {
      position: absolute;
      right: 0;
      top: 0;
      width: 42px;
      height: 42px;
      border: 1px solid #DADADA;
      border-radius: 4px;
      cursor: pointer;
      padding: 0;
      margin: 0;
      color: #fff;
      background-color: #009ab8;
      border-color: #00859f;
      text-decoration: none;
    }
  }

  &__spec_creator {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: flex-start;
    border: 1px solid #dadada;
    border-radius: 4px;
    overflow: hidden;

    >h3 {
      flex: 0 0 auto;
      background-color: #dadada;
      padding: 10px;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      font-weight: bold;
      white-space: nowrap;
      overflow-x: hidden;
      text-overflow: ellipsis;
      font-size: 12px;
      line-height: 14px;
      margin: 0;
    }

    &-about {
      flex: 0 0 auto;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      font-size: 14px;
      line-height: 16px;
      padding: 10px 10px 0 10px;

      >div:first-child {
        flex: 0 0 120px;
        width: 120px;
      }

      >div:last-child {
        flex: 1 1 auto;

        &::before {
          content: ': ';
          margin-right: 5px;
        }
      }
    }

    &-divider {
      padding: 10px;
      flex: 0 0 auto;

      &::before {
        content: '';
        display: block;
        width: 100%;
        height: 1px;
        background-color: #dadada;
      }
    }

    &-selectors {
      padding: 5px;
      flex: 1 1 auto;
    }

    &-footer {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
      flex: 0 0 auto;
      background-color: #dadada;
      padding: 10px;
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;

      >button {
        padding: 5px 10px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        background-color: #01afe5;
        color: white;
        border: 1px solid #0188b2;
        cursor: pointer;

        &:hover {
          background-color: #0188b2;
        }

        &[disabled] {
          cursor: not-allowed;
          background-color: grey;
          border-color: gray;
        }
      }
    }

    &-selectors {
      >div {
        padding-bottom: 10px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        >label {
          flex: 1 1 auto;

          >span {
            color: red;
            font-weight: bold;
          }
        }

        >select {
          width: 130px;
          flex: 0 0 130px;
        }
      }
    }
  }

  &__spec_editor {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: flex-start;
    border: 1px solid #dadada;
    border-radius: 4px;
    overflow: hidden;

    >h3 {
      flex: 0 0 auto;
      background-color: #dadada;
      padding: 10px;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      font-weight: bold;
      white-space: nowrap;
      overflow-x: hidden;
      text-overflow: ellipsis;
      font-size: 12px;
      line-height: 14px;
      margin: 0;
    }

    input {
      padding: 3px;
      height: 24px;
      background: #fff;
      outline: none;
      border: 1px solid #DADADA;
      border-radius: 4px;
      flex: 1 1 auto;
    }

    &-about {
      flex: 0 0 auto;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      font-size: 14px;
      line-height: 16px;
      padding: 10px 10px 0 10px;

      >div:first-child {
        flex: 0 0 120px;
        width: 120px;
      }

      >div:last-child {
        flex: 1 1 auto;

        &::before {
          content: ': ';
          margin-right: 5px;
        }
      }
    }

    &-divider {
      padding: 10px;
      flex: 0 0 auto;

      &::before {
        content: '';
        display: block;
        width: 100%;
        height: 1px;
        background-color: #dadada;
      }
    }

    &-props {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: stretch;

      >div {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;
      }

      button {
        padding: 5px 10px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        background-color: #01afe5;
        color: white;
        border: 1px solid #0188b2;
        cursor: pointer;
        margin: 3px;

        >icon {
          margin: 0 3px;
        }

        &:hover {
          background-color: #0188b2;
        }

        &[disabled] {
          cursor: not-allowed;
          background-color: grey;
          border-color: gray;
        }
      }
    }

    &-prop_about {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: stretch;

      >div:nth-child(1) {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        font-size: 14px;
        line-height: 16px;
        padding: 10px 10px 0 10px;

        &::after {
          content: ':';
        }
      }

      >div:not(:nth-child(1)) {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        font-size: 14px;
        line-height: 16px;
        padding: 10px 10px 0 10px;

        >div:first-child {
          flex: 0 0 120px;
          width: 120px;
        }

        >div:last-child {
          flex: 1 1 auto;

          &::before {
            content: ': ';
            margin-right: 5px;
          }
        }
      }
    }

    &-vals {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: stretch;
      padding-left: 50px;

      >div {
        &:nth-child(1) {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: center;
          font-size: 14px;
          line-height: 16px;
          padding: 10px 10px 0 10px;

          &::after {
            content: ': ';
          }
        }

        &:not(:nth-child(1)) {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: stretch;

          >div {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            font-size: 14px;
            line-height: 16px;
            padding: 10px 10px 0 10px;

            >div:first-child {
              flex: 0 0 120px;
              width: 120px;
            }

            >div:last-child {
              flex: 1 1 auto;

              &::before {
                content: ': ';
                margin-right: 5px;
              }
            }
          }
        }
      }
    }


    &-footer {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
      flex: 0 0 auto;
      background-color: #dadada;
      padding: 10px;
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;

      >button {
        padding: 5px 10px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        background-color: #01afe5;
        color: white;
        border: 1px solid #0188b2;
        cursor: pointer;

        &:hover {
          background-color: #0188b2;
        }

        &[disabled] {
          cursor: not-allowed;
          background-color: grey;
          border-color: gray;
        }
      }
    }

    &-selectors {
      >div {
        padding-bottom: 10px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        >label {
          flex: 1 1 auto;

          >span {
            color: red;
            font-weight: bold;
          }
        }

        >select {
          width: 130px;
          flex: 0 0 130px;
        }
      }
    }
  }
}