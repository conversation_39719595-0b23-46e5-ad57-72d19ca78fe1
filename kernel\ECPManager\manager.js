import eimzoProvider from "./providers/eimzo";
import kgProvider from "./providers/kg";
import { Entity } from '@iac/data'
import { Event } from '@iac/core'

import Settings from "../settings";


class ecpManager extends Entity {

    @Event onChange;

    @Event async onUpdate() {
        let type = this.ecp_type?.id;
        let data = this[type]
        this.onChange({
            type: type,
            data: data,
            info: await ecpManager.providers[type]?.class.getInfo(data)
        })
    }

    constructor(context) {
        super(context);
        this.kg = context.kg

        this.ecp_providers = (Settings.ecp?._providers || []).map((id)=>{
            return {
                id: id,
                ...ecpManager.providers[id]
            }
        });

        console.log(this.ecp_providers);


        if (this.ecp_providers.length <= 1)
            this.ecp_type = this.ecp_providers[0]

        //this.ecp_type = {id: "kg"}
    }

    static get providers() {
        return {
            "eimzo": {
                class: eimzo<PERSON>rovider,
                name: "<PERSON>-<PERSON><PERSON><PERSON>"
            },
            "kg": {
                class: kgProvider,
                name: "ОЦП КГ"
            }
        }
    }

    get list() {
        return {
            "eimzo": new eimzoProvider({
                model: this,
                name: "eimzo",
                attributes: {
                    label: "!",
                    type: "model"
                }
            }),
            "kg": new kgProvider({
                model: this,
                name: "kg",
                attributes: {
                    label: "!",
                    type: "model"
                }
            })
        }
    }
    props() {
        return {
            ecp_type: {
                type: "entity",
                label: "ecp",
                dataSource: this.ecp_providers,
                hidden: () => {
                    return this.ecp_providers.length <= 1;
                }
            },

            eimzo: new eimzoProvider({
                model: this,
                name: "eimzo",
                attributes: {
                    label: "!",
                    type: "model",
                    hidden: () => {
                        return !this.ecp_type || this.ecp_type.id != "eimzo"
                    }
                }
            }),
            kg: new kgProvider({
                model: this,
                name: "kg",
                attributes: {
                    label: "!",
                    type: "model",
                    hidden: () => {
                        return !this.ecp_type || this.ecp_type.id != "kg"
                    }
                }
            })

        }
    }

    static async createPkcs7(message = 'createPkcs7', certificate) {
        console.log("createPkcs7",certificate, message);
        if (!certificate)
            return;
        let type = certificate.type;
        certificate = certificate.data;
        if (!certificate || !type)
            return;
        console.log("message, certificate", message, certificate);
        return await ecpManager.providers[type]?.class?.createPkcs7(message, certificate);
    }

    static async subscribe(message, details) {
        let certificate = await Vue.Dialog({
            data: function () {
                return {
                    certificate: undefined
                }
            },
            template: `
                <div>
                    <main>
                        <iac-ecp-manager v-model='certificate' />
                    </main>
                    <footer>
                        <ui-btn type='primary' v-on:click.native='Close(certificate)'>{{$t("subscribe")}}</ui-btn>
                    </footer>
                </div>
            `
        }).Modal({

        });
        if (!certificate)
            return;
        return await ecpManager.createPkcs7(message, certificate)
    }
}

export default ecpManager;