import { DataSource, RemoteStore, Query, RefStore } from '@iac/data';

export default {
  data() {
    return {
      columns: [{field: "location",label:"region"},{field: "name",label:"ecp.company"},{field: "inn",
      }],
      dataSource: new DataSource({
        search: true,
        query: {
          queryText: {
            label: "!search"
          }
        },
        store: {
          ref: "ref_green_suppliers",
          context: (context)=>{
            context.location = [context.respublica,context.district].filter((item)=>{
                return item;
            }).join(', ')
            return context
          }
        },
        template:'template-company'
      })
    }
  },
  template: `
    <div>
      <iac-section type='header'>
        <ol class='breadcrumb'>
          <li><router-link to='/'>{{ $t('home') }}</router-link></li>
          <li>{{ $t("public_reestr_buyers_green_product") }}</li>
        </ol>
        <h1>{{$t("public_reestr_buyers_green_product")}}</h1>
      </iac-section>
      <iac-section>
        <ui-layout-group>
          <ui-alert type='warning'>
<ui-markdown-view :content='$t("green_buyers.description")'/>
          </ui-alert>
          <ui-data-grid class='top_filter' :dataSource='dataSource' :columns='columns'/>
        </ui-layout-group>
      </iac-section>
    </div>
  `
}
