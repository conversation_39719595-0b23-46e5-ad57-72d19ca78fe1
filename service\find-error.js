import { Http } from '@iac/core';

const findError = Vue.Dialog({
  props: ['selected_text', 'url'],
  data() {
    return {
      comment: '',
    };
  },
  methods: {
    async save() {
      this.wait(async() => {
        const { url, selected_text, comment } = this;
        const { data, error } = await Http.api.rpc('send_error', {
          url,
          selected_text,
          comment,
        });

        if (!error) {
          this.Close(true);
          if (data.message) {
            await Vue.Dialog.MessageBox.Success(data.message);
          }
        } else {
          await Vue.Dialog.MessageBox.Error(error);
        }
      });
    },
  },
  template: `
    <div>
      <header></header>
      <main>
        <p>{{ selected_text }}</p>
        <ui-text label='comment' v-model='comment'></ui-text>
      </main>
      <footer>
        <ui-btn type='secondary' @click.native='Close()'>{{ $t('close') }}</ui-btn>
        <ui-btn type='primary' @click.native='save'>{{ $t('send') }}</ui-btn>
      </footer>
    </div>
  `,
});

document.addEventListener('keydown', function({ ctrlKey, keyCode }) {
  if (ctrlKey && keyCode === 13) {
    const selected_text = window.getSelection().toString();
    if (selected_text.length) {
      findError.Modal({
        selected_text,
        url: location.href,
      });
    }
  }
});
