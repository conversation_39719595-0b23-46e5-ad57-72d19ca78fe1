export var DataViewItem = {
    name: "ui-data-view-item",
    props: ["model"],
    computed: {
        classes() {
            return [
                (() => {

                })(),
                {
                    ["status_" + this.model.status_type]: this.model.status_type
                }
            ]
        }
    },
    template: `
        <div class='ui-data-view-item' v-bind:class='classes'>
            <div class='header'>
                <div class='info'><slot name='header'/></div>
                <ui-action icon='action' :actions='model.actions' />
            </div>
            <div class='content'>
                <div class='body'>
                    <div class='title'>
                        <label>
                            <slot name='check' v-if='model.checkedItems'>
                                <input  type='checkbox' :id='model.id' :value='model.expValue || model.id' v-model="model.checkedItems" />
                            </slot>
                            <slot name='title' />
                        </label>
                        <span><slot name='sub_title' /></span>
                    </div>
                    <div class='description'><slot name='description' /></div>
                </div>
                <div v-if='$slots.props' class='props'>
                    <slot name='props'/>
                </div>
            </div>
            <div v-if='$slots.footer' class='footer'>
                <slot name='footer'/>
            </div>
        </div>
    `
}

var itemLink = {
    props: ["prop"],
    computed: {
        link() {
            if (typeof this.prop.link == "function")
                return
            return this.prop.link
        }
    },
    methods: {
        onclick() {
            if (typeof this.prop.link == "function") {
                this.prop.link();
            }
        }
    },
    template: `
        <router-link  v-if='link && (link.indexOf("//") < 0)' :to='link'>{{prop.text}}</router-link>
        <a v-else-if='link' :href='link'>{{prop.text}}</a>
        <a v-else href='javascript:void(0)' v-on:click='onclick'>{{prop.text}}</a>
    `
}

export var DataViewItemDefault = {
    props: ["model"],
    components: {
        itemLink: itemLink,
    },
    template: `
        <ui-data-view-item :model='model'>
            <div v-if='header' slot='header' v-for='header in model.header'>
                <template v-if='!header.component'>
                    {{header}}
                </template>
                <component v-else :is='header.component' v-bind='header.props' />
            </div>

            <template slot='title'>
                <template v-if='model.title && model.title.text'>
                    <template v-if='model.title.link'>
                        <itemLink :prop='model.title' />
                    </template>
                    <span v-else>{{model.title.text}}</span>
                </template>
                <span v-else>{{model.title}}</span>
            </template>

            <template slot='sub_title'>
                <template v-if='model.sub_title && model.sub_title.text'>
                    <template v-if='model.sub_title.link'>
                        <itemLink :prop='model.sub_title' />
                    </template>
                    <span v-else>{{model.sub_title.text}}</span>
                </template>
                <span v-else>{{model.sub_title}}</span>
            </template>
            
            <div slot='description' v-for='desc, idx in model.description'>
                <template v-if='!desc.template'>
                    <label v-if='desc.label'>{{desc.label}}: </label>
                    <template v-if='desc.link'>
                        <itemLink :prop='desc' />
                    </template>
                    <span v-else>{{desc.text}}</span>
                </template>
                <component v-if='desc' :is='desc' :model='model' :key='idx' />
            </div>

            <template slot='props' v-for='prop, idx in model.props'>
                <div v-if='!prop.template'>
                    <label>{{prop.label}}: </label>
                    <div :title='prop.title || prop.text'>
                        <template v-if='prop.link'>
                            <itemLink :prop='prop' />
                        </template>
                        <span v-else>{{prop.text}}</span>
                    </div>
                </div>
                <component :title='prop.title' v-if='prop' :is='prop' :model='model' :key='idx' />
            </template>

            <div slot='footer' v-for='footer, idx in model.footer' v-if='model.footer'>
                <component :is='footer' :model='model' :key='idx' />
            </div>

        </ui-data-view-item>
    `
}