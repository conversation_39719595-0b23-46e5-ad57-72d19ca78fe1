import Information from './information';
import Base from './base';
import Specification from './specification';
import File from './file';
import Requisites from './requisites';

export default {
  props: ['model'],
  data: function () {
    console.log(' SimplifiedCore model:', this.model);
    return {
      debug: false,
    };
  },
  mounted() {
    console.log('SimplifiedCore смонтирован');
  },
  components: {
    Information,
    Base,
    Specification,
    File,
    Requisites
  },
  methods: {
    async remove_contract() {
      await this.$wait(async () => {
        if(await this.model.remove_contract(this))
          this.$router.push({ path: `/workspace/contract` });
      });
    },
    async public_contract() {
      await this.$wait(async () => {
        await this.model.public_contract();
      });
    },
    async org_sign_contract() {
      await this.$wait(async () => {
        await this.model.org_sign_contract();
      });
    },
    async winner_sign() {
      await this.$wait(async () => {
        await this.model.winner_sign();
      });
    },
    async reject_sign(side) {
      await this.$wait(async () => {
        await this.model.reject_sign(side);
      });
    }
  },
  template: `
    <div v-if='model'>
      <div style=' padding: 20px; margin: 10px 0;'>
        🚀 
      </div>
      <div class='grid'>
        <ui-layout v-if='model.simplifiedBody' :wait='model.simplifiedBody.wait' :readonly='model.simplifiedBody.readonly' class='row' :fields='model.simplifiedBody.fields' />
      </div>
      <File :model='model' />
      <ui-btn-group>
        <ui-btn v-if='model.rights.publish' :disabled='!model.public_contract' type='primary' @click.native='public_contract'>{{ $t('contract.public') }}</ui-btn>
        <ui-btn v-if='model.rights.org_sign' :disabled='!model.org_sign_contract' type='primary' @click.native='org_sign_contract'>{{ $t('contract.sign') }}</ui-btn>
        <ui-btn v-if='model.rights.org_reject_sign' type='secondary' @click.native='reject_sign("org")'>{{ $t('reject_sign') }}</ui-btn>
        <ui-btn v-if='model.rights.winner_sign' type='primary' @click.native='winner_sign'>{{ $t('contract.sign') }}</ui-btn>
        <ui-btn v-if='model.rights.delete' type='danger' @click.native='remove_contract'>{{ $t('contract.remove') }}</ui-btn>
      </ui-btn-group>
    </div>
  `,
};