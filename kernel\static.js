import { Http } from '@iac/core'
import { Language } from '@iac/core'

const Refs = Symbol('Refs')

export default class Static {
  static [Refs] = {};
  static async reg(name, options = {}) {

    //let key = `${name}_${Language.local}`;
    this[Refs][name] = {
      options: options
    }
  }

  static get(name, source='ref') {

    //let key = `${name}_${Language.local}`;
    this[Refs][name] = this[Refs][name] || {
      options: {
        method: source,
        params: {
          op: "read",
          ref: name
        }
      }
    }

    let promise = `promise_${Language.local}`;
    this[Refs][name][promise] = this[Refs][name][promise] || new Promise(async (resolve, reject) => {
      let { method, params } = this[Refs][name].options;
      let { error, data } = await Http.api.rpc(method, params);
      if (!error)
        resolve(data.map((item)=>{
          item.id = item.id || item.code;
          item.code = item.code || item.id;
          return item;
        }))
      else
        resolve([])
    })

    return this[Refs][name][promise];
  }

  static async map(name, source) {
    let map = `map_${Language.local}`;
    
    if(this[Refs][name] && this[Refs][name][map])
      return this[Refs][name][map];

    let items = await this.get(name, source);
    return this[Refs][name][map] = items.reduce((pre,item)=>{
      pre[item.code || item.id] = item;
      return pre;
    },{})
  }
}