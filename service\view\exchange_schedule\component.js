import { Http, Language } from "@iac/core"
let component = {
    data: function () {
        return {
            model: undefined,
            error: undefined,
            localization: undefined
        }
    },
    mounted() {
        this.$wait(async () => {
            let { error, data } = await Http.default.rpc("backend_storage_get", {
                scope: 'global',
                key: `exchange_schedule`
            });

            if (error) {
                this.error = error
            } else {
                this.localization = data.localization;
                this.model = data.items
            }
        })
    },
    methods: {
        translate(key){
            let dictionary = this.localization[Language.local] || this.localization['ru-RU'] || {}
            return dictionary[key] || key
        }
    },
    template: `
<div class='iac-exchange_schedule'>
    <div style='display: flex;flex-wrap: wrap;justify-content: space-between; padding: 12px 0;font-size: 14px;'>
        <div>{{$t('bidding_time')}} *</div> <div>{{$t('bidding_time_from')}}</div>
    </div>
    <div class='details'>
        <template v-for='session,session_id in model'>
            <div :class='"session_"+session_id' class='title header'>{{$t('session_'+session_id)}}</div>
            <div v-if='0' :class='"border session_"+session_id' class='time header'>Время покупателя</div>
            <div v-if='0' :class='"border session_"+session_id' class='groups header'>Секция</div>
            <div v-if='0' :class='"border session_"+session_id' class='products header'>Наименование товара</div>

            <template v-for='event in session'>

                <div :class='"border session_"+session_id' class='time' :style='"grid-row: span "+event.span'>{{event.time}}</div>
                <template v-for='section,index in event.sections'>
                    <div :class='["session_"+session_id,{border: index == event.sections.length-1}]' class='groups'  :style='"grid-row: span "+section.span'>
                        <span>{{translate(section.group)}}</span>
                    </div>
                    <div v-if='0' :class='["session_"+session_id,{border: index == event.sections.length-1}]' class='products'  :style='"grid-row: span "+section.span'>
                        <span v-for='product in section.products'>{{product}}</span>
                    </div>
                </template>
            </template>
        </template>
    </div>
    <div style='white-space: break-spaces; margin-top: 40px; font-size: 13px;'>{{$t('exchange.schedule_desc')}}</div>
    
</div>
        `
}

Vue.component('iac-exchange-schedule',component)
