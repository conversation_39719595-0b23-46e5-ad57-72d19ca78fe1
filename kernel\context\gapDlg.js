import { DataSource } from '@iac/data'

export default Vue.Dialog({
    props: ["users"],
    data: function () {
        
        return {
            sourceUser: new DataSource({
                store: {
                    data: this.users
                }
            })
        }
    },
    methods:{
        onItem(item){
            this.Close(item.id)
        }
    },
    computed: {
        warning(){
            return `
             <div>{{$t('due_to_the_fact_that_you_are_on_the_list_of_taxpayers_classified_as_a_high_risk_tax_group_your_participation_in_government_procurement_process_is_temporarily_limited')}}</div>
             <div>{{$t('you_can_check_via_the_following_email_address')}}: 
             <a targen='_blank' href='https://soliq.uz/activities/tax-risk-analysis?lang=uz'>https://soliq.uz/activities/tax-risk-analysis</a> 
             </div>
             <div>{{$t('phone_for_information')}}: 71 202-32-82, 1198</div>`
        }
    },
    template: `
        <div>
            <header>{{$t('dear_participant')}}</header>
            <main class='warning' v-html='warning'>
            </main>
            <main v-if='users && users.length > 0'>
                <p>{{$t('choose_another_available_company')}}</p>
                <ui-list :dataSource='sourceUser' v-on:item='onItem' />
            </main>
            <footer>
                <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
            </footer>
        </div>
    `
})