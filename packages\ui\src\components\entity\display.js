import DataSource from './../../../../data/src/data_source'

export var EntityDisplay = {
  name: "ui-entity-display",
  props: ["dataSource", "value"],
  data: function () {
    return {
      source: DataSource.get(this.dataSource),
      model: undefined,
    }
  },
  watch: {
    value: {
      immediate: true,
      async handler(val, oldVal) {

        if (val == oldVal || !this.source)
          return;
        if (typeof val != 'object') {
          this.model = await this.source.byKey(val)
        } else {
          this.model = val
        }
      }
    }
  },
  computed: {
 
  },
  template: `
    <span v-if='model'>{{model.name}}</span>
  `
}