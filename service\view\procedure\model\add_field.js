
import { Entity, Property, DataSource, ArrayStore, BackEndStore } from '@iac/data'
import { Http, Language } from '@iac/core'
import { <PERSON><PERSON>p, Static, Settings, Config } from '@iac/kernel'

class StatusView {
    static Corp = 2;
    static Gos = 4;
    static Gup = 8;
}

class ModelFieldProperty extends Property {
    constructor(context) {
        super(context)
    }

    async Upload(value) {
        var send = async (file) => {
            let formData = new FormData();
            formData.append('scope_tender_participant', this.model.proc_id);
            formData.append('data', file, file.name);
            let { data, error } = await Http.upload.form('tender/attach', formData);
            if (error) {
                await Vue.Dialog.MessageBox.Error(error);
                return;
            }

            return {
                id: data.uuid,
                name: data.meta.name,
                meta: {
                    "type": data.meta.type,
                    "content_type": data.meta.content_type,
                    "type_group": data.meta.group,
                    "size": data.meta.size
                }
            }
        }

        if (Array.isArray(value)) {
            for (let key in value) {
                let item = value[key];
                if (item && item.file && item.file.name) {
                    let _value = await send(item.file);
                    value[key] = _value;
                }
            }
            value = (value || []).filter((item) => {
                return item;
            })
        } else {
            let item = value;
            if (item && item.file && item.file.name) {
                let _value = await send(item.file);
                value = _value
            }
        }
        return value
    }


    get meta() {
        if (this.type == 'file' && this.value) {
            return {
                ...this._meta,
                url: (value) => {
                    if (!value.id)
                        return;
                    return `${Config.api_server}/file/${value.id}`
                },
                url_mod: async (value) => {
                    if (!Context.User.access_token && this.attr?.access_unauthorized_denied) {
                        Vue.Dialog.MessageBox.Info(Language.t("view_file.unauthorized"))
                        return;
                    }
                    let url = this.meta.url(value);
                    if (!url)
                        return;

                    await Context.User.refreshToken();
                    return `${url}?token=${Context.User.access_token}`
                }
            }
        }
        return this._meta
    }
}

class ModelField extends Entity {
    get propertyModel() {
        return ModelFieldProperty;
    }

    constructor(context = {}) {
        super(context)
        this.context = context;

        this._new_criteria = context._new_criteria;

        this.extra_langs = context.extra_langs?.id || context.extra_langs;
        if (this.extra_langs == 'none' || !this._new_criteria)
            this.extra_langs = undefined;



        this.template_id = undefined;
        this.green = context.green
        this.gos = context.gos
        this.gup = context.gup

        this.hide_mark = context.hide_mark || false;

        /*this.root_groop = context.category;
        if (["quality_fields"].indexOf(this.root_groop) < 0) {
            this.root_groop = undefined;
        }*/

        this.root_groop = (Array.isArray(context.category) ? context.category : [context.category]).filter((i) => {
            return ["quality_fields", "tech_fields"].indexOf(i) >= 0
        }) || [];

        if (this.root_groop.length <= 0)
            this.root_groop = undefined;


        // Временное решение для tech_fields
        //if (this.root_groop == "tech_fields" && !context.category_id) {
        //    context.category_id = 'tech_criteria'
        //}

        this.parent_group = context.group.split("/")[0]
        this.root_category = this.parent_group

        this.set_context(context);


        this.template_source = new DataSource({
            store: new BackEndStore({
                scope: "local",
                storage_key: 'criteria_templates',

                inject: async (items) => {
                    if (!items)
                        return items;

                    return items.filter((item) => {

                        if (this.method_marks == 'min_price' && item.data.type_proposal != 'boolean') {
                            return false;
                        }

                        if (item.data.category_id) {

                            let data = context.ref_criteria_categories.filter((i) => {
                                if (!this.green && i.only_green)
                                    return false
                                if (this.root_groop.indexOf(i.group) < 0) {
                                    return false;
                                }
                                return i.id == item.data.category_id
                            })

                            if (data && data.length > 0)
                                return true;
                            else
                                return false;
                        }



                        let group = item.data.group || "";

                        if (this.root_groop) {

                            let exit = true;
                            this.root_groop.forEach(root => {

                                if (group.indexOf(root) >= 0) {
                                    exit = false;
                                }
                            });
                            if (exit)
                                return false;
                        } else {
                            if (group.indexOf(this.root_category) < 0) {
                                return false;
                            }
                        }

                        return true;
                    });
                },
                context: (context) => {

                    switch (context.data.type_proposal) {
                        case "text":
                            context.desc = 'requirement.type_proposal_text';
                            break;
                        case "number":
                            context.desc = 'requirement.type_proposal_number';
                            break;
                        case "boolean":

                            if (context.data.vi_max) {
                                context.desc = 'requirement.type_proposal_boolean_0';
                            } else if (context.data.vi_min) {
                                context.desc = 'requirement.type_proposal_boolean_1';
                            } else {
                                context.desc = 'requirement.type_proposal_boolean';
                            }
                            break;
                    }


                    context.actions = [
                        {
                            label: 'delete',
                            header: () => {

                            }
                        }
                    ]
                    return context;
                }
            })
        })
    }

    async set_context(context) {

        if (!this.method_marks) {
            if (context.method_marks && context.method_marks.id) {
                this.method_marks = context.method_marks.id;
            } else {
                this.method_marks = context.method_marks;
            }
        }

        this.categoryDataSource = new DataSource({
            ignore_listeners: true,
            store: {
                data: context.ref_criteria_categories,
                filter: (item) => {

                    if (!this.gos && !this.gup && (item.company_view & StatusView.Corp) == 0) {
                        return false;
                    }
                    if (this.gos && (item.company_view & StatusView.Gos) == 0) {
                        return false;
                    }
                    if (this.gup && (item.company_view & StatusView.Gup) == 0) {
                        return false;
                    }
                    if (!this.green && item.only_green)
                        return false;

                    return this.root_groop.indexOf(item.group) >= 0;
                },
                context: (context) => {
                    if (context.group.indexOf('new_') == 0) {
                        context.company_view = StatusView.Gos | StatusView.Gup;
                        context.group = context.group.replace('new_', '');
                        context.new = true;

                        context.source = new DataSource({
                            valueExp: ["id", "meta"],
                            store: {
                                ref: 'ref_criteria_types',
                                injectQuery: (params) => {
                                    params.filters = {
                                        group: [context.id]
                                    }
                                    return params;
                                }
                            }
                        })
                    }
                    if (this._new_criteria) {
                        context.company_view = context.new ? StatusView.Gos | StatusView.Gup : StatusView.Corp;

                    } else {
                        context.company_view = context.new ? 0 : StatusView.Corp | StatusView.Gos | StatusView.Gup;
                    }



                    return context
                }
            },
        })

        await this.categoryDataSource.load();
        let data = this.categoryDataSource._store._array.filter((item) => {
            return this.categoryDataSource._store.filter.call(this, item)
        })

        if (context.category_id && data) {
            context.category_id = data.find((category) => {
                return category.id == context.category_id
            })
        }

        if (!context.category_id && data && data[0]) {
            context.category_id = data[0].id
        }

        this.category_id = context.category_id;
        this.properties.category_id.onChange();

        if (context.sample_file_attach && !Array.isArray(context.sample_file_attach))
            context.sample_file_attach = [context.sample_file_attach]

        this.sample_file_attach = context.sample_file_attach;


        this.id = this.id || context.id;
        this.proc_id = this.proc_id || context.proc_id;
        this.lot_id = this.lot_id || context.lot_id;
        this.item_id = this.item_id || context.item_id;

        this.group = this.group || context.group;
        this.order = this.order || context.order;

        this.name = context.name
        this["name_" + this.extra_langs] = context["name_" + this.extra_langs]

        this.description = this._new_criteria ? context.description : context.value
        this["description_" + this.extra_langs] = context["description_" + this.extra_langs]

        this.file_attach = context.file_attach || false;
        this.f_label = context.f_label;

        this.var_min_1 = 0;
        this.var_max_1 = 0;

        this.mark_auto = context.mark_auto || false;
        this.mark = context.mark || "number";

        switch (context.type_proposal) {
            case "boolean":
                if (context.vi_min) {
                    this.type_proposal = "boolean_1"
                } else if (context.vi_max) {
                    this.type_proposal = "boolean_0"
                } else {
                    this.type_proposal = "boolean"
                }
                this.max = context.max;
                this.min = context.min;
                break;
            case "number":
                this.type_proposal = "number"

                let max = context.is_reverse ? context.min : context.max
                let min = context.is_reverse ? context.max : context.min

                if (max != undefined) {
                    this.range_max = context.vi_max ? max : undefined
                    if (context.is_reverse)
                        this.range_max = context.vi_min ? max : undefined
                    this.var_max_1 = 1;
                }

                if (min != undefined) {
                    this.range_min = context.vi_min ? min : undefined
                    if (context.is_reverse)
                        this.range_min = context.vi_max ? min : undefined
                    this.var_min_1 = 1;
                }

                this.min = min;
                this.max = max;

                this.mark_auto = true

                break;
            default:
                this.type_proposal = context.type_proposal || "boolean"
                break;
        }

        this.min_mark = context.is_reverse ? 5 : 0;
        this.max_mark = context.is_reverse ? 0 : 5;

        this.weight = context.weight || 10;
        this.text_variants = context.text_variants

        if (this.method_marks == 'min_price') {
            this.mark = 'boolean'

        } else {

        }

        if (this.type_proposal == 'boolean' || this.type_proposal == 'boolean_0' || this.type_proposal == 'boolean_1') {

            this.properties.min_mark.label = '-requirement.boolean_min_mark_label'
            this.properties.max_mark.label = '-requirement.boolean_max_mark_label'
        } else {
            this.properties.min_mark.label = 'requirement.mark_label'
            this.properties.max_mark.label = '!requirement.mark_label'
        }

    }


    get template_save() {
        return async (id) => {
            let result = await Vue.Dialog.MessageBox.Form({
                caption: "template_save_caption",
                fields: [
                    {
                        name: "id",
                        label: "template_save_name",
                        description: "template_save_description",
                        type: "string",
                        required: true,
                        status: undefined,
                        value: this.template_id,
                    }
                ]
            });
            if (!result || !result.id)
                return;

            this.template_id = result.id;
            let request = this.request;
            request.id = undefined;
            delete request.id;


            let { error, data } = await this.template_source.store.set_item({
                id: result.id.toLowerCase().replace(/ /gm, '').replace(/\./gm, '_'),
                name: result.id,
                data: request
            })
            this.template_source.reload();

        }
    }

    get template_delete() {
        return async (id) => {
            if (!id) {
                let result = await Vue.Dialog.MessageBox.Form({
                    caption: "delete",
                    fields: [
                        {
                            name: "id",
                            label: "!name",
                            type: "string"
                        }
                    ]
                });
                if (!result || !result.id)
                    return;
                id = result.id;
            }

            let { error, data } = await this.template_source.store.delete(id)
            this.template_source.reload();

        }
    }


    get request() {

        let fields = this.fields.filter((field) => {
            if (field.type == 'info' || field.type == 'static')
                return false;
            return true;
        }).map((field) => {

            let hidden = false;
            if (field.hidden && typeof field.hidden == 'function') {
                hidden = field.hidden();
            } else {
                hidden = field.hidden;
            }

            if (hidden) {
                return {
                    name: field.name,
                    value: null
                }
            }

            let value = field.value;

            if (field.value && field.value.exp && field.value.exp.value != undefined) {
                value = field.value.exp.value
            }

            if (Array.isArray(value) && value.length <= 0)
                value = null;

            return {
                name: field.name,
                value: value
            }
        }).reduce((prev, curr) => {
            prev[curr.name] = curr.value
            return prev;
        }, {});

        let params = {
            id: this.id,
            group: this.group,
            order: this.order,
            type: "text",
            offer_dub: true,
            offer_required: true,
            part_hide: false,


            name: fields.name,
            description: fields.description,
            file_attach: fields.file_attach,
            f_label: fields.f_label,
            type_proposal: fields.type_proposal,

            mark_auto: fields.mark_auto,
            mark: fields.mark,

            min: fields.range_min ?? fields.min ?? null,
            vi_min: !Number.isNaN(Number(fields.range_min ?? undefined)),
            min_mark: 0,

            max: fields.range_max ?? fields.max ?? null,
            vi_max: !Number.isNaN(Number(fields.range_max ?? undefined)),
            max_mark: 5,

            weight: fields.weight || 10,
            text_variants: fields.text_variants,

            category_id: fields.category_id,
            criteria_type: fields.criteria_type?.id,
            sample_file_attach: fields.sample_file_attach
        }

        if (this.extra_langs) {
            params["name_" + this.extra_langs] = fields["name_" + this.extra_langs] ?? null
            params["description_" + this.extra_langs] = fields["description_" + this.extra_langs] ?? null
        }

        if (this.root_category == "quality_fields") {
            //params.weight = 0;
        }

        if (!params.mark) {
            params.mark = params.type_proposal == 'number' ? 'number' : 'boolean'
        }

        switch (fields.type_proposal) {
            case "text":
                break;
            case "select":
                break;
            case "number":
                break;
            case "boolean":
                params.mark = 'boolean';
                params.min = "Нет";
                params.max = "Да";
                break;
            case "boolean_0":
                params.type_proposal = 'boolean'
                params.mark = 'boolean';
                params.min = "Нет";
                params.max = "Да";
                params.vi_min = false;
                params.vi_max = true;
                break;
            case "boolean_1":
                params.type_proposal = 'boolean'
                params.mark = 'boolean';
                params.min = "Нет";
                params.max = "Да";
                params.vi_min = true;
                params.vi_max = false;
                break;
        }

        if (fields.min_mark == 5 || fields.max_mark == 0) {
            params.is_reverse = true;
            if (params.type_proposal != "boolean") {
                let res = params.min;
                params.min = params.max;
                params.max = res

                res = params.vi_min;
                params.vi_min = params.vi_max;
                params.vi_max = res
            } else {
                //let res = params.vi_min;
                //params.vi_min = params.vi_max;
                //params.vi_max = res
            }
        } else {
            params.is_reverse = false;
        }

        return params; //JSON.stringify(params, null, '\t')
    }

    async save() {
        let params = this.request;
        if (!this._new_criteria) {
            params.value = params.description;
            params.description = undefined;
        }
        let method = this.id ? "upd_field" : "set_field";
        let { error, data } = await Http.proc.rpc(method, {
            proc_id: this.proc_id,
            lot_id: this.lot_id,
            item_id: this.item_id,
            field: params
        });

        if (error) {
            if (!this.setError(error, "field")) {
                await Vue.Dialog.MessageBox.Error(error)
            }
        }

        return {
            error,
            data
        }
    }

    validate() {

        let params = this.request;

        let match = /^[\~\-\!]+/gm.exec(this.name)
        if (match) {
            return !this.setError({ data: [{ name: "name", message: Language.t('field_setting.label_error') }] });
        }

        let valid_required = () => {

            if (params.sample_file_attach) {
                if ((Array.isArray(params.sample_file_attach) ? params.sample_file_attach : [params.sample_file_attach]).filter((file) => {
                    return !file.id
                }).length > 0)
                    return false;
            }

            if (!params.name || !params.description || (!params.category_id && this.root_groop) || (!this._new_criteria && params.file_attach && !params.f_label))
                return false;

            if (params.type_proposal == 'select' && (!params.text_variants || !params.text_variants[0] || !params.text_variants[10])) {
                return false;
            }

            return true;
        }

        // Очищаем только ошибки 
        for (let prop in this.properties) {
            if (this.properties?.[prop]?.status?.type == 'error') {
                this.properties[prop].status = undefined;
            }
        }

        if (params.type_proposal != 'number')
            return valid_required();

        if (this.range_min != undefined && this.range_max != undefined && this.range_min >= this.range_max) {
            this.setError({
                data: [
                    { name: "range_min", message: `${Language.t('requirement.valid_1')} ${this.range_max}` },
                    { name: "range_max", message: `${Language.t('requirement.valid_2')} ${this.range_min}` }
                ]
            })
            return false;
        } else if (params.mark_auto && params.min != null && params.max != null) {

            if ((params.min >= params.max && !params.is_reverse) || (params.min <= params.max && params.is_reverse)) {
                this.setError({
                    data: [
                        { name: "min", message: `${Language.t('requirement.valid_1')} ${this.max}` },
                        { name: "max", message: `${Language.t('requirement.valid_2')} ${this.min}` }
                    ]
                })
                return false;
            }
        } else if (params.mark_auto) {
            let var_min_1 = this.var_min_1
            if (this.var_min_1 && this.var_min_1.exp && this.var_min_1.exp.value != undefined) {
                var_min_1 = this.var_min_1.exp.value
            }

            let var_max_1 = this.var_max_1
            if (this.var_max_1 && this.var_max_1.exp && this.var_max_1.exp.value != undefined) {
                var_max_1 = this.var_max_1.exp.value
            }

            let error_data = [];
            if (var_min_1 == 1 && this.min == null) {
                error_data.push({ name: "min", message: `${Language.t('required_field')}` },)
            }
            if (var_max_1 == 1 && this.max == null) {
                error_data.push({ name: "max", message: `${Language.t('required_field')}` },)
            }
            if (error_data.length > 0) {
                this.setError({ data: error_data });
                return false;
            }

        }

        return valid_required();
    }

    props() {
        let $this = this;

        let ds_min_price = DataSource.get([
            { id: "boolean", name: 'requirement.type_proposal_boolean', desc: "requirement.type_proposal_boolean_desc" },
            { id: "boolean_0", name: 'requirement.type_proposal_boolean_0', desc: "requirement.type_proposal_boolean_0_desc" },
            { id: "boolean_1", name: 'requirement.type_proposal_boolean_1', desc: "requirement.type_proposal_boolean_1_desc" },
        ])

        let ds_default = DataSource.get([
            //{ id: "text", name: 'requirement.type_proposal_text', desc: "requirement.type_proposal_text_desc" },
            { id: "select", name: 'requirement.type_proposal_select', desc: "requirement.type_proposal_select_desc" },
            { id: "number", name: 'requirement.type_proposal_number', desc: "requirement.type_proposal_number_desc" },
            { id: "boolean", name: 'requirement.type_proposal_boolean', desc: "requirement.type_proposal_boolean_desc" },
            { id: "boolean_0", name: 'requirement.type_proposal_boolean_0', desc: "requirement.type_proposal_boolean_0_desc" },
            { id: "boolean_1", name: 'requirement.type_proposal_boolean_1', desc: "requirement.type_proposal_boolean_1_desc" },
        ])

        let ds_quality = DataSource.get([
            //{ id: "text", name: 'requirement.type_proposal_text', desc: "requirement.type_proposal_text_desc" },
            { id: "boolean", name: 'requirement.type_proposal_boolean', desc: "requirement.type_proposal_boolean_desc" },
            { id: "boolean_0", name: 'requirement.type_proposal_boolean_0', desc: "requirement.type_proposal_boolean_0_desc" },
            { id: "boolean_1", name: 'requirement.type_proposal_boolean_1', desc: "requirement.type_proposal_boolean_1_desc" },
        ])

        let all_hidden = () => {
            if (!this.category_id && this.root_groop && this.root_groop.length > 1)
                return true;
        }


        let is_mark_expert = () => {

            let mark_auto = (this.mark_auto && this.mark_auto.id != undefined) ? this.mark_auto.id : this.mark_auto;
            return !mark_auto;
        }

        let type_proposal = () => {
            return (this.type_proposal && this.type_proposal.id != undefined) ? this.type_proposal.id : this.type_proposal;
        }

        let is_mark = () => {
            return true;
            //let type = type_proposal();
            //return type != 'boolean_0' && type != 'boolean_1'
        }


        return {
            category_id: {
                group: '!Требования/!-category',
                type: 'entity',
                required: true,
                dataSource: this.categoryDataSource,
                onChange: async (category, oldCategory) => {

                    if (category) {
                        category = await this.properties.category_id.dataSource.byKey(category)
                    }

                    this.root_category = (category && category.group) || this.parent_group


                    switch (this.root_category) {
                        case "quality_fields":
                            this.properties.type_proposal.setAttributes({
                                dataSource: this.method_marks == 'min_price' ? ds_min_price : ds_quality
                            })
                            if (this.type_proposal && (this.type_proposal.id == 'number' || this.type_proposal.id == 'select')) {
                                this.type_proposal = "boolean"
                            }

                            this.mark_auto = false;
                            this.mark = "boolean";

                            break;
                        default: {
                            this.properties.type_proposal.setAttributes({
                                dataSource: this.method_marks == 'min_price' ? ds_min_price : ds_default
                            })
                        }
                    }

                    if (category) {
                        this.properties.criteria_type.setAttributes({
                            dataSource: category.source
                        });
                        if(this.context.criteria_type == 'other')
                            this.context.criteria_type = undefined;
                        this.criteria_type = this.context.criteria_type
                    }
                },
                hidden: () => {
                    return !this.root_groop
                }
            },
            criteria_type: {
                group: '!Требования/!-category',
                type: 'entity',
                label: 'field_setting.criteria_type',
                has_del: true,
                /*dataBind: {
                    property: 'dataSource',
                    name: 'category_id',
                    field: 'source',
                },*/
                onChange: (value) => {
                    if (value?.meta?.file != undefined) {
                        this.file_attach = true
                    }
                }
            },
            criteria_type_description: {
                group: '!Требования/!-category',
                type: 'widget',
                label: "field_setting.criteria_type_description",
                widget: {
                    name: "ui-alert",
                    props: {
                        type: "warning",
                        style: "margin: 0"
                    },
                },
                bind: {
                    "widget.content": "criteria_type.description",
                    "hidden": "!criteria_type || !criteria_type.description"
                }
            },

            name: {
                label: 'requirement.name',
                group: '!Требования/!-name',
                type: "string",
                description: "$requirement.name_info",
                required: true,
                attr: {
                    react: true
                },
                onChange: (value) => {
                    let extra = this.properties["name_" + this.extra_langs]
                    if (!extra)
                        return;
                    if (value != this.context.name)
                        extra.status = {
                            type: "warning",
                            message: Language.t('field_setting.extra_langs.warning')
                        }
                    else {
                        extra.status = undefined;
                    }
                }
            },
            ["name_" + this.extra_langs]: {
                label: 'field_setting.extra_langs',
                group: '!Требования/!-name',
                type: "string",
                //description: "$requirement.name_info",
                //required: true,
                attr: {
                    react: true,
                    placeholder: "requirement.name"
                },
                hidden: !this.extra_langs
            },

            description: {
                label: 'requirement.description',
                group: 'Требования/!-description',
                type: "text",
                description: "$requirement.description_info",
                required: true,
                attr: {
                    react: true
                },
                onChange: (value) => {
                    let extra = this.properties["description_" + this.extra_langs]
                    if (!extra)
                        return;
                    if (value != this.context.description)
                        extra.status = {
                            type: "warning",
                            message: Language.t('field_setting.extra_langs.warning')
                        }
                    else {
                        extra.status = undefined;
                    }
                }
            },
            ["description_" + this.extra_langs]: {
                label: 'field_setting.extra_langs',
                group: 'Требования/!-description',
                type: "text",
                //description: "$requirement.description_info",
                //required: true,
                attr: {
                    react: true,
                    placeholder: "requirement.description"
                },
                hidden: !this.extra_langs
            },

            file_attach: {
                group: 'Требования/!-file',
                type: 'entity',
                label: 'requirement.file_attach',
                //value: false,
                dataSource: [{ id: false, name: "no" }, { id: true, name: "yes" }],
                required: true,
                bind: {
                    readonly: () => {
                        return this.criteria_type?.meta?.file != undefined
                    }
                }
            },
            f_label: {
                group: 'Требования/!-file',
                type: 'string',
                label: 'requirement.file_attach_name',
                required: true,
                attr: {
                    react: true
                },
                hidden: () => {
                    if (this._new_criteria)
                        return true;

                    if (!this.file_attach || this.file_attach.id != true)
                        return true;
                    if (this.criteria_type?.meta?.file != undefined) {
                        return true;
                    }
                }
            },
            sample_file: {
                group: 'Требования/!-file',
                type: 'widget',
                label: "field_setting.sample_file",
                widget: {
                    name: {
                        computed: {
                            items() {
                                let files = $this.criteria_type?.meta?.file
                                if (!Array.isArray(files))
                                    files = [files];
                                return files.filter((file) => {
                                    return file && file.link
                                })
                            }
                        },
                        template: `
                            <div style='font-size: 14px;'>
                                <ui-alert v-if='!items || items.length <=0' type='warning'>{{$t('no_data')}}</ui-alert>
                                <template v-else>
                                    <div v-for='file in items' style='line-height: 24px;'><a :href='file.link' target="_blank">{{file.label || file.link}}</a></div>
                                </template>
                            </div>
                        `
                    }
                },
                hidden: () => {
                    if (!this._new_criteria)
                        return true;
                    if (!this.file_attach || this.file_attach.id != true)
                        return true;
                    if (this.criteria_type?.meta?.file == undefined) {
                        return true;
                    }
                }
            },
            sample_file_attach: {
                group: 'Требования/!-file',
                type: 'file',
                label: "field_setting.sample_file_attach",
                description: 'field.approvement_info_15',
                multiple: true,
                hidden: () => {
                    if (!this._new_criteria)
                        return true;
                    if (!this.file_attach || this.file_attach.id != true)
                        return true;
                    if (this.criteria_type?.meta?.file != undefined) {
                        return true;
                    }
                }
            },

            f_label_empty: {
                group: 'Требования/!-file',
                type: 'static',
                label: '!',
                hidden: () => {
                    if (!this.file_attach || this.file_attach.id != true)
                        return false;
                    return true
                }
            },
            type_proposal: {
                type: "entity",
                group: 'Требования/!-type',
                label: "requirement.type_proposal",
                required: true,
                dataSource: () => {
                    if (this.method_marks == 'min_price')
                        return ds_min_price

                    if (this.parent_group == 'quality_fields') {
                        return ds_quality
                    }

                    return ds_default
                },
                hidden: all_hidden,
                onChange: (value) => {
                    this.min = this.max = undefined;

                    this.range_min = this.range_max = undefined;
                    this.var_min_1 = this.var_max_1 = 0;

                    this.min_mark = 0;
                    this.max_mark = 5;
                    if (value == 'select') {
                        this.mark_auto = false;
                    } else if (value == 'text') {
                        this.mark_auto = false;
                    } else if (value == 'boolean' || value == 'boolean_0' || value == 'boolean_1') {

                        if (value == 'boolean_0') {
                            this.min_mark = 5;
                            this.max_mark = 0;
                        }

                        this.properties.min_mark.label = '-requirement.boolean_min_mark_label'
                        this.properties.max_mark.label = '-requirement.boolean_max_mark_label'
                    } else {
                        this.properties.min_mark.label = 'requirement.mark_label'
                        this.properties.max_mark.label = '!requirement.mark_label'
                    }

                    if (value == 'number') {
                        this.mark_auto = true
                    }

                }
            },
            range_min: {
                type: "float",
                group: 'Требования/!-type/<range>',
                label: "from",
                hidden: () => {
                    if (all_hidden())
                        return true
                    if (this.type_proposal && this.type_proposal.id == 'number')
                        return false;
                    return true
                },
                onChange: (value) => {
                    if (value != undefined) {
                        this.min = value
                        this.var_min_1 = 1
                    } else {
                        this.min = undefined
                        this.var_min_1 = 0
                    }
                }
            },
            range_max: {
                type: "float",
                group: 'Требования/!-type/<range>',
                label: "to",
                hidden: () => {
                    if (all_hidden())
                        return true
                    if (this.type_proposal && this.type_proposal.id == 'number')
                        return false;
                    return true
                },
                onChange: (value) => {
                    if (value != undefined) {
                        this.max = value
                        this.var_max_1 = 1
                    } else {
                        this.max = undefined
                        this.var_max_1 = 0
                    }
                }
            },
            range_empty: {
                type: "static",
                group: 'Требования/!-type/<range>',
                label: "!",
                hidden: () => {
                    if (this.type_proposal && this.type_proposal.id == 'number')
                        return true;
                    return all_hidden()
                }
            },
            mark_auto: {
                group: 'field_setting.mark/!-top',
                type: "entity",
                label: "requirement.mark_auto",
                dataSource: [
                    { id: true, name: "requirement.mark_auto_0", desc: "requirement.mark_auto_0_desc" },
                    { id: false, name: "requirement.mark_auto_1", desc: "requirement.mark_auto_1_desc" }
                ],
                //value: false,
                readonly: () => {
                    //if(is_mark_expert())
                    // return true;

                    if (this.root_category == "quality_fields")
                        return true;

                    if (this.type_proposal && (this.type_proposal == 'text' || this.type_proposal.id == 'text' || this.type_proposal == 'select' || this.type_proposal.id == 'select' || this.type_proposal == 'number' || this.type_proposal.id == 'number'))
                        return true;
                },
                hidden: () => {
                    return !is_mark() || all_hidden();
                }
            },
            mark: {
                group: 'field_setting.mark/!-top',
                type: "entity",
                label: "requirement.mark",
                dataSource: [
                    { id: "number", name: "requirement.mark_number" },
                    { id: "boolean", name: "requirement.mark_boolean" }
                ],
                //value: "number",
                hidden: () => {

                    let type_prop = type_proposal();
                    if (type_prop == 'boolean' || type_prop == 'boolean_0' || type_prop == 'boolean_1')
                        return true;

                    if (this.root_category == "quality_fields")
                        return true;

                    return !is_mark_expert() || !is_mark() || all_hidden()
                },

            },
            mark_empty_0: {
                group: 'field_setting.mark/!-top',
                type: 'string',
                label: "requirement.mark",
                readonly: true,
                value: Language.t('requirement.mark_boolean'),
                hidden: () => {
                    if (all_hidden())
                        return true
                    if (this.root_category == "quality_fields")
                        return false;

                    if (!is_mark_expert())
                        return true;

                    let type_prop = type_proposal();
                    if (type_prop == 'boolean' || type_prop == 'boolean_0' || type_prop == 'boolean_1')
                        return false;

                    return true;
                }
            },
            mark_empty_1: {
                group: 'field_setting.mark/!-top',
                type: "static",
                label: "!",
                hidden: () => {
                    return is_mark_expert() || all_hidden()
                }
            },
            var_min_1: {
                group: 'field_setting.mark/!mark/<min>',
                type: "entity",
                label: "requirement.var_label",
                dataSource: new DataSource({
                    displayExp: (item) => {
                        if (item.id == 1) {
                            return this.range_min != undefined ? "requirement.var_eq" : "requirement.var_lte"
                        }
                        return "requirement.var_min";
                    },
                    store: new ArrayStore([{ id: 0 }, { id: 1 }])
                }),
                //value: 0,
                hidden: () => {
                    if (type_proposal() != 'number')
                        return true;
                    return is_mark_expert() || !is_mark() || all_hidden()
                },
                readonly: () => {
                    return this.range_min != undefined
                },
                onChange: (value) => {
                    if (value)
                        this.properties.min.label = '!requirement.min_label'
                    else {
                        this.properties.min.label = '!'
                        this.min = undefined
                    }
                }
            },
            min: {
                group: 'field_setting.mark/!mark/<min>',
                type: "float",
                label: "!",
                readonly: () => {
                    return (!$this.var_min_1 || !$this.var_min_1.id) || this.range_min != undefined
                },
                hidden: () => {
                    if (type_proposal() != 'number')
                        return true;
                    let var_min = !this.var_min_1 || (this.var_min_1 != undefined && !this.var_min_1.id);
                    let var_max = !this.var_max_1 || (this.var_max_1 != undefined && !this.var_max_1.id);
                    if (var_min && var_max && type_proposal() == 'number')
                        return true
                    return is_mark_expert() || !is_mark() || all_hidden()
                }
            },
            min_mark: {
                group: 'field_setting.mark/!mark/<min>',
                type: "entity",
                label: "requirement.mark_label",
                dataSource: [
                    { id: 0, name: `0 ${Language.t('field_setting.points')}` },
                    { id: 5, name: `5 ${Language.t('field_setting.points')}` },
                ],
                //value: 0,
                hidden: () => {
                    if (type_proposal() == 'boolean_1')
                        return true;
                    return is_mark_expert() || !is_mark() || all_hidden()
                },
                readonly: () => {
                    if (type_proposal() == 'boolean_0')
                        return true;
                },
                onChange: (value) => {
                    this.max_mark = value == 0 ? 5 : 0
                }
            },
            var_max_1: {
                group: 'field_setting.mark/!mark/<max>',
                type: "entity",
                label: "!var_max_1",
                dataSource: new DataSource({
                    displayExp: (item) => {
                        if (item.id == 1) {
                            return this.range_max != undefined ? "requirement.var_eq" : "requirement.var_gte"
                        }
                        return "requirement.var_max";
                    },
                    store: new ArrayStore([{ id: 0 }, { id: 1 }])
                }),
                //value: 0,
                hidden: () => {
                    if (type_proposal() != 'number')
                        return true;
                    return is_mark_expert() || !is_mark() || all_hidden()
                },
                readonly: () => {
                    return this.range_max != undefined
                },
                onChange: (value) => {
                    if (value)
                        this.properties.max.label = '!requirement.min_label'
                    else {
                        this.properties.max.label = '!'
                        this.max = undefined
                    }
                }
            },
            max: {
                group: 'field_setting.mark/!mark/<max>',
                type: "float",
                label: "!",
                readonly: () => {
                    return (!$this.var_max_1 || !$this.var_max_1.id) || this.range_max != undefined
                },
                hidden: () => {
                    if (type_proposal() != 'number')
                        return true;
                    let var_min = !this.var_min_1 || (this.var_min_1 != undefined && !this.var_min_1.id);
                    let var_max = !this.var_max_1 || (this.var_max_1 != undefined && !this.var_max_1.id);
                    if (var_min && var_max)
                        return true

                    return is_mark_expert() || !is_mark() || all_hidden()
                }

            },
            max_mark: {
                group: 'field_setting.mark/!mark/<max>',
                type: "entity",
                label: "!requirement.mark_label",
                dataSource: [
                    { id: 0, name: `0 ${Language.t('field_setting.points')}` },
                    { id: 5, name: `5 ${Language.t('field_setting.points')}` },
                ],
                //value: 5,
                hidden: () => {
                    if (type_proposal() == 'boolean_0')
                        return true;
                    return is_mark_expert() || !is_mark() || all_hidden()
                },
                readonly: () => {
                    if (type_proposal() == 'boolean_1')
                        return true;
                },
                onChange: (value) => {
                    this.min_mark = value == 0 ? 5 : 0
                }
            },

            text_variants: {
                type: 'model',
                group: 'field_setting.mark',
                label: 'field_setting.text_variants',
                description: "-field_setting.text_variants.description",
                required: true,
                fields: {
                    ...[0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5].map((points, index) => {
                        let border_top = points > 0 ? '0 0' : '5px 5px'
                        let border_bottom = points < 5 ? '0 0' : '5px 5px'
                        return {
                            label: `!${Language.t('field_setting.text_variants.placeholder')} ${(index == 0 || index == 10) ? ("(" + Language.t('required_field') + ")") : ""}`,
                            prefix: index + 1,
                            required: (index == 0 || index == 10),
                            attr: {
                                style: `margin-bottom:-1px;border-radius: ${border_top} ${border_bottom};`,
                                react: true,
                            },
                            bind: {
                                suffix: () => {
                                    let mark = this.mark?.id || this.mark;
                                    return mark == 'number' ? Language.t('field_setting.point_count', { count: points.toString() }) : '';
                                }
                            }
                        }
                    })
                },
                hidden: () => {
                    let mark = this.mark?.id || this.mark;
                    let type = this.type_proposal?.id || this.type_proposal

                    if (type != 'select') {
                        return true
                    }
                }
            },
            weight: {
                group: 'field_setting.mark',
                type: "range",
                label: 'requirement.weight',
                min: 0.1,
                max: 10,
                //value: 10,
                description: "$requirement.weight_info",
                hidden: function () {
                    if (this.model.root_category == "quality_fields")
                        return true;
                    if (this.model.method_marks == 'min_price')
                        return true;
                    return !is_mark() || all_hidden()
                },
                attr: {
                    step: 0.1
                }
            },
            mark_setting: {
                group: 'field_setting.mark',
                type: 'setting',
                hidden: this.hide_mark
            }
        }
    }
    static async get(params) {
        let { error, data } = await Http.proc.rpc("get_field_settings", { ...params, ref_criteria_categories: undefined });

        if (data && data.type_proposal == 'text') {
            return {
                error: {
                    message: Language.t('requirement.type_proposal_text_error')
                }
            }
        }

        if (!error)
            return {
                data: new ModelField({
                    ...data,
                    ...params,
                })
            }

    }
}

Vue.Dialog("AddField", {
    props: ['hide_mark', 'method_marks', "id", "group", "order", "proc_id", "lot_id", "item_id", "category", "green", "extra_langs", "gos", "gup", "_new_criteria"],
    data: function () {
        return {
            model: undefined,
            error: undefined,
            page: 0,
            template_dropdown: false
        }
    },
    computed: {
        is_valid() {
            return !this.model || !this.model.validate();
        }
    },
    async mounted() {

        this.$wait(async () => {
            let ref_criteria_categories = await Static.get("ref_criteria_categories")
            let {error,data} = await ModelField.get({
                ref_criteria_categories: ref_criteria_categories || [],
                _new_criteria: this._new_criteria,
                extra_langs: this.extra_langs,
                hide_mark: this.hide_mark,
                green: this.green,
                gos: this.gos,
                gup: this.gup,
                method_marks: this.method_marks,
                group: this.group,
                category: this.category,
                order: this.order,
                proc_id: this.proc_id,
                lot_id: this.lot_id,
                item_id: this.item_id,

                field_id: this.id,
            });

            this.model = data;
            this.error = error
        })


        Develop.onChangeProperty.bind(this.devBind)
    },
    destroyed: function () {
        Develop.onChangeProperty.unbind(this.devBind);
    },
    methods: {
        async save() {
            this.wait(async () => {
                let { error, data } = await this.model.save();
                if (!error)
                    this.Close(data);
            })
        },
        onItem(item) {
            this.model.template_id = item.name;

            //this.model.lock = true;
            this.model.set_context(item.data);
            //this.model.lock = false;


        },
        template(method, params) {
            this.wait(async () => {
                if (this.model[`template_${method}`]) {
                    await this.model[`template_${method}`](params);
                }
            });
        }
    },
    template: `
    <div>
        <div class='iac-dialog-header'>
            {{$t('requirement.create')}}
            
            <div style='position: relative; '  v-if='0'>
                <span v-on:click='()=>{this.template_dropdown = true}' 
                    style='font-size: 14px; cursor: pointer; line-height: 14px; border: 1px solid #dbdbdb; padding: 2px 8px;border-radius: 4px;'>
                    <span>{{$t('Template')}}</span> 
                    <icon style='vertical-align: middle; font-size: 7px; color: #999;'>arrow</icon>
                </span>
                <div style='position: absolute; z-index: 100; width: 300px; background: #fff; right: 0;border: 1px solid #ccc;border-radius: 5px;box-shadow: 0px 0px 4px #0003;padding: 5px;' v-if='template_dropdown' v-on-clickaway='()=>{this.template_dropdown = false}'>
                    <ui-list :sync='false' :dataSource='model.template_source' v-on:item='onItem' style='max-height: 400px;overflow: auto;border: 1px solid #e9e9e9;border-radius: 2px;'>
                        <template slot='template' slot-scope='props'> 
                            <div class='display'>{{props.item.name}}
                             <div v-if='props.item.desc' class='desc'>{{$t(props.item.desc)}}</div>
                            </div>
                            <ui-btn style='flex: 0 0 auto;' type='danger xs' @click.native.stop.prevent='template("delete", props.item.id)'><icon>delete</icon></ui-btn>
                        </template>
                        <template slot='not-found'>
                            <ui-alert type='warning' style='margin: -10px;font-size: 14px;'>{{$t('No_list_of_templates')}}</ui-alert>
                        </template>
                    </ui-list>

                    <ui-btn-group style='margin-top: 10px;'>
                        <ui-btn type='primary sm' v-if='model && model.template_save' v-on:click.native='template("save")'>{{$t('action.save_template')}}</ui-btn>
                    </ui-btn-group>
                </div>
            </div>


        </div>
        <main class='error' v-if='error'>{{error.message}}</main>
        <main v-if='model'>
            <ui-layout root1='ui-layout-tab'  :fields='model.fields'/>
            <ui-alert v-if='$develop.add_field_show_request && model.request' type='danger'>
<pre><code>минимальное: {{model.request.min}}
критичность: {{model.request.vi_min}}
оценка: {{model.request.min_mark}}

максимальное: {{model.request.max}}
критичность: {{model.request.vi_max}}
оценка: {{model.request.max_mark}}

реверс: {{model.request.is_reverse}}</code></pre></ui-alert>
            <ui-alert v-if='$develop.add_field_show_request && model' type='warning'><pre><code>{{model.request}}</code></pre></ui-alert>
        </main>
        <footer>
            <ui-btn type='secondary' v-on:click.native='()=>Close()'>{{$t('cancel')}}</ui-btn>
            <ui-btn v-if='model' type='primary' :disabled='is_valid' v-on:click.native='save'>{{$t(model && model.id ? 'edit' : 'save')}}</ui-btn>
        </footer>
    </div>
    `

})