import { DataSource, RemoteStore, Query, RefStore } from '@iac/data';

export default {
  data() {
    return {
      dataSource: new DataSource({
        request_count: true,
        store: new RefStore({
          ref:'ref_bad_suppliers',
          context: (context)=>{
            context.id = undefined
            return context
          }
        }),
        template:'template-company'
      })
    }
  },
  template: `
    <div>
      <iac-section type='header'>
        <ol class='breadcrumb'>
          <li><router-link to='/'>{{ $t('home') }}</router-link></li>
          <li>{{ $t('hp.registries.link4') }}</li>
        </ol>
        <h1>{{ $t('hp.registries.link4') }}</h1>
      </iac-section>
      <iac-section>
        <ui-layout-group>
          <ui-data-view :dataSource='dataSource'/>
        </ui-layout-group>
      </iac-section>
    </div>
  `
}
