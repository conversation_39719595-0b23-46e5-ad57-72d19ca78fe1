import {Entity, DataSource} from '@iac/data'
import { Language } from '@iac/core'
import {Settings} from '@iac/kernel'

export default class CompanyModel extends Entity{
    constructor(context){
        super(context)
    }
    props(){
        return {
            inn: {
                label: '-inn',
                group: 'general',
                type: "static",
            },
            full_title: {
                group: 'general/-!title',
            },
            title: {
                label: 'short_title',
                group: 'general/-!title',
            },

            anno: {
                group: 'general',
                type: 'text',
                required: true
            },
           

            company_type_code: {
                group: "general/-!codes",
                type: 'entity',
                required: true,
                dataSource: "ref_company_type",
            },
            oked_code: {
                group: "general/-!codes",
                type: 'entity',
                required: true,
                dataSource: "ref_economic_acivity_type"
            },

            isFilial: {
                label: 'is_filial',
                group: 'general/-!filial/!left',
                type: 'bool',
            },
            org_code: {
                group: 'general/-!filial/!right',
                description: '$org_code_description',
                hidden:  ()=> {
                    return !this.isFilial
                }
            },

            email: {
                label: '~email',
                group: 'additionally',
                required: true,
            },
            web: {
                label: '~web',
                group: 'additionally',
            },
            phone: {
                label: '~phone',
                group: 'additionally',
                required: true,
            },
            beneficiary: {
                group: 'additionally',
                type: 'text',
            },


            is_seller: {
                label: "seller",
                group: "role_in_the_system/-!seller/!left",
                type: 'bool',
                //description: Language.t("company_reg_is_seller_attention"),
                onChange: function(value){
                    if(value){
                        this.description = Language.t("company_reg_is_seller_attention")
                    }else{
                        this.description = undefined;
                    }
                }
            },
            is_small_business: {
                group: "role_in_the_system/-seller/!right",
                type: 'bool',
                hidden:  ()=> {
                    return !this.is_seller
                }
            },
            free_economic_zone_id: {
                group: "role_in_the_system/-seller/!right",
                type: 'entity',
                dataSource: {
                    valueExp: 'id',
                    //search: true,
                    store: {
                        key: 'id',
                        method: 'ref_free_economic_zone',
                    }
                },
                hidden:  ()=> {
                    return !this.is_seller || Settings._country == 'KG'
                }
            },


            is_buyer: {
                label: "buyer",
                group: "role_in_the_system/-!buyer/!left",
                type: 'bool',
                //description: Language.t("company_reg_is_seller_attention"),
                onChange: function(value){
                    if(value){
                        this.description = Language.t("company_reg_is_seller_attention")
                    }else{
                        this.description = undefined;
                    }
                }
            },
            soogu_code: {
                group: "role_in_the_system/-buyer/!right",
                type: 'entity',
                dataSource: "ref_soogu",
                hidden: ()=>{
                    return !this.is_buyer
                },
            },
            is_strategical: {
                group: "role_in_the_system/-buyer/!right",
                type: 'bool',
                value: false,
                hidden: ()=>{
                    return !this.is_buyer
                },
            },

            is_broker: {
                label: "Запросить статус брокера",
                group: "role_in_the_system/-!is_broker/!left",
                type: 'bool',
                //description: Language.t("company_reg_is_seller_attention"),
                onChange: function(value){
                    if(value){
                        this.description = Language.t("company_reg_is_seller_attention")
                    }else{
                        this.description = undefined;
                    }
                }
            },
            sert: {
                label: "сертификаты",
                group: "role_in_the_system/-is_broker/!right",
                type: 'file',
                value: undefined,
                multiple: true,
                hidden:  ()=> {
                    return !this.is_broker
                }
            },

        }
    }
}