.iac-navigation-top {
  width: 100%;
  background-color: @white;
  color: @black;
  padding: 0 20px;

  &__top {
    margin: 0 -20px;
    border-bottom: 1px solid @light-gray;
    padding: 5px 20px 6px;
    display: flex;
  }

  &__body {
    display: flex;
    height: 80px;
    align-items: center;

    >.toggle {
      line-height: 0;

      align-items: center;
      justify-content: center;
      display: flex;
      color: inherit;
      cursor: pointer;

      border-radius: 50%;
      margin: 0 10px 0 -10px;
      width: 40px;
      height: 40px;
      flex: 0 0 40px;


      &:hover {
        background: rgba(0, 0, 0, 0.05);
      }
    }

    >.brand-lockup {
      margin-right: auto;
      display: flex;
      align-items: center;
      margin-left: 0;
      padding-left: 0
    }

    >.nav {
      display: flex;
      align-items: center;
      flex-grow: 1;


      &.calc_margin{
        margin-left: ~"max( 20px, calc( ( 100vw - 1500px ) / 2 - 200px) )";
      }      

      .user {
        position: relative;

        .user_account{
          display: flex;
          align-items: center;
          .info {
            display: flex; 
            flex-direction: column;
            max-width: 250px;
            padding-right: 8px;

            .name{
              font-size: 14px;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              display: inline-block;
              color: #333;
              &:hover{
                color: #000;
              }
            }
            .team_name{
              font-size: 14px;
              color: #999;
              text-decoration: none;
            }
            a.team_name:hover{
              text-decoration: underline;
                color: #777;
            }

          }
        }
        .icon-wrapper {
          cursor: pointer;
          border: 1px solid #ccc;
          border-radius: 50%;
          display: inline-block;

          .image{
            width: 42px;
            height: 42px;  
            overflow: hidden;
            border: 2px solid #fff;
            border-radius: 50%;
       
          }

          .icon {
            width: 42px;
            height: 42px;
            border: 2px solid #fff;
            border-radius: 50%;
            //display: inline-block;
            background: #ccc;
            color: #fff;
            //line-height: 42px;
            font-size: 24px;
            text-align: center;
            //background: url('https://sun1-47.userapi.com/s/v1/ig2/9rv24qJX5Aa59gsw_GN6FJgplAxDlmKFf3KwXfDL4ngbTMj9KnJzcoQvoxprXKTTOZetwE2HaVwrw7xU58ATRQoa.jpg?size=50x50&quality=96&crop=228,294,686,686&ava=1') 50% 50%;
            background-size: cover;

            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        &_menu {
          box-shadow: 0 6px 18px 0 rgba(14, 21, 47, .13), 0 -2px 6px rgba(14, 21, 47, .03);
          position: absolute;
          right: 0;
          top: 0;
          background: #fff;
          border: 1px solid #e1e1e1;
          border-radius: 4px;
          //margin-top: 8px;
          min-width: 270px;
          padding: 8px 0;
          color: #666;

          >.block {
            &:not(:first-child){
              border-top: 1px solid #ddd;
              padding-top: 8px;
              margin-top: 8px;
            }
            >.item {
              display: flex;
              align-items: center;
              padding: 8px 13px 8px;
              font-size: 14px;
              color: #777;
              &.nowrap{
                white-space: nowrap;
              }
              &.hover {
                cursor: pointer;
                &:hover{
                  background: #eee;
                  color: #000;
                }
              }

              >:first-child {
                flex: 0 0 67px;
                width: 67px;

                display: flex;
                align-items: center;
                justify-content: center;

                ~* {
                  flex: 1 1 100%;
                }
              }
            }
          }
        }

        .menu {
          box-shadow: 0 6px 18px 0 rgba(14, 21, 47, .13), 0 -2px 6px rgba(14, 21, 47, .03);
          position: absolute;
          right: 0;
          top: 0;
          background: #fff;
          border: 1px solid #e1e1e1;
          border-radius: 4px;
          //margin-top: 8px;
          min-width: 270px;
          padding: 8px 0;
          color: #666;

          .item {
            &_user {
              padding: 4px 15px;
              white-space: nowrap;

              >div {
                display: inline-block;

                &.icon-wrapper {
                  position: relative;
                  display: inline-block;
                  margin-right: 10px;

                  .image{
                    width: 42px;
                    height: 42px;  
                    overflow: hidden;
                    border: 2px solid #fff;
                    border-radius: 50%;
               
                  }

                  >.icon {
                    touch-action: manipulation;
                    text-decoration: none;
                    outline: none;
                    display: inline-block;
                    vertical-align: middle;
                    width: 42px;
                    height: 42px;
                    border-radius: 50%;
                    background-size: cover;
                  }
                }

                &.info {
                  vertical-align: middle;

                  .title {
                    color: #000;
                    font-size: 14px;
                    font-weight: 500;
                  }

                  .desc {
                    font-size: 13px;
                  }
                }
              }
            }

            &_menu {
              font-size: 13px;
              line-height: 28px;
              display: block;
              margin: 0;
              padding: 0;

              li {
                display: list-item;
                text-align: match-parent;
                font-size: 13px;
                line-height: 13px;
                list-style: none;

                >* {
                  padding: 8px 12px 8px 67px;
                  display: block;
                  color: #000;
                  cursor: pointer;

                  &:hover {
                    background-color: #ededed;
                  }
                }
              }
            }

            &_separator {
              height: 1px;
              margin: 8px 0;
              background: #e5e5e5;
            }

            &_addcompany {
              padding: 4px 15px;
              white-space: nowrap;
              display: flex;
              align-items: center;
              cursor: pointer;

              &:hover {
                background-color: #ededed;
              }

              >div {
                display: inline-block;
                font-size: 14px;
                color: #000;

                &.add-wrapper {
                  display: inline-block;
                  width: 32px;
                  height: 32px;
                  margin: 0 13px 0 7px;
                  position: relative;

                  >.add {
                    position: absolute;
                    top: -1px;
                    right: -1px;
                    bottom: -1px;
                    left: -1px;
                    content: '';
                    opacity: .6;
                    border: 1px solid #b3b3b3;
                    border-radius: 50px;
                    background: #ccc url(//yastatic.net/s3/frontend/yandex-lego/serp-header/_/44AbkRhw.svg) center no-repeat;
                  }
                }
              }

            }
          }
        }

        .menu1 {
          position: absolute;
          right: 0;
          background: #fff;
          border: 1px solid #e1e1e1;
          border-radius: 4px;
          box-shadow: 0 0 3px 2px #0001;
          margin-top: 8px;
          width: 270px;

          color: #666;

          >div {
            //white-space: nowrap;
            //cursor: pointer;
            padding: 8px 16px;

            &:not(:first-child) {
              border-top: 1px solid #ccc;
            }

            &.hover {
              white-space: nowrap;
              cursor: pointer;

              &:hover {
                background: #eee;
              }
            }
          }
        }
      }
    }
  }
}

.iac-nav-top {
  width: 100%;
  background-color: @white;
  color: @black;
  padding: 0 20px;

  &__top {
    margin: 0 -20px;
    border-bottom: 1px solid @light-gray;
    padding: 5px 20px 6px;
  }

  &__body {
    display: flex;
    height: 80px;
    align-items: center;

    >.toggle {
      line-height: 0;

      align-items: center;
      justify-content: center;
      display: flex;
      color: inherit;
      cursor: pointer;

      border-radius: 50%;
      margin: 0 10px 0 -10px;
      width: 40px;
      height: 40px;
      flex: 0 0 40px;


      &:hover {
        background: rgba(0, 0, 0, 0.05);
      }
    }

    >.brand-lockup {
      margin-right: auto;
      display: flex;
      align-items: center;
      margin-left: 0;
      padding-left: 0
    }

    >.nav {
      display: flex;
      align-items: center;
      flex-grow: 1;
    }
  }

  .user {
    >.info {
      display: flex;
      align-items: center;

      .title {
        padding: 0 16px;
        color: inherit;
      }

      .logout {
        cursor: pointer;
        padding: 8px;
        font-size: 24px;

        &:hover {
          color: #000;
        }
      }
    }

  }
}

@media screen and (min-width:1321px) {

  .iac-navigation-top,
  .iac-nav-top {

    &__body {

      >.toggle {
        display: none;
      }
    }
  }
}

@media screen and (max-width:1320px) {

  .iac-navigation-top .brand-lockup,
  .iac-nav-top .brand-lockup {
    margin-left: 0;
    padding-left: 0
  }
}

@media (max-width:700px) {

  .iac-navigation-top .brand-lockup .info .desc,
  .iac-nav-top .brand-lockup .info .desc {
    display: none;
  }
}

@media (max-width: 450px) {

  .iac-navigation-top,
  .iac-nav-top {
    padding: 0 15px;

    &__top {
      margin: 0 -15px;
      padding: 0 15px;
    }

    .brand-lockup {

      svg {
        width: 35px;
      }
    }

    .user {

      >.info {

        .title {
          padding: 0 5px;
          font-size: 13px;
        }

        .logout {
          padding: 5px;
        }
      }

      .ui-btn {
        margin-left: 5px;
      }
    }
  }

  .brand-lockup {

    .info {
      padding-left: 5px;

      .title {
        font-size: 14px;
      }
    }

  }

  .iac-language {
    padding: 0 5px;
  }
}

@media (max-width: 800px) {
  .iac-navigation-top__body > .nav .user .user_account .info {
    display: none;
  }
}

@media screen and (min-width: 1321px) {
  .side_bar .iac-navigation-top {
    .nav.calc_margin {
      margin-left: ~"max( 90px, calc( ( 100vw - 1820px ) / 2 + 80px) )";
    }
  }
}