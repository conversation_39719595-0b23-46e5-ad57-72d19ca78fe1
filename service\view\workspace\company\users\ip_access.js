import { Http } from '@iac/core'
import { Context } from '@iac/kernel'

class ipAccessModel {
    constructor(data) {
        this.users = [
            { id: 0, expand: false, full_name: Context.User.team_name, allowed_ips: data.allowed_company_ips || [] },
            ...data.users.map((item, index) => {
                return {
                    expand: false,
                    id: item.id,
                    full_name: item.full_name,
                    allowed_ips: item.allowed_ips || []
                }
            })]

        let allowed_ips = [];
        this.users.forEach(user => {
            user.allowed_ips = user.allowed_ips || []
            allowed_ips = allowed_ips.concat(user.allowed_ips)
        });

        this.allowed_ips = new Set(allowed_ips);
    }

    async save() {
        let params = this.users.map((item) => {
            return { id: item.id, allowed_ips: item.allowed_ips, }
        })
        let company = params.shift();
        params = {
            allowed_company_ips: company.allowed_ips,
            users: params
        };

        let { error, data } = await Http.api.rpc("company_whitelist_save", params);
        if (error)
            await Vue.Dialog.MessageBox.Error(error)
    }

    static async get() {
        let { error, data } = await Http.api.rpc("company_whitelist_get");
        return {
            error,
            data: data && new ipAccessModel(data)
        }
    }
}

var ipAccess = {
    data: function () {
        return {
            users: undefined,
            allowed_ips: undefined,
            current: -1,
            model: undefined,
            error: undefined,
        }
    },
    mounted() {
        this.$wait(async () => {
            let { error, data } = await ipAccessModel.get();
            this.model = data
            this.error = error           
            this.$emit("model", this.model)
        })
    },
    methods: {
        save() {
            this.$wait(async () => {
                await this.model.save();
            });
        },

        onEnter(user, target) {
            this.$wait(async () => {

                let ip = target.innerText.trim();
                if (!ip)
                    return;
                target.innerText = ''

                if (user.allowed_ips.includes(ip)) {
                    console.log("IN");
                    return;
                }

                user.allowed_ips.push(ip)
                if (!this.model.allowed_ips.has(ip)) {
                    this.model.allowed_ips.add(ip);
                }

                console.log(user.id, ip, true)


            })

        },
        update(user, event) {
            console.log(user.id, event.target.id, event.target.checked)
        }
    },
    template: `
        <div class='ip_access'>
            <div v-if='model' class='users'>
                <div :id='user.id' class='user' v-for='user, index in model.users'>
                    <div :class='(user.allowed_ips.length > 0 ? "title active" : "title")' v-on:click='user.expand = !user.expand' v-on:click1='current = current == index ?  -1 :  index'>
                        <icon>goszakupki</icon>
                        <div>{{user.full_name}} <span v-if='index == 0'>( {{$t('company')}} )</span></div>
                    </div>

                    <template v-if='user.expand'>
                        <div class='content'>
                            <div class='desc' v-if='user.desc'>{{user.desc}}</div>
                            <label class='ip' v-for='ip in model.allowed_ips'>
                                <span v-if='0' style='flex: 0 0 7px; height: 1px; border-top: 1px dotted #666; margin-right: 3px; '/>
                                <input type='checkbox' :id="ip" :value="ip" v-model="user.allowed_ips" @change="e=>update(user, e)" />
                                <span>{{ip}}</span>
                            </label>

                            <div class='add' >
                                <icon>edit</icon>
                                <div :placeholder='$t("enter_ip")' class='input' tabindex='1' v-on:keydown.enter.stop.prevent='(e)=>onEnter(user,e.target)' v-on:focusout='(e)=>onEnter(user,e.target)'  contenteditable='true'></div>
                            </div>
                    
                        </div>
                    </template>
                    
                </div>
            </div>
            <ui-error v-else-if='error' :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>

            <div class='actions' v-if='!$listeners.model'>
                <ui-btn type='primary' v-on:click.native='save'>{{$t("save")}}</ui-btn>
            </div>

        </div>
    `
}

Vue.Dialog("ip_access", {
    data: function(){
        return {
            model: undefined
        }
    },
    components: {
        ipAccess: ipAccess
    },
    methods: {
        onModel(model) {
            this.model =model
        },
        save() {
            this.$wait(async () => {
                await this.model.save();
            });
        },
    },
    template: `
        <div>
            <header>{{$t('ip_access')}}</header>
            <main><ip-access v-on:model='onModel'/></main>
            <footer style='background: linear-gradient(0deg, rgb(255, 255, 255), rgba(255, 255, 255, 0));
            position: sticky;
            bottom: 0px;
            margin: 0px;
            padding: 24px;
            pointer-events: none;'>
                <ui-btn type='secondary' v-on:click.native='Close'>{{$t("close")}}</ui-btn>
                <ui-btn :disabled='!model' type='primary' v-on:click.native='save'>{{$t("save")}}</ui-btn>
            </footer>
        </div>
    `
})

export default ipAccess;