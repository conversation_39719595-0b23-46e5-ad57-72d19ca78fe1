import { Language } from '@iac/core'

export default {
  name: 'ebpSearchTable',
  props: {
    searchResult: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      focusedItem: undefined
    }
  },
  methods: {
    setCurrentItem(item) {
      this.$emit('setCurrentItem', item)
    },
    getCurrentName(product) {
      let currentName = product.name[Language.local]
      if (!currentName) {
        const existedNameLang = Object.keys(product.name).find(lang => product.name[lang])
        currentName = product.name[existedNameLang] || ""
      }
      return currentName
    }
  },
  template: `
    <div class="iac--ebp-search-table"  @mouseleave="focusedItem=undefined" @keydown.enter="e=>setCurrentItem(focusedItem)">
      <div v-for="item in searchResult" @mouseover="focusedItem=item" :class="{ active: focusedItem==item }" @click="e=>setCurrentItem(item)">
        <div :title="getCurrentName(item.product)">{{getCurrentName(item.product)}}</div>
      </div>
    </div>
    `
}