import { DataSource, RefStore, Query } from '@iac/data'
import { Http, Action } from '@iac/core';
import { Context } from '@iac/kernel'
import KtruBurseProduct from '../../../../components/ktru-burse-widget'

export default {
    data() {
        return {
            BurseProductRequestDataSource: new DataSource({
                query: new Query(
                    {
                        status: {
                            group: "status",
                            label: "!",
                            type: "enum",
                            dataSource: "burse_product_request_status_private"
                        }
                    }
                ),
                store: new RefStore({
                    method: "contract_ref",
                    ref: "burse_product_request_private_registry",
                    context: context => {
                        context.actions = [
                            {
                                label: "send_to_agreement",
                                hidden: () => {
                                    return context.status != 'draft'
                                },
                                handler: async () => {
                                    const { error, data } = await Http.api.rpc('contract_action', {
                                        action: 'publish',
                                        number: context.number
                                    });
                                    if (error) {
                                        Vue.Dialog.MessageBox.Error(error)
                                    } else {
                                        this.BurseProductRequestDataSource.reload()
                                    }
                                }
                            },
                            {
                                label: "delete",
                                hidden: () => {
                                    return context.status != 'draft'
                                },
                                handler: async () => {
                                    const { error, data } = await Http.api.rpc('contract_action', {
                                        action: 'delete',
                                        number: context.number
                                    });
                                    if (error) {
                                        Vue.Dialog.MessageBox.Error(error)
                                    } else {
                                        this.BurseProductRequestDataSource.reload()
                                    }
                                }
                            },
                            {
                                label: "show",
                                hidden: () => {
                                    return !context.product
                                },
                                handler: async () => {
                                    await KtruBurseProduct.showProduct(context.product)
                                }
                            },
                            {
                                label: "agreement",
                                hidden: () => {
                                    return !(context.status == 'published' && Context.Access.policy['exchange_burse_product_request_agreement'])
                                },
                                handler: async () => {
                                    const productMergeWithDifferense = await KtruBurseProduct.mergeProduct(context.product)
                                    if (productMergeWithDifferense) {
                                        if (productMergeWithDifferense.approved === true) {
                                            delete productMergeWithDifferense.approved
                                            this.$wait(async () => {
                                                const { error, data } = await Http.api.rpc('contract_action', {
                                                    action: 'agreement',
                                                    number: context.number,
                                                    params: {
                                                        product: productMergeWithDifferense.product,
                                                        meta:productMergeWithDifferense.meta,
                                                        id: productMergeWithDifferense.id
                                                    }
                                                });
                                                if (error) {
                                                    Vue.Dialog.MessageBox.Error(error)
                                                } else {
                                                    this.BurseProductRequestDataSource.reload()
                                                }
                                            })
                                        } else if (productMergeWithDifferense.approved === false) {
                                            delete productMergeWithDifferense.approved
                                            const { error, data } = await Http.api.rpc('contract_action', {
                                                action: 'cancellation',
                                                number: context.number,
                                                params: {
                                                    product: productMergeWithDifferense.product,
                                                    id: productMergeWithDifferense.id
                                                }
                                            });
                                            if (error) {
                                                Vue.Dialog.MessageBox.Error(error)
                                            } else {
                                                this.BurseProductRequestDataSource.reload()
                                            }
                                        }
                                    }
                                }
                            },
                        ]
                        return context
                    }
                }),
                template: "template-burse_request",
                actions: [
                    {
                        label: 'add_burse_product_request',
                        handler: () => this.add_burse_product_request(),
                        hidden: !Context.Access.policy['exchange_burse_product_request_create'] || Context.Access.policy['exchange_burse_product_request_agreement']
                    },
                    {
                        label: 'burse_product_request_dashboard',
                        handler: () => KtruBurseProduct.dashboardProduct(),
                        hidden: !Context.Access.policy['exchange_burse_product_request_agreement']
                    }
                ]
            })
        }
    },
    methods: {
        async add_burse_product_request() {
            const product = await KtruBurseProduct.addProduct()
            if (product) {
                const { error, data } = await Action["contract.create"]({ product, type: 'burse_product_request' }) || {};
                if (error) {
                    Vue.Dialog.MessageBox.Error(error)
                } else {
                    this.BurseProductRequestDataSource.reload()
                }
            }
        },
    },
    template: `
    <iac-access :access='$policy.exchange_burse_product_request'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.burse_product_request')}}</li>
            </ol>
            <h1>{{$t('burse_product_request')}}</h1>
        </iac-section>
        <iac-section>
            <ui-data-view :dataSource='BurseProductRequestDataSource'/>
        </iac-section>
    </iac-access>
    `
}