import { Entity, DataSource, ArrayStore, RemoteStore, Query } from '@iac/data'
import { Language } from '@iac/core'

export default {
    data:function(){
        return {
            dataSource: new DataSource({
                request_count: true,
                store: {
                    ref: "ref_special_suppliers",
                    context:(context)=>{
                        return {
                            header: [`${Language.t("inn")}:${context.inn}`, 
                            {
                                component: {
                                    props: ["products"],
                                    computed:{
                                        count(){
                                            if(this.products)
                                                return this.products.length;
                                            return 0;
                                        }
                                    },
                                    methods: {
                                        onclick(){
                                            Vue.Dialog({
                                                props: ["products"],
                                                template: `
                                                <div>
                                                    <header>{{$t('link.products')}}</header>
                                                    <main>
                                                    <table class='table table-bordered table-striped table-hover'>
                                                    <thead>
                                                        <tr><th>{{$t("code")}}</th><th>{{$t("product_name")}}</th></tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr v-for='product in products'>
                                                            <td style='white-space: nowrap; vertical-align: top;'>{{product.product_code}}</td>
                                                            <td style='width:100%;'>
                                                                <div>{{product.product_name}}</div>
                                                                <div v-if='product.description' style='font-size: 12px; color: #777;'>{{product.description}}</div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                    </table>
                                                    </main>
                                                    <footer>
                                                        <ui-btn type='secondary' v-on:click.native='Close'>{{$t('close')}}</ui-btn>
                                                    </footer>
                                                </div>
                                            `
                                            }).Modal({
                                                products: this.products,
                                                size: "lg"
                                            });
                                        }
                                    },
                                    template: `
                                    <div v-if='!count'>{{count}} {{$t("product",{ count: count })}}</div>
                                    <a v-else href='javascript:void(0)' v-on:click='onclick'>{{count}} {{$t("product",{ count: count })}}</a>
                                    `
                                },
                                props: {
                                    products: context.meta?.products
                                },

                            }],
                            title: {text: context.name},
                            description: [
                                {label: Language.t("contacts"),text: context.contacts}
                            ]
                        }
                    }
                }
            })
        }
    },
    template: `
        <div>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('hp.registries.link3')}}</li>
                </ol>
                <h1>{{$t('hp.registries.link3')}}</h1>
            </iac-section>
            <iac-section>
                <ui-layout-group>
                    <ui-data-view :dataSource='dataSource'/>
                </ui-layout-group>
            </iac-section>
        </div>
    `
}