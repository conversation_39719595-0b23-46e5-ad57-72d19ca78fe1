import { Language } from '@iac/core'

const MemberImageComponent = {
    name: "iac-member-image",
    props: {
        value: String,
        wait: {
            type: Boolean,
            default: false
        },
        width: {
            type: Number,
            default: 100
        },
        circle: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        classes() {
            return [
                {
                    "iac-wait": this.wait
                }
            ]
        }
    },
    methods: {
        async onChange(event) {
            const toImage = file => new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => {
                    const inputImage = new Image();
                    inputImage.onload = () => {
                        resolve(inputImage)
                    }
                    inputImage.onerror = error => reject(error);
                    inputImage.src = reader.result
                };
                reader.onerror = error => reject(error);
            });
            let file = event.target.files && event.target.files[0];
            if(!file)
                return;

            try {
                let image = await toImage(file);

                if (image.naturalHeight < this.width || image.naturalWidth < this.width) {
                    throw {
                        message: Language.t('image_is_too_small')
                    }
                }

                let crop_image = await Vue.Dialog.ImageDlg.Modal({
                    image: image,
                    out_width: this.width,
                    circle: this.circle
                })

                if (crop_image)
                    this.$emit('change', crop_image)

            } catch (error) {
                Vue.Dialog.MessageBox.Error(error.message || Language.t('incorrect_file_format'))
            }
        },
        select() {
            if (this.$refs.input) {
                this.$refs.input.click()
            } else if (this.value) {
                
            }
        }
    },
    template: `
    <div class='iac-member-image'  v-bind:class="classes">
        <input ref='input' style='display: none;' type="file" accept=".png,.jpg,.gif,.jpeg,.bmp" v-on:change='onChange' />
        <div>
            <button v-if='value' class='action' v-on:click='select'><icon>photo</icon></button>
        </div>    
        <div>
            <div class='image'>
                <img v-if='value' :src='value' />
                <div v-else v-on:click='select'><icon>photo</icon></div>
            </div>
        </div> 
        <div>
            <button class='action' v-if='value' v-on:click='$emit("change", undefined)'><icon>trash</icon></button>
        </div> 

    </div>
    `
}

Vue.component('iac-member-image', MemberImageComponent);

Vue.Fields.member_image = { is: 'iac-member-image' }
