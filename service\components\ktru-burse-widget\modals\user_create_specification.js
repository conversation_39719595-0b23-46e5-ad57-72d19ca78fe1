import { Language } from '@iac/core'
import getUtils from '../utils'

export default Vue.Dialog({
  props: {
    currentItem: {
      type: Object,
      required: true
    },
    onSelect: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      ...getUtils()
    }
  },
  methods: {
    createSpecAndSendIt() {
      const spec = this.createBurseProductSpec()
      this.onSelect?.(spec)
      this.Close(spec)
    },
    createBurseProductSpec() {
      const { id, name, skp, tnved, groups, unit, master_unit, properties } = this.currentItem
      const raw = {
        id,
        name,
        skp,
        tnved,
        groups: groups.map(({ id, parent_id, name }) => ({ id, parent_id, name })),
        unit: { name: unit.name, ratio: unit.ratio },
        master_unit: { name: master_unit.name, ratio: master_unit.ratio },
        properties: properties.map(({ name, value }) => ({ name, value: value ? { name: value.name } : value }))
      }

      return {
        category: { code: "", id: "", uid: "", title: "" },
        product_id: id,
        product_uid: id,
        product_name: this.getCurrentName(this.currentItem.name),
        type: 0,
        product_properties: properties.filter(prop => prop.value).map(prop => ({
          val_numb: 0,
          val_name: this.getCurrentName(prop.value.name),
          value_id: 0,
          prop_numb: 0,
          prop_name: this.getCurrentName(prop.name),
          prop_id: 0
        })),
        unit: this.getCurrentName(unit.name),
        unit_ratio: unit.ratio,
        master_unit: this.getCurrentName(master_unit.name),
        master_unit_ratio: master_unit.ratio,
        skp,
        tnved,
        groups: groups.map(({ id, name, parent_id }) => ({ id, parent_id, name: this.getCurrentName(name) })),
        raw
      }
    },
    getCurrentName(name) {
      return name[Language.local] || name[Object.keys(name)[0]]
    },
    getSpecTitle() {
      return Language.t('specification_creation_for_code_for').replace('_____', this.getCurrentName(this.currentItem.name))
    }
  },
  async created() {
    this.prepareGroups(this.currentItem)
    this.prepareMU(this.currentItem)
    this.prepareLangs(this.currentItem, this.langs)
    this.currentItem.master_unit = this.currentItem.units.find(unit => unit.master_unit)
  },
  template: `
  <div>
    <header>{{getSpecTitle()}}</header>
    <main>
      <div class="iac--ktru__spec_creator">
        <div class="iac--ktru__spec_creator-about">
          <div>{{$t('product_name')}}</div>
          <div v-for="(group,index) in currentItem.groups" :key="index">{{getCurrentName(group.name)+' / '}}</div>
          <div>{{getCurrentName(currentItem.name)}}</div>
        </div>
        <div class="iac--ktru__spec_creator-about">
          <div>{{$t('tnved')}}</div>
          <div>{{currentItem.tnved}}</div>
        </div>
        <div class="iac--ktru__spec_creator-about">
          <div>{{$t('skp')}}</div>
          <div>{{currentItem.skp}}</div>
        </div>
        <div class="iac--ktru__spec_creator-about">
          <div>{{$t('master_unit')}}</div>
          <div>{{getCurrentName(currentItem.master_unit.name)}} ({{currentItem.master_unit.ratio}})</div>
        </div>
        <div iac--ktru__spec_creator-selectors>
          <label><span>*</span>{{$t('unit')}}</label>
            <select v-model="currentItem.unit">
            <option v-for="unit in currentItem.units" :value="unit">{{getCurrentName(unit.name)}} ({{unit.ratio}})</option>
          </select>
        </div>
        <div class="iac--ktru__spec_creator-divider"/>
        <div class="iac--ktru__spec_creator-selectors">
          <div v-for="prop in currentItem.properties">
            <label>{{getCurrentName(prop.name)}}</label>
            <select v-model="prop.value">
              <option :value="undefined">{{$t('not_selected')}}</option>
              <option v-for="val in prop.values" :value="val">{{getCurrentName(val.name)}}</option>
            </select>
          </div>
        </div>
      </div>
    </main>
    <footer>
      <ui-btn :disabled="!currentItem.unit" type='primary' @click.native="e=>createSpecAndSendIt()">{{$t('add')}}</ui-btn>
    </footer>
  </div>
    `
})