import eimzo from '@iac/eimzo'
import Context from './context'
import Settings from './settings'
import { Language } from '@iac/core'

let subscribe = eimzo.subscribe;
eimzo.subscribe = async function (...args) {
    let result = await subscribe.apply(this, [...args]);

    if (result && result.error && Context.User.id && !Context.User.team_has_sign) {
        if (await Vue.Dialog.MessageBox.Question(Language.t('question_attention_there_is_no_esignature_for_your_company_do_you_want_to_continue_request_without_signature'), result.error.message) == Vue.Dialog.MessageBox.Result.Yes) {
            return {
                data: "ignore_eimzo"
            }
        } else {
            return;
        }
    }

    if (result && result.error && Settings.ignore_eimzo) {
        if (await Vue.Dialog.MessageBox.Question("Внимание! Включен тестовый режим 'ignore_eimzo'. Продолжить запрос с игнорированием ошибки?", result.error.message) == Vue.Dialog.MessageBox.Result.Yes) {
            return {
                data: "ignore_eimzo"
            }
        } else {
            return;
        }
    }

    return result;
}

export default eimzo;