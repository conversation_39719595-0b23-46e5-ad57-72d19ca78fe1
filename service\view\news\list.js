import { DataSource, RefStore, RemoteStore, Query } from '@iac/data';

export default {
  data() {
    return {
      newsList: new DataSource({
        limit: 9,
        query: new Query({
          select: {
            hidden: true,
            value: [
              'id',
              'title',
              'digest_txt',
              'digest_pic',
              'category',
              'category_id',
              'created_at',
            ],
          },
          category_id: {
            type: 'tag',
            group: 'filter_by_tags',
            label: '!filter_by_tags',
            dataSource: new DataSource({
              store: new RefStore({
                ref: 'ref_tag_cloud',
              }),
            }),
          },
        }),
        store: new RemoteStore({
          method: 'ref_get_latest_digest',
        }),
      }),
    };
  },
  methods: {
    updateNewsQuery(category_id) {
      this.newsList.query.set({ category_id });
    },
  },
  template: `
    <div class='news'>
      <iac-section type='header'>
        <ol class='breadcrumb'>
          <li><router-link to='/'>{{ $t('home') }}</router-link></li>
          <li>{{ $t('link.news') }}</li>
        </ol>
        <div class='title'>
          <h1>{{ $t('link.news') }}</h1>
        </div>
      </iac-section>
      <iac-section>
        <div class='news-list'>
          <ui-data-list :dataSource='newsList'>
            <template slot='items' slot-scope='props'>
              <ui-list :dataSource='newsList'>
                <template slot='items' slot-scope='props'>
                  <div v-for='item in props.items' :key='item.id'
                    class='pr-12 pl-12 mb-24 iac-col-lg-4 iac-col-md-6'>
                    <widget-news-card :item='item' @onTag='updateNewsQuery' />
                  </div>
                </template>
              </ui-list>
            </template>
          </ui-data-list>
        </div>  
      </iac-section>
    </div>
  `,
};
