export default {
  props: ['chats', 'current'],
  template: `
    <div class='chat-groups tender-chat__left'>
      
    <a v-for='chat in chats' :key='chat.id'
        @click.prevent='$emit("switch-chat", chat.id)' class='chat-groups__item' :class='{"chat-groups__item--active": chat.id === current}'>
        <div class='chat-groups__left'>
          <h3 class='chat-groups__title'> {{ chat.title }}</h3>
          <div class='chat-groups__message clamp_3' v-html='chat.last_message && chat.last_message.text_without_tags'></div>
        </div>
        <div class='chat-groups__right'>
          <iac-date v-if='chat.last_message' :date='chat.last_message.sent_at' class='chat-groups__date'></iac-date>
          <div class='chat-groups__unread' v-show='chat.unread_messages_count'>{{ chat.unread_messages_count }}</div>
        </div>
      </a>
    </div>
  `
}