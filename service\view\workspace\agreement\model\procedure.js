import { Http, Language } from '@iac/core'
import { ecp } from '@iac/kernel'
import AgreementEntity from './entity';

export default class Procedure extends AgreementEntity {
    constructor(context) {
        super(context)

        this.rights = context.rights != undefined ? context.rights : 7;

        this.status = context.status;
        this.title = context.title;
        this.anno = context.anno;
        this.start_at = context.start_at;
        this.end_at = context.end_at;

        this.object_type = context.object_type;
        this.object_id = context.object_id;
    }

    props() {
        return {
            title: {
                label: "approval_name",
                readonly: function () {
                    return this.model.status != "created"
                }
            },
            /*start_at: {
                label: "date_from",
                type: "datetime",
                group: '!date-',
                readonly: function () {
                    return this.model.status != "created"
                }
            },
            end_at: {
                label: "date_to",
                type: "datetime",
                group: '!date-'
            },*/
            anno: {
                label: "test_task_desc",
                type: "text",
                readonly: function () {
                    return this.model.status != "created"
                }
            },
        }
    }

    async vote(params) {
        let result = await ecp.subscribe(this.anno || "аргумент #0", {
            title: this.title,
            message: this.anno
        });
        if (!result)
            return;
        let { error: error_ecp, data: pkcs7B64 } = result;
        if (error_ecp) {
            return { error: error_ecp }
        }
        params.pkcs7B64 = pkcs7B64;
        params.procedure_id = this.id
        return await Http.api.rpc("put_agreement_vote", params);
    }

    async close_procedure(){
        return await Http.api.rpc("force_close_procedure", {
            procedure_id: this.id,
            stage_id: this.stage && this.stage.id
        });
    }

    async subscribe() {
        let { error: calcError, data: calc } = await Http.api.rpc("calc_stage_protocol", {
            procedure_id: this.id,
            stage_id: this.stage && this.stage.id
        });

        if (calcError)
            return { error: calcError }

        let result = await ecp.subscribe(JSON.stringify({
            digest_id: calc.digest_id,
            data_size: calc.data_size,
            data_hash: calc.data_hash
        }));

        if (!result)
            return;

        let { error: error_ecp, data: pkcs7B64 } = result;
        if (error_ecp) {
            return { error: error_ecp }
        }


        return await Http.api.rpc("sign_protocol", {
            pkcs7B64: pkcs7B64,
            procedure_id: this.id,
            stage_id: this.stage && this.stage.id
        });
    }


    async save() {
        const { error, data } = await Http.api.rpc("update_agreement_procedure", {
            id: this.id,
            title: this.title || '',
            anno: this.anno || '',
            start_at: this.start_at,
            end_at: this.end_at
        });
        if (error) {
            if (!this.setError(error)) {
                await Vue.Dialog.MessageBox.Error(error);
            }
        }
        return { error, data };
    }

    async start() {
        let { error, data } = await Http.api.rpc("start_agreement_procedure", {
            id: this.id,
        });
        if (error) {
            if (!this.setError(error)) {
                await Vue.Dialog.MessageBox.Error(error);
            }
        } else {
            this.status = 'process'
        }
        return { error, data };
    }

    static async get(id) {
        let { error, data } = await Http.api.rpc("get_agreement_procedure_info", { id: id });
        let agreement;
        if (!error) {
            let response = await AgreementEntity.stage(id)
            if (response.error)
                return {
                    error: response.error
                }
            data.stage = response.data;
            agreement = new Procedure(data);
        }

        let { error: votesError, data: votesData = [] } = await Http.api.rpc("get_agreement_votes", { procedure_id: id });
        if (!votesError && votesData.length) {
            agreement.votes = votesData
            agreement.votes.forEach(vote => {
                vote.result = Language.t('did_not_vote')
                if (vote.created_at) {
                    vote.result = Language.t({ null: 'voted_abstained', true: 'voted_positively', false: 'voted_negatively' }[vote.vote])
                    vote.created_at = new Date(vote.created_at)
                }
                vote.procRole=Language.t(vote.is_chairman?'comission_chairperson':'comission_member')
            });
        }

        return {
            error,
            data: agreement
        }
    }
}