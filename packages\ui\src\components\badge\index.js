export var Badge = {
    name: "ui-badge",
    props: ["type", "model", "groups"],
    data: function(){
        return {
            _model: undefined
        }
    },
    watch: {
        "model": {
            immediate: true,
            async handler(contract, oldVal) {
                this.sumObjectsByKey();
            }
        }
    },
    methods: {
        sumObjectsByKey(){
            if(this.groups && this.model){
                this._model = this.groups.reduce((a, group) => {
                    for (let type in this.model[group]) {
                        if (this.model[group].hasOwnProperty(type))
                            a[type] = (a[type] || 0) + this.model[group][type];
                    }
                    return a;
                },{});
            }else{
                this._model = this.model
            }
        }
    },

    template: `
        <span v-if='model' class='ui-badge-group'>
            <label class='ui-badge' v-bind:class="[type]" :key='type' v-for='value,type in _model'>{{value}}</label>
        </span>
        <label v-else class='ui-badge' v-bind:class="[{[type]: type}]"><slot/></label>
    `
}