import { Assets } from '@iac/core';

export default {
    data: function(){
        return {
            loading:true
         }
    },
    async mounted() {
        await Assets.script('iac.doc.js',true);
        await Assets.script('iac.edit.js',true);
        await Assets.css('https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/vs.min.css');
        this.loading=false
    },
    template: `
        <div>
            <edit-component v-if="!loading"></edit-component>            
        </div>
    `
}