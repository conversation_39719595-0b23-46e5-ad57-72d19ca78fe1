import { Http } from '@iac/core';
import { DataSource, RemoteStore, Query } from '@iac/data';
import Vue from 'vue';

let SelectUserDlg = Vue.Dialog({
  data() {
    return {
      dataSource: new DataSource({
        query: new Query({
          voters: {
            value: true,
            hidden: true,
          }
        }),
        store: new RemoteStore({
          method: 'get_company_users'
        }),
      }),
    };
  },
  methods: {
    roles(item) {
      if (!item.roles) {
        return;
      }
      return item.roles.map((role) => {
        return '- ' + role.name;
      }).join('\n');
    },
  },
  template: `
    <div>
      <header>{{ $t('choose_user') }}</header>
      <main>
        <ui-data-list :dataSource='dataSource' check>
          <template slot='template' slot-scope='props'>
            <div :class='{"text-primary": props.item.system_company}'
              style='flex: 1 1 auto;'>
              <b>{{ props.item.name }}</b>
              <div style='white-space: pre-line; font-size: 12px;'>{{ roles(props.item) }}</div>
            </div>
          </template>
        </ui-data-list>
      </main>
      <footer>
        <ui-btn type='secondary' @click.native='Close()'>{{ $t('close') }}</ui-btn>
        <ui-btn type='primary' @click.native='Close(dataSource.checkedItems)'>{{ $t('add') }}</ui-btn>
      </footer>
    </div>
  `,
});

export default class Stage {
  constructor(context) {
    this.context = context;
    this.procedure_id = context.procedure_id;
    this.id = context.id;

    this.users = new DataSource({
      query: new Query({
        stage_id: this.id,
      }),
      store: new RemoteStore({
        method: 'get_agreement_users',
      }),
    });

    this.actions = [
      {
        label: 'add_users',
        handler: async () => {
          let users = await SelectUserDlg.Modal();
          if (!users) {
            return;
          }
          let response = await Http.api.rpc('add_agreement_user', {
            user_ids: users,
          });
          if (response.error) {
            await Vue.Dialog.MessageBox.Error(response.error);
            return {
              error: response.error,
            };
          }

          response = await Http.api.rpc('add_agreement_user', {
            procedure_id: this.procedure_id,
            user_ids: users,
          });
          if (response.error) {
            await Vue.Dialog.MessageBox.Error(response.error);
            return {
              error: response.error,
            };
          }

          response = await Http.api.rpc('add_agreement_user', {
            stage_id: this.id,
            user_ids: users,
          });
          if (response.error) {
            await Vue.Dialog.MessageBox.Error(response.error);
            return {
              error: response.error,
            };
          }
          this.users.reload();
        },
      },
      {
        label: 'delete_selected_users',
        handler: async () => {
          await Http.api.rpc('delete_agreement_user', {
            stage_id: this.id,
            user_ids: this.users.checkedItems,
          });
          this.users.reload();
        },
        hidden: () => {
          return !this.users.checkedItems || this.users.checkedItems.length <= 0;
        }
      },
    ];
  }

  async set_chairman(user) {

    let { error } = await Http.api.rpc('set_agreement_stage_chairman', {
      procedure_id: this.procedure_id,
      stage_id: this.id,
      chairman_id: user.id,
    });

    if (error) {
      return await Vue.Dialog.MessageBox.Error(error);
    }

    this.users.reload();
  }
}
