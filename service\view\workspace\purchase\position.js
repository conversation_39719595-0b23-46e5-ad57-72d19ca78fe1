import { DataSource, RemoteStore, RefStore, Query, Entity, ArrayStore } from "@iac/data";
import { Http, Language } from '@iac/core'
import { Context, Develop, Settings } from '@iac/kernel'
import Item from './item'

export default class Position extends Entity {
    constructor(context = {}) {
        super(context);     

        this.id = context.id;
        this.reference_id = context.reference_id;
        this.status = context.status || "draft";
        this.proc_id = context.proc_id;
        this.proc_type = context.proc_type;
        this.year = context.year;
        this.month_begin = context.month_begin;
        this.month_end = context.month_end;
        this.type = "project"; // context.type;
        this.name = context.name;
        this.expense_item_code = undefined; // context.expense_item_code;
        this.category_id = undefined; // context.category_id;
        this.currency = context.currency || Settings._default_currency;

        this.items = new DataSource({
            query: new Query({
                positions_id: this.id
            }),
            store: new RefStore({
                key: 'id',
                method: 'ref_schedule',
                ref: 'schedule_goods',
                context: (context) => {

                    context.product_properties = context.product_properties || []//  [{"val_name":"высший","prop_name":"сорт"},{"val_name":"первая","prop_name":"категория"},{"val_name":"кг","prop_name":"Единица измерения"}]
                    context.actions = [
                        {
                            label: 'delete',
                            icon: 'delete',
                            hidden: () => {
                                return this.status != 'draft' || this.proc_id || !Context.Access.policy["schedule_create"]
                            },
                            handler: async () => {
                                if (await Vue.Dialog.MessageBox.Question(Language.t('question_delete_product')) != Vue.Dialog.MessageBox.Result.Yes)
                                    return;
                                let { error, data } = await Http.api.rpc("ref_schedule", {
                                    ref: "schedule_goods",
                                    op: "delete",
                                    filters: {
                                        id: context.id
                                    }
                                })

                                if (error) {
                                    Vue.Dialog.MessageBox.Error(error)
                                } else {
                                    this.items.reload();
                                }
                            }
                        },
                        {
                            label: "Тонкая настройка",
                            hidden: ()=>{
                                return !Context.Access.policy.system_entity_editor
                            },
                            handler: async () => {
                                let { data, error } = await Http.api.rpc("debug_panel", {
                                    action: "get_entity",
                                    id: context.id,
                                    _: "%{table: \"goods\", service: :common, module: MsPurchaseList.Repo}"
                                })
                                if (error && error.code != "AbortError") {
                                    Vue.Dialog.MessageBox.Error(error);
                                }
                            }
                        }
                    ]

                    let item = new Item(context, this);
                    return item;
                },
                inject: (items) => {
                    if (!items)
                        return;

                    let product_ids = {};
                    items.forEach((item) => {

                        if (item.product_name)
                            return;
                        //item.product_name = item.product_id
                        product_ids[item.product_id] = product_ids[item.product_id] || {
                            items: []
                        };
                        product_ids[item.product_id].items.push(item)
                    })

                    DataSource.get("ref_products").byKeys(Object.keys(product_ids)).then((products) => {
                        products.forEach((product_item) => {
                            let items = product_ids[product_item.id] && product_ids[product_item.id].items;
                            if (items) {
                                items.forEach((item) => {
                                    item.product_name = product_item.name;
                                })
                            }
                        })
                    })

                    return items;
                }
            }),
            actions: [
                {
                    label: 'add_product',
                    hidden: () => {

                        if (this.status != 'draft')
                            return true;

                        if (!Context.Access.policy["schedule_create"])
                            return true;

                        let type = this.type.id || this.type;
                        if (this.proc_id)
                            return true;

                        if ((this.items.state & DataSource.STATE_ERROR) != 0)
                            return true;

                        if ((this.items.state & DataSource.STATE_REQUEST) != 0)
                            return true;

                        if (type == 'position' && this.items.items && this.items.items.length >= 1)
                            return true;

                        return false;
                    },
                    handler: async () => {
                        let product;


                        product = await Vue.Dialog.SelectProduct.Modal({
                            size: "right"
                        })
                        if (!product)
                            return;


                        let position = this;
                        let result = await Vue.Dialog({
                            data: function () {
                                return {
                                    model: new Item(product, position)
                                }
                            },
                            methods: {
                                save() {
                                    this.$wait(async () => {
                                        let { error, data } = await this.model.save();
                                        if (!error) {
                                            this.Close(data);
                                        }
                                    })
                                },
                                properties(){
                                    Vue.Dialog({
                                        props: ['model'],
                                        template: `
                                          <div>
                                            <header>{{model.product_name || model.name}}</header>
                                            <main>
                                              <iac-layout-static :value='model.product_properties' />
                                            </main>
                                            <footer>
                                              <ui-btn type='secondary' v-on:click.native='Close'>{{$t('Close')}}</ui-btn>
                                            </footer>
                                          </div>
                                        `
                                      }).Modal({
                                        model: this.model
                                      })
                                }
                            },
                            template: `
                            <div>
                                <header>{{$t('add_product')}}</header>
                                <main>
                                <ui-data-view-item :model='model' style='margin: -8px -16px;'>
                                    <template slot='header'>
                                        <div><span v-if='model.product_name'>{{model.product_id}}</span></div>
                                        <div>&nbsp;<span v-if='model.price && model.amount'>{{$t('summa')}}: <iac-number :value='model.price*model.amount' delimiter=' ' part='2' /> {{model.currency && model.currency.id}}</span></div>
                                    </template>
                                    <template slot='title'>

                                        <span v-if='model.product_name'>
                                            <a v-if='model.product_properties' href='' v-on:click.prevent='properties'>{{model.product_name}}</a>
                                            <span v-else>{{model.product_name}}</span>
                                        </span>

                                        <ui-field v-else :model='model.properties.product_id'/>

                                    </template> 
                                    <template slot='description' v-if='0'>
                                        <div v-if='prop' v-for='prop in model.product_properties'>
                                            <label>{{prop.prop_name}}:</label>
                                            <span>{{prop.val_name}}</span>
                                        </div>
                                    </template>
                                    <template slot='description'>
                                        <ui-layout style='border: none; padding: 0' :fields='model.fields'/>
                                    </template>
                                </ui-data-view-item>
                                </main>
                                <footer>
                                    <ui-btn type='secondary' @click.native='Close()'>{{$t('cancel')}}</ui-btn>
                                    <ui-btn type='primary' v-on:click.native='save'>{{$t('create')}}</ui-btn>
                                </footer>
                            </div>
                            `
                        }).Modal({
                            size: "lg"
                        })

                        if (result) {
                            this.items.reload();
                        }
                    }
                }
            ],
            template: {
                props: ["model"],
                methods: {
                    properties(){
                        Vue.Dialog({
                            props: ['model'],
                            template: `
                              <div>
                                <header>{{model.product_name || model.name}}</header>
                                <main>
                                  <iac-layout-static :value='model.product_properties' />
                                </main>
                                <footer>
                                  <ui-btn type='secondary' v-on:click.native='Close'>{{$t('Close')}}</ui-btn>
                                </footer>
                              </div>
                            `
                          }).Modal({
                            model: this.model
                          })
                    }
                },
                template: `
                    <ui-data-view-item class='purchase_item' :model='model'>
                        <template slot='header'>
                            <div>{{model.product_id}}</div>
                            <div>{{$t('summa')}}: <iac-number :value='model.price*model.amount' delimiter=' ' part='2' /> {{model.currency && model.currency.id}}</div>
                        </template> 
                        <template slot='title'>
                            <a v-if='model.product_properties' href='' v-on:click.prevent='properties'>{{model.product_name}}</a>
                            <span v-else>{{model.product_name}}</span>
                        </template>
                        <template slot='props'>
                            <ui-layout :fields='model.fields'/>
                        </template>
                        <template slot='description' v-if='0'>
                            <div v-if='prop' v-for='prop in model.product_properties'>
                                <label>{{prop.prop_name}}:</label>
                                <span>{{prop.val_name}}</span>
                            </div>
                        </template>
                    </ui-data-view-item>
                `
            }
        })
    }

    isType(types) {
        if (typeof types == 'string')
            types = [types];
        if (this.type && this.type.exp && this.type.exp.value) {
            for (let type of types) {
                if (this.type.exp.value == type) {
                    return false;
                }
            }
        }
        return true;
    }

    validate() {
        // this.setError(undefined, '', true);

        let month_begin = this.month_begin.id
        let month_end = this.month_end.id

        if (month_begin > month_end) {
            this.properties.month_begin.status = {
                type: 'error',
                message: Language.t("purchase.month_begin.error")
            }
            this.properties.month_end.status = {
                type: 'error',
                message: Language.t("purchase.month_end.error")
            }
            return false;
        } else {
            this.properties.month_begin.status = this.properties.month_end.status = undefined
        }
        return true;
    }

    async onChangeProperty(property) {
        if (!this.id)
            return;

        if ((property.name == 'month_begin' || property.name == 'month_end') && !this.validate()) {
            return;
        }

        let { error, data } = await Http.api.rpc("ref_schedule", {
            ref: "schedule_position",
            op: "update",
            filters: {
                id: this.id
            },
            data: {
                [property.name]: property.value !== undefined ? property.value : null
            }
        })

        if (error && error.data) {
            error.data.forEach(error => {
                if (error.name == property.name) {
                    property.property.status = {
                        type: 'error',
                        message: error.message,
                        data: error.data
                    }
                }
            });
        }

    }

    props() {
        return {
            status: {
                label: '-status',
                type: 'static',
                attr: {
                    lng: true
                },
                hidden: () => { return !this.id }
            },
            procedure: {
                label: '-procedure',
                type: "widget",
                widget: {
                    name: {
                        props: ['proc_id', 'proc_type'],
                        computed: {
                            procedure_url() {
                                if(this.proc_type == 'custom_contract'){
                                    return `/workspace/contract/${this.proc_id}.0.0/core` 
                                }
                                return `/procedure/${this.proc_id}/core`
                            }
                        },
                        template: `
                            <div><router-link :to='procedure_url'>{{$t(proc_type)}} № {{proc_id}}</router-link></div>
                        `
                    },
                    props: {
                        proc_id: this.proc_id,
                        proc_type: this.proc_type,
                    }
                },
                hidden: () => {
                    return !this.proc_id
                }
            },
            name: {
                label: 'position_name',
                required: true,
                readonly: () => {
                    return this.status != 'draft' || this.proc_id || !Context.Access.policy["schedule_create"]
                },
                bind: {
                    suffix: `((name || '').length)+'/450'`
                },
                attr: {
                    predicate: '^.{0,450}$',
                    react: true,
                },
            },
            year: {
                group: '!top-',
                type: "entity",
                dataSource: DataSource.get([
                    ...(((this.status != 'draft' || this.proc_id || !Context.Access.policy["schedule_create"]) && [2022,2023,2024]) || []), 
                    2025, 2026, 2027, 2028, 2029
                ]),
                required: true,
                readonly: () => {
                    return this.status != 'draft' || this.proc_id || !Context.Access.policy["schedule_create"]
                }
            },
            month_begin: {
                label: 'from',
                group: '!top-/<month>',
                type: "entity",
                dataSource: DataSource.get("ref_months"),
                required: true,
                readonly: () => {
                    return this.status != 'draft' || this.proc_id || !Context.Access.policy["schedule_create"]
                }
            },
            month_end: {
                label: 'to',
                group: '!top-/<!month>',
                type: "entity",
                dataSource: DataSource.get("ref_months"),
                required: true,
                readonly: () => {
                    return this.status != 'draft' || this.proc_id || !Context.Access.policy["schedule_create"]
                }
            },
            currency: {
                label: 'currency',
                type: "entity",
                dataSource: 'ref_currency',
                group: '!top-',
                required: true,
                //value: Settings._default_currency,
                readonly: () => {
                    return  !Settings.purchase_list._select_currency || !Context.Access.policy["schedule_create"]
                }
            },
            type: {
                /*type: "entity",
                group: '!type-',
                dataSource: [
                    { id: 'position', name: 'type.position' },
                    { id: 'project', name: 'type.project' },
                    { id: 'category', name: 'type.category' },
                ],
                required: true,
                readonly: () => {
                    return this.id || !Context.Access.policy["schedule_create"]
                },*/
                type: "hidden"
            },
            expense_item_code: {
                group: '!type-',
                type: "entity",
                dataSource: "ref_expense_item",
                hidden: () => {
                    return true;// this.isType(['project']);
                },
                required: false,
                readonly: () => {
                    return this.proc_id || !Context.Access.policy["schedule_create"]
                }
            },
            category_id: {
                group: '!type-',
                type: "entity",
                dataSource: "ref_categories",
                hidden: () => {
                    return true;// this.isType('category');
                },
                readonly: () => {
                    return this.id || !Context.Access.policy["schedule_create"]
                },
                required: true
            },
        }
    }

    async save() {

        let fields = this.fields.filter((field) => {
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).reduce((prev, curr) => {
            if (curr.readonly && curr.readonly() && curr.name != 'currency')
                return prev

            prev[curr.name] = curr.value && curr.value.exp ? curr.value.exp.value : curr.value

            return prev
        }, {})

        let { error, data } = await Http.api.rpc("ref_schedule", {
            ref: "schedule_position",
            op: "create",
            data: { ...fields, reference_id: this.reference_id }
        })

        if (error) {
            this.setError(error);
        }
        return { error, data }
    }

    static async get(id) {
        let { error, data } = await Http.api.rpc("ref_schedule", {
            ref: "schedule_position",
            op: "read",
            filters: {
                id: id
            }
        })
        if (error)
            return { error }
        if (data && Array.isArray(data))
            data = data[0];

        if (data) {
            data = new Position(data)
        } else {
            return {
                error: {
                    status: "404",
                    message: "PositionNotFound"
                }
            }
        }

        return { error, data }
    }
}