export var LayoutGroup = {
    name: "ui-layout-group",
    props: {
        type: String,
        icon: String,
        label: String,
        actions: {
            type: Object
        },
        actions_buttons: {
            type: Boolean,
            default: false
        },
        showLabel: {
            type: Boolean,
            default: true
        },
        horizontal: Boolean,
        expanded: {
            type: Boolean,
            default: undefined
        },
        active: {
            type: Boolean,
            default: false
        },
        wait: {
            type: Boolean,
            default: false       
        },
        status: {
            type: Object
        },
        toolbar: {
            type: Object
        }
    },
    computed: {
        collapsed() {
            return this.label && this.expanded != undefined
        },
        classes() {
            return [
                `horizontal_${this.horizontal}`,
                {
                    horizontal: this.horizontal,
                    collapsed: this.collapsed,
                    expanded: this.expanded,
                    "iac-wait": this.wait
                }]
        },
        contentComponent() {
            let show = true;
            if (this.label && this.expanded != undefined)
                show = this.expanded;
            if (show) {
                return 'group-content'
            }
        }
    },
    components: {
        groupContent: {
            template: `<div><slot/></div>`
        }
    },
    methods: {
        onLabel(){
            if(this.collapsed){
                this.expanded = !this.expanded
                this.$emit("expanded",this.expanded);
            }
        }
    },
    template: `<div class='ui-layout-group' v-bind:class="classes">
            <div class='label' v-if='label && showLabel' v-on:click='onLabel'>
                <div class='title'>{{$t(label)}}</div>
                <ui-action :buttons='actions_buttons' :actions='actions'/>
            </div>
            <transition name='fade'>
                <keep-alive>
                    <component :is='contentComponent' class='content'>
                        <slot/>
                    </component>
                </keep-alive>
            </transition>
    </div>`
}