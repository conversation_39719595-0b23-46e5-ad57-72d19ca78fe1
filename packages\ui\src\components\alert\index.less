.ui-alert {
  padding: @alert-padding;
  margin-bottom: @line-height-computed;
  border: 1px solid transparent;
  border-radius: @alert-border-radius;

  // Headings for larger alerts
  h4 {
    margin-top: 0;
    color: inherit; // Specified for the h4 to prevent conflicts of changing @headings-color
  }

  // Provide class for links that match alerts
  .alert-link {
    font-weight: @alert-link-font-weight;
  }

  // Improve alignment and spacing of inner content
  > p,
  > ul {
    margin-bottom: 0;
  }

  > p + p {
    margin-top: 5px;
  }
}

// Alternate styles
//
// Generate contextual modifier classes for colorizing the alert.

.ui-alert-success {
  .alert-variant(@alert-success-bg; @alert-success-border; @alert-success-text);
}

.ui-alert-info {
  .alert-variant(@alert-info-bg; @alert-info-border; @alert-info-text);
}

.ui-alert-warning {
  .alert-variant(@alert-warning-bg; @alert-warning-border; @alert-warning-text);
}

.ui-alert-danger {
  .alert-variant(@alert-danger-bg; @alert-danger-border; @alert-danger-text);
  .ui-btn-danger {
    &:hover{
      text-decoration: underline
    }
  }
}

.text_success {
  color: @alert-success-text!important;
}

.text_info {
  color: @alert-info-text!important;
}

.text_warning {
  color: @alert-warning-text!important;
}

.text_danger{
  color: @alert-danger-text!important;
}

.text_success,
.text_info,
.text_warning,
.text_danger {

  &::marker {
    color: inherit !important;
  }
}