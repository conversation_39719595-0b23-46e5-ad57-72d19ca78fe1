import { Http, Language } from "@iac/core"
import { DataSource, RefStore, Query, ArrayStore } from "@iac/data"

export default [
    {
        path: '/purchase',
        component: {
            data: function () {
                return {
                    dataSourcePgz: new DataSource({
                        template: "template-pgz",
                        query: {
                            plan_date_from_gte: {
                                group: "pgz.date_start",
                                type: "date",
                                label: "!",
                                prefix: this.$t("from"),
                                has_del: true,
                                bind: {
                                    status: `date_start_error && {"type":"error"}`
                                },
                            },
                            plan_date_from_lte: {
                                group: "pgz.date_start",
                                type: "date",
                                label: "!",
                                prefix: this.$t("to"),
                                has_del: true,
                                bind: {
                                    status: `date_start_error && {"type":"error"}`
                                },
                            },
                            plan_date_to_gte: {
                                group: "pgz.date_end",
                                type: "date",
                                label: "!",
                                prefix: this.$t("from"),
                                has_del: true,
                                bind: {
                                    status: `date_end_error && {"type":"error"}`
                                },
                            },
                            plan_date_to_lte: {
                                group: "pgz.date_end",
                                type: "date",
                                label: "!",
                                prefix: this.$t("to"),
                                has_del: true,
                                bind: {
                                    status: `date_end_error && {"type":"error"}`
                                },
                            },
                            date_start_error: {
                                sync: false,
                                group: "pgz.date_start",
                                type: "model",
                                label: "!",
                                bind: {
                                    value: "plan_date_from_gte > plan_date_from_lte",
                                    status: `date_start_error && {"type":"error","message":"${this.$t('to_from_error')}"}`
                                },
                            },
                            date_end_error: {
                                sync: false,
                                group: "pgz.date_end",
                                type: "model",
                                label: "!",
                                bind: {
                                    value: "plan_date_to_gte > plan_date_to_lte",
                                    status: `date_end_error && {"type":"error","message":"${this.$t('to_from_error')}"}`
                                },
                            },
                            company_id: {
                                group: "company",
                                label: "!company",
                                type: "entity",
                                has_del: true,
                                dataSource: {
                                    displayExp: "title",
                                    search: true,
                                    store: {
                                        method: "company_ref",
                                        ref: "companies"
                                    }
                                }
                            }
                            
                        },
                        store: {
                            method: 'ref_public_schedule',
                            ref: 'schedule_items_pgz',
                            injectQuery: params => {
                                params.filters = params.filters || {};
                                params.filters.date_start_error = undefined;
                                params.filters.date_end_error = undefined;
                                return params;
                            },
                        },
                    }),
                    source: new DataSource({
                        request_count: true,
                        query: new Query({
                            year: {
                                type: "entity",
                                group: "year",
                                label: "!current_year",
                                dataSource: new DataSource({
                                    store: new ArrayStore({
                                        keyType: 'number',
                                        data: async () => {
                                            let { error, data } = await Http.api.rpc("ref_public_schedule", {
                                                'ref': 'schedule_position',
                                                'op': 'get_years'
                                            })
                                            if (!error && data && Array.isArray(data)) {
                                                return data.sort().map((year) => {
                                                    return {
                                                        id: year,
                                                        name: year,
                                                    }
                                                })
                                            }
                                            return []
                                        }
                                    })
                                }),
                                has_del: true,
                            },
                            month: {
                                type: "entity",
                                group: "month",
                                label: "!select_month",
                                dataSource: DataSource.get("ref_months"),
                                has_del: true,
                                hidden: true,
                            },
                            area_path: {
                                group: "area",
                                label: "!area",
                                type: "enum-tree",
                                dataSource: "ref_uz_region_lv4",
                                //multiple: true,
                                hidden: true,
                            },
                            //TODO: переговорить с Костей на тему - вытереть ли совсем или придумать как сделать такой же для КТРУ
                            product_id: {
                                group: 'product_id',
                                type: 'entity',
                                label: '!choose_product',
                                has_del: true,
                                dataSource: new DataSource({
                                    valueExp: 'id',
                                    displayExp: "product_name",
                                    search: true,
                                    store: new RefStore({
                                        method: "ref_public_schedule",
                                        ref: "schedule_goods_all",
                                    })
                                }),
                                multiple: true,
                            },
                            company_id: {
                                group: "company",
                                label: "!company",
                                type: "entity",
                                multiple: true,
                                has_del: true,
                                dataSource: {
                                    search: true,
                                    displayExp: "title",
                                    valueExp: "id",
                                    store: {
                                        method: "company_ref",
                                        ref: "companies",
                                        injectQuery: params => {
                                            params.filters = params.filters || {}
                                            params.filters.role = "buyer";
                                            return params
                                        },
                                    }
                                },
                            },
                            proc_id: {
                                group: "procedure_existing",
                                label: "!procedure_existing",
                                type: "entity",
                                has_del: true,
                                dataSource: [{ id: false, name: Language.t("positions_without_procedures") }, { id: true, name: Language.t("positions_with_procedures") }],
                            },
                        }),
                        store: new RefStore({
                            method: "ref_public_schedule",
                            ref: "schedule_position",
                            context: (context) => {
                                context.public = true;
                                //context.id = undefined;
                                return context;
                            }
                        }),
                        template: "template-purchase",
                    })
                }
            },
            template: `
                <div class='purchase-page'>
                <iac-section type='header'>
                    <ol class='breadcrumb'>
                        <li><router-link to='/'>{{$t('home')}}</router-link></li>
                        <li>{{$t('hp.registries.name7')}}</li>
                    </ol>
                    <h1>{{$t('hp.registries.name7')}} </h1>
                </iac-section>
                <iac-section>

                <ui-layout-tab>

                <ui-layout-group label='pgz.announced'>
                    <ui-data-view :dataSource='source'/>
                    </ui-layout-group>

                <ui-layout-group v-if="$settings.purchase_list && $settings.purchase_list._approved" label='pgz.approved'>
                <a href='https://hayotbirja.uz/files/2505/17530fa1-8d0c-4a7b-9331-3b1f24edc212.xlsx?ololo=123' style="margin-bottom: 10px;">{{$t('purchase_ref1')}}</a>
                <ui-data-view :dataSource='dataSourcePgz' />  
                </ui-layout-group>
                </ui-layout-tab>
                
                </iac-section>
                </div>
            `
        },
    },
]