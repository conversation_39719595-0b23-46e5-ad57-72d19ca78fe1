var host = "test.xt-xarid.uz:8443";

var get_token = async ()=>{
    try{
        let response = await fetch(`https://api.${host}/auth`, {
            method: 'POST',
            body: JSON.stringify({  
                method: "token",
                params:{
                    client_id: "af36f6cbc",
                    grant_type: "debug",
                    user_id: 1        
                }
            }),
            headers: {
                'Content-Type': 'application/json;charset=utf-8',
            }, 
        });
    
        return await response.json()
    }catch(e){
        console.log("E",e)
    }
    return {result: {}}
}


var set = async(data)=>{
    let response;
    response = await get_token();
    if(!response?.result?.access_token){
        console.error("Ошибка получения токена:")
        return;
    }
    response = await fetch(`https://api.${host}/rpc`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json;charset=utf-8',
            'authorization': `Bear<PERSON> ${response?.result?.access_token}`
        },
        body: JSON.stringify({  
            id: 1,
            jsonrpc: "2.0",
            method: "backend_storage_set",
            params:{
                scope: 'global',
                key: "exchange_schedule",
                data: data      
            }
        }),
    })

    console.log(await response.json());

}

set({
    localization: {
        "ru-RU": {
            group_1: "Продовольственная продукция",
            group_2: "Сельскохозяйственная продукция",
            group_3: "Строительные материалы и металлы",
            group_4: "Горюче-смазочные материалы",
            group_5: "Химическая промышленность",
        },
        "en-US": {
            group_1: "Food products",
            group_2: "Agricultural products",
            group_3: "Construction materials and metals",
            group_4: "Fuels and lubricants",
            group_5: "Chemical industry", 
        },
        "uz-UZ@cyrillic": {
            group_1: "Озиқ-овқат маҳсулотлари",
            group_2: "Қишлоқ хўжалиги маҳсулотлари",
            group_3: "Қурилиш материаллари ва металлар",
            group_4: "Ёқилғи ва мойлаш материаллари",
            group_5: "Кимё саноати",         
        },
        "uz-UZ@latin": {
            group_1: "Oziq-ovqat mahsulotlari",
            group_2: "Qishloq xo'jaligi mahsulotlari",
            group_3: "Qurilish materiallari va metallar",
            group_4: "Yoqilg'i-moylash materiallari",
            group_5: "Kimyo sanoati",         
        }
    },
    items:[
    [
        {time: "10:00-10:20",span: 1, sections:[
            {group: "group_1",products: []},
        ]},
        {time: "10:25-10:45",span: 1,sections:[
            {group: "group_2",products: []},
        ]},
        {time: "10:50-11:10",span: 1,sections:[
            {group: "group_3",products: [],span: 1},
        ]},
        {time: "11:15-11:35",span: 1,sections:[
            {group: "group_4",products: [],span: 1},
        ]},
        {time: "11:40-12:00",span: 1,sections:[
            {group: "group_5",products: [],span: 1},
        ]},
    ],
    [
        {time: "15:00-15:20",span: 1, sections:[
            {group: "group_1",products: []},
        ]},
        {time: "15:25-15:45",span: 1,sections:[
            {group: "group_2",products: []},
        ]},
        {time: "15:50-16:10",span: 1,sections:[
            {group: "group_3",products: [],span: 1},
        ]},
        {time: "16:15-16:35",span: 1,sections:[
            {group: "group_4",products: [],span: 1},
        ]},
        {time: "16:40-17:00",span: 1,sections:[
            {group: "group_5",products: [],span: 1},
        ]},
    ],
]})