.event_page {

    .event_days {
        display: flex;
        flex-direction: column;

        >.header {
            background: #fff;

            .content {
                display: flex;
                border-top: 1px solid #f2f2f2;
                padding-right: 8px;

                >.gmt {
                    flex: 0 0 60px;
                    position: relative;

                    >span {
                        position: absolute;
                        bottom: 3px;
                        font-size: 11px;
                        right: 2px;
                        color: #666;
                    }
                }
            }

            .days {
                flex: 1 1 auto;
                border-bottom: 1px solid #ccc;
                padding-left: 10px;

                >.container {
                    display: flex;

                    >.day {
                        padding: 20px 0 0;
                        flex: 0 0 100%;

                        >.week {
                            font-size: 11px;
                            text-transform: uppercase;
                            color: #888;
                            text-align: center;
                        }

                        >.number {
                            font-size: 26px;
                            text-align: center;
                        }
                    }
                }

                .events {
                    min-height: 16px;
                    position: relative;
                    display: grid;
                    grid-auto-flow: dense;
                    grid-template-columns: repeat(1, 100%);

                    .column {
                        border-left: 1px solid #ccc;
                        height: 100%;
                        position: absolute;
                    }

                    .event_item {
                        margin: 0 2px 2px 2px;
                        padding: 0 6px;
                        line-height: 22px;
                        border: none;
                        >.title{
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            overflow: hidden;
                        }

                        &.begin {
                            padding-left: 16px;

                            &::before {
                                content: " ";
                                position: absolute;
                                box-sizing: border-box;
                                top: 0;
                                left: 0px;
                                border: 11px solid #fff;
                                width: 11px;
                                height: 22px;
                                border-right: 8px solid transparent;
                                border-left: none;
                            }
                        }

                        &.finish {
                            padding-right: 16px;

                            &::after {
                                content: " ";
                                position: absolute;
                                box-sizing: border-box;
                                top: 0;
                                right: 0px;
                                border: 11px solid #fff;
                                width: 11px;
                                height: 22px;
                                border-left: 8px solid transparent;
                                border-right: none;
                            }
                        }
                    }
                }
            }
        }

        .main {
            display: flex;
            background: #fff;
            z-index: 0;
            padding: 0 8px 8px 0;

            >.hours {
                flex: 0 0 60px;

                >div {
                    position: relative;
                    height: 60px;
                    padding-right: 8px;
                    text-align: right;

                    >span {
                        display: block;
                        position: relative;
                        top: -6px;
                        font-size: 12px;
                        color: #888;
                    }
                }
            }

            >.content {
                flex: 1 1 auto;
                display: flex;
                position: relative;

                .days {
                    display: flex;
                    margin-left: 10px;
                    width: 100%;

                    >.day {
                        border-left: 1px solid #ccc;
                        position: relative;
                        flex: 0 0 100%;

                        .event_item {
                            position: absolute;
                            border-radius: 4px;
                            margin-left: -1px;
                            margin-top: 1px;
                            outline: none;
                            z-index: 5;
                            padding: 5px;

                        }
                    }
                }
            }
        }

        &.count_4 {

            .header .content .events {
                grid-template-columns: repeat(4, 25%);
            }

            .header .content .days .day,
            .main .content .days .day {
                flex: 0 0 25%;
            }
        }

        &.count_7 {
            .header .content .events {
                grid-template-columns: repeat(7, 14.28%);
            }

            .header .content .days .day,
            .main .content .days .day {
                flex: 0 0 14.28%;
            }
        }

        .event_item {
            position: relative;
            border-radius: 2px;
            padding: 4px;
            outline: none;
            z-index: 5;
            padding: 5px;
            font-size: 12px;
            background: rgb(158, 105, 175);
            color: #fff;
            border: 1px solid #ffffff9c;
            //border: 1px solid #e18dd0;
            cursor: pointer;
            user-select: none;

            &.green {
                background: #6e8d67;
                //color: #597b52;
                //border: 1px solid #d5ddd3;
            }

            &.blue {
                background:  #1689b7;
                //color: rgb(7, 128, 151);
                //border: 1px solid #56cfe6;
            }

        }






    }

    .rows {
        >div {
            height: 60px;

            &::after {
                content: "";
                border-bottom: 1px solid #ccc;
                position: absolute;
                width: 100%;
                margin-top: -1px;
                z-index: 3;
                pointer-events: none;
            }
        }
    }

    .sidebar {
        >div >.create {
            height: 80px;
            line-height: 80px;
            text-align: left;
            border-bottom: 1px solid #f2f2f2;
            cursor: pointer;
            padding: 0 16px;
            color: #2c9ace;

            >icon {
                font-size: 24px;
                margin-right: 16px;
                line-height: 80px;
            }

            &:hover {
                background: #f2fafb;
            }
        }
        >div >.ui-layout-group{
            .label{
                padding: 0 8px 0 16px;
                margin-bottom: 0;
                >.title{
                    padding: 8px 0px;
                }
            }
        }
    }

    .move {
        user-select: none;
        color: #737373;
        line-height: 32px;
        font-size: 24px;
        text-align: center;
        display: flex;
        flex: 0 0 32px;

        >span {
            flex: 0 0 32px;
            align-items: center;
            justify-content: center;
            padding: 0 16px;
            display: flex;

            &:not(.value) {
                cursor: pointer;
                border-radius: 5px;
                margin: 5px;

                &:hover {
                    background: #f2f3f3;
                }

                &:before {
                    content: " ";
                    display: block;
                    width: 9px;
                    height: 9px;
                    border: 2px solid #868e96;
                    transform: rotateZ(45deg);
                    line-height: 32px;
                }
            }

            &:first-child:before {
                border-right: none;
                border-top: none;
            }

            &:last-child:before {
                border-left: none;
                border-bottom: none;
            }

            &.value {
                white-space: nowrap;
                flex: 1 1 auto;
                user-select: none;
            }
        }
    }
}