import DynamicStore from './dynamic_store'
import ArrayStore from './array_store'
import RemoteStore from './remote_store'
import RefStore from './ref_store'
import BackEndStore from './backend_store'
import DataSource from './data_source'
import Query from './query'
import Entity from './entity'
import Model from './model'
import Property from './property'

var Data = {
    install: (Vue, args) => {
        
    }
}

Data.DynamicStore = DynamicStore;
Data.ArrayStore = ArrayStore;
Data.RemoteStore = RemoteStore;
Data.RefStore = RefStore;
Data.DataSource = DataSource;
Data.Query = Query;
Data.Model = Model;
Data.Entity = Entity;
Data.Property = Property;
Data.BackEndStore = BackEndStore;

export default Data;

if (typeof window !== 'undefined' && window.Vue) {
    window.Vue.use(Data)
}