export var Enum = {
    name: "ui-enum",
    props: {
        dataSource: Object,
        value: {
            type: Array,
            default: []
        },
        actions: {},
        status: {},
        disabled: <PERSON><PERSON><PERSON>,
        readonly: <PERSON><PERSON><PERSON>,
        label: {},
    },
    computed: {
        valueExp() { return this.dataSource && this.dataSource.valueExp },
        displayExp() { return this.dataSource && this.dataSource.displayExp }
    },
    mounted: function () {
        if (this.dataSource)
            this.dataSource.onQueryUpdate.bind(this.onQueryUpdate);
    },
    destroyed: function () {
        if (this.dataSource)
            this.dataSource.onQueryUpdate.unbind(this.onQueryUpdate);
    },
    methods: {
        onQueryUpdate() {
        },
        action(event) {
            if (event == 'clear') {
                this.value = [];
            }
        },
        display(item) {
            let name;

            if (typeof this.displayExp == 'function') {
                name = this.displayExp(item)
            } else {
                name = item[this.displayExp];
            }

            return this.$t(name);
        }
    },
    watch: {
        value: {
            immediate: true,
            async handler(val, oldVal) {
                this.$emit('input', val)
            }
        },
        dataSource: {
            immediate: false,
            async handler(val, oldVal) {
                if (val)
                    val.onQueryUpdate.bind(this.onQueryUpdate);
                if (oldVal)
                    oldVal.onQueryUpdate.unbind(this.onQueryUpdate);
            }
        }
    },
    template: `<ui-control class='ui-enum' :label='label' opened v-on:action='action' :status='status' :readonly='readonly' v-if='dataSource'>
            <div class='control'>
            <div :key='item.key' v-bind:class='["ui-checkbox",{"readonly": readonly}]' v-for='(item,position) in dataSource.items'>
                <label><input type='checkbox' :disabled='readonly' :id='item[valueExp]' :value='item[valueExp]' v-model='value' /> <span v-html="display(item)"/></label>
            </div>
            </div>
    </ui-control>`
}
