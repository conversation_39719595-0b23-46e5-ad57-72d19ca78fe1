import Model from './model'
import { Http } from '@iac/core'
import { DataSource } from '@iac/data'
const lngEdit = {
    props: ["value", "readonly"],
    data: function () {
        return {
            text: this.value
        }
    },
    methods: {
        onPaste(e){
            var contentOnBlur = (e.originalEvent || e).clipboardData.getData('text/plain') || prompt('Paste something..');
            contentOnBlur = contentOnBlur.replace(/(<([^>]+)>)/ig,'');
            document.execCommand('insertText', false, contentOnBlur);

        },
        onChange(e) {
            let value = e.target.innerText.trim()

            if (value)
                this.$emit("input", value)
            else
                this.$emit("input", undefined)
        }
    },
    computed: {
        classes() {
            return [
                "editable",
                {
                    // change: this.text && this.text != this.value,
                    // new: !this.text && this.text != this.value,
                }]
        },
    },
    template: `
        <td v-bind:class="classes" style='white-space: pre-line;' v-on:paste.stop.prevent='onPaste' :contentEditable=!readonly v-on:input='onChange'>{{text}}</td>
        
        
    `
}

const Section = {
    props: ["doc", "index", "readonly"],
    data: function () {
        return {
            draggable: false,
            dragover_item: -1,
            drag_item: -1,
        }
    },
    computed: {
        model() {
            return this.doc.content[this.index];
        },
        items() {
            return [...this.model.items, {}];
        }
    },
    methods: {
        deleteSection() {
            this.doc.content.splice(this.index, 1);
        },
        deleteItem(i) {
            this.model.items.splice(i, 1);
        },
        onChangeTitle(lng, value) {
            Vue.set(this.model.title, lng, value)
        },
        onChangeItem(i, lng, value) {
            let item = this.items[i];
            Vue.set(item, lng, value)

            if (i == this.items.length - 1) {
                this.model.items.push(item)
            }
        },
        checkError(data) {
            for (let lng of this.doc.languages) {
                if (!data[lng])
                    return true;
            }
            return false;
        },
        dragStart(i) {
            this.draggable = true;
        },
        dragend() {
            this.draggable = false;
            this.drag_item = -1;
            this.dragover_item = -1;
        },
        dragover(i) {
            this.dragover_item = i
            return false;
        },

        dragleave() {
            this.dragover_item = -1
        },
        dragenter(event) {
            if (!this.draggable) {
                return false;
            }
            event.stopPropagation();
            event.preventDefault();
            return true;
        },
        drop() {

        }
    },
    components: {
        lngEdit
    },
    template: `
        <tbody :key='model' v-on:dragenter.prevent='e=>dragenter(e,index)'>
            <tr :key='(index+1)' :class='"title"+(checkError(model.title) ? " error" : "")'>
                <td>{{(index+1)}}</td>
                <lngEdit :readonly='readonly || doc.status != "draft"' :style='"width: "+ 100/doc.languages.length+"%"' v-for='lng in doc.languages' :value='model.title[lng]'  v-on:input='value=>onChangeTitle(lng, value)' />
                
                <td  v-if='doc.status == "draft"' class='action'><ui-btn type='danger xs' v-on:click.native='deleteSection()'><icon>trash</icon></ui-btn></td>
            </tr>

            <tr style='position: relative;' :key='item' :class='"item"+(checkError(item) ? " error" : "")' v-for='item, i in items'
                    :draggable=false
                    v-on:dragstart="dragStart(i)"
                    v-on:dragend='dragend'
                    v-on:dragleave.prevent='dragleave'            
            >
                <td>&nbsp;<span v-if='i!=items.length-1'>{{(index+1)+"."+(i+1)}}</span></td>

                <template v-if='!readonly && doc.status == "draft"'>   
                    <lngEdit :readonly='readonly || doc.status != "draft"' :style='"width: "+ 100/doc.languages.length+"%"'  v-for='lng in doc.languages' :value='item[lng]' v-on:input='value=>onChangeItem(i, lng, value)'  />
                </template>
                <template v-else>
                    <td style='padding: 0 2px;'  v-for='lng in doc.languages'>
                        <ui-markdown-view :content='item[lng] || "" ' />
                    </td>
                </template>

                <td v-if='doc.status == "draft"' class='action'><ui-btn v-if='i!=items.length-1' type='danger xs' v-on:click.native='deleteItem(i)'><icon>trash</icon></ui-btn></td>

                <template v-if='draggable'>
                    <div style='border:1px solid #f00; left: 0; top: 0; right: 0; bottom: 51%; position: absolute;'
                    v-on:drop.prevent="drop(i)" 
                        v-on:dragover.prevent="dragover(i)"
                    ></div>
                    <div style='border:1px solid #0f0; left: 0; top: 49%; right: 0; bottom: 0; position: absolute'
                        v-on:dragover.prevent="dragover(i+1)"
                        v-on:drop.prevent="drop(i+1)" 
                    ></div>
                </template>
            </tr>
      
        </tbody>
    
    `
}

export default {
    data: function () {
        return {
            version_dropdown: undefined,
            model: undefined,
            error: undefined,
            readonly: false,
            items: [1, 2, 3]
        }
    },
    computed: {
        details() {
            if (this.model)
                return JSON.stringify(this.model, null, '\t')
        }
    },
    mounted() {
        this.update();
    },
    watch: {
        $route(to, from) {
            this.update();
        }
    },
    methods: {
        createVersion() {
            this.$wait(async () => {
                let { error, data } = await Http.api.rpc("ref", {
                    ref: "ref_documentation_pages",
                    op: "create",
                    data: {
                        type: "procurement"
                    }

                })
                if (error) {
                    return Vue.Dialog.MessageBox.Error(error);
                }
                let { id } = data
                this.$router.push({ path: '/workspace/settings/doc/' + id })

            });
        },
        public() {
            if (!this.model)
                return;
            this.$wait(async () => {
                if (await this.model.public()) {
                    this.update();
                }
            });
        },
        public1() {
            this.$wait(async () => {
                let { error, data } = await Http.api.rpc("ref", {
                    ref: "ref_documentation_pages",
                    op: "update",
                    data: {
                        data: { d: "a", d1: "awdawdawd2323" },
                        id: Number(this.$route.params.id)
                    },
                    //filters: {
                    //    id: Number(this.$route.params.id)
                    //}

                })
                if (error) {
                    return Vue.Dialog.MessageBox.Error(error);
                }
                //let {id} = data 
                //this.$router.push({ path: '/workspace/settings/doc/' + id })

            });
        },
        addSection() {
            this.model.content.push({
                title: {},
                items: []
            })
        },
        save() {
            this.$wait(async () => {
                await this.model.save();
            });
        },
        update() {
            this.$wait(async () => {
                let { error, data } = await Model.get(this.$route.params.id)
                this.model = data;
                this.error = error
            });
        },
        hideSelectVersion() {
            this.version_dropdown = false;
        },
        onSelectVersion({ id }) {
            this.version_dropdown = false;
            if (this.model && this.model.id == id)
                return;
            this.$router.push({ path: '/workspace/settings/doc/' + id })
        },
        onChangeTitle(e) {
            console.log(e);
        },
        onChangeItem(index, i, lng, target) {
            let items = this.model.content[index].items;
            console.log(items, i, items.length);
            if (i == items.length - 1) {
                this.model.content[index].items.push({

                })
            }

        },
        onAddItem(index, lng, target) {
            // console.log(index,lng);
            let item = {
                lng: target.innerText.trim()
            }
            console.log(this.model.content[index]);
            this.model.content[index].items.push(item)
        },
    },
    components: {
        docSection: Section
    },
    template: `
        <ui-layout-group class='settings-doc'>
            <template v-if='model'>
                <div class='sticky header'>
                <div class='info' >
                    <div>
                        <div style='display: inline-block;'>{{$t(model.status)}}</div>
                        <span v-if='0'>версия от:</span>  
                        <div style='display: inline-block;'>
                            <div><iac-date :date='model.updated_at' withoutTime v-on:click.native.stop.prevent='version_dropdown=!version_dropdown' style='color: #2973b3; text-decoration: underline; cursor: pointer;'/></div>

                            <div v-if='version_dropdown' style='    max-width: 500px; z-index: 10;position: absolute; min-width: 200px; max-height: 300px; overflow: auto;background: #fff;    box-shadow: 0 6px 18px 0 rgba(14, 21, 47, 0.13), 0 -2px 6px rgba(14, 21, 47, 0.03);'>
                                <ui-list v-on-clickaway="hideSelectVersion" :dataSource='model.versions' v-on:item='onSelectVersion' />
                            </div>
                        </div>
                        
                    </div>
                    <ui-btn-group>

                        <template v-if='model.status == "draft"'>
                            <ui-btn type='primary sm' v-on:click.native='public'>{{$t("action.publish_procedure")}}</ui-btn>
                        </template>
                        <ui-btn v-else-if='model.status == "public"' type='primary sm' v-on:click.native='createVersion'>Добавить новую версию</ui-btn>
                    </ui-btn-group>
                </div>
                <div v-if='model.status == "draft" || model.comment'>
                    <ui-input label='comment' :readonly='model.status == "public"' v-model='model.comment' />
                </div>
                </div>
                
                <ui-scroller class=''>
                <table :key='model.update' class='content' cellspacing="0" cellpadding="0">
                    <tbody>
                        <tr>
                            <td>#</td>
                            <td v-for='lng in model.languages'><div style='display: flex;justify-content: space-between;'><span>{{lng}}</span><ui-btn v-if='0' type='danger xs'><icon>trash</icon></ui-btn></div></td>
                            <td v-if='model.status == "draft"' class='action'><ui-btn type='success xs' style='width: 100%;'><icon>+</icon></ui-btn></td>
                        </tr>
                    </tbody>
                    <docSection :readonly='readonly' v-for='section, index in model.content' :doc='model' :index='index'/>
                </table>
                <template slot='footer' v-if='model.status == "draft"'>
                    <ui-btn-group style='margin: 5px 0;'>
                        <ui-btn type='primary' v-on:click.native='save'>{{$t("save")}}</ui-btn>
                        <ui-btn type='success' v-on:click.native='addSection'>Добавить раздел</ui-btn>

                        <ui-btn v-if='!readonly' type='warning  ' v-on:click.native='readonly=true'>{{$t("preview")}}</ui-btn>
                        <ui-btn v-else type='warning  ' v-on:click.native='readonly=false'>{{$t("edit")}}</ui-btn>
                    </ui-btn-group>
                </template>
                </ui-scroller>
                <pre v-if='0' style='overflow: auto;'><code>{{details}}</code></pre>

            </template>
        </ui-layout-group>
    `
}