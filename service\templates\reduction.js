const Component = {
    props: ['model'],
    computed: {
        url() {
            return `/procedure/${this.model.id}/core`
        },
        remainTime() {
            return Date.now() + this.model.remain_time * 1000;
        },
        lng() {
            return Language.local;
        },
        lotNum() {
            const { $t, model } = this;
            return `${$t('lot_num')}: ${model.id}`;
        },
        title() {
            return this.listToString({
                listName: 'good_maps',
                sliceCallback: (list) => list.slice(0, 3),
            });
        },
        area_paths() {
            return this.listToString({
                listName: 'area_path',
                sliceCallback: (list) => [list[1], ...list.slice(3)],
            });
        }
    },
    methods: {
        listToString({ listName, sliceCallback }) {
            const { model } = this;
            const list = model && model.meta && model.meta[listName];
            if (list && list.length) {
                return sliceCallback(list).map(({ name }) => name).join(', ');
            }
        },
        openProducts() {
            const { lotNum, model: { good_count, meta } } = this;
            Vue.Dialog.products.Modal({
                size: 'right',
                model: {
                    title: lotNum,
                    count: good_count,
                    items: meta.good_maps,
                }
            })
        }
    },
    template: `
        <ui-data-view-item :model='model'>
            
            <template slot='header'>
                <div><iac-entity-edit  v-if='model.id' :value='{id: model.id, type: "reduction"}' /> &nbsp;&nbsp; <iac-date v-if='model.publicated_at' :date="model.publicated_at"  withoutTime withMonthName/></div>
                <div>
                    <div v-if='model.green && $settings.procedures && $settings.procedures._green' :title='$t("green_procedure")'  class='iac-marker green'/><ui-ref source='status_reduction' :value='model && model.status'/>
                </div>
            </template>

            <template slot='title'>
                <router-link :to='url' class='title clamp_7'>{{title}}</router-link>
            </template>
            <template slot='sub_title'>
                <a @click.prevent='openProducts' href='#' class='widget-wide__show-all'>{{model.good_count}} {{ $t('product',{count: model.good_count}) }}</a>
            </template>           
            <template slot='description'>
                <div>
                    <label>{{ $t('contract.organizer')}}:</label>
                    <span>{{ model.meta && model.meta.company_name }}</span>
                    <div>{{ area_paths }}</div>
                </div>
            </template>
            <template slot='props'>
                <div>
                    <label>{{ $t('start_price') }}</label>
                    <div>
                        <iac-number :value='model.start_price' delimiter=' ' part='2' />&nbsp;{{ model.currency }}
                    </div>
                </div>
                <div v-if="!['close', 'cancel', 'rejected', 'moderated'].includes(model.status)">
                    <label>{{ $t('remain_time') }}</label>
                    <div><iac-timer :date='remainTime' /></div>
                </div>
                <div>
                    <label>{{ $t('lowest_price') }}</label>
                    <div>
                        <iac-number :value='model.last_price' delimiter=' ' part='2' />&nbsp;{{ model.currency }}
                    </div>
                </div>
                <div>
                    <label>{{ $t('amount_participants') }}</label>
                    <div>{{ model.part_count }}</div>
                </div>
                <div v-if='model.contract_pay_percent != undefined'>
                    <label>{{ $t('differentiated_company') }}:</label> 
                    <div>{{model.contract_pay_percent}}%</div>
              </div>
            </template>

        
        </ui-data-view-item>
    `
}

Vue.component('template-reduction', Component);