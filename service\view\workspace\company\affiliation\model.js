import { Entity } from '@iac/data'
import { Http } from '@iac/core'
export default class Model extends Entity {
    constructor(context, introspect, scheme) {
        super(context)
        this.context = context;
    }

    props() {
        return {
            fio: {
                //group: "member",
                required: true,
                label: "aff.fio",
                value: this.context.fio,
                description: "$aff.fio_description"
            },
            company_name: {
                group: "-aff.company_group",
                required: true,
                label: 'aff.company_name',
                value: this.context.company_name,
                description: "$aff.company_name_description"
            },           
            inn: {
                group: "-aff.company_group",
                required: true,
                label: 'company_inn',
                value: this.context.inn,
            },
            share_type: {
                group: "!share/!-group",
                value: this.context.share_type,
                type: "entity",
                has_del: true,
                label: "aff.share_type",
                dataSource: [
                    {id: "share", name: "aff.share_type.share"},
                    {id: "relation", name: "aff.share_type.relation"}
                ],
           },
            share_empty: {
                type: "static",
                group: "!share/!-group",
                label: "!",
                hidden: () => {
                    if (!this.share_type)
                        return false;
                    return true;
                }
            },
            share_percent: {
                label: 'percent',
                type: "float",
                group: "!share/!-group",
                value: this.context.share_percent,
                hidden: () => {
                    if (this.share_type && (this.share_type == 'share' || this.share_type.id == 'share'))
                        return false;
                    return true;
                }
            },
            share_official: {
                label: 'aff.share_official',
                group: "!share/!-group",
                value: this.context.share_official,
                description: '$aff.share_official_description',
                hidden: () => {
                    if (this.share_type && (this.share_type == 'relation' || this.share_type.id == 'relation'))
                        return false;
                    return true;
                }
            },
            evidence_affiliate: {
                group: "!reason",
                label: "reason",
                multiple: true,
                //required: true,
                value: this.context.evidence_affiliate,
                description: "$aff.evidence_affiliate_description"
            }
        }
    }

    async save() {
        let params = this.fields.filter((field) => {
            if (field.type == 'static')
                return false;
            //if (field.hidden && typeof field.hidden == 'function') {
            //    return !field.hidden();
            //}
            return true;// !field.hidden;
        }).reduce((prev, curr) => {
            prev[curr.name] = curr.value && curr.value.exp ? curr.value.exp.value : curr.value

            if (Array.isArray(prev[curr.name]) && prev[curr.name].length <= 0) {
                prev[curr.name] = null;
            }

            if (prev[curr.name] == undefined)
                prev[curr.name] = null;

            if (curr.hidden && typeof curr.hidden == 'function') {
                if (curr.hidden())
                    prev[curr.name] = null;
            }
            return prev
        }, {})

        let { error, data } = await Http.api.rpc("common_ref", {
            ref: "ref_affiliated_person",
            op: this.id ? "update" : "create",
            filters: {
                id: this.id
            },
            data: params
        })

        params.id = this.id

        if (error) {
            if (!this.setError(error)) {
                await Vue.Dialog.MessageBox.Error(error);
            }
        }

        if (!error && data && !this.id) {
            params.id = data.id
        }

        return {
            error,
            data: params
        };
    }
}
