import { DataSource, RefStore, Query } from '@iac/data'
import { Language } from '@iac/core'

const columns = [
  'num',
  { field: 'updated_at', label:'moderate_ad_updated_at', type: 'date', },
  "status",
  { field: 'product_name', style: 'width:100%;' },
];

Object.freeze(columns);

export default {
  data() {
    let divide_on;
    try{
      divide_on = localStorage.getItem("moderate_divide_on") || "";
      divide_on = JSON.parse(divide_on);
    }catch(e){
      divide_on = undefined
    }
    return {
      columns,
      source: new DataSource({
        limit: 10,
        sort: [
          {icon: "sort_asc", id: "id_asc", name: "column_id"},
          {icon: "sort_desc", id: "id_desc", name: "column_id"}
        ],
        query: new Query({
          status: {
            group: 'status',
            label: "!",
            type: 'enum',
            has_del: true,
            dataSource: [
              {
                id: 'moderated',
                name: 'moderation',
              },
              {
                id: 'publicated',
                name: 'approved',
              },
              {
                id: 'rejected',
                name: 'rejected',
              },
              {
                id: 'close',
                name: 'closed',
              },
            ],
          },
          divide_on: {
            sync: false,
            type: "model",
            group: "moderators.divide_on",
            label: "!moderators.divide_on",
            fields: {
              number: {
                label: "!moderators.number",
                type: "number",
                prefix: "№",
                group: "<divide_on>"
              },
              count: {
                type: "number",
                label: "!moderators.count",
                prefix: Language.t("from_moderators"),
                group: "<divide_on>"
              }
            },
            value: divide_on,
            onChange: (value)=>{
              localStorage.setItem("moderate_divide_on",JSON.stringify(value));
            }
          },
          updated_at_gte: {
            group: 'moderate_ad_updated_at',
            type: 'date',
            label: 'from',
            has_del: true
          },
          updated_at_lte: {
            group: 'moderate_ad_updated_at',
            type: 'date',
            label: 'to',
            has_del: true
          },
          relation: {
            value: 'moderator',
            type: "hidden",
            sync: false
          },
        }),
        store: new RefStore({
          ref: 'ref_reduction_object',
          context: context => {
            Object.defineProperty(context, "status_type", {
              configurable: true,
              enumerable: true,
              get: () => {
                if (context.status === "publicated") {
                  return 'success'
                }
                if (context.status === "moderated") {
                  return 'warning'
                }
                if (context.status === "rejected") {
                  return 'danger'
                }
              }
            })

            context.proc_type = "reduction"

            context.updateContext = async ()=>{
              let data = (await this.source.byKey(context.id)) || {}
              context.status = data.status || context.status;
            }

            return context
          },
          injectQuery: (params)=>{
            if(params.filters?.divide_on?.number && params.filters?.divide_on?.count){
              params.filters.divide_on = [params.filters.divide_on.number,params.filters.divide_on.count]
            }else if(params.filters){
              params.filters.divide_on = undefined;
            }
            params.fields = [
              "id",
              "updated_at",
              "status",
              //"product_name",
              //"properties",
              "reject_reason",

            ]
            //params.order_by = "id_desc"
            return params;
          }
        }),
        template: 'template-moderate'
      })
    }
  },
  template: `
    <iac-access :access='$policy.reduction_moderate'>
      <iac-section type='header'>
        <ol class='breadcrumb'>
          <li><router-link to='/'>{{ $t('home') }}</router-link></li>
          <li>{{ $t('nav.moderate') }}</li>
          <li>{{ $t('nav.auction') }}</li>
        </ol>
        <div class='title'>
          <h1>{{ $t('nav.auction') }}</h1>
        </div>
      </iac-section>
      <iac-section>
        <ui-layout-group>
          <ui-data-view :dataSource='source'/>
        </ui-layout-group>
      </iac-section>
    </iac-access>
  `,
};
