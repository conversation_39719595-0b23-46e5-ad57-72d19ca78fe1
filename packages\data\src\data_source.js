import Query from './query'
import Event from './../../core/src/event'
import Guid from '../../core/src/guid';
import Store from './store';
import ArrayStore from './array_store';
import RefStore from './ref_store';
import RemoteStore from './remote_store';
import Language from './../../core/src/language'

export default class DataSource {

    static STATE_NONE = 0;
    static STATE_REQUEST = 1;
    static STATE_ERROR = 2;
    static STATE_EOF = 4;

    // События
    @Event onItemsUpdate;

    @Event async onQueryUpdate() {
        if (this.listeners.length > 0 || this.ignore_listeners) {
            this._items = [];
            this.load();
        }
    }

    onEventListener(event) {
        if (this.ignore_listeners)
            return;
        if (event == 'bind' && this.listeners.length == 1) {
            this._items = [];
            this.load();
        }
        if (event == 'unbind' && this.listeners.length <= 0) {
            this._items = [];
        }
    }

    constructor(options) {
        this._store = options.store;
        if (typeof this._store == 'object' && !(this._store instanceof Store)) {
            if (this._store.ref) {
                this._store = new RefStore(this._store)
            } else if (this._store.method) {
                this._store = new RemoteStore(this._store);
            } else {
                this._store = new ArrayStore(this._store);
            }
        }
        if (options.query instanceof Query) {
            this._query = options.query
        } else {
            this._query = new Query(options.query)
        }

        if(options.sort){
            this._query.properties.order_by.setAttributes({
                dataSource: options.sort
            })
        }


        this._query.onUpdate.bind(this.onQueryUpdate)
        this._state = DataSource.NONE;
        this._items = [];
        this.ignore_listeners = options.ignore_listeners
        this.valueExp = options.valueExp || 'id';
        this.displayExp = options.displayExp || 'name';
        this.descExp = options.descExp || 'desc';
        this.iconExp = options.iconExp || 'icon';
        this.error = undefined;
        this._position = 0;
        this.checkedItems = [];
        this.search = options.search
        this.summary = undefined;
        this.take = options.limit || 50;
        this.columns = options.columns;
        this.template = options.template;
        this._actions = options.actions || [];
        this.count = undefined;
        this.request_count = options.request_count;

        if ((typeof this.displayExp != "function")) {
            let displayProps = this.displayExp.split(".")
            if (displayProps && displayProps.length > 1) {

                this.displayExp = function (item) {

                    for (let prop of displayProps) {
                        item = item[prop]
                        if (!item)
                            return;
                    }
                    return item
                }
            }
        }

        if ((typeof this.descExp != "function")) {
            let descProps = this.descExp.split(".")
            if (descProps && descProps.length > 1) {

                this.descExp = function (item) {

                    for (let prop of descProps) {
                        item = item[prop];
                        if (!item)
                            return;
                    }
                    return item
                }
            }
        }

        if(this.onCreate){
            this.onCreate(options);
        }

    }

    get actions() {
        return [...this._actions, ...(this.store.actions || [])]
    }
    set actions(actions) {
        this._actions = actions;
    }

    get position() {
        return this._position
    }
    set position(value) {
        this._position = value;
    }

    get current() {
        if (this.items && this.items.length > 0 && this.position < this.items.length) {
            return this.items[this.position];
        }
    }

    get state() {
        return this._state;
    }

    get items() {
        return this._items;
    }

    get query() {
        return this._query;
    }

    get store() {
        return this._store;
    }

    initExp(item) {
        if (!item)
            return item;

        let value = Array.isArray(this.valueExp) ? this.valueExp.reduce((prev, exp) => {
            prev[exp] = item[exp]
            return prev
        }, {}) : item[this.valueExp]

        item.expValue = value
        item.exp = {
            value: value,
            display: (typeof this.displayExp == "function") ? this.displayExp(item) : item[this.displayExp],
            icon: item[this.iconExp],
            desc: (typeof this.descExp == "function") ? this.descExp(item) : item[this.descExp],
        }
        return item;
    }

    async byKeys(keys) {
        let items = await this._store.byKeys(keys)
        if (items) {
            items.forEach((item) => {
                this.initExp(item);
            })
        }
        return items;
    }

    async byKey(key) {
        let item = await this._store.byKey(key)
        if (item) {
            this.initExp(item);
        }
        return item;
    }

    async load(skip = 0, take = this.take) {
        if (this.controller) {
            this.controller.abort();
        }


        // Проверяем на ошибки в Query

        let errors_fields = this.query.fields.filter((field) => {
            if (field.status && field.status.type == 'error')
                return true;
            return false;
        })

        if (errors_fields && errors_fields.length > 0) {
            this._state &= ~DataSource.STATE_REQUEST;
            this._state |= DataSource.STATE_ERROR;
            this.error = {
                status: "400",
                message: Language.t("some_fields_contain_errors")
            };

            return {
                error
            };
        }


        this.controller = new AbortController();

        this._state = DataSource.STATE_REQUEST;
        if (this._store instanceof ArrayStore) {
            skip = 0;
            take = 20000;
        }

        let { error, data: items } = await this._store.load({
            skip: skip,
            take: take + 1,
            query: this.query.local,
            search: this.query.search_query,
            signal: this.controller.signal
        });

        if (error) {
            if (error.code == "AbortError") {
                return {
                    error
                };
            }

            this._state &= ~DataSource.STATE_REQUEST;
            this._state |= DataSource.STATE_ERROR;
            this.error = error;

            return {
                error
            };
        }

        if (items && !Array.isArray(items)) {
            this.summary = items.summary;
            items = items.items;
        } else {
            this.summary = undefined;
        }


        if (items.length <= take) {
            this._state |= DataSource.STATE_EOF;
        } else {
            items.length = take;
            this._state &= ~DataSource.STATE_EOF;
        }

        let offset = this._items.length;
        items.forEach((item, index) => {
            item = this.init_item(item);
            item.index = offset + index + 1;
        });
        this._items = this._items.concat(items)
        this.onItemsUpdate();
        this._state &= ~DataSource.STATE_REQUEST;

        this.update_count();

        return {
            data: this.items
        };
    }

    async update_count() {
        if (!this._store.count || !this.request_count)
            return;
        let { error, data } = await this._store.count({
            query: this.query.local,
            signal: this.controller.signal
        });
        if (data) {
            this.count = data.count
        }
    }

    init_item(item) {
        item.key = Guid.newGuid();
        item.dataSource = item.dataSource;

        Object.defineProperty(item, "checkedItems", {
            configurable: true,
            enumerable: true,
            get: () => {
                if (!item.checkbox || !this.checkedItems)
                    return;
                if (typeof item.checkbox == 'function' && !item.checkbox())
                    return
                return this.checkedItems;
            },
            set: (value) => {
                this.checkedItems = value
            }
        });

        let value = Array.isArray(this.valueExp) ? this.valueExp.reduce((prev, exp) => {
            prev[exp] = item[exp]
            return prev
        }, {}) : item[this.valueExp]

        item.expValue = value
        item.exp = {
            value: value,
            display: (typeof this.displayExp == "function") ? this.displayExp(item) : item[this.displayExp],
            icon: item[this.iconExp],
            desc: (typeof this.descExp == "function") ? this.descExp(item) : item[this.descExp],
        }
        return item;
    }

    unshift_item(item = {}) {
        item = this.init_item(this._store.context(item));
        this.items.unshift(item);
    }

    push_item(item = {}) {
        item = this.init_item(this._store.context(item));
        this.items.push(item);
    }

    set_items(items = []) {
        items = items.map((item) => {
            return this.init_item(this._store.context(item));
        })
        this._items = items;
    }

    async reload() {
        this._items = [];
        this.checkedItems = [];
        await this.load();
    }

    async next(take) {
        await this.load(this.items.length, take);
    }

    static reg(name, source) {
        DataSource.Sources = DataSource.Sources || {};
        DataSource.Sources[name] = source;
    }

    static get(dataSource) {
        if (typeof dataSource === 'string') {
            dataSource = DataSource.Sources[dataSource];
        }

        if (typeof dataSource === 'function') {
            dataSource = dataSource();
        }

        if (dataSource instanceof DataSource)
            return dataSource;
        else if (typeof dataSource == 'object' && !Array.isArray(dataSource)) {
            return new DataSource(dataSource)
        }

        if (!dataSource)
            return;

        let data = Array.isArray(dataSource) ? dataSource : dataSource.data;

        if (!data) {
            return dataSource;
        }

        data = data.map((item) => {
            if (typeof item == 'string' || typeof item == 'number') {
                return {
                    [dataSource.valueExp || "id"]: item,
                    [dataSource.displayExp || "name"]: item,
                }
            }

            return item;
        })

        return new DataSource({
            valueExp: dataSource.valueExp,
            displayExp: dataSource.displayExp,
            iconExp: dataSource.iconExp,
            descExp: dataSource.descExp,
            store: new ArrayStore({
                key: "id",
                data: data || [
                    { id: 1, name: "ООО Золушка" },
                    { id: 2, name: "ЗАО Чикатило" }
                ]
            })
        });
    }

}
