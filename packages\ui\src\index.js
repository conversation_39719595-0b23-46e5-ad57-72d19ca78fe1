import * as components from './components'
import * as directives from './directives'
import Dialog from './dialog'

var Plugin = {
    install: (Vue, args) => {

        // Регистрация компонентов
        for (const key in components) {
            const component = components[key]
            Vue.component(component.name || `ui-${key}`, component)
        }
        // Регистрация директив
        for (const key in directives) {
            const directive = directives[key];
            Vue.directive(key, directive);
        }

        // Заглушка для переводов
        if (!Vue.prototype.$t) {
            Vue.prototype.$t = function t(key, options) {
                return `{${key}}`;
            };
        }

        Vue.mixin({
            beforeCreate: function () {
                this.$wait = async (fn) => {
                    if(this.$el)
                    this.$el.classList.add("iac-wait");
                        await fn();
                    if(this.$el)
                        this.$el.classList.remove("iac-wait");
                };
            }
        });

        Vue.use(Dialog);
    }
}

export default Plugin;

if (typeof window !== 'undefined' && window.Vue) {
    window.Vue.use(Plugin)
}