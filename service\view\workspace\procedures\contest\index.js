import { Context, <PERSON>elo<PERSON> } from '@iac/kernel'
import { Action } from '@iac/core'
import { DataSource, RemoteStore, RefStore, Query } from '@iac/data'

export default {
    data: function () {
        return {
            user: Context.User,
            contest_own: new DataSource({
                query: new Query({
                    status: {
                        type: 'enum',
                        dataSource: "status_contest",
                        value: [],
                    },
                    my_procedures:{
                        type: 'entity',
                        dataSource: [{ id: false, name: "no" }, { id: true, name: "yes" }],
                        has_del: true,
                        hidden: ()=>{
                            return !Develop.filter_develop
                        }
                    },
                    relation:{
                        value: "owner",
                        type: "hidden",
                        sync: false,
                    }
                }),
                store: new RefStore({
                    ref: "ref_contest_private",
                    injectQuery: (params) => {
                        params.fields = ["id","publicated_at","status","name","good_count","close_at","totalcost","currency", "lang","part_count","meta"]

                        params.filters.delivery_regions = params.filters.area_path;
                        params.filters.area_path = undefined;

                        return params;
                    }
                }),
                template: 'template-contest'
            }),
            team_contest_party_own: new DataSource({
                query: new Query({
                    status: {
                        type: 'enum',
                        dataSource: "participant_status_contest",
                        value: [],
                    },
                    relation:{
                        value: "participant",
                        type: "hidden",
                        sync: false,
                    }
                }),
                store: new RefStore({
                    ref: "ref_contest_private",
                    injectQuery: (params) => {
                        params.fields = ["id","publicated_at","status","name","good_count","close_at","totalcost","currency", "lang","part_count","meta"]


                        params.filters.delivery_regions = params.filters.area_path;
                        params.filters.area_path = undefined;

                        return params;
                    }
                }),
                template: 'template-contest'
            })
        }
    },
    methods: {
        async create() {

            let response =  await Action["procedure.create"]();
            
            if(!response)
                return;
            let { error, data } = response;
            if (!error) {
                this.$router.push({path: `/procedure/${data[0].proc_id}/core`});
            }
        }
    },
    template: `
    <div>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('contests')}}</li>
            </ol>
            <div class='title'>
                <h1>{{$t('contests')}}</h1>
                <div v-if='0 && $policy.contest_create'><ui-btn type='primary' v-on:click.native='create'>{{$t('create_contest')}}</ui-btn></div>
            </div>
        </iac-section>
        <iac-section>
            <ui-layout-tab :clear='true'>
                
                <ui-layout-group key='Organize' v-if='$policy.contest_list_own' label='Organize'>
                    <ui-data-view :dataSource='contest_own'/>
                </ui-layout-group> 

                <ui-layout-group key='Participate' v-if='$policy.contest_list_participant' label='Participate'>
                    <ui-data-view  :dataSource='team_contest_party_own'/>
                </ui-layout-group>  

            </ui-layout-tab>
        </iac-section>
    </div>
    `
}