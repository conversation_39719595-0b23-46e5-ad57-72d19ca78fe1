import { DataSource } from '@iac/data'
import Item from '../model/item';
import './_searchContract'
import selectTileDlg from './_selectTile'
var groupComponent = {
    props: ["space", "group"],

    data: function () {
        return {
            dropdownGroup: false,
            dropdownTop: false,
        }
    },
    watch: {
        dropdownGroup: {
            immediate: true,
            async handler(val, oldVal) {
                if (val == true) {
                    this.reposition();
                    //    window.addEventListener('scroll', this.reposition);
                } else {
                    //    window.removeEventListener('scroll',this.reposition)
                }
            }
        }
    },
    computed: {
        classes() {
            return [
                "group",
                {
                    "dropdown-top": this.dropdownTop,

                }
            ]
        }
    },
    methods: {
        onSetGroup(event) {
            this.$emit("group", event);
            this.dropdownGroup = false
        },
        reposition() {
            const elClientRect = this.$el.getBoundingClientRect();
            let clCenter = {
                x: elClientRect.left + elClientRect.width / 2,
                y: elClientRect.top + elClientRect.height / 2
            }
            let htmlSize = {
                width: document.documentElement.clientWidth,
                height: document.documentElement.clientHeight,
            }

            //this.dropdownRight = (clCenter.x > htmlSize.width / 2)
            this.dropdownTop = (clCenter.y > htmlSize.height / 2)
        }
    },
    template: `
    <span  v-bind:class="classes">
        <template v-if='group'>
            <span class='icon delete' v-on:click='onSetGroup()'>
                <icon>delete</icon>
            </span>
        </template>
        <template v-else>
            <span class='icon' v-on:click='dropdownGroup = true'>
                <icon>delete</icon>
            </span>
            <div v-if='dropdownGroup' class='content' v-on-clickaway='()=>{this.dropdownGroup = false}'>
                <ui-list :dataSource='space.groupsSource'  v-on:item='onSetGroup'>
                    <div :class="'ui-list-item group_'+props.item.index" slot='item' slot-scope='props' v-on:click='onSetGroup(props.item)'>
                        <div class='content'>
                            <span class='icon'/>
                            {{props.item.label || ("Группа "+props.item.index) }}
                        </div>
                    </div> 
                </ui-list>
            </div>
        </template>
    </span>
    `
}

export default {
    props: {
        window: {},
        horizontal: {},
        allow: {
            type: Number,
            default: 3
        }
    },
    data: function () {
        return {
            onDrag: false,
            position: {},
            ord: undefined,
            full: false,
            dropdownGroup: false,
            general_setting: Vue.Tiling["settings"].general_setting
        }
    },
    computed: {
        $setting() {
            return this.general_setting.params
        },
        classes() {
            return [
                "terminal-window",
                ["group_" + this.current_item.group_id],
                {
                    //"iac-page": this.full,
                    //full: this.full
                },
                (() => {

                })()]
        },
        current_item() {
            return this.window.items[this.window.active_item];
        },
        current_group() {
            if (this.current_item) {
                return this.current_item.group;
            }
        },
        tab_toolbar() {
            if (!this.$setting.show_maximize)
                return;
            return [
                {
                    icon: !this.full ? "maximize" : "minimize",
                    handler: () => { this.full = !this.full }
                }
            ]
        }
    },
    methods: {
        closeItem(index) {
            this.window.delete_item(index);

        },
        dragover(event) {
            this.onDrag = true;
        },
        async drop(event) {
            this.onDrag = false;
            let item = undefined;
            try {

                let text = event.dataTransfer.getData("text/plain");
                if (text) {
                    item = Item.get({
                        label: "Note", tile: "note", params: { text: text }
                    })
                }
                let tile = event.dataTransfer.getData("tile")
                if (tile) {
                    item = JSON.parse(tile);
                    if (item) {
                        if (!Array.isArray(item)) {
                            item = [item];
                        }
                        if (item.length > 1) {
                            //item = await selectTileDlg.Modal({
                            //    tiles: item
                            //})
                        }
                        if (!item)
                            return;

                        item = item.map((i) => {
                            return Item.get(i)
                        })

                        let _group = undefined;
                        for (let _g of this.window.space.groups) {
                            if (!_g.id && _g.id != 0){
                                _group = _g;
                                break;
                            }
                        }
                        if (_group) {
                            item.forEach((_item) => {
                                _item.set_group(_group, false);
                            })
                        }
                    }
                }


                let moveTail = this.window.space.manager.moveTail;
                if (!item && moveTail) {

                    if (moveTail.index) {
                        item = moveTail.window.items[moveTail.index]
                    } else {
                        item = moveTail.window.items;
                        item.forEach((itm,index)=>{
                            itm.active = (index == moveTail.window.active_item)
                        })
                    }
                }

            } catch (e) {
                console.log(e);
            }
            if (!item)
                return;

            switch (this.ord) {
                case "add":
                    this.window.add_item(item)
                    break;
                case "left":
                case "right":
                    if (this.horizontal)
                        this.window.split(item, this.ord == 'left');
                    else {
                        this.window.insert_parent(item, this.ord == 'left')
                    }
                    break;
                case "top":
                case "bottom":
                    if (!this.horizontal)
                        this.window.split(item, this.ord == 'top');
                    else {
                        this.window.insert_parent(item, this.ord == 'top')
                    }
                    break;
            }


        },
        dragenter(event) {
            this.ord = event?.target?.dataset?.ord;
            this.position = { //x offsetX movementX pageX screenX clientX layerX
                target: this.ord,
                x: event.pageX,
                y: event.pageY
            }
            this.onDrag = true;
            return true;
        },
        dragleave(event) {
            let relatedTarget = event.relatedTarget;

            while (relatedTarget) {
                if (relatedTarget == this.$el) return;

                relatedTarget = relatedTarget.parentNode;
            }

            this.onDrag = false;
        },
        onTab(item) {
            this.window.setActive(item);
        },
        dragStart(event) {
            let manager = this.window.space.manager;

            manager.moveTail = {
                window: this.window,
                index: event.target.dataset.index
            }
            event.dataTransfer.effectAllowed = 'move';
        },
        start_test(event, contract) {
            event.dataTransfer.setData("tile", JSON.stringify([
                { tile: "order_book", contract: { id: 1011233, product_name: { "ru-RU": "test" } }, params: {} },
                { tile: "proposal", contract: { id: 1011233, product_name: { "ru-RU": "test" } }, params: {} },
            ]));

        },
        dragend(event) {
            let manager = this.window.space.manager;
            manager.moveTail = undefined
        },
        tile(item) {

        },
        keyup(e) {
            // console.log(e.target)
        },
        onSetGroup(group) {
            this.current_item.set_group(group);
            this.dropdownGroup = false;
        },
    },
    components: {
        group: groupComponent
    },
    template: `
        <iac-maximize tabindex1="1" v-on:keyup.native.prevent.stop="keyup" :parent='true' :maximize='full' class='terminal-window document' :class='current_item && current_item.class' v-on:drop.native.prevent="drop" v-on:dragover.native.prevent="dragover" v-on:dragenter.native.prevent='dragenter' v-on:dragleave.native.prevent='dragleave' >
            <div v-if='0' draggable='true' v-on:dragstart='e=>start_test(e)'>123</div>
            <ui-layout-tab :title='current_item.title' :active='window.active_item' v-on:tab='onTab' name='doc' :sync='false' direction='bottom' v-bind:class="classes" :show_all='true' :toolbar='tab_toolbar'
                v-on:closeItem='closeItem'
                v-on:dragstart="dragStart"
                v-on:dragend='dragend'
            >  
                <group v-if='current_item.set_group' :space='window.space' :group='current_item.group'  slot='before_title' v-on:group='onSetGroup'  />
                <span v-if='current_item.select_contract' class='search' slot='before_title' v-on:click='current_item.select_contract'>
                    <icon>search</icon>
                </span>
                <ui-layout-group :toolbar='item.toolbar'  class='thin-scroll' style='display: flex;' :label='"tile."+item.label' v-for='item,key in window.tile_items'>
                    <component v-if='current_item.contract || (!current_item.meta.required_contract && !current_item.required_contract)' :is='item.component' v-bind='item.params' :model='item' v-on:params='current_item.set_params' v-on:contractItem='current_item.set_contract' />            
                    <div v-else class='empty'>
                        <div class='arrow'>
                         <svg xmlns="http://www.w3.org/2000/svg" width="89" height="132" fill="none"><path fill="#62A1FF" d="M7.278.308a1 1 0 0 1 1.414-.03l6.494 6.232a1 1 0 1 1-1.385 1.443L8.03 2.413 2.49 8.187a1 1 0 1 1-1.443-1.385L7.278.308Zm80.4 131.639C61.378 123 8.717 84.305 7 1.021l2-.042c1.697 82.335 53.71 120.362 79.322 129.074l-.644 1.894Z"></path></svg>
                        </div>
                        <div class='content'>
                            <icon>search</icon>
                            <p>{{$t('terminal.select_contract')}}</p>
                            <p>{{$t('terminal.select_contract.desc')}}</p>
                        </div>
                    </div>
                </ui-layout-group>
            </ui-layout-tab> 

            <div :class='"move "+ord' data-ord='add' v-if='onDrag'>
                <div class='mask'/>
                <div v-if='!full && (allow & 2) != 0' data-ord='top'/>
                <div v-if='!full && (allow & 2) != 0' data-ord='bottom'/>
                <div v-if='!full && (allow & 1) != 0' data-ord='left'/>
                <div v-if='!full && (allow & 1) != 0' data-ord='right'/>
            </div>

        </iac-maximize>       
        
        `
}