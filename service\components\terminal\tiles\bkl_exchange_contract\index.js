import { DataSource, Query } from '@iac/data'
import { Http, Language } from '@iac/core'
import ContractSetting from './../exchange_contract/setting'

var dataSourceSearchContext = {
    ignore_listeners: true,
    limit: 50,
    store: {
        ref: "list_of_stock_exchange",
        injectQuery: (params)=>{
           if(params?.query)
            return params;
        },
        context: (context)=>{
            try{
                if(typeof context.product_name == 'string')
                context.product_name = JSON.parse(context.product_name)
            }catch(e){

            }
            return {
                ...context,
                type: "contract",
                get product_name(){
                    return context.product_name && (context.product_name[Language.local] || context.product_name["ru-RU"]);
                },
                $product_name: context.product_name
            }
        }
    }
}

var dataSourceContext = {
    ignore_listeners: true,
    query1: {
        status: {
            value: ["commited","on_sale"]
        }
    },
    store: {
        ref: "groups_of_stock_exchange",
        /* DEV DRAFT
        data: [
            {id:1,cnt:10,children:[],group_name: {"ru-RU": "test"}}
        ],*/
        context: (context) => {
            context.expanded = false;
            return {
                count: context.cnt,
                expanded: false,
                id: context.id,
                type: "group",
                products: undefined,
                companies: undefined,
                get name(){
                    return context.group_name[Language.local] || context.group_name["ru-RU"];
                },
                children: context.children && context.children.map((item) => {
                    context.cnt -= item.cnt;
                    return {
                        expanded: false,
                        id: item.id,
                        cnt: item.cnt,
                        count: item.cnt,
                        type: "sub_group",
                        children: [],
                        products: undefined,
                        companies: undefined,
                        get name(){
                            return context.group_name[Language.local] || context.group_name["ru-RU"];
                        }
                    };
                }),
                cnt: context.cnt,
            };
        }
    }
};

let dataSource = new DataSource(dataSourceContext);
let dataSourceSearch = new DataSource({...dataSourceSearchContext,query: dataSource.query});

let contractItem = {
    props: ["item", "levels", "group_by"],
    data: function () {
        return {
            contract_setting: Vue.Tiling["exchange_contract"].setting,
            general_setting: Vue.Tiling["settings"].general_setting,
        }
    },
    computed: {
        $contract_setting(){
            return this.contract_setting.params
        },
        $general_setting(){
            return this.general_setting.params
        },
        quantities(){
            //if(!this.$contract_setting.expanded){
                return [{
                    price: this.item.start_price,
                    amount: this.item.quantity_lots
                }]
            //}
            return this.item.start || [{}]
        }
    },
    template: `
        <tbody>
            <tr :class='item.type' v-for='quantity,index in quantities'>
                <td class='first' v-if='index==0' :rowspan='quantities.length' :colspan='item.type != "contract" ? 6 : 1'>
                <div style='display: inline-table;height: 100%;'>
                    <div style='display: table-row'>
                        <span style='display: table-cell;' :class='"none "+"none_"+level' v-for='level in levels'>
                        </span>

                        <span style='padding-right: 5px; display: table-cell;' class='icon' v-if='item.type != "contract"'>
                            <icon v-if='item.type == "product"'>field</icon>
                            <icon v-else-if='item.type == "company"'>field</icon>
                            <icon v-else>vscode</icon>
                        </span>
                        <span style='display: table-cell;width: 5px' v-else />

                        <span style='display: table-cell;'>{{item.name || item.id}}</span>
                    </div>
                </div>
                </td>
                <template v-if='item.type == "contract"'>
                    <template v-if='index==0'>
                        <td class='first' :rowspan='quantities.length' v-if='group_by != "products"'><span>{{item.product_name}}</span></td>
                        <td class='first' :rowspan='quantities.length' v-if='group_by == "products"'><span>{{item.company_name}}</span></td>
                        <td :rowspan='quantities.length'>
                            <iac-number :value='item.quantity_in_one_lot' delimiter=' ' part='2' />
                            {{item.unit}}
                        </td>
                    </template>
                    <td><iac-number :value='quantity.amount' delimiter=' ' /></td>
                    <td><iac-number :value='quantity.price'  delimiter=' ' part='2' /></td>
                    
                    
                    <td><iac-number :value='quantity.volume' delimiter=' ' part='2' /></td>
                </template>
            </tr>
        </tbody>
    `
}

let contractContent = {
    props: ["setting","source","source_search","contract"],
    data: function () {
        let dataSource = this.source ? this.source : new DataSource(dataSourceContext)
        return {
            contract_setting: Vue.Tiling["exchange_contract"].setting,
            general_setting: Vue.Tiling["settings"].general_setting,
            dataSource: dataSource,
            searchSource: this.source_search ? this.source_search : new DataSource({...dataSourceSearchContext, query: dataSource.query }),
            group: undefined,
        }
    },
    computed: {
        $contract_setting(){
            return this.contract_setting.params
        },
        $general_setting(){
            return this.general_setting.params
        },
    },
    mounted() {
        if (this.dataSource && !this.dataSource.state && this.dataSource.items.length <= 0) {
            this.$wait(async () => {
                await this.dataSource.load();
            })
        }
    },
    methods: {
        onItem(item) {

            if(item.type == 'contract'){
                this.$emit('item', item)
                return;
            }

            if (!item.children)
                return;

            if (!item.expanded && item.cnt && !item.products) {
                this.$wait(async () => {
                    let { error, data } = await Http.api.rpc("ref", {
                        ref: "list_of_stock_exchange",
                        op: "read",
                        query: this.dataSource.query.queryText,
                        filters: {
                            group_id:  item.type != "sub_group" ? item.id : undefined,
                            subgroup_id: item.type == "sub_group" ? item.id : null,
                            //status: ["commited","on_sale"]
                        },
                        limit: 100
                    })

                    /*DEV DRAFT 
                    data = [{
                        id: 1,
                        product_name: {
                            "ru-RU": "asdasd"
                        },
                        product: {
                            id: 1
                        },
                        product_id: 1,
                        company_id: 1,
                        amount_of_annul_lots: 11,
                        price_for_annul_lots: 11,
                    }]*/

                    if (error) {
                        Vue.Dialog.MessageBox.Error(error)
                    }
                    if (data) {

                        let products = {}
                        let companies = {}
                        data.forEach(item => {


                            try{
                                if(typeof item.product_name == 'string')
                                item.product_name = JSON.parse(item.product_name)
                            }catch(e){

                            }

                            let types = ["annul","extra","new","norealized"]

                            let contract = {
                                id: item.id,
                                type: "contract",
                                product: item.product,
                                company_id: item.company_id,
                                company_name: item.company_name,

                                start_price: item.start_price,
                                quantity_lots: item.quantity_lots,
                                quantity_in_one_lot: item.quantity_in_one_lot,
                                volume: item.volume,
                                unit: item.unit,

                                start: types.map((type)=>{
                                    let start = {
                                        amount: item[`amount_of_${type}_lots`],
                                        price:   item[`price_for_${type}_lot`],
                                    }
                                    return start.amount && start.price && start;
                                }).filter((start)=>{
                                    return start;
                                }),
                                quantity: [
                                    {
                                        start_price: item.start_price,
                                        quantity_lots: item.quantity_lots,
                                        quantity_in_one_lot: item.quantity_in_one_lot,
                                        volume: item.volume,                                        
                                    },
                                    {
                                        start_price: item.start_price*2,
                                        quantity_lots: item.quantity_lots*2,
                                        quantity_in_one_lot: item.quantity_in_one_lot*2,
                                        volume: item.volume*2,                                        
                                    }
                                ],

                                get product_name(){
                                    return item.product_name && (item.product_name[Language.local] || item.product_name["ru-RU"]);
                                },
                                $product_name: item.product_name
                            }

                            products[item.product_id] = products[item.product_id] || {
                                id: item.product_id,
                                get name(){
                                    return item.product_name && (item.product_name[Language.local] || item.product_name["ru-RU"]);
                                },
                                type: "product",
                                expanded: false,
                                children: []
                            }
                            products[item.product_id].children.push(contract)

                            companies[item.company_id] = companies[item.company_id] || {
                                id: item.company_id,
                                name: item.company_name || "Без компаний",
                                type: "company",
                                expanded: false,
                                children: []
                            }
                            companies[item.company_id].children.push(contract)

                        });

                        item.products = products;
                        item.companies = companies;
                        item.expanded = !item.expanded;
                    }
                })
            } else {
                item.expanded = !item.expanded;
            }
        }
    },
    components: {
        contractItem
    },
    render: function (c) {
        if(!this.$contract_setting)
            return;
        let getContractItems = (products) => {
            if (!products)
                return
            if (this.$contract_setting.group_by)
                return Object.keys(products).map((id) => {
                    return products[id]
                })
            return Object.keys(products).reduce((acc, key) => {
                acc.push(...products[key].children)
                return acc;
            }, [])

        }

        let renderItems = (items, levels = [],prod = false) => {
            if (!items)
                return;
            return items.map((item, index) => {
                return [
                    c("contract-item", {
                        class: {
                            active: item.type == 'contract' && this.contract && this.contract.id == item.id
                        },
                        domProps: {
                            draggable: item.type == 'contract',
                        },
                        props: {
                            
                            item: item,
                            levels: [...levels, (((index + 1) == items.length)  && !prod) ? 3 : 2],
                            group_by: !(!this.$contract_setting.search_group && this.searchSource?.query?.queryText) && this.$contract_setting.group_by,
                        },
                        nativeOn: {
                            dragstart: (event)=>{
                                event.dataTransfer.setData("tile", JSON.stringify([
                                    {tile: "order_book",contract: {id: item.id,product_name: item.$product_name}, params: {}},
                                    {tile: "proposal",contract: {id: item.id,product_name: item.$product_name}, params: {}},
                                    {tile: "history",contract: {id: item.id,product_name:item.$product_name}, params: {}},
                                ]));
                            },
                            click: () => {
                                this.onItem(item);
                            },
                        }
                    }),
                    item.expanded && renderItems(item.children, [...levels, ((index + 1) == items.length) ? 0 : 1],item.cnt),
                    item.expanded && renderItems(getContractItems(item[this.$contract_setting.group_by || "products"]), [...levels, ((index + 1) == items.length  && !prod) ? 0 : 1])
                ]
            })
        }

        let $colgroup = [

        ];

        let $thead = c('thead', {}, [c('tr', [
            c('th', {
                attrs: {
                    colspan: 2
                },
                style: "width: 100%"
            },[c("ui-field",{
                style: "margin: 0",
                props: {
                    model: this.dataSource.query.properties.queryText
                }
            })]),
            /*c('th',{
                style: "width: 100%; min-width: 200px; text-align: left"
            }, this.$contract_setting.group_by != "products" ? "Товар" : "Компания"),*/

            c('th', Language.t("exchange.lot.amount")),
            c('th', Language.t("exchange.lot.count")),
            c('th', Language.t("exchange.price.start")),
            
            c('th', Language.t("amount")),
        ])])
        let $tbody = c('template', {}, [renderItems(this.dataSource.items)])

        return c('table', {
            class: ["iac-exchange-contract",{"striped": this.$general_setting.striped}],
            attrs: {
                cellpadding: 0,
                border: 0,
                cellspacing: 0
            }
    }, [$colgroup, $thead, (this.$contract_setting.search == 'line' && this.searchSource?.query?.queryText) ? renderItems(this.searchSource.items) : renderItems(this.dataSource.items)]);
    }
}

Vue.component("iac-bkl-exchange-contract",contractContent);


let ContractComponent = {
    props: ["model"],
    data: function () {
        return {
            dataSource: dataSource,
            dataSourceSearch: dataSourceSearch
        }
    },
    components: {
        contractContent
    },
    methods: {
        onContractItem(item){
            this.$emit("contractItem",{ 
                id: item.id,
                product_name: item.$product_name
            })
        }
    },
    template: `
        <div class='iac-exchange-contract-tile' >
            <div class='content thin-scroll' style='overflow: auto;flex: 1 1 100%;'><contractContent :contract='model.contract' v-on:item='onContractItem' :source='dataSource'  :source_search='dataSourceSearch' /></div>
        </div>
    `
}
Vue.Tiling("bkl_exchange_contract", {
    component: ContractComponent,
    setting: ContractSetting,
    select_group: true
})