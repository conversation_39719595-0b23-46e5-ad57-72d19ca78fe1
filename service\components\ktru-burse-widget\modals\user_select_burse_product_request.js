import Vue from "vue"
import ebpSearchTable from '../components/ebp_search_table'
import ebpSearchTree from '../components/ebp_search_tree'
import { Http, Language } from '@iac/core'
import getUtils from '../utils'
import userCreateBurseProductRequest from './user_create_burse_product_request'
let searchTimeoutID = undefined

const SelectBurseProduct = {
  props: {
    onSelect: {
      type: Function,
      required: true
    }
  },
  components: {
    'ebpSearchTree': ebpSearchTree,
    'ebpSearchTable': ebpSearchTable
  },
  data() {
    return {
      searchQuery: "",
      searchResult: undefined,
      searchLoading: false,
      ...getUtils()
    }
  },
  watch: {
    searchQuery(_newVal, _oldVal) {
      clearTimeout(searchTimeoutID)
      searchTimeoutID = setTimeout(() => this.search(), 300)
    }
  },
  methods: {
    clearQuery() {
      this.searchQuery = ""
      this.searchResult = undefined
      this.searchLoading = false
    },
    async search() {
      const { searchQuery: query = "" } = this
      if (query.length < 3) {
        return
      }
      this.searchLoading = true
      const { error, data } = await Http.api.rpc("ref", {
        ref: "ref_enkt_burse_product",
        op: "read",
        limit: 15,
        offset: 0,
        query,
        fields: ['id', 'product', 'meta']
      })
      if (!error && data) {
        data.forEach(item => {
          delete item.__schema__
          item.product.id = item.id
          item.product.meta = item.meta
          delete item.id
          delete item.meta
        })
        this.searchResult = data
      }
      this.searchLoading = false
    },
    async openEditor(currentItem) {
      const editedProduct = await userCreateBurseProductRequest.Modal({ size: "lg", currentItem, onSelect: this.onSelect })
      this.Close(editedProduct)
    },
    async setCurrentItem(item = undefined) {
      const currentItem = item
      if (currentItem) {
        this.openEditor(currentItem.product)
      } else {
        this.Close()
      }
    },
    async createEmptySpec() {
      const name = this.getNamesFromOldKTRUItem()
      const newProduct = {
        id: "",
        skp: "",
        name,
        tnved: "",
        units: [],
        group_id: null,
        properties: []
      }

      if (await Vue.Dialog.MessageBox.Question(Language.t("product.create_new.spec.question")) == Vue.Dialog.MessageBox.Result.Yes) {
        const KTRUProduct = await Vue.Dialog.SelectProduct.Modal({ size: 'right', mode: "spec" })

        if (KTRUProduct) {
          const { units, properties } = this.getPropsAndUnitsFromOldKTRU(KTRUProduct.properties)
          const name = this.getNamesFromOldKTRUItem(KTRUProduct.name)

          newProduct.name = name
          newProduct.units = units
          newProduct.properties = properties
          newProduct.motherland = {
            source: 'ktru',
            id: KTRUProduct.code
          }
          this.openEditor(newProduct)
        }
      } else {
        this.openEditor(newProduct)
      }
    },
    getNamesFromOldKTRUItem(oldNames = []) {
      const name = { "ru-RU": "", "en-US": "", "uz-UZ@cyrillic": "", "uz-UZ@latin": "" }
      oldNames.forEach(nameItem => {
        if (nameItem.LOCALE == "ru_RU") {
          name["ru-RU"] = nameItem.VALUE
        } else if (nameItem.LOCALE == "en_US") {
          name["en-US"] = nameItem.VALUE
        } else if (nameItem.LOCALE == "uz_UZ@latin") {
          name["uz-UZ@latin"] = nameItem.VALUE
        } else if (nameItem.LOCALE == "uz_UZ@cyrillic") {
          name["uz-UZ@cyrillic"] = nameItem.VALUE
        }
      })
      return name
    },
    getPropsAndUnitsFromOldKTRU(oldProps) {
      const units = [], properties = []
      const findMarkers = ["Единица измерения", "Бирлик", "Birlik", "Unit"].map((item) => item.toLowerCase());

      oldProps.forEach(prop => {
        if (findMarkers.includes(prop.data[0].VALUE.toLowerCase())) {
          prop.values.forEach(val => {
            const name = this.getNamesFromOldKTRUItem(val.data)
            units.push({ name: name, ratio: 1 })
          })
        } else {
          const propName = this.getNamesFromOldKTRUItem(prop.data)
          const values = prop.values.map(val => {
            const name = this.getNamesFromOldKTRUItem(val.data)
            return { name: name }
          })
          properties.push({ name: propName, values })
        }
      })
      return { units, properties }
    }
  },
  mounted() {
    setTimeout(() => this.$refs?.search?.focus?.(), 300)
  },
  template: `
  <div>
    <header>{{$t('classifier_of_goods_works_services_burse')}}</header>
    <main style='display: flex; padding: 0;'>
      <div class='iac--ktru-burse' @keydown.esc="e=>setCurrentItem()">
        <div class="iac--ktru-burse__search">
          <div>
            <icon v-if="searchLoading" class="iac--to-spin">spinner</icon>
            <icon v-else>search</icon>
          </div>
          <input ref="search" :placeholder="$t('search_for_product')" v-model="searchQuery" @keydown.enter="search"/>
          <button v-if="searchQuery.length || searchResult " @click="clearQuery">
            <icon>delete</icon>
          </button>
        </div>
        <div class="iac--ktru-burse__add_new_burse_product">
          <button @click="createEmptySpec">
            {{$t('product.create_new')}}
          </button>
        </div>
        <ebpSearchTable v-if="searchResult" :searchResult="searchResult" @setCurrentItem="setCurrentItem"/>
        <ebpSearchTree v-else @setCurrentItem="setCurrentItem"/>
      </div>
    </main>
  </div>
  `
}

export default Vue.Dialog(SelectBurseProduct)