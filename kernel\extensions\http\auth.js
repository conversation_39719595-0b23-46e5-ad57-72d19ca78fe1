import Context from "../../context";

export class Auth {
    static request(config) {
        config.headers = config.headers || {};
        let access_token = Context.User.access_token;
        if (access_token) {
            if(access_token.length > 4*1024){
                Vue.Dialog.MessageBox.Error("Ошибка авторизации.\nОбратитесь пожалуйста к администратору.")
                Context.User.logOut()
            }else{
                config.headers['Authorization'] = `Bearer ${access_token}`    
            }            
        }
        return config;
    }

    static async responseError(rejection) {
        let { response, config, handler } = rejection;

        

        if (!config.isDuplicateRequest && (response.error && response.error.status == 401)) {
            config.isDuplicateRequest = true;

            
            // Обновляем токен
            let error = await Context.User.refreshToken();
            
            if(!error){
                config.isDuplicateRequest = false;
                return await handler();
            }else{
                console.log("refreshToken ERROR: ",error)    
            }
        }else{
            config.isDuplicateRequest = false;
        }
        return response;
    }
}