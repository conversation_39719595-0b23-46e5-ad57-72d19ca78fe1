import { DataSource, RemoteStore, RefStore, Query, Entity, ArrayStore } from "@iac/data";
import { Http, Language } from '@iac/core'
import { Context } from '@iac/kernel'
export default class Item extends Entity {
    constructor(context = {}, position) {
        super(context);

        this.position = position;
        //this.parent = position;

        this.category = context.category
        this.type = context.type

        this.category_id = position.category_id && (typeof position.category_id == 'string' ? position.category_id : position.category_id.id);

        this.amount = context.amount;
        this.unit = context.unit;
        this.price = context.price;
        this.actions = context.actions;

        this.id = context.id;
        this.product_id = context.product_id;
        this.product_name = context.product_name;
        this.product_properties = context.product_properties;
    }

    async onChangeProperty(property) {
        if (!this.id)
            return;

        if (property.name == "currency")
            return;

        if (property.name == "product_name")
            return;

        let { error, data } = await Http.api.rpc("ref_schedule", {
            ref: "schedule_goods",
            op: "update",
            filters: {
                id: this.id
            },
            data: {
                [property.name]: property.value != undefined ? property.value : null
            }
        })

        if (error && error.data) {
            error.data.forEach(error => {
                if (error.name == property.name) {
                    property.property.status = {
                        type: 'error',
                        message: error.message,
                        data: error.data
                    }
                }
            });
        }
    }

    props() {
        let $this = this;
        return {
            category: {
                type: 'hidden'
            },
            type: {
                type: 'hidden'
            },
            product_id: {
                type: 'entity',
                group: '<product>',
                label: '!product_id',
                hidden: () => {
                    return true;// this.id || this.product_name
                },
                dataSource: new DataSource({
                    search: "product_name",
                    query: new Query({
                        parent_id: {
                            value: $this.category_id && `0.${$this.category_id}`
                        },
                        product_name: {

                        }
                    }),
                    store: new RemoteStore({
                        method: "tree_products",
                        context: (context) => {
                            context.select_btn = false;
                            return context
                        },
                    })
                })
            },
            type_item: {
                dataBind: {
                    property: 'value',
                    name: 'product_id',
                    field: 'category_type',
                },
                hidden: true,
            },
            amount: {
                label: '!amount',
                type: 'float',
                group: '!t-',
                required: true,
                bind: {
                    suffix: 'unit'
                },
                readonly: () => {
                    return this.position.status != 'draft' || this.position.proc_id || !Context.Access.policy["schedule_create"]
                }
            },
            unit: {
                label: '!unit',
                type: "hidden",
                dataSource: this.product_name ? [this.unit] : 'ref_unit',
            },
            price: {
                label: '!price_unit',
                type: 'float',
                group: '!t-',
                required: true,
                bind: {
                    suffix: 'currency.id'
                },
                readonly: () => {
                    return this.position.status != 'draft' || this.position.proc_id || !Context.Access.policy["schedule_create"]
                }
            },
            currency: {
                label: '!currency',
                type: "hidden",
                dataSource: 'ref_currency',
                //required: true,
                value: $this.position.currency,
                dataBind: {
                    model: $this.position,
                    property: 'value',
                    name: 'currency',
                },
                readonly: true,
            }

        }
    }

    async save() {
        let fields = this.fields.filter((field) => {
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).reduce((prev, curr) => {
            if (curr.readonly && curr.name !== 'unit') {
                if (typeof curr.readonly != 'function' || curr.readonly()) { return prev; }
            }

            prev[curr.name] = curr.value && curr.value.exp ? curr.value.exp.value : curr.value

            return prev
        }, {})

        fields.category_id = undefined;
        fields.positions_id = this.position.id;

        let product_id = this.properties.product_id.value && this.properties.product_id.value.exp ? this.properties.product_id.value.exp.value : this.properties.product_id.value
        fields.product_id = this.product_name ? this.product_id : (product_id && (product_id.split('.')[2]));

        fields.product_name = this.product_name
        fields.product_properties = this.product_properties
        fields.type_item = this.type_item || 0;

        let { error, data } = await Http.api.rpc("ref_schedule", {
            ref: "schedule_goods",
            op: "create",
            data: fields
        })

        if (error) {
            if (!this.setError(error) && error.message) {
                Vue.Dialog.MessageBox.Error(error)
            }
            return {
                error
            }
        }
        return {
            data
        }
    }
}