import Vue from 'vue';

const Component = {
  props: ['item'],
  template: `
    <article class='iac-card iac-card--big-news'>
      <div class='news-meta'>  
        <iac-date class='news-meta__date' :date='item.created_at' withoutTime />
        <router-link v-if='item.category' :to='"/news?category_id=" + item.category_id'
          class='ui-tag-item'>{{ item.category }}</router-link>
      </div>
      <h3 class='section-title mb-12 text-black'>
        <router-link :to='"news/" + item.id' class='link-inherit'>{{ item.title }}</router-link>
      </h3>
      <div v-html='item.digest_txt' class='just-text'></div>
    </article>
  `,
};

Vue.component('widget-big-news', Component);
