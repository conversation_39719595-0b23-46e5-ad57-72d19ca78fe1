
import Store from './store'
import Http from '../../core/src/http';

export default class RefStore extends Store {
  static Introspect = false;
  constructor(options) {
    super(options);
    this.host = options.host || Http.default;
    this.method = options.method || "ref";
    this.ref = options.ref;
    this.params = options.params || {};
    this.op = options.op || 'read'

    this.actions = [
      {
        label: "ref introspect",
        btn_type: "warning",
        handler: async () => {
          let { error, data } = await this.introspect()
          if (error) {
            Vue.Dialog.MessageBox.Error(error);
          } else if (data) {
            let info = JSON.stringify(data, null, '\t')
            Vue.Dialog({
              props: ["message"],
              template: `
                <div><main>
                <ui-scroller style='border: 1px solid #ccc;'><pre><code>{{message}}</code></pre></ui-scroller>
                </main>
                <footer><ui-btn type='secondary' v-on:click.native='Close()'>{{$t("close")}}</ui-btn></footer>
                </div>
              `
            }).Modal({ size: "lg", message: info })
          }
        },
        hidden: () => {
          return !RefStore.Introspect;
        }
      }
    ]
  }

  async byKeys(keys) {
    if (keys == undefined)
      return;

    let limit = 1;
    if (Array.isArray(keys)) {
      limit = keys.length;
    }

    let key = Array.isArray(this.key) ? this.key[0] : this.key

    if (this.ref) {
      let { error, data } = await this.host.rpc(this.method, {
        op: this.op,
        ref: this.ref,
        filters: {
          [key]: keys,
          ...this.params,
        },
        limit: limit,
        //offset: 0,
      });
      return data
    }
    return key;
  }

  async byKey(key) {
    if (key == undefined)
      return;

    let data = await this.byKeys(key);
    return Array.isArray(data) ? this.context(data[0]) : this.context(data)
  }

  async queryByOptions(options) {
    options = { ...options } || {};
    if (options.query.queryText) {
      options.search = options.query.queryText;
      options.query.queryText = undefined
    }

    if (options.query.order_by) {

      options.order_by = options.query.order_by;
      options.query.order_by = undefined
    }

    let { take = 10, skip = 0 } = options || {};
    let params = await this.injectQuery({
      ref: this.ref,
      op: this.op,
      query: options.search,
      order_by: options.order_by,
      limit: take,
      offset: skip,
      filters: {
        ...this.params,
        ...options.query
      }
    })

    if (!params)
      return { error: {} };

    if (params && params.filters) {
      params.filters.queryText = undefined
      params.filters.order_by = undefined
    }
    
    let {error, data} = await this.host.rpc(this.method, params, {
      signal: options.signal
    });

    if (!error && data) {
        data = data.map((context) => {
            return this.context(context);
        })
    }

    return {error, data}
  }

  async introspect() {
    return await this.host.rpc(this.method, {
      op: "introspect",
      ref: this.ref,
    });
  }

  async count(options) {
    options = { ...options } || {};
    if (options.query.queryText) {
      options.search = options.query.queryText;
      options.query.queryText = undefined
    }

    if (options.query.order_by) {
      options.order_by = options.query.order_by;
      options.query.order_by = undefined
    }

    let params = await this.injectQuery({
      op: "count",
      ref: this.ref,
      query: options.search,
      order_by: options.order_by,
      filters: {
        ...this.params,
        ...options.query
      }
    })

    return await this.host.rpc(this.method, {
      op: "count",
      ref: params.ref,
      query: params.query,
      order_by: params.order_by,
      filters: params.filters
    }, {
      signal: options.signal
    });
  }

  async get(filters) {
    // let key = Array.isArray(this.key) ? this.key[0] : this.key

    return await this.host.rpc(this.method, {
      ref: this.ref,
      op: "get",
      filters: {
        ...filters
      }
    }, {

    });
  }

  static async get(name, query) {
    console.log("Если QA Увидят это сообщения то скажите куда вы клацнули? А лучше, не закрывая браузер зовите Константина")
    return await Http.default.rpc("ref", {
      ref: name,
      op: "read",
      filters: {
        ...query
      }
    });
  }
}
