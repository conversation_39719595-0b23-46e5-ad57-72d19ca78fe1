import { DataSource } from '@iac/data'
import { Http, Language } from '@iac/core'
import { Config } from '@iac/kernel'
export default [{
    path: 'complaints',
    component: {
        data: function () {
            return {
                source: new DataSource({
                    query: {

                    },
                    store: {
                        ref: "ref_complaints_sip",
                        injectQuery: async (params) => {
                            params.fields = ["inserted_at", "status", "meta"]

                            return params;
                        }
                    },
                    actions: [
                        {
                            label: "nav.complaints_add",
                            hidden: () => {

                            },
                            handler: async () => {
                                await Http.api.rpc("complaints", undefined)
                            }
                        },
                    ],
                    template: {
                        props: ["model"],
                        methods: {
                            show_details() {
                                

                                Vue.Dialog({
                                    props: ["model"],
                                    data: function () {
                                        let model = this.model;
                                        let forms = [
                                            "",
                                            "Физическое лицо",
                                            "Юридическое лицо"
                                        ]
                                        let sips = [
                                            "",
                                            "Узбекская республиканская товарно-сырьевая биржа",
                                            "ООО \"XT-Xarid Texnologiyalari\"",
                                            "Электронный кооперационный портал Республики Узбекистан",
                                            "Электронная тендерная платформа проведения гос.закупок в сфере строительства"
                                        ]
                                        let indications = [
                                            "","","","","",
                                            "Неправомерное действие заказчика",
                                            "Неправомерное бездействие заказчика",
                                            "Неправомерное действие закупочной комиссии",
                                            "Неправомерное бездействие закупочной комиссии",
                                            "Неправомерное действие закупочной комиссии (Членов)",
                                            "Неправомерное бездействие закупочной комиссии (Членов)",
                                            "Неправомерное действие Оператора",
                                            "Неправомерное бездействие Оператора",
                                            "Нарушение закупочной процедуры",
                                            "Необоснованное изменение срока подачи предложений заказчиками",
                                            "Необоснованное непринятие или отстранение заказчиком предложения участников",
                                            "Неправомерный выбор заказчиком неконкурентных способов осуществления государственных закупок",
                                            "Разглашение заказчиком информации о составе участников государственной закупки",
                                            "Необоснованное ограничение заказчиком числа участников или завышение требований к их квалификации, иные формы недопущения, ограничения или устранения конкуренции, за исключением случаев, установленных актами законодательства",
                                            "Наличие сговора участников с целью искажения цен или результатов отбора участников",
                                            "Предоставление или распространение недостоверной или искаженной информации, а также необоснованное ограничение доступа к информации о государственных закупках",
                                            "Прочие"
                                        ]
                                        return {
                                            fields: [
                                                {
                                                    name: "core",
                                                    label: "!",
                                                    type: "layout-static",
                                                    value: [
                                                        { title: "name_of_applicant", value: model.applicant_full_name },
                                                        { title: "email", value: model.email },
                                                        { title: "company_inn_short", value: model.tin },
                                                        { title: "pinfl", value: model.pnfl },
                                                        { title: "type_of_applicant", value: forms[model.form] },
                                                        { title: "telephone_number", value: model.phone },
                                                        { title: "address", value: model.address },
                                                        { title: "name_of_special_information_portal", value: sips[model.sip_id] },

                                                        { title: "an_indication_of_the_contested_action", value: indications[model.indication_id] },
                                                        { title: "the_essence_of_the_complaint", value: model.complaint_text },
                                                    ]
                                                },
                                                {
                                                    name: "lot",
                                                    label: "-lot_number",
                                                    type: "link",
                                                    attr: {
                                                        text: `№ ${model.lot_number}`,
                                                        to: `/procedure/${model.lot_number}`
                                                    }
                                                },
                                                {
                                                    name: "files",
                                                    label: "-attachment",
                                                    type: "file",
                                                    readonly: true,
                                                    multiple: true,
                                                    hidden: () => {
                                                        return !model.files || model.files.length <= 0
                                                    },
                                                    value: model.files,
                                                    meta: {
                                                        url: (value) => {
                                                            if (!value.id)
                                                                return;
                                                            return `${Config.api_server}/file/${value.id}`
                                                        },
                                                    }
                                                },
                                                {
                                                    name: "source",
                                                    label: "!",
                                                    type: "markdown",
                                                    value: `[${Language.t('source')}](${model.link})`
                                                }
                                            ]
                                        }
                                    },
                                    template: `
                                        <div>
                                            <main>
                                                <ui-layout :fields='fields' />
                                            </main>
                                            <footer>
                                                <ui-btn type='secondary' v-on:click.native='Close'>{{$t("close")}}</ui-btn>
                                            </footer>
                                        </div>
                                    `
                                }).Modal({
                                    size: "right",
                                    model: this.model.meta
                                })
                            }
                        },
                        template: `
                        <ui-data-view-item class='complaints-item' :model='model'>
                            <template slot='header'>
                                <div>
                                    <iac-date v-if='model.inserted_at' :date="model.inserted_at" withoutTime />
                                </div>
                                <div>{{model.status}}</div>
                            </template>

                            <template slot='description'>
                                <div class='clamp_2' v-if='model.meta && model.meta.complaint_text'>
                                    <label>{{$t('complaint_text')}}: </label>
                                    <span :title='model.meta.complaint_text'>{{ model.meta.complaint_text }}</span>
                                </div>
                                <div>
                                    <a v-if='1' href='#' v-on:click.prevent.stop='show_details'>{{$t('detailing')}}</a>
                                    <a v-else-if='model && model.meta && model.meta.link' :href='model.meta.link' target='_blank'>{{$t('detailing')}}</a>

                                </div>
                            </template>
                            <template slot='props'>
                                <div v-if='model.meta && model.meta.lot_number'>
                                    <label>{{ $t('procedure') }}:</label>
                                    <div>{{model.meta.lot_number}}</div>
                                </div>
                            </template>

                        </ui-data-view-item>
                    `}
                })
            }
        },
        template: `
        <div class='page-cart'>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('nav.complaints')}}</li>
                </ol>
                <div class='title'>
                    <h1 style='margin: 0;'>{{$t('nav.complaints')}}</h1>
                </div>
            </iac-section>
            <iac-section>
                <ui-data-view  v-if='$settings.complaints' :dataSource='source'/>
                <ui-error v-else class='page' code='403' :message='$t("NoAccess")'  />
            </iac-section>
        </div>
      `
    }
}]