import { Http } from '@iac/core';

export default {
  data() {
    return {
      bigNews: undefined,
      otherNews: undefined,
      error: undefined,
    };
  },
  async mounted() {
    let { data, error } = await Http.api.rpc('ref_get_latest_digest', {
      select: [
        'id',
        'title',
        'digest_txt',
        'category',
        'category_id',
        'created_at',
      ],
      limit: 4,
    });
    data = data ?? [];
    [this.bigNews, ...this.otherNews] = data;
    this.error = error;
  },
  template: `
    <section v-if='bigNews !== undefined' class='section-block bg-white'>
      <div class='iac-container'>
        <h2 class='section-title'>{{ $t('link.news') }}</h2>
        <div class='iac-row'>
          <div class='iac-col-md-7'>
            <widget-big-news :item='bigNews' :style='"background-image: url("+($settings.news._background_img)+");"'  />
          </div>
          <div v-if='otherNews.length' class='iac-col-md-5'>
            <div class='iac-card iac-card--latest-news'>
              <div class='iac-card__header iac-card__row'>
                <h3 class='iac-card__title'>{{ $t('latest_news') }}</h3>
              </div>
              <div class='iac-card__body iac-card__row'>
                <article v-for='item in otherNews' class='iac-card__item'>
                  <div class='news-meta'>  
                    <iac-date class='news-date news-meta__date' :date='item.created_at' withoutTime />
                    <router-link v-if='item.category' :to='"/news?category_id=" + item.category_id'
                      class='ui-tag-item'>{{ item.category }}</router-link>
                  </div>
                  <h3 class='news-title'>
                    <router-link :to='"news/" + item.id' class='link-inherit'>{{ item.title }}</router-link>
                  </h3>
                </article>
              </div>
              <div class='iac-card__footer'>
                <router-link to='/news' class='ui-btn ui-btn-primary d-block'>{{ $t('read_all_news') }}</router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section v-else-if='error !== undefined' class='section-block bg-white'>
      <div class='iac-container'>
        <ui-error :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
      </div>
    </section>
  `,
};
