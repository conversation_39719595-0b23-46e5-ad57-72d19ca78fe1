import { Entity } from '@iac/data'
import { Http, Language } from '@iac/core'

class ModelInfoField extends Entity {
    constructor(context = {}) {
        super(context)

        this.proc_id = context.proc_id;
        this.lot_id = context.lot_id;
        this.item_id = context.item_id;

        this.group = context.group;
        this.order = context.order;

    }
    props() {

        return {
            value: {
                label: "field_setting.info_value",
                group: 'field_setting.general',
                type: "text",
                required: true
            },
            file: {
                label: "field_setting.add_file",
                group: 'field_setting.general',
                type: "file",
                multiple: true
            },
        }
    }

    async save() {
        this.properties.value.status = undefined;
        let fieldsError = false;

        let fields = [];
        // грузим файлы
        if (this.file && this.file.length > 0) {

            let errorData = [];

            for (let index in this.file) {
                let fieldData = this.file[index];
                if (!fieldData.file)
                    continue;
                let formData = new FormData();
                formData.append('scope_tender_participant', this.proc_id);
                formData.append('data', fieldData.file, fieldData.file.name);

                let { data, error } = await Http.upload.form('tender/attach', formData);
                if (error) {
                    errorData.push({
                        i: index,
                        message: error.message
                    })
                } else {
                    this.file[index] = {
                        id: data.uuid,
                        name: data.meta.name,
                        meta: {
                            "type": data.meta.type,
                            "content_type": data.meta.content_type,
                            "type_group": data.meta.group,
                            "size": data.meta.size
                        }
                    }
                }
            }

            if (errorData && errorData.length > 0) {
                fieldsError = true;
                this.properties.file.status = {
                    type: "error",
                    data: errorData
                }
            } else {
                fields.push({
                    type: "file",
                    multiple: true,
                    value: this.file
                })
            }
        } else if (!this.value) {
            fieldsError = true;
            this.properties.value.status = {
                type: "error"
            }
        }
        if (fieldsError) {
            return {
                error: {
                    message: Language.t("bad_params")
                }
            }
        } else if (this.value) {
            fields.unshift({
                type: "text",
                value: this.value
            })
        }

        for (let index in fields) {
            let field = fields[index]

            field.group = this.group;
            field.order = this.order;
            field.offer_dub = false;
            field.offer_required = false;
            field.part_hide = false;

            let { error, data } = await Http.proc.rpc("set_field", {
                proc_id: this.proc_id,
                lot_id: this.lot_id,
                item_id: this.item_id,
                field: field
            });

            if (error) {
                return {
                    error: error
                }
            } else {
                fields[index] = data;
            }

        }

        return {
            data: fields
        }
    }
}


Vue.Dialog("AddInfoField", {
    props: ["group", "order", "proc_id", "lot_id", "item_id"],
    data: function () {
        return {
            model: new ModelInfoField({
                group: this.group,
                order: this.order,
                proc_id: this.proc_id,
                lot_id: this.lot_id,
                item_id: this.item_id
            }),
        }
    },
    methods: {
        async create() {
            this.wait(async () => {
                let { error, data } = await this.model.save();
                if (error) {
                    await Vue.Dialog.MessageBox.Error(error)
                } else {
                    this.Close(data)
                }
            });

        }
    },
    template: `
        <div>
            <main>
                <ui-layout v-if='model' :fields='model.fields'/>
            </main>
            <footer>
                <ui-btn type='secondary' @click.native='Close()'>{{$t('cancel')}}</ui-btn>
                <ui-btn type='primary' v-on:click.native='create'>{{$t('create')}}</ui-btn>
            </footer>
        </div>
    `
});