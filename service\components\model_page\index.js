import { Http } from '@iac/core'
import { Entity, Property } from '@iac/data'
import { ecp } from '@iac/kernel'

class ModalProperty extends Property {
    constructor(context) {
        super(context)

        if (context.attributes.type == "action" && context.attributes.actions) {
            context.attributes.actions.forEach((action) => {
                if (action.subscribe) {
                    action.inject = async () => {
                        let result = await ecp.subscribe("Я люблю тех кому я нравлюсь... У них хороший вкус");
                        if (!result)
                            return {
                                error: {
                                    code: "AbortError",
                                    message: "AbortRequest"
                                }
                            }
                        if (result.error) {
                            await Vue.Dialog.MessageBox.Error(result.error);
                            return { error: result.error }
                        }
                        return {
                            data: { pkcs7B64: result.data }
                        }
                    }
                }
            })
        }
    }
}

class Model extends Entity {
    constructor(context) {
        context.props = context.fields,
            super(context)

        this.title = context.title;
    }

    get propertyModel() {
        return ModalProperty;
    }

}



var Page = {
    props: ["scope"],
    data: function () {
        return {
            model: undefined,
            error: undefined
        }
    },
    watch: {
        "$route.params.page": {
            immediate: true,
            async handler(value, oldValue) {
                this.update_page();
            }
        }
    },
    methods: {
        update_page() {
            this.$wait(async () => {
                let { error, data } = await Http.api.rpc("page", {
                    page: this.$route.params.page,
                    scope: this.scope,
                    url: window.location.href,
                })
                this.model = new Model(data);
                this.error = error;
            })
        }
    },
    template: `
        <div>
            <iac-page v-if='model' :title='model.title'>
                <iac-section style='flex: 0 0 auto;'>
                    <ui-layout :fields='model.fields' />
                </iac-section>    
            </iac-page>
            <ui-error class='page' v-else-if='error' :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
        </div>
    `
}

export default Page;