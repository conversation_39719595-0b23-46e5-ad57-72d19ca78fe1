import { DataSource, RemoteStore, RefStore, Query } from '@iac/data'
import { Context } from '@iac/kernel';
import { Http, Language } from '@iac/core';

export default {
    data: function () {
        return {
            searchDataSource: new DataSource({
                request_count: true,
                query: new Query({
                    role: {
                        type: 'entity',
                        has_del: true,
                        dataSource: [
                            { id: "buyer", name: "company.buyer" },
                            { id: "seller", name: "company.seller" },
                            //{ id: 'commercial', name: 'company.commercial' },
                        ]
                    },

                    liquidated_company_type: {
                        label: "archive_tab", 
                        type: 'entity',
                        has_del: true,
                        dataSource: [
                            { id: "state", name: "liquidated.state" },
                            { id: "corporate", name: "liquidated.corporate" },
                        ]
                    }
                }),
                store: new RefStore({
                    method: 'company_ref',
                    ref: "public_procurement_subjects",
                    injectQuery:params=>{
                        params.a_q=params.queryText
                        delete params.queryText
                        return params
                    },
                    context: (context) => {

                        const icon = {
                            block: 'delete',
                            unblock: 'ok',
                        };

                        context.actions = [];

                        if (Context.Access.policy['company_edit_all']) {
                            context.actions.push({
                                label: 'set_roles',
                                handler: async () => {
                                    await Vue.Dialog.MessageBox.Success(Language.t('under_development'));
                                }
                            });
                        }

                        if (Context.Access.policy['company_block'] || Context.Access.policy['company_unblock']) {
                            context.actionType = context.blocked ? 'unblock' : 'block';
                            context.actions.push({
                                label: context.actionType,
                                icon: icon[context.actionType],
                                handler: async () => {
                                    const { error, data } = await Http.api.rpc(`company_${context.actionType}`, { 'company_id': context.id });
                                    if (error === undefined) {
                                        await Vue.Dialog.MessageBox.Success(data.message);
                                        this.searchDataSource.reload();
                                        return;
                                    }
                                    Vue.Dialog.MessageBox.Error(error);
                                }
                            })
                        }


                        return context;
                    }
                }),
                template:'template-company'
            })
        }
    },
    template: `
        <div>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('hp.registries.link1')}}</li>
                </ol>
                <h1>{{$t('hp.registries.link1')}}</h1>
            </iac-section>
            <iac-section>
                <ui-layout-group>
                    <ui-data-view :dataSource='searchDataSource'/>
                </ui-layout-group>
            </iac-section>
        </div>
    `
}