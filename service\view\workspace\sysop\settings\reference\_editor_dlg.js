import Model from './model'

export default Vue.Dialog({
    props: ["item", "introspect", "scheme"],
    data: function () {
        return {
            model: new Model(this.item, this.introspect, this.scheme)
        }
    },
    methods: {
        save() {
            //await this.model.save();
            //this.Close(this.model)
            //this.item.name = this.model.name
            this.$wait(async () => {
                let { error, data } = await this.model.save();
                if (!error)
                    this.Close(data)
            })

        }
    },
    template: `
        <div>
            <header>id: {{model[introspect.id]}}</header>
            <main>
                <ui-layout :fields='model.fields'/>
            </main>
            <footer style='background: linear-gradient(0, #FFF, #FFF0); position: sticky;bottom: 0;margin: 0;padding: 24px;pointer-events: none;'>
                <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('Cancel')}}</ui-btn>
                <ui-btn type='primary' v-on:click.native='save'>{{$t('Save')}}</ui-btn>
            </footer>
        </div>
    `
})