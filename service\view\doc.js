import { Assets } from '@iac/core';

import Menu from './../components/navigation/menu'

Menu.addItems((nav_menu_develop = false)=>{

    if(!nav_menu_develop){
        return []
    }

    let packages = {
        "@iac/core": ["Action","Guid","Language","Marked","ModelProvider", "Util","Assets","Event", "Http"],
        "@iac/data": ["object","model","entity","property"],
        "extensions": ["http"],
        "practice":["Groups"],
    };

    return [
        ...Object.keys(packages).map((key)=>{
            return packages[key].map((name)=>{
                return {
                    group: `documents/${key.replace('/','\\')}`,
                    label: `!${name}`,
                    path: `/doc/${key}/${name.toLocaleLowerCase()}`,
                }
            })
        }).reduce((arr,cur)=>{
            arr.push(...cur)
            return arr
        },[])
    ]
})

let Component = {
    data: function(){
        return {
            loading:true,
            menu: Menu,
         }
    },
    async mounted() {
        this.$wait(async ()=>{
            await Assets.script('iac.doc.js',true);
            await Assets.css('https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/vs.min.css');
            this.loading=false            
        })

    },
    template: `
        <div>
            <doc-component v-if="!loading" :page='$route.params.page' :menu='menu'></doc-component>            
        </div>
    `
}

export default {
    path: '/doc/:page*',
    component: Component
}
