import { Http } from '@iac/core';

export default {
  data() {
    return {
      rawMessage: '',
    }
  },
  props: ['message', 'currentChatId'],
  watch: {
    currentChatId() {
      const { message } = this;
      const { textarea } = this.$refs;
      if (textarea) {
        textarea.innerHTML = message;
        this.rawMessage = message;
      }
    },
  },
  methods: {
    send() {
      if (this.$refs.textarea.innerText.trim() === '') {
        this.$emit('update-message', '');
        return;
      }
      Http.api.rpc('send_chat_message',  {
        'chat_id': this.currentChatId,
        'text': this.message,
      });
      
      this.$emit('update-message', '');
      this.$refs.textarea.innerHTML = '';
    },
    updateMessage({ target }) {
      this.rawMessage = target.innerText.trim();
      this.$emit('update-message', target.innerHTML);
    },
  },
  template: `
    <form class='tender-chat__form' @submit.prevent='send'>
      <div class='tender-chat__textarea' contenteditable='true'
        :data-placeholder='$t("chat.placeholder")'
        @input='updateMessage' ref='textarea'></div>
      <input v-show='rawMessage' class='tender-chat__submit' type='submit'/>
    </form>
  `
}