
import { Entity as IacEntity } from '@iac/data'
import Property from './property'

export default class Entity extends IacEntity {
  constructor(context = {}, ext) {


    if (context.schema) {
      context.props = context.props || {};
      for (let group in context.schema) {
        context.props[group] = {
          type: 'model',
          label: '!',
          group: group,
          value: context.schema[group],
          fields: {}
        }
        for (let field in context.schema[group]) {
          context.props[group].fields[field] = {
            label: `-${field}`,
            type: 'static',
          }
        }
      }
    }

    if (context.props) {
      context.props = Object.keys(context.props).reduce((props, name, i) => {
        let prop = { ...context.props[name] }
        prop.label = prop.label || prop.name;
        prop.name = name;

        props[name] = prop;
        return props;
      }, {})
    }

    super(context, ext);
  }
  get propertyModel() {
    return Property;
  }
}