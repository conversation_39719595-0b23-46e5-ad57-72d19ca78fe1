import { Language } from '@iac/core'

Vue.Dialog("SelectProduct", {
    props: ["size", "mode"],
    data() { return {} },
    methods: {
        onSelect(data) {
            this.Close(data)
        }
    },
    template: `
        <div>
            <header>{{$t('classifier_of_goods_works_services')}}</header>
            <main style='display: flex; padding: 0;'>
                <ktru-widget :mode="mode" :onSelect="onSelect"/>
            </main>
        </div>
    `
});