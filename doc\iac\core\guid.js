export default `
## Guid
> Класс для работы с переменной типа Guid. На даннй момент у него очень урезанный функционал

Примеры создания переменной

    import {Guid} from '@iac/core'

    let guid_0 = new Guid("791a301b-1f4b-4a5f-8b5a-2d31abcc0b5d")
    let guid_1 = new Guid()
    let guid_2 = Guid.newGuid()

Все три метода формирую объект 

    {
        guid: "6edfa29e-1d37-603a-f0ba-4e16bf926971" 
    }


> Объект необходим для возможных будущих манипуляций.</br>
> У данного объекта также реализован метод \`toString\`.</br>
> Есть еще статический метод \`Guid._newGuid\` генерирует guid в строковом формате - в проекте не используется


`