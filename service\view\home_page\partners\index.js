import IacCarousel from '../../../components/carousel';

export default {
  data() {
    return {
      carouselOptions: undefined,
    };
  },
  components: {
    IacCarousel,
  },
  mounted() {
    this.carouselOptions = {
      items: 2,
      gutter: 60,
      loop: false,
      rewind: true,
      nav: false,
      controlsContainer: this.$refs.controlsContainer,
      responsive: {
        400: {
          items: 3
        },
        575: {
          items: 3,
        },
        700: {
          items: 4,
        },
        800: {
          items: 5,
        },
        1124: {
          items: 6,
        }       
      }
    }
  },
  template: `
    <section class='section-block'>
      <div class='iac-container'>
        <div class='title-wrap' style='margin-bottom: 20px;'>
          <h2 class='section-title'>{{ $t('partners') }}</h2>
          <div class='tns-controls'
           ref='controlsContainer'>
            <button>Prev</button>
            <button>Next</button>
          </div>
        </div>
        <iac-carousel v-if='carouselOptions' :model='carouselOptions'
          class='mb-13 align-items-center'>
          <a :href='item.href' target='_blank' v-for='item in $settings.partners._list'>
            <img :src='item.img' loading='lazy' :title="item.title" :alt='item.alt || item.title'/>
          </a>

        </iac-carousel>
      </div>
    </section>
  `,
}