.page-contract {
  color: #201D1D;
  font-size: 14px;
  line-height: 20px;

  &__chat-btn {
    //margin: 0 16px 0 auto;
  }

  .links {
    border: 1px solid @light-gray;
  }

  .contract-info {
    padding-top: 12px;
    color: @gray;

    >* {
      display: inline-block;

      &:not(:first-child) {
        &:before {
          content: "·";
          font-size: 20px;
          margin: 0 4px;
        }
      }
    }
  }

  .file-body {
    color: @primary-link;
    font-size: 14px;
    line-height: 20px;
    display: flex;
    align-items: center;
    margin: 0 -4px;

    >* {
      margin: 0 4px;

      &:first-child {
        font-size: 16px;
      }

      //&:last-child{

      //}
    }

    >icon {
      &:last-child {
        cursor: pointer;
        color: #201D1D;
        font-size: 12px;
      }
    }

  }

  .row {
    padding: 8px 0;

    label {
      font-size: 14px;
      line-height: 20px;
      color: #919191;
    }

    h2 {
      font-weight: 500;
      font-size: 20px;
      line-height: 24px;
      margin: 0;
      padding: 0 0 4px;
    }

    .total {
      font-weight: 500;
      font-size: 17px;
      line-height: 24px;
    }
  }

  .grid {
    background: #FFFFFF;
    /* Color / Light gray */
    border: 1px solid #F3F3F3;
    box-sizing: border-box;
    border-radius: 4px;
    margin-bottom: 24px;

    >.row {
      padding: 24px;
      margin: 0;

      &:not(:last-child) {
        border-bottom: 1px solid #F3F3F3;
      }
    }

    .graphic {
      overflow-x: auto;
      padding: 0;
      margin-left: -1px;
      margin-top: -1px;

      table {
        width: 100%;
        border-spacing: 0;

        th {
          white-space: nowrap;
          font-size: 13px;
          color: #909090;
          text-transform: uppercase;

          &:last-child {
            text-align: left;
          }
        }

        td {

          font-size: 14px;
          color: #201D1D;

          &:not(:last-child) {
            text-align: center;
            white-space: nowrap;
          }

          &:last-child {
            width: 100%;
          }
        }

        td,
        th {
          padding: 16px;
          border-left: 1px solid #F3F3F3;
          border-top: 1px solid #F3F3F3;
        }
      }
    }
  }

  .requisites {
    display: flex;
    flex-wrap: wrap;
    padding-left: 1px;

    >.grid {
      flex: 1 1 50%;
      margin-left: -1px;
      display: flex;
      flex-direction: column;

      >.fill {
        flex: 1 1 auto;
      }
    }

    >.row {
      flex: 0 0 100%;
    }
  }

  .finance {
    overflow-x: auto;

    table {
      width: 100%;
      border-spacing: 0;
      border: 1px solid #F3F3F3;

      td {
        padding: 5px;

        text-align: right;
        white-space: nowrap;

        &:nth-child(2) {
          width: 100%;
          white-space: normal;
          text-align: left;
        }

        &:not(:first-child) {
          border-left: 1px solid #F3F3F3;
        }
      }

      tr {
        &:not(:first-child) {
          td {
            border-top: 1px solid #F3F3F3;
          }
        }
      }
    }
  }

  .product_name {
    color: @primary-link;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    cursor: pointer;
  }

  .link {
    color: @primary-link;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    cursor: pointer;
  }
}

.extra-contracts {
  display: flex;
  margin: 0;
  border: 1px solid @light-gray;
  padding: 0;
  font-size: 13px;
  list-style: none;
  border-radius: 4px 4px 0 0;
  flex-wrap: wrap;

  &__item {
    border-right: 1px solid @light-gray;
    max-width: 203px;
    flex-grow: 1;
  }

  &__link {
    display: block;
    padding: 20px 10px 17px;
    color: @gray;
    text-align: center;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 0.05em;

    &:hover,
    &.router-link-active {
      color: @primary-link;
    }
  }
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.w-100 {
  width: 100%;
}

.ui-control {
  >.container {
    >span.control {
      display: block;
      line-height: 42px;
    }
  }
}

.mt-20 {
  margin-top: 20px;
}

.iac-dialog-body {

  &--contract {
    margin-top: 8px !important;
    font-size: 14px;
    color: @gray;
    line-height: 1.43;

    p {
      margin: 0 0 16px;
    }
  }
}