import { Entity, Property } from '@iac/data'
import { DataSource, ArrayStore, Query, RemoteStore } from '@iac/data'
import { Http, Event, Language } from '@iac/core'
class ModelField extends Entity {
    constructor(context = {}) {
        super(context)
        this.context = context;
        this.object = context.object || {};
        this.type = context.type || 'tender'
    }

    props() {
        return {
            type: {
                group: '!type-',
                type: 'entity',
                dataSource: [
                    { id: 'tender', name: 'tender' },
                    { id: 'contest', name: 'contest' },
                    localStorage.getItem("procedure_test") != 'true' ? undefined : { id: 'test_proc', name: 'Тестовая процедура' }
                ],
                readonly: function(){
                    if(this.model.context && this.model.context.type)
                        return true;
                    return false;
                }
                //value: "tender"
            },
            type_sample: {
                group: '!type-',
                label: "simplified_version",
                type: 'entity',
                dataSource: [
                    { id: true, name: 'Yes' },
                    { id: false, name: 'No' }
                ],
                value: false,
                hidden: function () {
                    if (this.model.type == 'test_proc' || this.model.type.id == 'test_proc')
                        return true;

                    return localStorage.getItem("procedure_sample") == 'true' ? false : true;
                }
            },
            method_marks: {
                type: 'entity',
                label: 'method_marks.edit',
                dataSource: [
                    { id: "min_price", name: "method_marks.min_price" },
                    { id: "rating", name: "method_marks.rating" }
                ],
                value: "min_price",
                hidden: function () {
                    if (this.model.type == 'test_proc' || this.model.type.id == 'test_proc')
                        return true;
                    if (this.model.type_sample == true || this.model.type_sample.id == true)
                        return true;
                }
            },
            /*object_type: {
                type: 'entity',
                label: 'Основание',
                dataSource: [
                    { id: "empty", name: "Создать пустую процедуру" },
                    { id: "purchase", name: "Создать на основе позиции из текущего план-графика" },
                    { id: "claim", name: "Создать по заявке" }
                ],
                value: "empty"
            },
            object_purchase_id: {
                type: 'entity',
                dataSource: new DataSource({
                    displayExp: "name",
                    valueExp: 'product_id',
                    store: new RemoteStore({
                        method: 'get_schedule_positions'
                    })
                }),
                hidden: function(){

                    if (this.model.object_type != 'purchase' && (this.model.object_type && this.model.object_type.id != 'purchase'))
                        return true;
                }
            },
            object_claim_id: {
                type: 'entity',
                dataSource: new DataSource({
                    displayExp: "id",
                    //valueExp: 'product_id',
                    store: new RemoteStore({
                        method: 'get_claims'
                    })
                }),
                hidden: function(){

                    if (this.model.object_type != 'claim' && (this.model.object_type && this.model.object_type.id != 'claim'))
                        return true;
                }
            }*/
        }
    }
    async create() {
        let fields = this.fields.filter((field) => {
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).map((field) => {
            let value = field.value;
            if (field.value && field.value.exp && field.value.exp.value != undefined) {
                value = field.value.exp.value
            }
            return {
                name: field.name,
                value: value
            }
        }).reduce((prev, curr) => {
            prev[curr.name] = curr.value
            return prev;
        }, {})

        let params = {
            type: fields.type_sample ? `sample_${fields.type}` : fields.type,
            method_marks: fields.method_marks,
            object: { ...this.object }
        }

        let { data, error } = await Http.proc.rpc("create_procedure", params);

        if (error) {
            if (!this.setError(error)) {
                await Vue.Dialog.MessageBox.Error(error);
            }
        }

        return { error, data };
    }
}

export default Vue.Dialog("ProcedureCreate", {
    props: ["context"],
    data: function () {
        return {
            model: new ModelField(this.context)
        }
    },
    methods: {
        async create() {
            await this.wait(async () => {
                let result = await this.model.create();
                if (!result.error)
                    this.Close(result)
            });
        }
    },
    template: `
        <div>
            <header>{{$t('procedure.create')}}</header>
            <main>
                <ui-layout :fields='model.fields' />
            </main>
            <footer>
                <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('cancel')}}</ui-btn>
                <ui-btn type='primary' v-on:click.native='create'>{{$t('create')}}</ui-btn>
            </footer>
        </div>
    `
})