import { Http } from '@iac/core';

export default {
  props: ['model'],
  computed: {
    procedure_url() {
      return `/procedure/${this.model.proc_id}/core`
    },
    showTimer() {
      return this.model.timers?.accept_period?.meta?.at && 
            !this.model.timers?.accept_period?.meta?.["fire?"] && 
            (this.model.status === "wait_accept_winner" || this.model.status === "wait_reserve_accept");
    }
  },
  methods: {
    async save() {
      await this.$wait(async () => {
        await this.model.edit_information();
      });
    },
    async user_repeat_isugf_call() {
      await this.$wait(async () => {
        await this.model.user_repeat_isugf_call();
      });
    },
    async download_contract() {
      this.$wait(async () => {
        const { data, error } = await Http.report.rpc('render_free_report', {
          ref: 'contract_auction',
          template: 'electron_contract_auction_base',
          type: 'pdf',
          params: {
            number: this.model.number,
          },
        }, {
          format: 'blob'
        });
        if (error && error.code == "AbortError")
        return;
        if (error !== undefined) {
          await Vue.Dialog.MessageBox.Error(error);
          return;
        }
        const url = URL || webkitURL;
        const fileUrl = url.createObjectURL(data);
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = `${this.$t('contract')}_№${this.model.number}.pdf`;
        link.click();
        url.revokeObjectURL(data);
      });
    },
  },
  template: `
    <div class='grid'>
      <ui-alert type='warning' style='margin: 15px 15px 0 15px; max-width: 550px;' v-if='model.status === "wait_accept_winner" && !model.is_reserve && model.reserve_contragent'>
        {{ $t('reserve.wait_winner_reject', { company: model.reserve_contragent.company_title }) }}
      </ui-alert>

      <ui-alert type='warning' style='margin: 15px 15px 0 15px; max-width: 550px;' v-if='model.status === "for_execution" && !model.is_reserve && model.reserve_contragent'>
        {{ $t('reserve.contract_cancelled', { company: model.reserve_contragent.company_title, date: model.contract_close_at_formatted }) }}
      </ui-alert>

      <ui-alert type='warning' style='margin: 15px 15px 0 15px; max-width: 550px;' v-if='model.status === "winner_reject_accept" && !model.is_reserve && model.reserve_contragent'>
        {{ $t('reserve.contract_activated', { company: model.reserve_contragent.company_title, date: model.winner_reject_accept_at_formatted }) }}
      </ui-alert>

      <div class='row'>
        <div class='col-sm-3'>
          <h2>{{ $t('contract.page_title') }}</h2>
          <label>{{ $t('status') }}:</label>
          <ui-ref source='auction_contract_status' :value='model && model.status'/>
        </div>
        <div class='col-sm-9'>
          <div class='row' style="display: flex; flex-direction: column;">
          <ui-alert style='margin: 0; flex: auto;' v-if='model.isugf_reply_positive == false && model.isugf_reply.ERRMSG' type='danger'>
            <div style='margin-bottom: 20px'>{{model.isugf_reply.ERRMSG}}</div>
            <div v-if="model.rights.user_repeat_isugf_call"><ui-btn type='xs danger' v-on:click.native='user_repeat_isugf_call'>{{$t('isugf.repeat_request')}}</ui-btn></div>
          </ui-alert> 
          </div>
        </div>
      </div>
      <div class='row' v-if='model.lot_id'>
        <label class='col-sm-3'>{{ $t('contract.lot_id') }}:</label>
        <div class='col-sm-5'>
          <div>{{ model.lot_id }}</div>
        </div>
      </div>

      <div class='row'>
        <label class='col-sm-3'>{{$t('contract.contract_name')}}:</label>
        <div class='col-sm-5'>
          <div>{{ model.title }}</div>
        </div>
      </div>

      <div class='row'>
        <label class='col-sm-3'>{{$t('company.buyer')}}:</label>
        <div class='col-sm-5'>
          <router-link :to='"/company/" + model.initiator.company_details.id'>
            {{ model.initiator.company_details.title }}
          </router-link>
        </div>
      </div>

      <div class='row'>
        <label class='col-sm-3'>{{$t('set_winner')}}:</label>
        <div class='col-sm-5'>
          <router-link :to='"/company/" + model.contragent.company_details.id'>
            {{ model.contragent.company_details.title }}
          </router-link>
        </div>
      </div>

      <div class='row'>
        <label class='col-sm-3'>{{ $t('contract.proc_type') }}:</label>
        <div class='col-sm-5' v-if='model.proc_id'><router-link :to='procedure_url'> {{ $t(model.proc_type) }} № {{model.proc_id}}</router-link></div>
        <div class='col-sm-5' v-else>{{ $t(model.proc_type) }}</div>
      </div>

      <div class='row'>
            <label class='col-sm-3'>{{ $t('currency') }}:</label>
            <div class='col-sm-5'>{{model.currency}}</div>
        </div>
      
      <div class='row' v-if='model && model.contract_close_at'>
        <label class='col-sm-3'>{{ $t('contract.contract_close_at') }}:</label>
        <div class='col-sm-5'><iac-date :date='model.contract_close_at' withoutTime /></div>
      </div>

      <div class='row' v-if='model.request_info.delivery'>
        <label class='col-sm-3'>{{ $t('delivery_time') }}:</label>
        <div class='col-sm-5'>
          {{model.request_info.delivery}}
        </div>
      </div>  

      <div class='row' v-if='showTimer'>
        <label class='col-sm-3'>{{ $t(model.status + '.accept_period')  }}:</label>
        <div class='col-sm-5'>
          <iac-timer :date='model.timers.accept_period.meta.at' />
        </div>
      </div>

      <div class='row' v-if='model && model.confirm_income_date'>
        <label class='col-sm-3'>{{ $t('contract.confirm_income_date') }}:</label>
        <div class='col-sm-5' v-if='model && model.confirm_income_date'><iac-date :date='model.confirm_income_date' withoutTime /></div>
      </div>

      <div class='row' v-if='model && model.confirm_income_date && $develop.content_debug'>
        <label class='col-sm-3' style='color: #700'>{{ $t('contract.delivery_date') }}:</label>
        <div class='col-sm-5' v-if='model && model.delivery_date' style='color: #700'><iac-date :date='model.delivery_date' withoutTime /></div>
      </div>
    
      <div class='row'>
        <label class='col-sm-3'>{{ $t('contract.file') }}:</label>
        <div class='col-sm-5'>
          <div class='file-body'>
            <icon>doc</icon> 
            <a :href='$t("contract") + "_№"+model.number+".pdf"' @click.prevent='download_contract'>{{ $t('contract') }}</a>
          </div>
        </div>
      </div>
      <div class='row'>
        <label class='col-sm-3'></label>
        <div class='col-sm-5'>
        {{$t([$settings._country+".contract.about_law_3015/52","contract.about_law_3015/52"])}}
        </div>
      </div>
      <div class='row' v-if='model.ready_for_delivery && model.ready_for_delivery.datetime'>
        <label class='col-sm-3'>{{ $t('contract.ready_for_delivery') }}:</label>
        <div class='col-sm-5'>
          <iac-date :date='model.ready_for_delivery.datetime' full />
        </div>
      </div> 
    </div>
  `,
};
