
export default {
  props: ['model'],
  data() {
    return {
    }
  },
  methods: {
  },
  template: `
    <div v-if='model' class='grid'>
      <div class='row'>
        <div v-if='model.sync_time'>
          <label>{{ $t('contract.sync_time') }}</label> 
          (<iac-date :date='model.sync_time' withoutTime />):
        </div>
      </div>
      <div class='row' v-if='model.usd'>
        <label class='col-sm-3'>{{ $t('USD') }}:</label>
        <div class='col-sm-5'>
          <div><iac-number :value='model.usd' delimiter=' ' part='2'/>&nbsp;{{$settings._default_currency}}</div>
        </div>
      </div>
      <div class='row' v-if='model.rub'>
        <label class='col-sm-3'>{{ $t('RUB') }}:</label>
        <div class='col-sm-5'>
          <div><iac-number :value='model.rub' delimiter=' ' part='2'/>&nbsp;{{$settings._default_currency}}</div>
        </div>
      </div>
      <div class='row' v-if='model.eur'>
        <label class='col-sm-3'>{{ $t('EUR') }}:</label>
        <div class='col-sm-5'>
          <div><iac-number :value='model.eur' delimiter=' ' part='2'/>&nbsp;{{$settings._default_currency}}</div>
        </div>
      </div>
    </div>
  `,
};
