import Router from './router'
import Kernel from '@iac/kernel'
import { Http, Language } from '@iac/core'
import { Context, Config, Develop } from '@iac/kernel'

import './components'
import './templates'
import './dialog'
import './find-error'


class Service {
    static install = (Vue, args) => {
        // Регистрация компонентов глобальных 
        // компонентов данного сервиса

        // Ожидаем запуска сервиса
        this.onRun().then(() => {
            console.log("Сервис запущен")

            Vue.CurrentRouter = Router;
            new Vue({
                data: function () {
                    return {
                        lng: Language,
                        access: Context.Access,
                    }
                },
                computed: {
                    key() {
                        let key = this.lng.local + JSON.stringify(this.access.policy) + Develop.lng_debug + Context.User.id;
                        return key
                    }
                },
                router: Router,
                async created() {

                    // Проверка бога
                    let { god,develop } = this.$route.query;
                    let query = { ...this.$route.query }
                    let replace = false;

                    if (god) {
                        query.god = undefined;
                        replace = true;
                    }
                    if (develop) {
                        query.develop = undefined;
                        replace = true;
                    }

                    let params = {};
                    for (let key in query) {
                        if (key.indexOf("next_url_") != 0)
                            continue;
                        params[key] = query[key]
                        query[key] = undefined;
                        replace = true;
                    }

                    if (params.next_url__url) {
                        params.next_url__auth = params.next_url__auth || "nomatter"
                        if (params.next_url__auth == "required" && !Context.User.team_id) {
                            await Vue.Dialog.SignIn.Modal({})
                        }

                        if (Context.User.team_id || params.next_url__auth == "nomatter") {

                            try {
                                const response = await fetch(params.next_url__url, {
                                    method: 'GET',
                                    headers: {
                                        'Content-Type': 'application/json;charset=utf-8',
                                        'Authorization': `Bearer ${Context.User.access_token}`,
                                        'X-DBRPC-Language': Language._local
                                    },
                                });

                                const body = await response.json();
                                if (body && body.message) {
                                    if (response.status != 200)
                                        Vue.Dialog.MessageBox.Error(body.message)
                                    else
                                        Vue.Dialog.MessageBox.Success(body.message)
                                }
                            } catch (e) {
                                console.log("CATCH FETCH NEXT_URL", e)
                            }

                        }
                    }

                    if (replace) {
                        this.$router.replace({ query: query })
                    }

                },
                template: `<router-view  :key='key'/>`
            }).$mount("#app")
        });
    }
    static onRun() {
        if (!this._onRunPromise) {
            this._onRunPromise = new Promise((onRunResolve) => {
                this._onRunResolve = onRunResolve;
            })
        }
        return this._onRunPromise
    }

    
    static async Run(config) {
        window.scrollTo(0, 0);
        try {
            let response = await fetch(`${window.location.origin}/service.cfg?d=${new Date().toLocaleString()}`, {
                method: "GET",
            });
            config = { ...config, ...await response.json() };
        } catch (e) {

        }

        if(!await Kernel.Run(config))
            return;

        let search = new URLSearchParams(location.search)
        let god = search.get("god");

        if (god && Context.User.id != god) {
            await Context.User.refreshToken(false);
            await Context.User.authorization_god(god);
        } else {
            await Context.User.refreshToken();
        }


        let develop = search.getAll("develop");
        develop.forEach((param)=>{
            param = param.split("-").reverse()
            let property = Develop.properties[param[0]]
            if(property && property.type == 'bool'){
                if(param.length > 1){
                    property.value = false
                }else{
                    property.value = true
                }
            }
        })


        var Content = {

        }

        Vue.mixin({
            data: function () {
                return {
                    kernel_content: Content,
                }
            },
            computed: {
                $content() {
                    return this.kernel_content;
                },
            }
        });

        let { error, data } = await Http.api.rpc("get_page", { id: "content" });
        if (data && data.data) {
            for (let key in data.data) {
                Vue.set(Content, key, data.data[key])
            }
        } else {
            for (let key in Content) {
                Vue.set(Content, key, undefined)
            }
        }

        Language.onUpdate.bind(async () => {
            let { error, data } = await Http.api.rpc("get_page", { id: "content" });
            for (let key in Content) {
                Vue.set(Content, key, undefined)
            }
            if (data && data.data) {
                for (let key in data.data) {
                    Vue.set(Content, key, data.data[key])
                }
            }
        })

        this._onRunResolve();
    }

}

export default Service;

if (typeof window !== 'undefined' && window.Vue) {
    window.Vue.use(Service)
}