import { DataSource } from '@iac/data'
import { Http } from '@iac/core'

export default Vue.Dialog("set_user_role", {
    props: ["id"],
    data: function () {
        return {
            roles: undefined
        }
    },
    async created() {
        await this.wait(async () => {
            this.roles = new DataSource.Sources.get_roles();
            let { error, data } = await Http.api.rpc("get_user_roles", { "user_id": this.id });
            if (!error) {
                this.roles.checkedItems = data.map((role) => {
                    return role.id
                });
            }
        });
    },
    methods: {
        async save() {
            await this.wait(async () => {
                const { error, data } = await Http.api.rpc("set_user_role", {
                    user_id: this.id,
                    role_ids: this.roles.checkedItems
                });
                if (error) {
                    await Vue.Dialog.MessageBox.Error(error);
                } else {
                    this.Close(true);
                }
            });
        }
    },
    template: `
        <div>
            <header>{{$t('set_roles')}}</header>
            <main>
                <ui-list check :dataSource='roles' />
            </main>
            <footer>
                <ui-btn type='primary' v-on:click.native='save'>{{$t('set_roles')}}</ui-btn>
            </footer>
        </div>
    `
});