import VueRouter from 'vue-router'
Vue.use(VueRouter)

import Info from './view/info'
import Demo from './view/demo'
import Doc from './view/doc'
import Edit from './view/edit'
import Admin from './view/admin'
import BKL from './view/bkl'
import FinancialQuotation from './view/financial_quotation'
import Business from './view/business'

import CompanyPage from './view/company_page'
import Company from './view/company'

import RegistryParticipants from './view/registry/participants'
import RegistryOrganizers from './view/registry/organizers'
import RegistrySuppliers from './view/registry/suppliers'
import RegistryBadSuppliers from './view/registry/bad_suppliers'
import RegistryCorpCompanies from './view/registry/corp_companies';
import RegistryExposition from './view/registry/exposition_list'
import RegistryGreenBuyers from './view/registry/green_buyers'
import RegistryLocalProducers from './view/registry/local_manufacturers'
import RegistryExchangeDeals from './view/registry/exchange_deals'

import WorkSpace from './view/workspace'
import Space from './view/space'
import HomePage from './view/home_page'
import MainPage from './view/main_page'
import Layout from './view/_layout'
import AccountRegister from './view/register'
import AccountPasswordRecovery from './view/password_recovery'

import SearchCompany from './view/search_company'
import DiffCompany from './view/diff_company'
import RegisterFinish from './view/register_finish'
import AcceptInvitation from './view/accept_invitation'


import Procedure from './view/procedure'
import Contract from './view/contract'
import Purchase from './view/purchase'
import SuperLogin from './view/super-login'
import News from './view/news'
import IssuingSchedule from './view/issuing_schedule'

import ErrorPage from './view/error'
import ContactPage from './view/contact'
import SupportPage from './view/support'

import ModelPage from './components/model_page'
import ExchangeSchedule from './view/exchange_schedule'
import CargoProcedure from './view/cargo_procedure'

import Practice from './view/practice'

export default new VueRouter({
    mode: 'history',
    scrollBehavior(to, from, savedPosition) {
        if (to.path == from.path)
            return;
        return { x: 0, y: 0 }
    },
    routes: [
        {
            path: '',
            component: Space,
            children: [
                {
                    path: '/',
                    component: {
                        components: {
                            HomePage: HomePage,
                            MainPage: MainPage,
                        },
                        template: `
                            <main-page v-if='settings._main_page && !$develop.old_home_page' />
                            <home-page v-else />
                        `
                    }
                },
                {
                    path: '',
                    component: Layout,
                    children: [
                        {
                            path: '/page/:page*', component: ModelPage, props: {
                                scope: "public"
                            }
                        },
                        Doc,
                        Practice,
                        {
                            path: '/demo',
                            component: Demo
                        },
                        {
                            path: '/edit',
                            component: Edit
                        },
                        ...ErrorPage,
                        ...Company,
                        ...BKL,
                        ...FinancialQuotation,
                        ...Business,
                        {
                            path: "/company/:id",
                            component: CompanyPage
                        },
                        {
                            path: "/account/register_finish",
                            component: RegisterFinish
                        },
                        {
                            path: "account/password_recovery",
                            component: AccountPasswordRecovery
                        },
                        {
                            path: "account/accept_invitation",
                            component: AcceptInvitation
                        },
                        {
                            path: "/account/register",
                            component: AccountRegister
                        },
                        {
                            path: "/info/:id",
                            component: Info
                        },
                        {
                            path: "/registry/participants",
                            component: RegistryParticipants
                        },
                        {
                            path: "/registry/organizers",
                            component: RegistryOrganizers
                        },
                        {
                            path: "/registry/suppliers",
                            component: RegistrySuppliers
                        },
                        {
                            path: "/registry/bad_suppliers",
                            component: RegistryBadSuppliers
                        },
                        {
                            path: "/registry/green_buyers",
                            component: RegistryGreenBuyers
                        },
                        {
                            path: "/registry/local_manufacturers",
                            component: RegistryLocalProducers
                        },
                        {
                            path: "/registry/corp_companies",
                            component: RegistryCorpCompanies,
                        },
                        {
                            path: "/registry/exchange_deals",
                            component: RegistryExchangeDeals,
                        },
                        {
                            path: 'exposition_list',
                            component: RegistryExposition,
                            props: {scope: 'public'},
                        },
                        {
                            path: '/search_company',
                            component: SearchCompany
                        },
                        {
                            path: '/diff_company',
                            component: DiffCompany
                        },
                        {
                            path: '/super-login/:id',
                            component: SuperLogin
                        },
                        ...SupportPage,
                        ...ContactPage,
                        ...Procedure,
                        ...Contract,
                        ...Purchase,
                        ...IssuingSchedule,
                        ...ExchangeSchedule,
                        ...CargoProcedure,
                        ...News,
                        ...Admin,
                        WorkSpace,
                        {
                            path: '*',
                            component: {
                                template: `<div><ui-error class='page' code='404' message='PageNotFound'  /></div>`
                            }
                        }
                    ]
                }
            ]
        }
    ]
});