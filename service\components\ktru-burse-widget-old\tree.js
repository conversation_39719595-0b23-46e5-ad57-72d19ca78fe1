import { Http, Language } from '@iac/core'

const loadMoreAndMore = async (method, params, callback) => {
    const limit = 90
    const tempItems = []
    let needMoreItems = true
    while (needMoreItems) {
        const { error, data = [] } = await Http.api.rpc(method, { ...params, limit, offset: tempItems.length })
        !error && data.length && tempItems.push(...(callback ? callback(data) : data))
        needMoreItems = data.length == params.limit
    }
    return tempItems
}

const renderTreeNode = {
    name: "renderTreeNode",
    components: {
        'renderTreeNode': renderTreeNode
    },
    props: {
        data: {
            type: Object,
            default: {}
        }
    },
    data() {
        return {
            loadingGroups: false,
            loadingProducts: false,
            groups: [],
            products: []
        }
    },
    methods: {
        async getGroups(rootGroup = {}) {
            const { id: parent_id = null } = rootGroup
            this.loadingGroups = true
            this.groups = await loadMoreAndMore(
                "ref",
                {
                    ref: "ref_enkt_burse_product_groups",
                    op: "read",
                    filters: { parent_id },
                    fields: ['id', 'parent_id', 'name', 'meta', 'has_children', 'has_children_prod']
                },
                items => {
                    items.forEach(item => {
                        const { name, has_children = 0, has_children_prod = 0 } = item
                        delete item.__schema__
                        item.temporary = {
                            currentName: name[Language.local] || name[Object.keys(name)[0]],
                            isShowable: Boolean(has_children + has_children_prod),
                            isShow: false
                        }
                    })
                    return items
                }
            )
            this.loadingGroups = false
        },
        async getProducts(rootGroup = {}) {
            const { id: group_id } = rootGroup
            if (!group_id) {
                return
            }
            this.loadingProducts = true
            this.products = await loadMoreAndMore(
                "ref",
                {
                    ref: "ref_enkt_burse_product",
                    op: "read",
                    filters: { group_id },
                    fields: ['id', 'product', 'meta']
                },
                items => {
                    items.forEach(item => {
                        delete item.__schema__
                        item.product.temporary = { currentName: item.product.name[Language.local] || item.product.name[Object.keys(item.product.name)[0]] }
                        item.product.id = item.id
                        delete item.id
                    })
                    return items
                }
            )
            this.loadingProducts = false
        },
        setCurrentItem(item) {
            this.$emit('setCurrentItem', item)
        }
    },
    mounted() {
        this.getGroups(this.data)
        this.getProducts(this.data)
    },
    template: `
    <icon v-if="loadingGroups || loadingProducts" class="iac--to-spin">spinner</icon>
    <div v-else-if="!groups.length&&!products.length">
        Nothing
    </div>
    <div class="iac--ktru-burse__tree-node" v-else>
      <div class="iac--ktru-burse__tree-category" v-for="(item,index) in groups" :key="index">
        <div @click="item.temporary.isShowable && (item.temporary.isShow = !item.temporary.isShow)" :title="item.temporary.currentName">
          <span v-if="item.temporary.isShowable">+</span>
          <span>{{item.temporary.currentName}}</span>
        </div>
        <renderTreeNode v-if="item.temporary.isShow" :data="item"  @setCurrentItem="setCurrentItem"/>
      </div>
      <div class="iac--ktru-burse__tree-product" v-for="(item,index) in products" :key="index" @click="e=>setCurrentItem(item)" :title="item.product.temporary.currentName">
        <span>{{item.product.temporary.currentName}}</span>
      </div>
    </div>
    `
}

export default renderTreeNode