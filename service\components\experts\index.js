import {Language} from '@iac/core'
export default {
    data: function () {
        return {
            isChatOpen: undefined
        }
    },
    computed:{
        source(){
            if(!this.isChatOpen)
                return;
            
            let url = localStorage.getItem('lastUrl');
            url =  typeof this.isChatOpen == "boolean" && this.$settings.experts._url && `${this.$settings.experts._url}${url ? url : ''}`
            if(url){
                url = new URL(url)

                if(this.$settings.experts._$lng)
                    url.searchParams.set(typeof this.$settings.experts._$lng == 'string' ? this.$settings.experts._$lng : "lng", Language._local_socket)
                if(this.$settings.experts._$url)
                    url.searchParams.set(typeof this.$settings.experts._$url == 'string' ? this.$settings.experts._$url : "url", encodeURIComponent(location))
            }
            return url;
        },
        styles(){
            return {
                scale: this.$settings.experts._scale || 1
            }
        }
    },
    methods: {
        toggleChat() {
            this.isChatOpen = !this.isChatOpen;
        },
        clickaway(){
            if(typeof this.isChatOpen != "boolean")
                return;
            this.isChatOpen = false;
        },
        onMessage(event){
            let data = event.data; 
            if (event.source != this.$refs?.chatEnd?.contentWindow || !data)
                return;
                

            if(data.type === 'redirect' && data.url) {
                localStorage.setItem('lastUrl', data.url)
            }
            
            if(data.type === 'close') {
                this.isChatOpen = false;
            }
        }
    },
    mounted() {
        window.addEventListener("message",this.onMessage)
    },
    beforeDestroy(){
        window.removeEventListener("message",this.onMessage)
    },
    template: `
        <div style='z-index: 1000' v-on-clickaway="clickaway">
            <div v-if='!isChatOpen'  v-on:click='toggleChat' class='toggle' :style='styles' style='position: fixed; bottom: 12px; right: 0px; zIndex: 1050; cursor: pointer;transform: rotateZ(90deg) translateY(100%); transform-origin: right bottom;'>
                <svg xmlns="http://www.w3.org/2000/svg" width="188" height="47" viewBox="0 0 188 47" fill="none">
                    <g clip-path="url(#clip0_2253_11471)">
                        <g filter="url(#filter0_d_2253_11471)">
                                <rect x="2.5" y="0.162109" width="183" height="40" rx="4" fill="#5F4ADA"/>
                                <g clip-path="url(#clip1_2253_11471)">
                                    <path d="M22.3989 31.2332C23.1294 31.2332 23.5784 30.8519 24.9445 29.9928L29.9993 27.2926H30.4852C34.8383 27.2926 36.7808 24.9549 36.7808 20.8029V17.2117C36.7808 13.0604 34.8383 10.7227 30.4852 10.7227L20.5472 10.7227C16.2091 10.7227 14.2191 13.0465 14.2191 17.2117V20.8029C14.2191 24.9688 16.2085 27.2926 20.5472 27.2926H21.2484V29.3266C21.2484 30.4178 21.3911 31.2332 22.3989 31.2332ZM23.4392 28.5335V26.0739C23.4392 25.3091 23.1475 25.0111 22.3441 25.0111H20.5615C17.5966 25.0111 16.5708 23.5518 16.5708 20.789V17.2117C16.5708 14.449 17.5966 13.0042 20.5621 13.0042H30.4852C33.4359 13.0042 34.4285 14.449 34.4285 17.2117V20.789C34.4285 23.5518 33.4359 25.0111 30.4852 25.0111H29.8964C29.093 25.0111 28.6698 25.1248 28.1139 25.6773L23.4392 28.5335Z" fill="#F9F9F9"/>
                                    <path d="M24.7949 12.1328L17.0394 12.8379L15.6293 15.6581V22.7086L18.4495 24.8237L21.9747 26.2338L22.6798 29.7591L29.0252 26.2338L33.2556 25.5288L35.3707 22.7086V16.3631L33.2556 12.1328H24.7949Z" fill="#F9F9F9"/>
                                </g>
                            <path d="M42.5927 22.1393H46.7741V23.8691H42.5927V22.1393ZM42.8333 25.5687H47.4509V27.3887H40.4869V18.74H47.2855V20.56H42.8333V25.5687ZM48.0104 27.3887L51.891 22.1845L51.8759 23.8239L48.1608 18.74H50.8381L53.2146 22.0942H52.0865L54.448 18.74H57.0501L53.35 23.7186V22.1544L57.2155 27.3887H54.4781L52.0564 23.8841H53.1544L50.7328 27.3887H48.0104ZM58.4216 27.3887V18.74H62.3624C63.1646 18.74 63.8515 18.8704 64.423 19.1311C65.0046 19.3818 65.4559 19.7428 65.7767 20.2141C66.0976 20.6854 66.2581 21.2569 66.2581 21.9288C66.2581 22.5805 66.0976 23.1471 65.7767 23.6284C65.4559 24.1097 65.0046 24.4757 64.423 24.7264C63.8515 24.9771 63.1646 25.1024 62.3624 25.1024H59.7302L60.7981 24.0646V27.3887H58.4216ZM60.7981 24.3353L59.9258 23.2674H62.197C62.7485 23.2674 63.1646 23.1521 63.4454 22.9215C63.7261 22.6808 63.8665 22.3499 63.8665 21.9288C63.8665 21.4976 63.7261 21.1667 63.4454 20.936C63.1646 20.7054 62.7485 20.5901 62.197 20.5901H59.9258L60.7981 19.5222V24.3353ZM70.1043 22.1393H74.2858V23.8691H70.1043V22.1393ZM70.345 25.5687H74.9626V27.3887H67.9986V18.74H74.7972V20.56H70.345V25.5687ZM76.6942 27.3887V18.74H80.635C81.4372 18.74 82.1241 18.8704 82.6956 19.1311C83.2772 19.3818 83.7284 19.7428 84.0493 20.2141C84.3702 20.6854 84.5306 21.2569 84.5306 21.9288C84.5306 22.5805 84.3702 23.1421 84.0493 23.6134C83.7284 24.0846 83.2772 24.4456 82.6956 24.6963C82.1241 24.947 81.4372 25.0723 80.635 25.0723H78.0028L79.0707 24.0646V27.3887H76.6942ZM82.1391 27.3887L79.9581 24.2451H82.485L84.6961 27.3887H82.1391ZM79.0707 24.3353L78.1983 23.2674H80.4695C81.021 23.2674 81.4372 23.1521 81.718 22.9215C81.9987 22.6808 82.1391 22.3499 82.1391 21.9288C82.1391 21.4976 81.9987 21.1667 81.718 20.936C81.4372 20.7054 81.021 20.5901 80.4695 20.5901H78.1983L79.0707 19.5222V24.3353ZM87.8515 27.3887V20.5901H85.1291V18.74H92.9505V20.5901H90.228V27.3887H87.8515ZM100.957 24.7565C100.957 25.298 100.802 25.7793 100.491 26.2004C100.19 26.6216 99.7489 26.9525 99.1673 27.1931C98.5957 27.4338 97.8888 27.5541 97.0465 27.5541C96.3546 27.5541 95.6727 27.4689 95.0009 27.2984C94.3391 27.128 93.8026 26.9023 93.3915 26.6216L94.2187 24.8618C94.6098 25.1225 95.0661 25.328 95.5875 25.4785C96.1089 25.6289 96.6404 25.7041 97.1818 25.7041C97.7133 25.7041 98.0843 25.6289 98.2949 25.4785C98.5055 25.328 98.6107 25.1526 98.6107 24.952C98.6107 24.7515 98.5155 24.591 98.325 24.4707C98.1445 24.3504 97.9038 24.2551 97.603 24.1849C97.3122 24.1047 96.9863 24.0295 96.6253 23.9593C96.2744 23.8891 95.9184 23.7989 95.5574 23.6886C95.2064 23.5783 94.8806 23.4329 94.5797 23.2524C94.2889 23.0618 94.0483 22.8162 93.8578 22.5154C93.6773 22.2045 93.587 21.8185 93.587 21.3572C93.587 20.8157 93.7424 20.3394 94.0533 19.9283C94.3742 19.5071 94.8254 19.1762 95.407 18.9356C95.9886 18.6949 96.6805 18.5746 97.4827 18.5746C98.0241 18.5746 98.5756 18.6348 99.1372 18.7551C99.6987 18.8754 100.19 19.0659 100.611 19.3266L99.8441 21.0865C99.453 20.8458 99.0469 20.6753 98.6258 20.5751C98.2147 20.4748 97.8035 20.4246 97.3924 20.4246C96.891 20.4246 96.525 20.4998 96.2944 20.6503C96.0638 20.8007 95.9485 20.9912 95.9485 21.2218C95.9485 21.4224 96.0387 21.5828 96.2192 21.7031C96.3997 21.8235 96.6353 21.9237 96.9261 22.004C97.227 22.0742 97.5579 22.1443 97.9189 22.2145C98.2798 22.2847 98.6358 22.375 98.9868 22.4853C99.3377 22.5855 99.6636 22.7259 99.9644 22.9064C100.265 23.0869 100.506 23.3276 100.686 23.6284C100.867 23.9192 100.957 24.2952 100.957 24.7565Z" fill="#F9F9F9"/>
                            <path d="M41.6734 15.4192C41.2369 15.4192 40.9007 15.3013 40.6647 15.0653C40.4288 14.8294 40.3108 14.4961 40.3108 14.0655V9.70362H40.939V14.0301C40.939 14.3015 41.0068 14.5109 41.1425 14.6583C41.2841 14.8058 41.4846 14.8795 41.7442 14.8795C42.0214 14.8795 42.2514 14.7999 42.4343 14.6406L42.6555 15.0919C42.5316 15.2039 42.3812 15.2865 42.2042 15.3396C42.0332 15.3927 41.8562 15.4192 41.6734 15.4192ZM39.4791 11.252V10.73H42.3546V11.252H39.4791ZM46.1702 10.6857C46.5477 10.6857 46.878 10.7594 47.1612 10.9069C47.4502 11.0485 47.6743 11.2667 47.8336 11.5616C47.9987 11.8566 48.0813 12.2282 48.0813 12.6765V15.375H47.4531V12.7384C47.4531 12.2488 47.3293 11.8802 47.0815 11.6324C46.8397 11.3788 46.4976 11.252 46.0552 11.252C45.7249 11.252 45.4359 11.3198 45.1881 11.4555C44.9463 11.5852 44.7575 11.7769 44.6219 12.0306C44.4921 12.2783 44.4272 12.5791 44.4272 12.933V15.375H43.799V8.81H44.4272V12.004L44.3034 11.7651C44.4508 11.4289 44.6868 11.1664 45.0112 10.9777C45.3356 10.783 45.7219 10.6857 46.1702 10.6857ZM51.7512 15.4192C51.2675 15.4192 50.8428 15.319 50.4771 15.1184C50.1114 14.912 49.8253 14.6318 49.6189 14.2779C49.4124 13.9181 49.3092 13.5081 49.3092 13.0481C49.3092 12.588 49.4065 12.181 49.6012 11.8271C49.8017 11.4732 50.0731 11.1959 50.4152 10.9954C50.7632 10.7889 51.1525 10.6857 51.5831 10.6857C52.0196 10.6857 52.4059 10.786 52.7421 10.9865C53.0842 11.1812 53.3526 11.4584 53.5473 11.8182C53.7419 12.1721 53.8392 12.5821 53.8392 13.0481C53.8392 13.0775 53.8363 13.11 53.8304 13.1454C53.8304 13.1749 53.8304 13.2073 53.8304 13.2427H49.787V12.7738H53.4942L53.2464 12.9596C53.2464 12.6234 53.1727 12.3255 53.0253 12.066C52.8837 11.8005 52.689 11.5941 52.4413 11.4466C52.1936 11.2992 51.9075 11.2254 51.5831 11.2254C51.2646 11.2254 50.9785 11.2992 50.7249 11.4466C50.4712 11.5941 50.2736 11.8005 50.1321 12.066C49.9905 12.3314 49.9197 12.6352 49.9197 12.9773V13.0746C49.9197 13.4285 49.9964 13.7411 50.1498 14.0125C50.309 14.2779 50.5273 14.4873 50.8045 14.6406C51.0876 14.7881 51.4091 14.8618 51.7689 14.8618C52.052 14.8618 52.3145 14.8117 52.5563 14.7114C52.8041 14.6111 53.0164 14.4578 53.1934 14.2513L53.5473 14.6583C53.3408 14.9061 53.0813 15.0948 52.7687 15.2246C52.462 15.3544 52.1228 15.4192 51.7512 15.4192Z" fill="#F9F9F9"/>
                        </g>
                        <path d="M19.5743 45.4694C18.5077 46.5136 16.7146 46.0638 16.2672 44.6398L14.0955 37.7286C13.6268 36.2372 14.9737 34.8088 16.4901 35.1892L23.8385 37.0325C25.3549 37.4129 25.8681 39.3079 24.751 40.4016L19.5743 45.4694Z" fill="#5F4ADA"/>
                    </g>
                    <defs>
                        <filter id="filter0_d_2253_11471" x="-1.5" y="-1.83789" width="191" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                            <feOffset dy="2"/>
                            <feGaussianBlur stdDeviation="2"/>
                            <feComposite in2="hardAlpha" operator="out"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.733561 0 0 0 0 0.733561 0 0 0 0 0.733561 0 0 0 0.25 0"/>
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2253_11471"/>
                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2253_11471" result="shape"/>
                        </filter>
                        <clipPath id="clip0_2253_11471">
                            <rect y="0.162109" width="188" height="46" rx="4" fill="white"/>
                        </clipPath>
                        <clipPath id="clip1_2253_11471">
                            <rect width="30.0822" height="30.0822" fill="white" transform="translate(10.5 5.12109)"/>
                        </clipPath>
                    </defs>
                </svg>
            </div>

            <iframe v-show='isChatOpen' ref='chatEnd' :src='source' style='
                box-shadow: 0px 0px 10px 3px rgba(0, 0, 0, 0.25);
                width: 97%; 
                max-width: 544px;
                background: #f9f9f9;
                border-radius: 2px;
                border: 2px solid #ADC6F9;
                right: 5px;
                bottom: 5px;
                height: 75%;
                position: fixed;
                z-index: 2000;
            ' />
        </div>
    `
}