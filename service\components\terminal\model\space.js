import { Guid,Language } from '@iac/core'
import { Entity, DataSource } from '@iac/data'

import Node from './node'
import Item from './item'

class SpaceSetting extends Entity {
    constructor(context = {}) {
        super(context)
        this.space_label = context.space_label
    }
    props() {
        return {
            space_label: {
                label: "-product_name"
            }
        }
    }
    get params() {
        return this.fields.filter((field) => {
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).map((field) => {
            let value = field.value;

            if (field.value && field.value.exp && field.value.exp.value != undefined) {
                value = field.value.exp.value
            }

            return {
                name: field.name,
                value: value
            }
        }).reduce((prev, curr) => {
            prev[curr.name] = curr.value
            return prev;
        }, {})
    }
}

export default class Space {
    constructor(context) {
        this.visible = context.visible != undefined ? context.visible : true,
            this.status = undefined;
        this.manager = context.manager;
        this.label = context.label || "Space";
        let root_node = new Node({ space: this });

        context.groups = context.groups || {}

        this.groups = Array(7).fill(undefined).map((group = {}, index) => {

                group.product_name = (context.groups[index] && context.groups[index].product_name) || group.product_name,
                group.id = (context.groups[index] && context.groups[index].id != undefined) ? context.groups[index].id : group.id
                group.index = index+1;
                Object.defineProperty(group, "label", {
                    configurable: true,
                    enumerable: true,
                    get() {
                        let name = this.product_name && (this.product_name[Language.local] || this.product_name["ru-RU"])
                        if(this.id == 0){
                            return `${Language.t('group')} без контракта`
                        }
                        else if(this.id)
                            return `${this.id} ${name || ''}` ;
                    return `${Language.t('group')} ${index+1}`
                    },
                });

            return group;
        })

        this.groupsSource = DataSource.get(this.groups);

        for (let path in context.items) {
            let items = context.items[path];

            if (items.map((item, index) => {
                if (!Vue.Tiling[item.tile])
                    return;
                return item
            }).filter((item) => {
                return item
            }).length <= 0)
                continue;

            let current_node = root_node;
            // разбиваем пути на группы
            let nodes = path.split('.')
            for (let key of nodes) {
                let [name, size] = (key + "").split(":")
                name = "_" + name;
                let parent = current_node;
                current_node = current_node.child = (current_node.child || {});
                current_node = current_node[name] = current_node[name] || new Node({ id: name, parent: parent, size: size, space: this });
            }
            //current_node = current_node.child = current_node.child || {};
            current_node.items = current_node.items || [];
            current_node.items = items.map((item, index) => {
                item.space = this;
                if (item.active) {
                    current_node.active_item = index
                }
                return Item.get(item);
            });
        }

        // this.node = root_node;
        Vue.set(this,"node",root_node)
    }

    async setting() {
        let setting = await Vue.Dialog({
            props: ["model"],
            template: `
                <div>
                    <header>{{$t('nav.settings')}}</header>
                    <main><ui-layout :fields='model.fields'/></main>
                    <footer>
                        <ui-btn type='secondary' @click.native='Close()'>{{$t('cancel')}}</ui-btn>
                        <ui-btn type='primary' v-on:click.native='Close(model.params)'>{{$t('set')}}</ui-btn>
                    </footer>
                </div>
            `
        }).Modal({
            model: new SpaceSetting({
                space_label: this.label
            })
        })

        if (setting) {
            this.label = setting.space_label

            this.save();
        }
    }

    get toolbar() {
        let handler_insert_contract = (tile='bkl_exchange_contract', parent='exchange_contract') => {
            let tile_info = this.node.search(tile, {})
            if (tile_info) {
                tile_info.node.setActive(tile_info.index)
                
                tile_info.item.class = "insert"
                setTimeout(()=>{
                    tile_info.item.class = undefined; 
                },500)

            } else {

                tile_info = this.node.search(parent, {})
                let item = Item.get({
                    tile: tile
                });
                if (tile_info) {
                    tile_info.node.add_item(item)
                }else{
                    let keys = Object.keys(this.node.child);
                    if (keys) {
                        this.node.insert(keys[0], item, true)
                    }
                }
                item.class = "insert"
                setTimeout(()=>{
                    item.class = undefined; 
                },500)
            }
        }

        let handler_insert_item = (tile) => {
            let tile_info = this.node.search('bkl_exchange_contract', {})
            if(!tile_info){
                tile_info = this.node.search('exchange_contract', {})
            }

            let item = Item.get({
                tile: tile
            });

            if (tile_info && tile_info.node.horizontal) {
                tile_info.node.split(item, false)
            } else if (tile_info) {
                tile_info.node.insert_parent(item, false)
            } else {
                let keys = Object.keys(this.node.child);
                if (keys) {
                    this.node.insert(keys[0], item, true)
                }
            }   

            item.class = "insert"
            setTimeout(()=>{
                item.class = undefined; 
            },500)

        }

        return [
            {
                icon: "field",
                handler: handler_insert_contract,
                actions: [
                    {
                        icon: "field",
                        label: "tile.bkl_exchange_contract",
                        handler: handler_insert_contract,
                    },
                    {
                        icon: "field",
                        label: "tile.exchange_contract",
                        handler: () => {
                            handler_insert_contract('exchange_contract','bkl_exchange_contract');
                        },
                    },
                    {
                        type: "sep"
                    },
                    {
                        icon: "field",
                        label: "tile.order_book",
                        handler: () => {
                            handler_insert_item("order_book");
                        },
                    },
                    {
                        icon: "field",
                        label: "tile.proposal",
                        handler: () => {
                            handler_insert_item("proposal");
                        },
                    },{
                        icon: "field",
                        label: "tile.history",
                        handler: () => {
                            handler_insert_item("history");
                        },
                    },
                    {
                        type: "sep"
                    },
                    {
                        icon: "clock",
                        label: "tile.timer",
                        handler: () => {
                            let tile_info = this.node.search('timer')
                            if (tile_info) {
                                tile_info.node.setActive(tile_info.index)
                                tile_info.item.class = "insert"
                                setTimeout(()=>{
                                    tile_info.item.class = undefined; 
                                },500)
                            } else {
                                let keys = Object.keys(this.node.child);
                                if (keys) {
                                    let item = Item.get({
                                        tile: "timer"
                                    });
                                    this.node.insert(keys[keys.length - 1], item, false)
                                    item.class = "insert"
                                    setTimeout(()=>{
                                        item.class = undefined; 
                                    },500)
                                }
                            }
                        },
                    },,
                    {
                        type: "sep"
                    },
                    {
                        icon: "settings",
                        label: "tile.settings",
                        handler: () => {
                            let tile_info = this.node.search('settings')
                            if (tile_info) {
                                tile_info.node.setActive(tile_info.index)
                                tile_info.item.class = "insert"
                                setTimeout(()=>{
                                    tile_info.item.class = undefined; 
                                },500)
                            } else {
                                let keys = Object.keys(this.node.child);
                                if (keys) {
                                    let item = Item.get({
                                        tile: "settings"
                                    });
                                    this.node.insert(keys[keys.length - 1], item, false)
                                    item.class = "insert"
                                    setTimeout(()=>{
                                        item.class = undefined; 
                                    },500)
                                }
                            }
                        }
                    }
                ]
            },

        ]
    }

    save() {
        this.manager.save();
    }
    getStruct() {

        let struct = {};

        let forEach_items = (node, path = '') => {
            let items = [];
            if (node.child) {
                let new_id = 0;
                for (let id in node.child) {
                    new_id++;
                    let size = Math.floor(node.child[id].size);
                    let new_path = path ? `${path}.${new_id}:${size}` : `${new_id}:${size}`
                    forEach_items(node.child[id], new_path);
                }
            }
            if (node.items && node.items.length > 0) {
                items = items.concat(node.items);

                struct["_" + path] = node.items.map((item, index) => { 
                    return item.getStruct({
                        active: node.active_item == index ? true : false
                    })
                })

            }
        }

        forEach_items(this.node);

        let groups = this.groups.reduce((acc, item, index) => {
            if (item.id != undefined) {
                acc[index] = item
            }
            return acc;
        }, {})

        return {
            label: this.label,
            visible: this.visible,
            groups: groups,
            items: struct
        };

    }
}