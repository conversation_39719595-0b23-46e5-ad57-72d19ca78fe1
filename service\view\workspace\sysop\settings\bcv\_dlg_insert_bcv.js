
import {Http} from '@iac/core';

export default Vue.Dialog("insertBcv", {
    data: function () {
        return {
            value: 0,
            description: ''
        }
    },
    methods: {
        async save() {
            await this.wait(async e => {
                let { error, data } = await Http.api.rpc('create_base_calculated_value',  {value: parseFloat(this.value), description: this.description});
                if (!error) {
                    this.Close(true);
                    if (data.message)
                        await Vue.Dialog.MessageBox.Success(data.message);
                } else {
                    await Vue.Dialog.MessageBox.Error(error);
                }
            })
        }
    },
    template: `
        <div>
            <header>{{$t('insert_bcv')}}</header>
            <main>
                <ui-input type='number' v-model='value' label='summa' required='true'/>
                <ui-text v-model='description' label='description' required='true'/>
            </main>
            <footer>
                <ui-btn type='primary' v-on:click.native='save' :disabled='!value'>{{$t('send')}}</ui-btn>
            </footer>
        </div>
    `
})