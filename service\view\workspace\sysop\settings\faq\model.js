import { Http, Language } from '@iac/core'
import { Entity } from '@iac/data'


export default class Faq extends Entity {
  constructor(context = {}) {
    super(context)
    this.id = context.id;
    this.question = context.question;
    this.answer = context.answer;
    this.show = context.show || false;
    this.ordering = context.ordering;
  }
  props() {
    return {
      id: {
        hidden: true
      },
      question: {
        type: 'text',
        required: true
      },
      answer: {
        type: 'text',
        required: true
      },
      show: {
        type: 'bool'
      },
      ordering: {
        required: true,
        type: 'number'
      }
    }
  }

  async save() {
    const { error, data } = await Http.api.rpc("common_ref", {
      ref: "ref_faqs",
      op: this.id ? "update" : "create",
      data: {
        id: this.id,
        question: this.question,
        answer: this.answer,
        locale: Language._local_socket,
        show: this.show,
        ordering: this.ordering
      }
    });

    if (error) {
      if (!this.setError(error)) {
        await Vue.Dialog.MessageBox.Error(error);
      }
    }
    return { error, data };
  }

  async delete() {
    const result = await Http.api.rpc("common_ref", {
      ref: "ref_faqs",
      op: "delete",
      data: {
        id: this.id
      }
    });
    return result;
  }

  async toggleShow() {
    const result = await Http.api.rpc("common_ref", {
      ref: "ref_faqs",
      op: "update",
      data: {
        id: this.id,
        show: !this.show
      }
    });
    return result;
  }
}