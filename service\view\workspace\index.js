import { Context } from '@iac/kernel'

import Main from './main'
import ProcedureContractList from './../contract/search'
import ProcedureContract from './contract'
import ClaimList from './claim/claim_list'
import ExchangeRouter from './exchange'
import Complaints from './complaints'
import Report from './report'
import Purchase from './purchase'
import Notification from './notification';
import Terminal from './terminal';
import ModelPage from './../../components/model_page'

import Sysop from './sysop'
import Company from './company'
import Agreement from './agreement'
import Procedures from './procedures'

import Documentation from './documentation'


export default {
    path: '/workspace',
    component: {
        data: function () {
            return {
                user: Context.User
            }
        },
        template: `
            <router-view v-if='user.id || user.face_id' />
            <ui-error v-else class='page' code='403' :message='$t("NoAccess")'  />
        `
    },
    children: [
        { path: '/', component: Main },
        {
            path: 'page/:page*', component: ModelPage, props: {
                scope: "private"
            }
        },
        {
            path: 'exchange', children: [...ExchangeRouter], component: {
                template: '<router-view></router-view>'
            }
        },
        { path: 'claim', component: ClaimList },
        { path: 'contract', component: ProcedureContractList, props: { access: 'private' } },
        ...ProcedureContract,
        ...Report,
        ...Purchase,

        ...Notification,
        ...Complaints,
        ...Terminal,

        ...Sysop,
        ...Company,
        ...Agreement,
        ...Procedures,
        ...Documentation
    ]
}
