import OrderBookSetting from './setting'
import Contract from '../../model/contract'

let OrderBookComponent = {
    props: ["model"],
    data: function () {
        return {
            start: undefined,
            bids: undefined,
            contract: undefined
        }
    },
    watch: {
        "model.contract": {
            immediate: true,
            async handler(contract, oldVal) {
                if(contract && this.contract && contract.id == this.contract.id )
                    return;

                if (this.contract) {
                    this.contract.unwatch()
                    this.contract = undefined

                    //this.contract.onInfo.unbind(this.onInfo)
                    //this.contract = undefined
                }
                if (contract && contract.id) {
                    this.contract = Contract.get(contract)
                    this.contract.watch()
                    //this.contract.onInfo.bind(this.onInfo)
                }

                console.log("watch contract", contract);
            },
            //deep: true
        }
    },
    mounted1() {
        setInterval(() => {
            let price = Math.random() * (100 - 80) + 80;
            let amount = Math.random() * (1000 - 1) + 1;
            if (!this.bids[price]) {
                Vue.set(this.bids, price, [])
            }
            this.bids[price].push(amount);
        }, 1000)
    },
    beforeCreate() {
        //console.log("beforeCreate order");
    },
    created() {
        //console.log("created order");
    },
    mounted() {
        //console.log("mounted order");
        //this.contract.onInfo.bind(this.onInfo)

        this.$wait(async () => {
            // Получаем данные
            let { error, data } = {
                data: {
                    start: { price: 73, count: 5400 },
                    bids: {
                        72.40: [1220, 100, 440],
                        72.30: [20, 100, 440],
                        72.20: [20, -100, -440],
                        72: [1120, -100, 1000, -440, 400, -500],
                        71: [-1000, 1000, 440, 400, 500],
                        71.20: [100, 1000, 440, 400, 500],
                        70: [100, 1000, 440, 400, -500],
                        60: [100, 1000, 440, 400, -500],
                        50: [100, 1000, 440, 400, -500]
                    }
                }
            }

            this.start = data.start;
            this.bids = data.bids;

            // Подписываемся на события сокета
            /*
                        setInterval(() => {
                            let price = Math.random() * (100 - 80) + 80;
                            let amount = Math.random() * (1000 - 1) + 1;
                            if (!this.bids[price]) {
                                Vue.set(this.bids, price, [])
                            }
                            this.bids[price].push(amount);
                        }, 1000)
            */
        })

    },
    beforeDestroy() {
        setTimeout(() => {
            this.contract.unwatch();
            //this.contract.onInfo.unbind(this.onInfo)
        }, 200)

    },
    computed: {
        data() {
            if (!this.bids)
                return {};

            let total = 0;
            let bids = Object.keys(this.bids).sort((a, b) => {
                return a > b ? -1 : 1
            }).map((price) => {
                let bit = {
                    price: price,
                    items: this.bids[price],
                    count: 0,
                    my: [],
                };

                let _count = 0;
                bit.items.forEach(count => {
                    if (count < 0) {
                        bit.my.push({ left_count: _count, count: Math.abs(count) });
                    }
                    _count += Math.abs(count);

                    bit.count = bit.count + Math.abs(count);
                });



                bit.my.forEach((my) => {
                    my.width = my.count * 100 / bit.count;
                    my.left = my.left_count * 100 / bit.count;
                    return my;
                })

                total += bit.count;
                let indicator = bit.count + (this.start.count - total);
                if (indicator < 0)
                    indicator = 0;
                else if (indicator > bit.count) {
                    indicator = 100;
                } else {
                    indicator = 100 * indicator / bit.count
                }

                bit.indicator = indicator;

                return bit;
            })

            return {
                items: bids
            };
        }
    },
    methods: {
        onInfo() {

        }
    },
    template: `
        <div class='iac-order-book'>
            <div v-if='0' class='toolbar window-toolbar'>{{contract}}</div>
            <div class='start'>
                <div></div> <div></div>
            </div>
            <div class='content'>
                <div class='bids'>
                    <div :id='bid.price' :class='($setting.indicator_bid && bid.indicator && "in") + " bid"' v-for='bid in data.items'>
                        <div v-if='$setting.indicator_bid && bid.indicator' class='indicator all' :style='"width: "+bid.indicator+"%"  ' />
                        <div v-if='$setting.indicator_my_bid' class='indicator my'>
                            <div :title='my_bid.count' v-for='my_bid in bid.my' :style='"left: "+my_bid.left+"%; width: "+my_bid.width+"%;"'/>
                        </div>
                        <div class='cell my'></div>
                        <div class='cell all'>{{bid.count}}</div>
                        <div class='cell price'>{{bid.price}}</div>
                    </div>
                </div>
            </div>
        </div>
    `
}

Vue.Tiling("order_book", {
    component: OrderBookComponent,
    setting: OrderBookSetting,
    select_contract: true,
    select_group: true
})