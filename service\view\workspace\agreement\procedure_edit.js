import Model from './model/procedure'
import { Language } from '@iac/core';

let EditModel = {
    props: ["model"],
    methods: {
        async save() {
            await this.$wait(async () => {
                if (!this.model)
                    return;
                const { error } = await this.model.save();
                if (!error) {
                    await Vue.Dialog.MessageBox.Success(this.$t('procedure_saved'))
                }

            })

        },
        async start() {
            await this.$wait(async () => {
                if (!this.model)
                    return;
                const { error } = await this.model.start();
                if (!error) {
                    await Vue.Dialog.MessageBox.Success(this.$t('procedure_runned'))
                    this.$router.push({
                        path: '/workspace/agreement/procedure'
                    });
                }
            })
        },
        async set_chairman(item) {
            if (await Vue.Dialog.MessageBox.Question(this.$t('question_set_chairman')) != Vue.Dialog.MessageBox.Result.Yes) {
                return;
            }
            await this.$wait(async () => {
                if (this.model && this.model.stage) {
                    await this.model.stage.set_chairman(item)
                }
            })
        },
        roles(item) {
            if (!item.roles)
                return;
            return item.roles.map((role) => {
                return "- " + role.name
            }).join("\n")
        }
    },
    computed: {
        vote(){
            if ((this.model.rights & 7) == 0)
            return;
            return ()=>{
                this.$router.push({
                    path: `/workspace/agreement/procedure/${this.model.id}/vote`
                });
            }
        },
        close_procedure() {
            if ((this.model.rights & 8) == 0)
                return;
            return () => {
                this.$wait(async () => {
                    if (await Vue.Dialog.MessageBox.Question(Language.t("vote_close_procedure_question")) != Vue.Dialog.MessageBox.Result.Yes) {
                        return;
                    }

                    let result = await this.model.close_procedure()
                    if (result.error) {
                        await Vue.Dialog.MessageBox.Error(result.error);
                    } else {
                        this.$router.push({
                            path: `/workspace/agreement/procedure/${this.model.id}/vote`
                        });
                    }
                })
            }
        }
    },
    template: `
        <div>
            <ui-layout-group>
                <div style='margin-bottom: 20px'>
                    <router-link class='object' :title='model.object_title' :to='"/procedure/"+model.object_id+"/core"'>{{$t(model.object_type)}} №{{model.object_id}}</router-link>
                </div>
                <ui-layout-group horizontal=2>
                    <div v-if='model.start_at' style='margin-bottom: 10px'>
                        {{$t('agreement.date_from')}}: <iac-date :date='model.start_at' full></iac-date>
                    </div>
                    <div v-if='model.end_at' style='margin-bottom: 10px'>
                    {{$t('agreement.date_to')}}: <iac-date :date='model.end_at' full></iac-date>
                    </div>
                </ui-layout-group> 

                <ui-layout-group horizontal=2>
                    <ui-layout :fields='model.fields'  />
                    <ui-layout-group label='committee' :actions='model.stage.actions' v-if='model.status == "created"'>
                    <ui-list check :dataSource='model.stage.users'>
                        <template slot='template' slot-scope='props'>
                            <div style='flex: 1 1 auto;'>
                                <b>{{props.item.name}}</b>
                                <div style='white-space: pre-line; font-size: 12px;'>{{roles(props.item)}}</div>
                            </div>
                            <ui-btn v-if='props.item.is_chairman' type='primary'>{{ $t('chairman') }}</ui-btn>
                            <ui-btn v-else-if='props.item.can_be_chairman' type='default' @click.native='set_chairman(props.item)'>{{ $t('chairman') }}</ui-btn>
                            <span class='ui-btn ui-btn-link' v-if='props.item.is_sec' type='default'><b>{{ $t('secretary') }}</b></span>
                        </template>
                    </ui-list>
                </ui-layout-group> 

                </ui-layout-group>                
            </ui-layout-group>
            <div>
                <ui-btn type='primary' :disabled='model.wait' v-on:click.native='start' v-if='model.status == "created"' >{{$t('start')}}</ui-btn>
            </div>
            <div v-if="model.votes">
                <table class='table table-bordered table-striped table-hover' width="100%" cellspacing="0" cellpadding="0">
                    <thead>
                        <tr>
                            <th>{{$t('fullname')}}</th>
                            <th>{{$t('role')}}</th>
                            <th>{{$t('time_of_voting')}}</th>
                            <th>{{$t('vote')}}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(vote,index) in model.votes" :key="index">
                            <td>{{vote.name}}</td>
                            <td>{{vote.procRole}}</td>
                            <td><iac-date full :date='vote.created_at'/></td>
                            <td>{{vote.result}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div v-if='model.status != "close"'>
                <ui-btn type='primary' v-if='vote' v-on:click.native='vote'>{{ $t('nav.agreements') }}</ui-btn>
                <ui-btn type='primary' v-if='close_procedure' v-on:click.native='close_procedure'>{{ $t('close_procedure') }}</ui-btn>
            </div>
        </div>
    `
}

export default {
    data: function () {
        return {
            model: undefined,
            error: undefined
        }
    },
    mounted: async function () {
        await this.$wait(async () => {
            let { error, data } = await Model.get(this.$route.params.id);
            this.model = data;
            this.error = error;
        });
    },
    components: {
        EditModel
    },
    template: `
    <div>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li><router-link to='/workspace/agreement/procedure'>{{$t('nav.agreements_procedures')}}</router-link></li>
                <li>{{$t('nav.agreements')}}</li>
            </ol>
            <h1>{{$t('nav.agreements')}}: <ui-ref source='ref_status_agreement_procedure' :value='model && model.status'/> </h1>
        </iac-section>
        <iac-section>
            <div v-if='error'>{{error}}</div>
            <EditModel v-if='model' :model='model'/>
        </iac-section>
    </div>
    `
}