.iac-language{
  display: flex;
  align-items: center;
  position: relative;
  padding: 0 20px;
  font-size: 13px;
  font-weight: 500;
  text-transform: uppercase;

  &.row {
    color: #888;
    margin: 0 -8px;
    padding: 0;
    span {
      cursor: pointer;
      padding: 0 8px;
      &.active {
        color: @primary-link;
      }
    }
  }

  &:hover{
    //background: rgba(0,0,0,0.1);
  }

  &__toggle {
    display: flex;
    cursor: pointer;
    align-items: center;

    &:hover,
    &:focus {
      outline: none;
      color: @brand-primary;
    }
  }

  &__title {
    margin-right: 5px;
  }

  &__icon {
    font-size: 7.5px;
  }

  &__content {
    position: absolute;
    background-color: @white;
    top: 100%;
    right: 50%;
    margin-top: 5px;
    border-radius: 5px;
    border: 1px solid #ccc;
    z-index: 100;
    transform: translateX(50%);

    .item {
      padding: 5px 15px;
      cursor: pointer;
      color: #333;

      &:hover,
      &:focus {
        background-color: #eee;
        color: @dark;
        outline: none;
      }
    }
  }
}