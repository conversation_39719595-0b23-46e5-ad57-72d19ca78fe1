let _link = document.createElement('link');

let setThemeHref = ()=>{
    let setting = localStorage.getItem("develop_setting") || "{}";
    setting = JSON.parse(setting);
    _link.setAttribute('href', `/css/theme.${setting.theme_debug || theme_name || "default"}.css?v=${build_version}`);
}

let waitVisibility = function(){
    return new Promise((resolve)=>{
        let onVisibilityChange = ()=>{
            if (document.visibilityState == 'visible') {
                document.removeEventListener('visibilitychange', onVisibilityChange);
                resolve()
            }
        }            
        if(document.visibilityState == 'visible'){
            resolve()
        }else{
            document.addEventListener('visibilitychange', onVisibilityChange);
        }
    })
}

let RunService = async ()=>{
    if(wait_visibility)
        await waitVisibility();

    await iac.Core.Assets.script(`iac.kernel.js`,true);
    await iac.Core.Assets.script(`iac.service.js`,true);

    await iac.Service.Run({
        api_server: "//api.test.xt-xarid.uz",
        mocrm_server: 'https://api.mocrm.xt-xarid.uz'
    })  

    iac.Kernel.Develop.onChangeProperty.bind((event)=>{
        if(event.data.name == "theme_debug"){
            setThemeHref();
        }
    })
}

_link.onload = () => {
    if(!Array.prototype.flat || !Array.prototype.fill  || !self.fetch || !self.AbortController){
        let _script = document.createElement('script');
        _script.setAttribute('src', `/js/polyfill.js?v=1`);
        _script.onload = () => {
            RunService()
          }
          document.head.appendChild(_script)
    }else{
        RunService();   
    }
    _link.onload = undefined;
}

_link.setAttribute('rel', 'stylesheet');
setThemeHref();
document.head.appendChild(_link)