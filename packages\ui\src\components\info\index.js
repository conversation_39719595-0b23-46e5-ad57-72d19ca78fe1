import Language from "../../../../core/src/language";

export var Info = {
    name: "ui-info",
    props: ['label', 'status', 'value'],
    computed:{
        component_status() {
            if (typeof this.status == "string") {
                return {
                    type: this.status,
                    message: undefined
                }
            }
            return this.status;
        },
        classes() {
            return [
                (() => {
                    return this.component_status ? ["status", this.component_status.type] : '';
                })()
            ]
        },
        html(){
            let html = Language.t(this.label);
            return html.replace(/\n/gi,"</br>")
        }
    },
    template: `
        <div class='ui-info' v-html="html" v-bind:class="classes"></div>
    `
}