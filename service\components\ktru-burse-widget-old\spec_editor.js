import { Entity, DataSource } from '@iac/data'
import { Http, Language } from '@iac/core'

class ItemForm extends Entity {
  constructor(context) {
    super(context);
    const defaultName = { "en-US": "", "ru-RU": "", "uz-UZ@cyrillic": "", "uz-UZ@latin": "" }
    this.name = context.name || defaultName;
    this.ratio = context.ratio
    this.readonly = context.readonly || !context?.temporary?.is_new
  }

  props() {
    const { name, ratio, readonly } = this
    const formFields = {}
    Object.keys(name).forEach((lng) => formFields[lng] = {
      group: "name",
      validate() {
        if (this.value?.trim().length === 0)
          return "Обязательно к заполнению"
      },
      required: true,
      value: name[lng],
      readonly
    })
    if (ratio != undefined) {
      formFields['ratio'] = {
        group: "ratio",
        label: "!ratio",
        description: "ratio_about",
        validate() {
          if (this.value <= 0)
            return "Должно быть больше нуля"
        },
        type: "float",
        required: true,
        value: ratio,
        readonly
      }
    }
    return formFields
  }
}

export default {
  name: 'specEditor',
  props: {
    currentItem: {
      type: Object,
      required: true
    },
    mode: {
      type: String,
      default: 'add'//add/merge
    },
    onSelect: {
      type: Function,
      required: true
    },
    getGroups: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      staticFields: [],
      unitsFields: {},
      propsFields: [],
    }
  },
  methods: {
    async finishHim() {
      this.$wait(async () => {
        delete this.currentItem.__schema__
        const group = this.currentItem.temporary.group ?? {}
        delete this.currentItem.temporary.group

        this.currentItem.skp = this.currentItem.temporary.skp
        delete this.currentItem.temporary.skp
        this.currentItem.tnved = this.currentItem.temporary.tnved
        delete this.currentItem.temporary.tnved

        this.currentItem.group_id = group.id
        this.currentItem.groups = await this.getGroups(this.currentItem.group_id)

        let result = this.currentItem
        if (this.mode == 'merge') {
          const product = await this.getClearedNewProduct()//await this.currentItem.is_new ? this.getClearedNewProduct() : this.getMergedOldProduct()
          result = { id: this.currentItem.id, product }
        }

        this.onSelect(result)
      });
    },
    async getClearedNewProduct() {
      const { group_id, name, skp = "", tnved = "", units, properties } = this.currentItem
      return {
        group_id,
        name,
        skp,
        tnved,
        units: units.map(({ name, ratio }) => ({ name, ratio })),
        properties: properties.map(({ name, values }) => ({ name, values: values.map(({ name }) => ({ name })) }))
      }
    },
    /*async getMergedOldProduct() {
      const { id,group_id, name, skp, tnved, units, properties } = this.currentItem
      const { error, data } = await Http.api.rpc("ref", {
        ref: "ref_enkt_burse_product",
        op: "read",
        limit: 1,
        offset: 0,
        filters: { id },
        fields: ['product']
      })
      if (!error && data && data.product) {
        const oldProduct=data.product
        oldProduct.name
      }else {
          Vue.Dialog.MessageBox.Error(error)
      }
    },*/
    async editItem(item = {}, caption = "") {
      return await Vue.Dialog.MessageBox.Form({ fields: (new ItemForm({ ...item })).fields, caption })
    },
    async showItem(item = {}, caption = "") {
      return await Vue.Dialog.MessageBox.Info({ fields: (new ItemForm({ ...item, readonly: true })).fields, caption })
    },
    setStaticFields() {
      const currentItem = this.currentItem ?? {}
      const { groups, skp, tnved, master_unit } = currentItem

      this.staticFields = []
      if (this.mode != "show" && (this.currentItem?.temporary?.is_new || this.mode == "merge")) {
        this.staticFields.push({
          group: "<product_name>",
          label: "product_name",
          readonly: true,
          value: this.currentItem.temporary.currentName,
          status: { type: 'success' }
        })
        this.staticFields.push({
          group: "<product_name>",
          label: "!",
          type: "action",
          buttons: true,
          actions: [
            {
              icon: 'edit',
              btn_type: "warning",
              handler: async () => {
                const changedName = await this.editItem(this.currentItem, Language.t("ktru_burse_edit_product_name"))
                if (changedName && (changedName != 2)) {
                  this.currentItem.name = {
                    "ru-RU": changedName["ru-RU"],
                    "en-US": changedName["en-US"],
                    "uz-UZ@cyrillic": changedName["uz-UZ@cyrillic"],
                    "uz-UZ@latin": changedName["uz-UZ@latin"]
                  }
                  const name = this.currentItem.name
                  this.currentItem.temporary.currentName = name[Language.local] || name[Object.keys(name)[0]]
                  this.setStaticFields()
                }
              }
            }
          ]
        })
      } else {
        this.staticFields.push({ label: "-product_name", type: "static", value: this.currentItem.temporary.currentName })
      }

      if (this.mode == "merge") {
        this.staticFields.push({
          label: "-group",
          type: "entity",
          get value() { return currentItem.temporary.group },
          set value(newVal) { currentItem.temporary.group = newVal },
          dataSource: new DataSource({
            displayExp: "name.ru-RU",
            query: { parent_id: { value: null } },
            store: {
              ref: "ref_enkt_burse_product_groups",
              injectQuery(params) {
                params.fields = ['id', 'parent_id', 'name', 'meta', 'has_children']
                return params
              }
            }
          })
        })
      } else {
        this.staticFields.push({ label: "-group", type: "static", value: groups.map(group => group.temporary.currentName).join(' / ') })
      }

      if (this.mode == "merge") {
        this.staticFields.push({
          label: "-skp",
          get value() { return currentItem.temporary.skp || skp },
          set value(newVal) { currentItem.temporary.skp = newVal }
        })
        this.staticFields.push({
          label: "-tnved",
          get value() { return currentItem.temporary.tnved || tnved },
          set value(newVal) { currentItem.temporary.tnved = newVal }
        })
      } else {
        this.staticFields.push({ label: "-skp", type: "static", value: skp })
        this.staticFields.push({ label: "-tnved", type: "static", value: tnved })
      }

      this.staticFields.push({ label: "-master_unit", type: "static", value: master_unit ? `${master_unit.temporary.currentName} (${master_unit.ratio})` : "" })
    },
    getUnitGridActions() {
      const gridActions = []
      if (this.mode == 'show') {
        return gridActions
      }

      if (this.mode === 'add') {
        gridActions.push({
          label: "add", handler: async () => {
            const newUnit = await this.editItem({ ratio: 1, temporary: { is_new: true } }, Language.t("ktru_burse_add_new_unit"))
            if (newUnit && (newUnit != 2)) {
              const name = {
                "ru-RU": newUnit["ru-RU"],
                "en-US": newUnit["en-US"],
                "uz-UZ@cyrillic": newUnit["uz-UZ@cyrillic"],
                "uz-UZ@latin": newUnit["uz-UZ@latin"]
              }
              this.currentItem.units.push({
                name,
                ratio: newUnit.ratio,
                temporary: { is_new: true, currentName: name[Language.local] || name[Object.keys(name)[0]] }
              })
              if (!this.currentItem.master_unit) {
                this.currentItem.master_unit = this.currentItem.units.find(unit => unit.ratio == 1)
              }
              this.setUnitsFields()
              this.setStaticFields()
            }
          }
        })
      }
      return gridActions
    },
    getUnitRowActions(unit) {
      const rowActions = []
      if (this.mode == 'show') {
        return rowActions
      }
      if (this.mode === 'add' && unit.temporary.is_new) {
        rowActions.push({
          icon: "edit", btn_type: "warning", handler: async () => {
            const changedUnit = await this.editItem(unit, Language.t("ktru_burse_edit_new_unit"))
            if (changedUnit && (changedUnit != 2)) {
              unit.name = {
                "ru-RU": changedUnit["ru-RU"],
                "en-US": changedUnit["en-US"],
                "uz-UZ@cyrillic": changedUnit["uz-UZ@cyrillic"],
                "uz-UZ@latin": changedUnit["uz-UZ@latin"]
              }
              unit.ratio = changedUnit.ratio
              unit.temporary.currentName = unit.name[Language.local] || unit.name[Object.keys(unit.name)[0]]
              if (!this.currentItem.master_unit) {
                this.currentItem.master_unit = this.currentItem.units.find(unit => unit.ratio == 1)
              }
              this.setUnitsFields()
              this.setStaticFields()
            }
          }
        })
      } else {
        rowActions.push({ icon: "eye", btn_type: "info", handler: () => this.showItem({ ...unit }, Language.t("ktru_burse_show_unit")) })
      }
      if (this.mode === 'add' && unit.temporary.is_new) {
        rowActions.push({
          icon: "delete", btn_type: "danger", handler: () => {
            if (unit.temporary.is_new) {
              const indexToDelete = this.currentItem.units.indexOf(unit)
              if (indexToDelete !== -1) {
                this.currentItem.units.splice(indexToDelete, 1)
                if (!this.currentItem.master_unit) {
                  this.currentItem.master_unit = this.currentItem.units.find(unit => unit.ratio == 1)
                }
                this.setUnitsFields()
                this.setStaticFields()
              }
            }
          }
        })
      }
      return rowActions
    },
    setUnitsFields() {
      this.unitsFields = {
        label: "units",
        type: "data-grid",
        dataSource: new DataSource({
          actions: this.getUnitGridActions(),
          store: {
            data: this.currentItem.units.map(unit => ({ name: unit.temporary.currentName, ratio: unit.ratio, actions: this.getUnitRowActions(unit), is_new: unit.temporary.is_new })),
            context: (context) => {
              Object.defineProperty(context, "bindClass", { get: () => context.is_new ? "ui-alert ui-alert-success" : undefined })
              return context
            }
          }
        }),
        attr: {
          buttons: true,
          summary: false,
          columns: [
            { field: "name", label: "name", style: "text-align:left; width: 100%" },
            { field: "ratio", label: "ratio", style: "text-align:right; white-space: nowrap;" },
          ]
        },
      }
    },
    getPropGridActions(prop) {
      const gridActions = []
      if (this.mode == 'show') {
        return gridActions
      }

      if (this.mode === 'add') {
        gridActions.push({
          label: "add", handler: async () => {
            const newVal = await this.editItem({ temporary: { is_new: true } }, Language.t("ktru_burse_add_new_prop_val"))
            if (newVal && (newVal != 2)) {
              const name = {
                "ru-RU": newVal["ru-RU"],
                "en-US": newVal["en-US"],
                "uz-UZ@cyrillic": newVal["uz-UZ@cyrillic"],
                "uz-UZ@latin": newVal["uz-UZ@latin"]
              }
              prop.values.push({
                name,
                temporary: { is_new: true, currentName: name[Language.local] || name[Object.keys(name)[0]] }
              })
              this.setPropsFields()
            }
          }
        })
      }
      if (this.mode === 'add' && prop.temporary.is_new) {
        gridActions.push({
          icon: "edit", btn_type: "warning", handler: async () => {
            const changedProp = await this.editItem(prop, Language.t("ktru_burse_change_new_prop"))
            if (changedProp && (changedProp != 2)) {
              prop.name = {
                "ru-RU": changedProp["ru-RU"],
                "en-US": changedProp["en-US"],
                "uz-UZ@cyrillic": changedProp["uz-UZ@cyrillic"],
                "uz-UZ@latin": changedProp["uz-UZ@latin"]
              }
              prop.temporary.currentName = prop.name[Language.local] || prop.name[Object.keys(prop.name)[0]]
              this.setPropsFields()
            }
          }
        })
      } else {
        gridActions.push({ icon: "eye", btn_type: "info", handler: () => this.showItem({ ...prop }, Language.t("ktru_burse_show_prop")) })
      }
      if (this.mode === 'add' && prop.temporary.is_new) {
        gridActions.push({
          icon: "delete", btn_type: "danger", handler: () => {
            if (prop.temporary.is_new) {
              const indexToDelete = this.currentItem.properties.indexOf(prop)
              if (indexToDelete !== -1) {
                this.currentItem.properties.splice(indexToDelete, 1)
                this.setPropsFields()
              }
            }
          }
        })
      }
      return gridActions
    },
    getValRowActions(prop, val) {
      const rowActions = []
      if (this.mode == 'show') {
        return rowActions
      }

      if (this.mode === 'add' && val.temporary.is_new) {
        rowActions.push({
          icon: "edit", btn_type: "warning", handler: async () => {
            const changedVal = await this.editItem(val, Language.t("ktru_burse_change_new_prop_val"))
            if (changedVal && (changedVal != 2)) {
              val.name = {
                "ru-RU": changedVal["ru-RU"],
                "en-US": changedVal["en-US"],
                "uz-UZ@cyrillic": changedVal["uz-UZ@cyrillic"],
                "uz-UZ@latin": changedVal["uz-UZ@latin"]
              }
              val.temporary.currentName = val.name[Language.local] || val.name[Object.keys(val.name)[0]]
              this.setPropsFields()
            }
          }
        })
      } else {
        rowActions.push({ icon: "eye", btn_type: "info", handler: () => this.showItem({ ...val }, Language.t("ktru_burse_show_prop_val")) })
      }
      if (this.mode === 'add' && val.temporary.is_new) {
        rowActions.push({
          icon: "delete", btn_type: "danger", handler: () => {
            if (val.temporary.is_new) {
              const indexToDelete = prop.values.indexOf(val)
              if (indexToDelete !== -1) {
                prop.values.splice(indexToDelete, 1)
                this.setPropsFields()
              }
            }
          }
        })
      }
      return rowActions
    },
    setPropsFields() {
      this.propsFields = this.currentItem.properties.map(prop => ({
        label: prop.temporary.currentName,
        type: "data-grid",
        dataSource: new DataSource({
          actions: this.getPropGridActions(prop),
          store: {
            data: prop.values.map(val => ({ value: val.temporary.currentName, actions: this.getValRowActions(prop, val), is_new: val.temporary.is_new })),
            context: (context) => {
              Object.defineProperty(context, "bindClass", { get: () => context.is_new ? "ui-alert ui-alert-success" : undefined })
              return context
            }
          }
        }),
        attr: {
          buttons: true,
          summary: false,
          columns: [{ field: "value", label: "value", style: "text-align:left; width: 100%" }],
          style: prop.temporary.is_new ? "color: #3c763d; background-color: #dff0d8;" : undefined
        }
      }))
    },
    async addProperty() {
      const newProp = await this.editItem({ temporary: { is_new: true } }, Language.t("ktru_burse_add_new_prop"))
      if (newProp && (newProp != 2)) {
        const name = {
          "ru-RU": newProp["ru-RU"],
          "en-US": newProp["en-US"],
          "uz-UZ@cyrillic": newProp["uz-UZ@cyrillic"],
          "uz-UZ@latin": newProp["uz-UZ@latin"]
        }
        this.currentItem.properties.push({
          name,
          values: [],
          temporary: { is_new: true, currentName: name[Language.local] || name[Object.keys(name)[0]] }
        })
        this.setPropsFields()
      }
    },
    allNamesIsValidForFinish(item) {
      if (!item?.name || !Object.keys(item.name)) {
        return false
      }
      return Object.keys(item.name).every(lng => Boolean(item.name[lng].trim()))
    },
    unitsIsValidForFinish() {
      return this.currentItem.units.every(unit => !unit.temporary.is_new || this.allNamesIsValidForFinish(unit))
    },
    propValuesIsValidForFinish(prop) {
      return prop.values.length && prop.values.every(val => !val.temporary.is_new || this.allNamesIsValidForFinish(val))
    },
    propsIsValidForFinish() {
      return this.currentItem.properties.every(prop => !prop.temporary.is_new || this.allNamesIsValidForFinish(prop)) &&
        this.currentItem.properties.every(prop => this.propValuesIsValidForFinish(prop))
    },
    newDataIsExist() {
      return this.currentItem.units.some(unit => unit.temporary.is_new)
        || this.currentItem.properties.some(prop => prop.temporary.is_new || prop.values.some(val => val.temporary.is_new))
    },
    mergerValidation() {
      if (this.mode != 'merge') {
        return true
      }
      const { group, skp, tnved } = this.currentItem?.temporary ?? {}
      return group && skp && tnved && this.currentItem.master_unit
    },
    staticValidation() {
      return this.allNamesIsValidForFinish(this.currentItem) && this.currentItem.master_unit
    }
  },
  computed: {
    enableFinalButton() {
      const { unitsIsValidForFinish, propsIsValidForFinish, newDataIsExist, mergerValidation, staticValidation } = this
      //console.log('===ENABLE===\n', mergerValidation(), staticValidation(), newDataIsExist(), unitsIsValidForFinish(), propsIsValidForFinish())
      /*return mergerValidation()
        && staticValidation()
        && newDataIsExist()
        && unitsIsValidForFinish()
        && propsIsValidForFinish()*/
      return staticValidation()
        && newDataIsExist()
        && unitsIsValidForFinish()
        && propsIsValidForFinish()
    }
  },
  mounted() {
    //console.log('===ASDER===', this.currentItem)
  },
  created() {
    this.setStaticFields()
    this.setUnitsFields()
    this.setPropsFields()
  },
  template: `
  <ui-layout-group label="new_specification_editor" style="padding:5px" v-if="currentItem">
    <ui-layout style="padding:5px" v-if="currentItem" :fields="staticFields"/>
    <ui-field :model="unitsFields"/>
    <ui-layout-group label="properties">
      <ui-field v-for="(propFields,key) in propsFields" :key="key" :model="propFields"/>
    </ui-layout-group>
    <ui-btn v-if="!['show','merge'].includes(mode)" type="primary" @click.native="addProperty">{{$t('add_property')}}</ui-btn>
    <div class="iac--ktru__spec_editor-footer">
      <button v-if="mode=='show'" @click="e=>onSelect()">{{$t('close')}}</button>  
      <button v-else-if="mode=='merge'" :disabled="!enableFinalButton" @click="finishHim">{{$t('agreementing')}}</button>
      <button v-else :disabled="!enableFinalButton" @click="finishHim">{{$t('add')}}</button>
    </div>
    </ui-layout-group>
    `
}