
import {Language} from '@iac/core'
import { Tick } from '@iac/kernel';

const Component = {
    props: ['date', 'type'],
    data: function () {
        return {
            interval_id: undefined,
            duration: (new Date(this.date) - Date.now()) / 1000
        }
    },
    mounted() {
      Tick.onTick.bind(this.startTimer);
      this.startTimer();
    },
    destroyed(){
      Tick.onTick.unbind(this.startTimer);
    },
    computed: {
        display() {
            let {days,hours,minutes,seconds} = this.convert();

            switch(this.type){
                default:
                    let display = `${('00' + hours).slice(-2)}:${('00' + minutes).slice(-2)}:${('00' + seconds).slice(-2)}`;
                    if(days > 0){
                        display = `${days} ${Language.t('day', {count: days})} ${display}`;
                    }
                    return display
            }

        }
    },
    methods: {
        startTimer() {
          this.duration = (new Date(this.date) - Date.now()) / 1000;
          if (this.duration <= 0) {
            Tick.onTick.unbind(this.startTimer);
          }
        },
        convert(duration = this.duration) {
            var sec_num = parseInt(duration, 10) || 0;
            if (sec_num < 0)
                sec_num = 0;
            var days = Math.floor(sec_num / 86400);
            var hours = Math.floor((sec_num - (days * 86400)) / 3600);
            var minutes = Math.floor((sec_num - (days * 86400) - (hours * 3600)) / 60);
            var seconds = sec_num - (days * 86400) - (hours * 3600) - (minutes * 60);

            return {
                days,
                hours,
                minutes,
                seconds
            }
        }
    },
    template: `
        <span class='iac-timer'>{{display}}</span>
    `
}

Vue.component('iac-timer', Component);