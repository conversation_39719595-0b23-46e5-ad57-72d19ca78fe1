import { Http } from '@iac/core'
import { DataSource } from '@iac/data'

import Proposal from './proposal'

const Key = {
    List: Symbol('List'),
    bids: Symbol('bids')
}

// tp:procs:ID
// tp:procs:ID:order
// tp:procs:ID:general
export default class Contract {

    static Event_General = 1;
    static Event_Order = 2;
    static Event_ALL = Contract.Event_General | Contract.Event_Order;

    constructor(context) {

        this.id = context.id

        this._status = undefined;
        this.event_started = undefined;
        this.event_ends = undefined;

        this.listeners_general_count = 0
        this.listeners_order_count = 0

        this.general_channel = undefined;
        this.order_channel = undefined;

        this.bids_source = {};
        this.start = undefined
        this.key_bids = undefined

        document.addEventListener('visibilitychange', this.visibilitychange);

        //this.sourceHistory = DataSource.get("ref_country_");

        this.sourceHistory = new DataSource({
            //limit: 10,
            query: {
                proc_id: this.id
            },
            store: {
                host: Http.proc,
                method: "get_expositions",
                context: (context)=>{
                    switch(context.status){
                        case undefined:
                        case "":
                        case null:
                            context.bindClass = "ui-alert ui-alert-danger";
                            break
                        case "on_sale":
                            context.bindClass = "ui-alert ui-alert-success";
                            break
                        case "commited":
                            context.bindClass = "ui-alert ui-alert-warning";
                            break                           
                    }

                    return context;
                }
            }
        })
    }

    get status() {
        return this._status;
    }
    set status(value) {
        if (this.status == value)
            return;

        if ((['commited','on_sale','closed'].includes(this.status))){
            Proposal._list = Proposal.list.filter((b)=>{
                return b.contract_id != this.id;
            })
            this.sourceHistory.reload();
        }
        this._status = value;
    }

    get timer() {
        if (this.status == "on_sale")
            return this.event_ends;
        return this.event_started;
    }

    update_general(data) {
        
        if (this.status != data.status) {
            this.set_bids({},true)
        }

        this.status = data.status;
        this.event_started = data.event_started || this.event_started;
        this.event_ends = data.event_ends || this.event_ends;

        let start = ["annul", "extra", "new", "norealized"].map((type) => {
            let start = {
                amount: data[`amount_of_${type}_lots`],
                price: data[`price_for_${type}_lot`],
            }
            return start.amount && start.price && start;
        }).filter((start) => {
            return start;
        })

        if (start.length > 0)
            this.set_start(start)

    }

    set_bids(bids, clear = false) {
        this.bids_source = clear ? { ...bids } : { ...this.bids_source, ...bids }
        this.key_bids = undefined;
    }

    set_start(start) {
        this.start = [...start.map((item,index)=>{item.color=index;item.reserved=0;return item})].sort((a, b) => {
            if(a.price == b.price)
                return 0;
            return a.price > b.price ? -1 : 1
        })
        this.key_bids = undefined;
    }


    get bids() {
        if (!this.key_bids && this.start) {
            let current_price = 0;

            this.key_bids = Object.keys(this.bids_source).reduce((acc, price) => {
                this.bids_source[price].forEach(bid => {

                    let type = 1
                    let bid_id = undefined;
                    if (Array.isArray(bid)) {
                        type = 2
                        bid_id = bid[0]
                        bid = bid[1]
                    }
                    acc.push({
                        price: Number(price),
                        bid_id: bid_id,
                        amount: Math.abs(bid),
                        type: type
                    })

                });
                return acc;
            }, [...this.start.map((item,index) => {item.color=index; item.reserved = 0; return item })]).sort((a, b) => {
                if (a.price == b.price) {
                    if ((a.type == undefined || b.type == undefined) && (a.type || 0) > (b.type || 0)) {
                        return -1;
                    }
                }
                if(a.price == b.price)
                return 0;
                return a.price > b.price ? -1 : 1
            }).map((item) => {

                if (!item.type)
                    return item;
                let next = false;
                item.processed = 0;
                item.remains = item.amount;
                item.distribution = [];
                while (!next) {
                    let cur_start = this.start[current_price];
                    if (!cur_start) {
                        break;
                    }

                    if (item.price < cur_start.price) {
                        current_price++;
                        if (current_price > this.start.length) {
                            next = true;
                        }
                    } else {

                        let remains = cur_start.amount - (cur_start.reserved || 0);
                        item.processed = item.processed || 0

                        if (remains <= (item.amount - item.processed)) {
                            cur_start.reserved = (cur_start.reserved || 0) + remains;
                            item.distribution = item.distribution || [];
                            if(remains > 0) // ХЗ - НАДО ПЕРЕПРОВЕРИТЬ
                                item.distribution.push({ amount: remains, start: current_price })
                            item.remains = item.remains - remains;
                            item.processed += remains
                            // item.remains = item.amount - item.processed;

                            if (item.processed < item.amount) {

                                current_price++;
                                if (current_price > this.start.length) {
                                    next = true;
                                }
                            } else {

                                next = true;
                            }
                        } else {
                            remains = remains - (item.amount - item.processed);
                            cur_start.reserved = cur_start.amount - remains;
                            item.distribution = item.distribution || [];
                            item.distribution.push({ amount: (item.amount - item.processed), start: current_price })
                            item.remains = item.remains - (item.amount - item.processed);
                            //item.remains = item.amount - item.processed;
                            item.processed += remains
                            next = true;
                        }
                    }
                }

                item.info = {
                    bid_id: item.bid_id,
                    type: item.type,
                    price: item.price,
                    amount: item.amount,
                }

                item.distribution.forEach((i, index) => {
                    let prop = `_${i.start}`
                    item.info[prop] = i.amount;
                })
                return item;
            })
        }
        return this.key_bids || []
    }


    get listeners_count() {
        return this.listeners_general_count + this.listeners_order_count
    }

    join() {
        if (!this.general_channel && this.listeners_general_count > 0) {

            Http.proc.socket.join(`tp:procs:${this.id}:general`, (channel) => {
                this.general_channel = channel;
                this.status = 'init'
                channel.on('upd', (data = {}) => {
                    this.update_general(data);
                });
                channel.joinPush
                    .receive("ok", (data) => {
                        this.update_general(data);
                    })
                    .receive("error", (data) => {
                        this.channel = undefined
                        this.status = undefined;
                    })
            }, true);
        }

        if (!this.order_channel && this.listeners_general_count > 0) {

            Http.proc.socket.join(`tp:procs:${this.id}:order`, (channel) => {
                this.order_channel = channel;
                channel.on('upd', (data = {}) => {

                    this.set_bids(data)
                });
                channel.joinPush
                    .receive("ok", (data) => {

                        this.set_bids(data, true)
                    })
                    .receive("error", (data) => {
                        this.channel = undefined
                    })
            }, true);
        }

    }
    leave() {
        if (this.listeners_general_count <= 0 || document.visibilityState != 'visible') {
            if (this.general_channel) {
                Http.proc.socket.leave_channel(this.general_channel);
            }
            this.general_channel = undefined;
        }

        if (this.listeners_order_count <= 0 || document.visibilityState != 'visible') {
            if (this.order_channel) {
                Http.proc.socket.leave_channel(this.order_channel);
            }
            this.order_channel = undefined;
        }
    }

    watch(event = Contract.Event_ALL) {
        if ((event & Contract.Event_General) != 0) {
            this.listeners_general_count++;
        }
        if ((event & Contract.Event_Order) != 0) {
            this.listeners_order_count++;
        }
        this.join()
    }

    unwatch(event = Contract.Event_ALL) {
        if ((event & Contract.Event_General) != 0) {
            this.listeners_general_count--;
        }
        if ((event & Contract.Event_Order) != 0) {
            this.listeners_order_count--;
        }

        this.leave();

        if (this.listeners_count <= 0) {
            document.removeEventListener('visibilitychange', this.visibilitychange);
            delete Contract[Key.List][this.id];
        }

    }

    visibilitychange = (data) => {
        if (document.visibilityState == 'visible') {
            this.join();
        } else if (document.visibilityState == 'hidden') {
            this.leave();
        }
    }

    static [Key.List] = {};
    static get(context) {
        Contract[Key.List][context.id] = Contract[Key.List][context.id] || new Contract(context)
        return Contract[Key.List][context.id]
    }
}