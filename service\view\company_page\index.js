import { Http, Language, ModelProvider } from '@iac/core'
import { DataSource } from '@iac/data'
import { Context } from '@iac/kernel'

export default {
    data: function () {
        return {
            model: undefined,
            error: undefined
        }
    },
    mounted() {
        this.updateModel();
    },
    computed: {
        hasEdit(){
            if(Context.User.team_id == this.$route.params.id || Context.Access.policy['system_page_edit'])
                return true
            return false;
        }
    },
    methods: {
        onEdit() {
            let company_model = undefined;
            this.$wait(async () => {
                let { data, error } = await ModelProvider['company_model'].get(this.$route.params.id)

                if (error){
                    return await Vue.Dialog.MessageBox.Error(error);;
                }

                Vue.Dialog({
                    props: ["model"],
                    methods: {
                        save() {
                            this.$wait(async () => {
                                let { error, data } = await this.model.save();
                                if (error) {
                                    await Vue.Dialog.MessageBox.Error(error);
                                }
                            })
                        }
                    },
                    template: `
                <div>
                    <main><ui-layout :fields='model.fields'/></main>
                    <footer>
                        <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
                        <ui-btn type='primary' v-on:click.native='save'>{{$t('save')}}</ui-btn>
                    </footer>
                </div>
                `
                }).Modal({
                    model: data,
                    size: "lg"
                });
            })
        },
        updateModel() {
            this.$wait(async () => {
                const is_broker = Boolean(this.$route.query.is_broker);

                let { data, error } = await Http.api.rpc("team_attr", {
                    a_id: this.$route.params.id,
                    is_broker: is_broker
                });

                if (error !== undefined) {
                    this.error = error;
                    return;
                }

                if (!data) {
                    return;
                }

                data = Array.isArray(data) ? data[0] : data;
                if (!data) {
                    this.error = {
                        status: "501",
                        message: Language.t("no_data")
                    }
                    return;
                }

                if (is_broker) {
                    let broker_request_t = Http.api.rpc("company_ref", {
                        ref: "broker_request",
                        op: "read",
                        filters: {
                          company_id: Context.User.team_id,
                          broker_company_id: parseInt(this.$route.params.id),
                          status: ["wait", "accept"],
                        },
                    });

                    let has_broker_t = Http.api.rpc("company_ref", {
                        ref: "broker_request",
                        op: "has_broker"
                    });

                    let { error: br_error, data: br_data } = await broker_request_t;
                    if (br_error) { Vue.Dialog.MessageBox.Error(br_error); }

                    let { error: hbr_error, data: hbr_data } = await has_broker_t;
                    if (hbr_error) { Vue.Dialog.MessageBox.Error(hbr_error); }

                    data.broker_request = br_data[0];
                    data.has_broker = hbr_data;
                    data.is_broker = is_broker;
                }

                let legal_area = await DataSource.get("ref_area").byKeys(data.legal_area_id);
                data.legal_area = legal_area && legal_area[0] ? legal_area[0].name : legal_area;
                this.model = data;
            })
        }
    },
    template: `
        <div>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{model && model.title}}</li>
                </ol>
                <div class='title'>
                    <h1>{{model && model.title}}</h1>
                    <div><ui-btn v-if='hasEdit' type='primary' v-on:click.native='onEdit'>{{$t('edit')}}</ui-btn></div>
                </div>
            </iac-section>
            <iac-section>
                <widget-company-page @update-company='updateModel()' v-if="model" :item='model'/>
                <div v-else-if='error' style='margin-top: 30px'>
                    <ui-error :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
                </div>
            </iac-section>
        </div>
    `
}
