import Vue from 'vue';

const Component = {
  props: ['item'],
  methods: {
    onTag() {
      this.$emit('onTag', this.item.category_id + '');
    },
  },
  template: `
    <article class='news-card'>
      <div v-html='item.digest_pic' class='news-card__image'></div>
      <div class='news-card__wrap'>
        <div class='news-meta'>  
          <iac-date class='news-date news-meta__date' :date='item.created_at' withoutTime />
          <a v-if='item.category' @click.prevent='onTag' href='#'
            class='ui-tag-item'>{{ item.category }}</a>
        </div>
        <h3 class='news-title'>
          <router-link :to='"news/" + item.id' class='news-card__link'>{{ item.title }}</router-link>
        </h3>
        <div v-html='item.digest_txt' class='news-card__content'></div>
      </div>
    </article>
  `,
};

Vue.component('widget-news-card', Component);
