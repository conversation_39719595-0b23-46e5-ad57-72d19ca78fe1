#iac-dialog-container{
    z-index: 4000;
    position: fixed;
}

.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll;
  }

body.modal_show {
    overflow: hidden;
    //padding-right: 12px;
}

@keyframes animation_show_right_dlg {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

.iac-dialog {
    .iac-dialog_backdrop {
        position: fixed;
        background: rgba(0, 0, 0, 0.5);
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 1030;
    }
    .iac-dialog_modal {
        overflow-x: hidden;
        overflow-y: auto;
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 1030;
        .iac-dialog_modal_box {
            position: relative;
            width: auto;
            margin: 15px;
            pointer-events: none;

            &.modal-right {
                //max-width: 320px;
                margin: 0 0 0 auto;

                animation-name: animation_show_right_dlg;
                animation-duration: 300ms;
                //animation-iteration-count: infinite;
                animation-timing-function: cubic-bezier();

                .iac-dialog_modal_box-content {
                    border-radius: 0;
                    min-height: 100vh;
                }
                .iac-dialog_modal_box-close{
                    // position: fixed;
                }

            }

            @media (min-width: 576px) {
                max-width: 520px;
                margin: 30px auto;
                &.modal-sm {
                    max-width: 300px;
                }

                &.modal-lg {
                    max-width: 600px;
                }

                &.modal-full {
                    margin: 30px auto;
                    max-width: 1300px;
                }
            }
            @media (min-width: 992px) {
                &.modal-lg {
                    max-width: 800px;
                }
                &.modal-full {
                    margin: 30px auto;
                    //max-width: 992px;
                }
            }

            .iac-dialog_modal_box-close{
                position: absolute;
                width: 24px;
                height: 24px;
                top: 0;
                right: 0;
                text-align: center;
                font-size: 10px;
                line-height: 24px;
                color: #868e96;
                cursor: pointer;
                pointer-events: all;
                margin: 3px;
                border-radius: 3px;
                z-index: 10;
                &:hover{
                    color: #666;
                    background: #eee;
                }
            }

            .iac-dialog_modal_box-content {
                position: relative;
                background: #fff;
                box-shadow: 0 0 25px rgba(0, 0, 0, 0.4);
                border-radius: 5px;

                display: flex;
                flex-direction: column;
                pointer-events: auto;
                outline: 0;

                >header,
                >.iac-dialog-header{
                    display: flex;
                    align-items: flex-start;
                    justify-content: space-between;
                    padding: 24px 24px 0;
                    border-top-left-radius: .3rem;
                    border-top-right-radius: .3rem;
                    font-size: 20px;
                    line-height: 26px;
                    color: #000;
                }
                >main,
                >.iac-dialog-body{
                    position: relative;
                    flex: 1 1 auto;
                    padding: 0 24px;
                    //color: #999;
                    margin-top: 20px;
                    &.error {
                        background: #f8d7da;
                        color: #721c24;
                        padding: 16px 24px;
                        font-size: 14px;
                        white-space: pre-line;
                        ul {
                            list-style: none;
                            padding: 0;
                            margin: 0;
                        }
                    }
                    &.success {
                        background: #d4edda;
                        color: #155724;
                        padding: 16px 24px;
                        font-size: 14px;
                        white-space: pre-line;
                    }
                    &.warning {
                        background: @alert-warning-bg;
                        color: @alert-warning-text;
                        padding: 16px 24px;
                        font-size: 14px;
                        white-space: pre-line;
                    }
                    &.info {
                        background: @alert-info-bg;
                        color: @alert-info-text;
                        padding: 16px 24px;
                        font-size: 14px;
                        white-space: pre-line;
                    }
                }
                >footer,
                >.iac-dialog-footer {
                    margin: 24px;
                    display: flex;
                    justify-content: flex-end;
                    > :not(:last-child) {
                        margin-right: 8px;
                    }
                }

            }

            .tab-content-action .sticky{
                top: 0;
            }


        }
    }
}

.iac-wait{
    position: relative;
    &:before{
        content: "";
        position: absolute;
        top: 0;left:0;
        right: 0;
        bottom: 0;
        height: 100%;
        display: block;
        background: rgba(255, 255, 255, 0.5);
        z-index: 101;
    }
    &:after {
        display: inline-block;
        font-family: 'iac-icon';
        font-feature-settings: "liga" 1;
        text-transform: none !important;
        user-select: none;
        content: "spinner";
        position: absolute;
        left: calc(50% - 20px);
        top: calc(50% - 20px);
        font-size: 40px;
        line-height: 40px;
        width: 40px;
        height: 40px;


        animation-name: spin;
        animation-duration: 4000ms;
        animation-iteration-count: infinite;
        animation-timing-function: linear;

        z-index: 102;
    }
}