import {DataSource} from '@iac/data'
export default {
    data: function(){
        return {
            source: new DataSource({
                store: {
                    method: 'get_error_list',
                    injectQuery: (params)=>{
                        params.query = params.queryText;
                        params.queryText = undefined;
                        return params;
                    },
                    context: (context)=>{
                        return {
                            header: [new Date(context.created_at)],
                            title: {text: context.code, link: `/error/${context.code}`},
                            description: [
                                {text: context.content }
                            ]
                        }
                    }
                }
            })
        }
    },
    template: `
        <iac-access :access='1 || $policy.error_view'>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('errors')}}</li>
                </ol>
                <div class='title'>
                    <h1>{{$t('errors')}}</h1>
                </div>
            </iac-section>
            <iac-section>
                <ui-data-view :dataSource='source' />
            </iac-section>
        </iac-access>
    `
}