

import { Http } from '@iac/core'
import Data from './../../data'

import ecp from './../../ecp'

import Config from './../../config'
import Context from './../../context'


var QuestionDlg = Vue.Dialog({
    props: ["model", "title", "error","submit", "actions"],
    data: function(){

        return {
            
        }
    },
    computed: {
        disabled_send() {
            return this.model.validate(false)
        },
        buttons(){
            let buttons = {...this.actions};
            buttons.submit = buttons.submit != undefined ?  buttons.submit : this.submit
            
            let count = 0;
            for(let key in buttons){
                if(typeof buttons[key] == 'boolean'){
                    buttons[key] = {
                        key: key,
                        //label:  key,
                        hidden: !buttons[key]
                    }
                }else if(typeof buttons[key] == 'string'){
                    buttons[key] = {
                        key: key,
                        label: buttons[key],
                        hidden: false
                    }
                }
                buttons[key].key = buttons[key].key || key
                buttons[key].validate = buttons[key].validate != undefined ? buttons[key].validate : true
                if(buttons[key].params)
                    buttons[key].validate = false  

                if(key != 'close' && key != 'submit'){
                    buttons[key].order = buttons[key].order || 0
                    buttons[key].label = buttons[key].label || buttons[key].key || key
                }

                //if(key == 'close' || key == 'submit'){
                //    delete buttons[key].order;
                //    delete buttons[key].label
                //}

                if(!buttons[key].hidden && key != 'close')
                    count++;
            }
            buttons.submit = {...{
                order: 10,
                label: "send",
            },...buttons.submit}
            buttons.close = buttons.close || {};

            buttons.close = {...{
                order: -10,
                label: count < 1 ? "close" : "cancel",
                key: "close",
                type: "secondary"
            },...buttons.close}

            return Object.keys(buttons).map((key)=>{
                return buttons[key]
            }).sort((a,b)=>{
                return a.order - b.order
            });
        }
    },
    methods: {
        async send(context) {
            let {key, question, subscribe, params} = context;
            
            if (question && await Vue.Dialog.MessageBox.Question(question) != Vue.Dialog.MessageBox.Result.Yes){
                return;
            }

            if(key=='submit')
                key = undefined;
            if(key=='close')
                return this.Close();
            await this.$wait(async () => {
                let { error, data } = await this.model.send(key, params, subscribe);
                if (error && error.code == 'AbortError') {
                    return;
                    //this.Close({
                    //    error
                    //})
                }

                if (error && (error.fields || error.status == 303))
                this.Close({
                    error
                })

                if (!error)
                    this.Close({
                        data
                    })
            })
        }
    },
    template: `
        <div>
            <header v-if='title'>{{title}}</header>
            <main class='error' v-if='error && error'>
                <div>{{error}}</div>
            </main>
            <main>
                <ui-layout :fields='model.fields' />
            </main>
            <footer>
                <ui-btn :title='button.title' v-if='!button.hidden' v-for='button in buttons'  :disabled='button.key != "close" && button.validate && disabled_send' :type='button.type || "primary"' v-on:click.native='send(button)'>{{$t(button.label)}}</ui-btn>
            </footer>
        </div>
    `
})

class QuestionProperty extends Data.Property {
    constructor(context) {       
        super(context)
        this.update_field_react();
    }

    update_field_react(field = this){
        field.attr = field.attr || {};
        field.attr.react = true;
        if(field.type == 'model'){
            field.fields.forEach(field => {
                this.update_field_react(field);
            });
        }
    }

    get meta() {
        if (this.type == 'file' && this.value) {
            return {
                ...this._meta,
                url: (value) => {
                    if (!value.id)
                        return;
                    return `${Config.api_server}/file/${value.id}`
                },
                url_mod: async (value) => {
                    if (!Context.User.access_token) {
                        Vue.Dialog.MessageBox.Info(Language.t("view_file.unauthorized"))
                        return;
                    }
                    let url = this.meta.url(value);
                    if (!url)
                        return;

                    await Context.User.refreshToken();
                    return `${url}?token=${Context.User.access_token}`
                }
            }
        }
        return this._meta
    }

}


class QuestionEntity extends Data.Entity {
    constructor(context) {
        super(context);
        this.config = context.config;
        this.handler = context.handler;
        this.subscribe = context.subscribe;
    }

    get propertyModel() {
        return QuestionProperty;
    }

    async send(_action, params, subscribe = this.subscribe) {

        let init_and_send_file = async (model) => {
            let model_error;

            let models = model.fields.filter((field) => {
                return field.type == 'model'
            });

            for (let model of models) {
                let response = await init_and_send_file(model);
                if (response?.error) {
                    model_error = model_error || {};
                    model_error.data = model_error.data || [];
                    model_error.data.push(response.error)
                }
            }

            let files = model.fields.filter((field) => {
                if (field.hidden && typeof field.hidden == 'function') {
                    return !field.hidden();
                }
                return !field.hidden && field.type == 'file' && field.value;
            })
    
            if (!files || files.length <= 0)
                return {error: model_error}

            for (let index in files) {
                let field = files[index];
                if (!field.value)
                    continue;

                let send_file = async (file) => {
                    let formData = new FormData();
                    formData.append('scope_tender_participant', this.config?.params?.proc_id);
                    formData.append('data', file, file.name);

                    let { data, error } = await Http.upload.form('tender/attach', formData);
                    if (error) {
                        return { error };
                    }
        
                    return {
                        data: {
                            id: data.uuid,
                            name: data.meta.name,
                            meta: {
                                "type": data.meta.type,
                                "content_type": data.meta.content_type,
                                "type_group": data.meta.group,
                                "size": data.meta.size
                            }
                        }
                    }
                }
    
                let field_error;
                if (Array.isArray(field.value)) {

                    for (let key in field.value) {
                        let item = field.value[key];
                        if (item && item.file && item.file.name) {
                            let { error, data: _value } = await send_file(item.file);
                            if (error) {
                                field_error = field_error || {
                                    name: field.name,
                                }
                                field_error.data = field_error.data || [];
                                field_error.data.push({
                                    i: key,
                                    name: field.name,
                                    message: error.message
                                })
                            } else {
                                Vue.set(field.value,key, _value);
                                //field.value[key] = _value;
                            }
                        }
                    }
                } else {
                    let item = field.value;
                    if (item && item.file && item.file.name) {
                        let { error, data: _value } = await send_file(item.file);
                        //value = field.property._value = _value
                        if (error) {
                            field_error = field_error || {
                                name: field.name,
                                message: error.message
                            }
                        } else {
                            field.value = _value;
                        }
                    }
                }
                if (field_error) {
                    model_error = model_error || {};
                    model_error.data = model_error.data || [];
                    model_error.data.push(field_error)
                }
            }
            if (model_error) {
                if (!model.setError(model_error) && model_error.code != "AbortError") {
                    Vue.Dialog.MessageBox.Error(model_error);
                }
                return {
                    error: model_error
                }
            }
            console.log("model_error",model_error)
        }
        if (!params) {
            let response = await init_and_send_file(this);
            if (response?.error)
                return response;

            params = this.fields.filter((field) => {
                if (field.hidden && typeof field.hidden == 'function') {
                    return !field.hidden();
                }
                return !field.hidden;
            }).map((field) => {

                if(field.type == 'model'){
                    // Пересобрать значение
                    field.reCalcValue();
                }

                let value = field.value;
                if (field.value && field.value.exp && field.value.exp.value != undefined) {
                    value = field.value.exp.value
                }

                if (Array.isArray(value) && value.length <= 0)
                    value = undefined;

                return {
                    name: field.name,
                    value: value
                }
            }).filter((field) => {
                return field.value != undefined
            })
        }else{
            params = Object.keys(params).map((key)=>{  
                return {
                    name: key,
                    value: params[key]
                }
            })
        }



        if(_action){
            params.push({name: "@button", value: _action})
        }


        if (!params || params.length <= 0) {
            return {
                error: {

                }
            }
        }

        params = params.reduce((prev, curr) => {
            prev[curr.name] = curr.value
            return prev;
        }, {})

        if(subscribe){
            if (subscribe === true) {
                subscribe = JSON.stringify(params)
            }
            let ecp_result = await ecp.subscribe(subscribe);

            if (!ecp_result) {
                return {
                    error: {
                        code: "AbortError",
                        message: "AbortRequest"
                    }
                }
            }

            if(ecp_result.error){
                Vue.Dialog.MessageBox.Error(ecp_result.error)
                return ecp_result
            }
            params.pkcs7B64 = ecp_result.data;
        }

        let { error, data } = await this.handler(params);
        if (error) {
            if(error && (error.status == 303 || error.fields))
                return {error, data}
            if (!this.setError(error) && error.code != "AbortError") {
                Vue.Dialog.MessageBox.Error(error)
            }
        }
        return {
            error,
            data
        }
    }
}

export class Question {

    static async response(rejection) {
        let { response, config, handler } = rejection;

        if (!response || !response.data)
            return response

        // обработка DO
        if (response.data.do) {

            if (!Array.isArray(response.data.do)) {
                response.data.do = [response.data.do]
            }

            for (let i in response.data.do) {
                let action = response.data.do[i];
                switch (action.type) {
                    case "redirect":
                        console.log("REDIRECT",action.url);
                        if(Vue.CurrentRouter && action.url && action.url.indexOf('http') < 0 && action.url.indexOf('god=') < 0){
                           Vue.CurrentRouter.push(action.url)
                        }else{
                            location.href = action.url
                        }
                        break;
                }
            }

        }

        if (!response.data.question)
            return response

        let { fields, title, size, subscribe,actions, submit = true } = response.data.question;

        // Если требуется только подпись
        if (!fields && subscribe && handler) {
            let pkcs7B64;
            // Получаем текстовку для подписи
            let subscribe_text = subscribe;
            if (subscribe === true) {
                subscribe_text = "никогда не шортите теслу";
            } else if (typeof subscribe == 'object') {
                let { host, method, params } = subscribe;
                let { error, data } = await Http[host].rpc(method, params);
                if (error) {
                    return { error }
                } else {
                    subscribe_text = data;
                }
            }
            if (!subscribe_text)
                return response;

            let ecp_result = await ecp.subscribe(subscribe_text);
            if (!ecp_result) {
                return {
                    error: {
                        code: "AbortError",
                        message: "AbortRequest"
                    }
                }
            }
            let { error: error_ecp, data: data_ecp } = ecp_result;
            if (error_ecp) {
                Vue.Dialog.MessageBox.Error(error_ecp)
                return { error: error_ecp }
            } else {
                pkcs7B64 = data_ecp;
            }

            if (pkcs7B64) {
                return await handler({
                    pkcs7B64: pkcs7B64
                })
            }

            return response;
        }

        let questionModal = async (fields, error)=>{
                        
            let model = new QuestionEntity({
                config: config,
                handler: handler,
                props: fields,
                subscribe: subscribe
            })

            let result = await QuestionDlg.Modal({
                size: size,
                model: model,
                title: title,
                submit: submit,
                actions: actions,
                error,
            });  

            if (!result) {
                return {
                    error: {
                        code: "AbortError",
                        message: "AbortRequest",
                    }
                }
            }  
            return result;      

        }
        let result;
        while(!result || (result.error && result.error.fields) || (result.error && result.error.status == 303)){

            result = await questionModal(fields,result && result.error.status != 303 && result.error && result.error.message)

            if(result.error && result.error.status == 303){
                let data_question = (result.error.data && result.error.data.question) || result.error.data || {}
                
                fields = data_question.fields != undefined ? data_question.fields : fields
                title = data_question.title != undefined ? data_question.title : title
                size = data_question.size != undefined ? data_question.size : size
                subscribe = data_question.subscribe != undefined ? data_question.subscribe : subscribe
                submit = data_question.submit != undefined ? data_question.submit :  submit

                //result = undefined;
            }else if(result.error && result.error.fields){
                fields = result.error.fields
            }

        }



        return result;
    }
}