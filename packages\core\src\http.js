import Phoenix from "phoenix_js"

const Key = {
    Extensions: Symbol('Extensions'),
}

class HttpSocket {
    constructor(config) {
        this.host = config.host;
        this.channelParams = config.channelParams || function () { };
        this.onReceiveOk = config.onReceiveOk || function () { };
        this.onReceiveError = config.onReceiveError || function () { };
    }

    static connect(host) {
        host = host.replace('http://', 'ws://');
        host = host.replace('https://', 'wss://');

        if (!this[`_connect_${host}`]) {

            this[`_connect_${host}`] = new Phoenix.Socket(
                `${host}/socket`, {
                transport: Phoenix.WebSocket,
            }
            );

            this[`_connect_${host}`].onClose(()=>{
                console.log("ON SOCKET CLOSE")
            })
            this[`_connect_${host}`].onError(()=>{
                console.log("ON SOCKET ERROR")
            })

            let c = this[`_connect_${host}`].connect();
            console.log("CONNECT",this[`_connect_${host}`])

            // console.log("CONNECT",host, this[`_connect_${host}`]);

        }
        return this[`_connect_${host}`];
    }

    channel(name) {
        let connect = HttpSocket.connect(this.host);

        for(let ch of connect.channels){

            if(ch.topic == name)
                return ch
        }

        let channel = connect.channel(name, this.channelParams());

        channel.onError((event) => {
            console.log("channel onError",name, event)
            //channel.socket.remove(channel);
        })
        channel.onClose((event) => {
            // console.log("channel onClose", name, event)
            //channel.socket.remove(channel);
        })

        return channel;
    }

    join(name, initListener, rejoin = false) {
        let channel = this.channel(name);
        if (initListener && typeof initListener == 'function') {
            initListener(channel);
        }
        channel.join()
            .receive("ok", this.onReceiveOk)
            .receive("error", async (response = {}, reason)=>{
                if(await this.onReceiveError(response, reason)){
                    console.log("onReceiveError",name,rejoin)
                    if(rejoin){
                        console.log("REJOIN leave",name);
                        channel.leave().receive("ok", ({ messages }) => {
                            console.log("START REJOIN",name);
                            this.join(name,initListener)    
                        })
                        
                    }
                }
            })
            .receive("timeout", () => console.log("http: Networking issue. Still waiting..."))
        return channel;
    }
    leave_channel(channel) {
        channel = this.channel(channel.topic);
        channel.leave()
            .receive("ok", ({ messages }) => {  })
            .receive("error", ({ reason }) => { console.log("Leave ERROR", reason,channel); })
            .receive("timeout", () => { console.log("Leave TIMEOUT"); })

        //let connect = HttpSocket.connect(this.host);
        //connect.remove(channel)

    }
    leave(name) {
        let channel = this.channel(name);
        this.leave_channel(channel);
    }
}

class Host {
    constructor(config, onCompletePromise) {
        this._config = config;
        this._onCompletePromise = onCompletePromise;
        this[Key.Extensions] = {
            request: new Map(),
            requestError: new Map(),
            response: new Map(),
            responseError: new Map()
        };

        this.time_difference = 0;
    }

    dateNow() {
        return Date.now() - this.time_difference;
    }

    get config() {
        return (typeof this._config === 'function') ? this._config() : this._config
    }

    get baseURL() {
        return `${this.config.host}/${this.config.path}`
    }

    get socket() {
        if (!this._socket) {
            this._socket = new HttpSocket({
                host: this.config.host,
                ...this.config.socket
            });
        }
        return this._socket;
    }

    addExtension(extension) {
        if (Array.isArray(extension)) {
            return extension.forEach((extension) => {
                this.addExtension(extension)
            })
        }
        Object.keys(this[Key.Extensions]).forEach(extensionHandlerName => {
            if (extension[extensionHandlerName]) {
                this[Key.Extensions][extensionHandlerName].set(extension, extension[extensionHandlerName]);
            }
        });
    }

    async extensionsHandlersRun(data, handlers) {
        if(!handlers || handlers.size <= 0)
            return data

        let response = (data && data.response);
        for (let handler of handlers.values()) {
            response = await handler(data, this) || response;
            data.response = response;
        }
        return response;
    };


    async request(config = {}) {

        return body;
    }

    async _rpc(config) {
        if (this._onCompletePromise) {
            await this._onCompletePromise;
        }
        config.format = config.format || "json"
        config.headers = config.headers || {};
        config.headers['Content-Type'] = 'application/json;charset=utf-8';
        let input = this.baseURL
        let init = {
            method: "POST",
            body: JSON.stringify({
                id: 1,
                jsonrpc: "2.0",
                method: config.action,
                params: config.params
            }),
            headers: config.headers,
            signal: config.signal
        }
        let response, body;
        try {
            response = await fetch(input, init);
            let server_date = response.headers.get("date");
            if (server_date) {
                server_date = new Date(server_date);
                this.time_difference = Date.now() - server_date;
            }

            /*if (response.status == 500) {
                let details = await response.text();
                return {
                    error: {
                        status: 500,
                        details: details,
                        code: "InternalServerError",
                        message: "InternalServerError"
                    }
                }
            }*/

            let has_json = (response.headers.get("content-type") || "").indexOf("json");

            if (has_json >= 0) {
                body = await response.json();
            } else {
                switch (config.format) {
                    case "text":
                        body = await response.text();
                        break;
                    case "blob":
                        body = await response.blob();
                        break;
                    default:
                        body = await response.json();
                }
            }

            if (response.status != 200 || body.error) {

                let error_data = body.error && body.error.data;
                if (error_data && !Array.isArray(error_data)) {
                    error_data = [];
                }
                return {
                    error: {
                        status: response.status,
                        code: body.error && body.error.code || 'unidentified',
                        message: body.error && body.error.message || 'UnidentifiedError',
                        details: body.error && body.error.details,
                        number: body.error && body.error.error_number,
                        fields: body.error && body.error.fields,
                        data: response.status == 303 ? body.result : error_data,
                    }
                }
            }
        } catch (e) {
            return {
                error: {
                    //status: response.status,
                    code: e.name,
                    message: e.toString(),
                }
            }
        }

        if (body.result || body.result === false || body.result === null || body.result === 0 || body.result === '') {
            return {
                data: body.result
            }
        }

        return {
            data: body
        }
    }


    async rpc(action, params = {}, config = {}) {
        //config = {
        //    ...config,
        //    action: action,
        //    params: params,
        //}

        config.action = action;
        config.params = params;


        config = await this.extensionsHandlersRun(config, this[Key.Extensions].request)
        let response = await this._rpc(config);

        if (response.error) {
            response = await this.extensionsHandlersRun({
                response: response, config: config, handler: () => {
                    return this.rpc(action, params, config)
                }
            }, this[Key.Extensions].responseError)
        } else {
            response = await this.extensionsHandlersRun({
                response: response, config: config, handler: (args) => {
                    let rpc_params = { ...params, ...args }
                    return this.rpc(action, rpc_params, config)
                }
            }, this[Key.Extensions].response)
        }

        return response;
    }


    async _form(config) {
        if (this._onCompletePromise) {
            await this._onCompletePromise;
        }

        let input = `${this.baseURL}/${config.action}`
        let init = {
            method: config.method || "POST",
            body: config.params,
            headers: config.headers
        }

        try {
            let response = await fetch(input, init);
            let body = await response.json();

            if (response.status != 200 || body.error) {
                let error_data = body.error && body.error.data;
                if (error_data && !Array.isArray(error_data)) {
                    error_data = [];
                }
                return {
                    error: {
                        status: response.status,
                        code: body.error && body.error.code || 'unidentified',
                        message: body.error && body.error.message || 'UnidentifiedError',
                        data: error_data,
                    }
                }
            }
            return {
                data: body
            }
        } catch (e) {
            return {
                error: {
                    status: 400,
                    code: 'unidentified',
                    message: e.message
                }
            }
        }
    }

    async form(action, params = {}, config = {}) {
        config = {
            ...config,
            action: action,
            params: params,
        }
        config = await this.extensionsHandlersRun(config, this[Key.Extensions].request)
        let response = await this._form(config);
        if (response.error)
            response = await this.extensionsHandlersRun({
                response: response, config: config, handler: () => {
                    return this.form(action, params, config)
                }
            }, this[Key.Extensions].responseError)

        return response;
    };
}

export default class Http {

    static get onCompletePromise() {
        if (!this._onCompletePromise) {
            this._onCompletePromise = new Promise((onCompleteResolve) => {
                this._onCompleteResolve = onCompleteResolve;
            })
        }
        return this._onCompletePromise;
    }

    static addHost(name, config) {
        return this[name] = new Host(config, this.onCompletePromise);
    }

    static get default() {
        return this[Http._default];
    }
    static set default(value) {
        return Http._default = value;
    }

    static HttpSocket = HttpSocket;

    static Complete() {
        if (this._onCompleteResolve)
            this._onCompleteResolve()
    }
}
