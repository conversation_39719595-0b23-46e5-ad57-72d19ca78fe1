.ui-action{
    position: relative;
    >.ui-btn-group{
        height: 100%;
        >.ui-btn{
            height: 100%;
        }
    }
    >.toggle{
        cursor: pointer;
        color: #868e96;
        font-size: 14px;
        //margin: 0 -8px;
        display: flex;
        align-items: center;
        &:hover{
            //background: #f5f5f5; 
            >.arrow{ 
                border-left: 1px solid #eee;
            }
        }
        >icon{
            font-size: 14px;
            padding: 8px 4px;
            &:first-child{
                padding-left: 8px;
            }
            &:last-child{
                padding-right: 8px;
            }
            &.arrow{
                border-left: 1px solid transparent;
                line-height: 20px;
            }
            &:hover{
                color: #666;  
                background: #0001;  
            }
        }

        &.button{
            border-radius: 3px;
            overflow: hidden;
            &:hover{
                background: #0001;
                    color: #868e96;
            }
            >icon{
                &:hover{
                    background: #0001;
                    color: #777;
                }
            }
        }
    }
    >.content{
        position: absolute;
        left: 0;
        box-sizing: border-box;
        border-radius: 5px;
        background: #fff;
        z-index: 10;
        box-shadow: 0px 3px 7px rgba(0, 0, 0, 0.1);
        max-width: 300px;
        border: 1px solid #ccc;
        overflow: hidden;
        top: calc(100% + 3px);
        >.item{
            white-space: nowrap;
            
            >.content{
                >.handler{
                    display: flex;
                    align-items: center;
                    color: #888;
                    font-size: 14px;
                    cursor: pointer;

                    >.icon,
                    >icon{
                        width: 40px;
                        flex: 0 0 40px;
                        text-align: center;
                        font-size: 16px;
                    }
                    >.label{
                        flex: 1 1 auto;
                        padding: 8px 16px 8px 0;
                    }                   
                }

                &.list{
                    min-width: 250px;
                    >.handler{
                        >.label{
                            text-align: right;
                            color: #888;
                        }
                        border-bottom: 1px solid #ddd;
                    }
                    
                    .ui-list{
                       max-height: 300px;
                        overflow-y: auto; 
                    }
                }
                &.sep{
                    padding: 0;
                    margin: 0;
                    background: #ccc;
                    height: 1px;
                    box-sizing: border-box;
                }
            }
            

            &.sep{
                padding: 0;
                margin: 0;
                background: #ccc;
                height: 1px;
                box-sizing: border-box;
            }

            .action{
               cursor: pointer; 
               &:hover{
                   color: #666;
                    background: #f5f5f5;
                }  
            }
        }


    }
    &.dropdown-top{
       >.content{
        top: -3px;
        transform: translateY(-100%);
        box-shadow: 0px -3px 7px rgba(0, 0, 0, 0.1);
       } 
    }
    &.dropdown-right{
        >.content{
            right: 0;
            left: unset;
        }
    }
}