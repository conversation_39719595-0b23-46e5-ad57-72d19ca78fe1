@keyframes PageProgressAnimation {
    0% {
     transform:scaleX(0)
    }
    50% {
     transform:scaleX(5)
    }
    to {
     transform:scaleX(5) translateX(100%)
    }
}


.ui-list{
    //overflow: auto;
    // z-index: 0 !important;
    &:hover{
        //overflow: auto;
    }
    &.drop{
        max-height: 300px;
        overflow: auto;
    }
    >.loader{
        overflow: hidden;
        position: relative;
        height: 3px;
        background: #E3E3E3;
        &::before{
            position: absolute;
            width: 25%;
            height: 3px;
            display: block;
            content: '';
            background: @brand-primary;
            transform-origin: 0 0;
            transform:scaleX(0); 
            animation: PageProgressAnimation 1s infinite;
            animation-timing-function: ease;
            animation-delay: 0s;
            animation-timing-function: cubic-bezier(0.4,0.0,1,1);
            animation-delay: .1s;

        }
    }
    >.not-found{
        margin: 10px;
    }
    
    .ui-list-item{
        position: static;
        
        width: 100%;
        white-space: nowrap;
        color: #666;
        font-size: 14px;
        line-height: 17px;
        //display: table;
        //table-layout: fixed;
        
        &.more{
            text-align: center;
            font-weight: bold;

            display: flex;
            justify-content: center;
            align-items: center;
            padding: 16px;
            icon{
                margin-right: 10px;
            }
        }        
        &:not(.more){
            >.content{
                &:hover{
                    background: #DDEEFF;
                    color: #222;
                    >.display .desc{
                        color: #777; 
                    }
                }
            }
        }
        &.status_success{
            background-color: @alert-success-bg;

        }
        &.status_info{
            background-color: @alert-info-bg;
        }
        &.status_warning{
            background-color: @alert-warning-bg;
        }
        &.status_danger{
            background-color: @alert-danger-bg;
        }

        &.status_danger,
        &.status_warning,
        &.status_info,
        &.status_success{
            >.content{
                &:hover{
                    background: #0003;
                }
            }        
        }

        &:not(:last-child) >.content{
          border-bottom: 1px solid #E3E3E3;  
        }

        >.content{
            padding: 14px 15px;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
            display: flex;
            align-items: center;
            
            >.display{
                flex: 1 1 auto;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                >.desc{
                    font-size: 13px;
                    white-space: break-spaces;
                    color: #aaa;
                }
            }
            >.select{
                flex: 0 0 auto;  
                display: none;
                margin: -15px 0;
            }
            &:hover{
                >.select{
                    display: block;
                }
            }
        }
        &.group-title{
            font-weight: bold;
            color: #333;
            border-bottom: 2px solid #CCC;  
            &:hover{
                background: none;
            }
        }
    }
}