import { DataSource, RefStore, Query } from '@iac/data'

export default {
  data() {
    return {
      dataSource: new DataSource({
        request_count: true,
        limit: 30,
        store: new RefStore({
          ref: 'ref_corporate_company',
          context:(context)=>{
            context.id = undefined
            context.mfo=(context.mfo && context.mfo.trim())?context.mfo:undefined
            context.soogu=(context.soogu && context.soogu.trim())?context.soogu:undefined
            context.email=(context.email && context.email.trim())?context.email:undefined
            context.fax=(context.fax && context.fax.trim())?context.fax:undefined
            return context
          }
        }),
        template:'template-company'
      })
    }
  },
  template: `
    <div>
      <iac-section type='header'>
        <ol class='breadcrumb'>
          <li><router-link to='/'>{{ $t('home') }}</router-link></li>
          <li>{{ $t('hp.registries.name5') }}</li>
        </ol>
        <h1>{{ $t('hp.registries.name5') }}</h1>
      </iac-section>
      <iac-section>
        <ui-layout-group>
          <ui-data-view :dataSource='dataSource'/>
        </ui-layout-group>
      </iac-section>
    </div>
  `
}
