export var DataTile = {
    name: "ui-data-tile",
    props: {
        "dataSource": {

        },
        search: String,
        sync: {
          type: Boolean,
          default: true
        }
    },
    template:`
        <ui-data-list :search='search' class='ui-data-tile' raw :dataSource='dataSource' :sync='sync'>
            <template slot='template' slot-scope='props'> 
                <template class='tile-item'>
                    <slot name='template' :item='props.item'  />
                </template>
                <ui-action icon='action' :actions='props.item.actions' />
            </template>      
            <template slot='not-found'>
              <slot name='not-found'></slot>
            </template>      
        </ui-data-list>
    `
}