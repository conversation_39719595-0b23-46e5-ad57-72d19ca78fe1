.tns-ovh {
  overflow: hidden;
}

.tns-horizontal {
  display: flex;
}

.tns-visually-hidden {
  position: absolute;
  left: -9999em;
}

.tns-controls {
  text-align: right;

  button {
    position: relative;
    margin-left: 8px;
    border: none;
    padding: 0;
    width: 44px;
    height: 44px;
    background-color: @white;
    text-indent: -9999px;
    overflow: hidden;
    cursor: pointer;
    border-radius: 50%;
    box-shadow: 0 0 24px rgba(211, 211, 211, 0.25);
    transition: background-color .3s ease;

    &::after {
      position: absolute;
      top: 50%;
      left: 50%;
      font-size: 8px;
      font-family: 'iac-icon';
      color: @brand-primary;
      text-rendering: auto;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      font-feature-settings: "liga" 1;
      text-indent: 0;
      text-transform: none;
      transition: color .3s ease;
      transform-origin: 50%;
      transform: translate(-50%, -50%) rotate(90deg);
      content: 'arrow';
    }

    &:last-child {

      &::after {
        transform: translate(-50%, -50%) rotate(-90deg);
      }
    }

    &:hover,
    &:focus {
      outline: none;
      background-color: @brand-primary;

      &::after {
        color: @white;
      }
    }
  }

  &:focus {
    outline: none;

    button {
      background-color: @brand-primary;

      &::after {
        color: @white;
      }
    }
  }
}

.tns-controls-center {
  position: relative;

  .tns-controls {

    button {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);

      &:first-child {
        left: -68px;
      }

      &:last-child {
        right: -68px;
      }
    }
  }
}

.tns-nav {
  margin-top: 76px;
  text-align: center;

  button {
    display: inline-block;
    border: none;
    margin: 0 4px;
    padding: 0;
    width: 12px;
    height: 12px;
    background-color: @pre-action;
    vertical-align: top;
    border-radius: 50%;
    transition: background-color .3s ease;

    &:hover,
    &:focus {
      background-color: @brand-primary;
      outline: none;
    }
  }

  .tns-nav-active {
    background-color: @brand-primary;
  }
}

@media (max-width: 1300px) {
  
  .tns-controls-center {
    padding: 0 6px;

    .tns-controls {

      button {
        margin: 0;

        &:first-child {
          left: -38px;
        }

        &:last-child {
          right: -38px;
        }
      }
    }
  }
}

@media (max-width: 767px) {
  
  .tns-controls-center {
    padding: 0 29px;

    .tns-controls {

      button {

        &:first-child {
          left: -15px;
        }

        &:last-child {
          right: -15px;
        }
      }
    }
  }
}
