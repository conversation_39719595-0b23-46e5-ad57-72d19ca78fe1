import {DataSource} from "@iac/data"
import {Settings} from "@iac/kernel"
/*
ref_bad_suppliers
ref_bank_account_type
ref_bank_mfo
ref_buyer_type
ref_catalog_category
ref_company_type
ref_country
ref_currency_full
ref_customs_fee
ref_customs_post
ref_default_tender_fields
ref_economic_acivity_type
ref_free_economic_zone
ref_incoterms
ref_insurance
ref_method_marks
ref_product_movement_feature
ref_soogu
ref_status
ref_status_agreement_procedure
ref_status_contest
ref_status_contract
ref_status_schedule_version
ref_status_tender
ref_tax_office
ref_tender_actions
ref_tnved
ref_transaction_nature
ref_transport
ref_type_business
ref_type_deposit
ref_type_periodicity
ref_unbudget_buyer_type
*/

export default {
    data: function(){

        return {
            dataSource: DataSource.get((Settings.reference_edit && Settings.reference_edit._list) ? [...Settings.reference_edit._list] : [
                //{id: 'ref:ref_categories', name: 'Категории'},
                //{id: 'ref:ref_products', name: 'Продукты'},
                //{id: 'ref:ref_packing_types', name: 'Типы упаковки'},
                //{id: 'ref:ref_single_supplier_directory', name: 'ref_single_supplier_directory'},
                //{id: 'ref:ref_reduction_object_public', name: 'ref_reduction_object_public'}
            ])
        }
    },
    methods:{
        onItem(item){
            this.$router.push({ path: `/workspace/settings/reference/${item.id}` })
        }
    },
    template: `
        <iac-access :access='$policy.system_dict_managment'>
            <iac-section>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('nav.references')}}</li>
                </ol>
                <div class='title'>
                    <h2 style='margin: 0;'>{{$t('nav.references')}}</h2>
                </div>
            </iac-section>
            <iac-section>
                <ui-layout-group horizontal>
                    <ui-layout-group>
                        <router-view/>
                    </ui-layout-group>
                    <ui-layout-group style='max-width: 250px; background: #fff; border: 1px solid #eee; border-radius: 5px;'>
                        <ui-list :dataSource='dataSource' v-on:item='onItem' />
                    </ui-layout-group>
                </ui-layout-group>
                
            </iac-section>
        </iac-access>
    `
}