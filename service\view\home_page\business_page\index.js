import { DataSource, RemoteStore, RefStore, Query } from '@iac/data';
import IacCarousel from '../../../components/carousel';
import { Context } from '@iac/kernel'

var search_val = {
  value: undefined
}

export default {
  data() {
    return {
      search_val: search_val,
      user: Context.User,
      ad: new DataSource({
        query: new Query({
          is_national: {
            value: false,
            hidden: true,
            sync: false,
          },
        }),
        store: new RefStore({
          ref: 'ref_online_shop_public',
          injectQuery: (params) => {
            params.fields = ["green","product","unit","debug_info","id","publicated_at","status","name","price","close_at","totalcost","currency", "amount","min_amount","images","owner_legal_area_id","product_name","remain_time"]
            params.filters = params.filters || {};
            params.filters.is_comm_shop = true;
            params.limit = 6;
            return params;
          },
        }),
      }),
    }
  },
  mounted(){
    search_val.value = undefined
  },
  components: {
    IacCarousel,
    procedureSearch: {
      props: ["type"],
      data: function(){
        return {
          search_val: search_val
        }
      },
      computed: {
        label(){
          return `search.${this.type}.placeholder`;
        }
      },
      methods: {
        search(){
          if(search_val.value)
            this.$router.push({path: `business/${this.type}?queryText=${search_val.value}`})
          else
            this.$router.push({path: `business/${this.type}`})
        }
      },
      template: `
        <form  @submit.prevent='search' style='max-width: 700px; margin: 0 auto; width: 100%;'>
          <ui-control-group>
            <ui-input icon='search' :label='label' v-model='search_val.value' />
            <ui-btn type='primary'>{{$t('search')}}</ui-btn>
          </ui-control-group>
        </form>
      `
    }
  },
  computed: {
    adOptions() {
      return {
        items: 1,
        gutter: 24,
        nav: false,
        loop: false,
        rewind: true,
        autoplayTimeout: 5000,
        responsive: {
          600: {
            items: 2,
          },
          1000: {
            items: 3,
          },
          1300: {
            items: 4,
          },
        }
      };
    },
  },
  template: `
    <template>
        <ui-layout-tab class='proc_tab' name='business'>
          <ui-layout-group key='ad' label='nav.business_shop'>
            <procedure-search type='ad' />
            <ui-list :dataSource='ad' class='tns-controls-center mb-12'>
              <iac-carousel v-if='props && props.items && props.items.length' slot='items'
                slot-scope='props' :model='adOptions'>
                <div v-for='item in props.items' :key='item.id'>
                  <widget-shop type='business' :item='item' class='h-100' />
                </div>
              </iac-carousel>
            </ui-list>
            <div class='text-center'>
              <router-link class='ui-btn ui-btn-primary' to='/business/ad'>{{ $t('hp.goto_ad_page') }}</router-link>
            </div>
          </ui-layout-group>
        </ui-layout-tab>
    </template> 
  `
}