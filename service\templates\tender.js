const Component = {
    props: ['model'],
    computed: {
        url() {
            return `/procedure/${this.model.id}/core`
        },
        remainTime() {
            return Date.now() + this.model.remain_time * 1000;
        },
        area_paths() {
            return this.listToString({
                listName: 'area_path',
                sliceCallback: (list) => [list[1], ...list.slice(3)],
            });
        }
    },
    methods: {
        listToString({ listName, sliceCallback }) {
            const { model } = this;
            const list = model && model.meta && model.meta[listName];
            if (list && list.length) {
                return sliceCallback(list).map(({ name }) => name).join(', ');
            }
        },
        openProducts() {
            const { lotNum, model: { good_count, meta } } = this;
            
            Vue.Dialog.products.Modal({
                size: 'right',
                model: {
                    title: `${this.$t('tender')} №: ${this.model.id}`,
                    count: good_count,
                    items: meta.good_maps,
                }
            })
        }
    },
    template: `
        <ui-data-view-item :model='model'>
            
            <template slot='header'>
                <div><iac-entity-edit  v-if='model.id' :value='{id: model.id, type: "tender"}' /> &nbsp;&nbsp; <iac-date v-if='model.publicated_at' :date="model.publicated_at"  withoutTime withMonthName/></div>
                <div>
                    <div v-if='model.green && $settings.procedures && $settings.procedures._green' :title='$t("green_procedure")' class='iac-marker green'/><ui-ref source='status_tender' :value='model && model.status'/>
                </div>
            </template>

            <template slot='title'>
                <router-link :to='url' class='title clamp_7'>{{model.name}}</router-link>
            </template>
            <template v-if='model.lot_count>1' slot='sub_title'>
                <a>{{model.lot_count}} {{ $t('lotlot',{count: model.lot_count}) }}</a>
            </template>
            <template v-else slot='sub_title'>
                <a @click.prevent='openProducts' href='#' >{{model.good_count}} {{ $t('product',{count: model.good_count}) }}</a>
            </template> 
            <template slot='description'>
                <div>
                    <label>{{ $t('contract.organizer')}}:</label>
                    <span>{{ model.meta && model.meta.company_name }}</span>
                    <div>{{ area_paths }}</div>
                </div>
            </template>
            <template slot='props'>
                <div>
                    <label>{{$t('tender.close_at')}}:</label>
                    <div><iac-date v-if='model.close_at' :date="model.close_at"  withoutTime withMonthName/></div>
                </div>
                <div v-if="!['close', 'cancel', 'rejected', 'moderated'].includes(model.status)">
                    <label>{{ $t('remain_time') }}</label>
                    <div><iac-timer :date='remainTime' /></div>
                </div>
                <div>
                    <label>{{$t('tender.max_price')}}:</label> 
                    <div><iac-number :value='model.totalcost' delimiter=' ' part='2'/>&nbsp;{{model.currency}}</div>
                </div>
                <div>
                    <label>{{$t('tender.language')}}:</label>
                    <div><ui-ref source='ref_locale' :value='model && model.lang'/></div>
                </div>
                <div>
                    <label>{{$t('tender.offers')}}:</label> 
                    <div>{{model.part_count}}</div>
                </div>
            </template>

        
        </ui-data-view-item>
    `
}

Vue.component('template-tender', Component);