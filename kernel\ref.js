import { Http } from '@iac/core'
import { Language as LngProvider } from '@iac/core'

export default class Ref {
    static requst(name, suffix = 'ref_', key, params={}) {
        let promise_name = '_promise_' + key+name + (name != 'locale' ? "_" + LngProvider.local : '');
        this[promise_name] = this[promise_name] || new Promise(async (resolve, reject) => {
            const { data: result, error } = await Http.api.rpc((suffix == '_' ? '':suffix) + name, params);
            if (error)
                reject(error);
            else
                resolve(result);
        });
        return this[promise_name];
    };

    static async get(name, options = {}) {
        options.suffix = options.suffix || 'ref_'


        let items = await this.requst(name, options.suffix, options.key, options.params);
        return items;
    }

    static async map(name, options = {}) {
        options.suffix = options.suffix || 'ref_'

        let items = await this.get(name, options);
        let ret = {}
        if (items[0][options.suffix + name]) {
            return items[0][options.suffix + name];
        } else {
            items.forEach(function (item) {
                ret[item.code || item.id] = item;
            }, this);
        }
        return ret;
    }
}