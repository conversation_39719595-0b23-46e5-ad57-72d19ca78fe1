const path = require('path');
const fs = require('fs');
const _ = require('lodash');
const svg2ttf = require('svg2ttf');
const ttf2woff = require('ttf2woff');
const svgicons2svgfont = require('svgicons2svgfont');

const fontStream = new svgicons2svgfont({
    fontName: "iac-icons",
    fontWeight: 400,
    fixedWidth: false,
    centerHorizontally: false,
    normalize: true,
    fontHeight: 1792,
    ascent: undefined,
    descent: 256,
})



let build_dir = path.resolve(__dirname, "../_build/fonts");
let mkdirSync = (dir) => {
    if (fs.existsSync(dir)) {
        return;
    }
    mkdirSync(path.dirname(dir));
    fs.mkdirSync(dir);
}
mkdirSync(build_dir);

let fileStream = fs.createWriteStream(path.resolve(build_dir, "iac-icons.svg"));
fontStream.pipe(fileStream)
    .on('finish', function () {
        try {

            let ttf = svg2ttf(fs.readFileSync(this.path, 'utf8'), {});
            let woff = ttf2woff(ttf.buffer)

            fs.writeFileSync(path.resolve(build_dir, "iac-icons.ttf"), Buffer.from(ttf.buffer));
            fs.writeFileSync(path.resolve(build_dir, "iac-icons.woff"), Buffer.from(woff.buffer));

        } catch (err) {
            console.log("err", err);
        }
    })
    .on('error', function (err) {
        console.log("err", err);
    });

fs.readdirSync(path.resolve(__dirname, '../packages/glyph')).forEach(function (file) {
    if(file=='_iac-icons.less')
        return;
    let glyph = fs.createReadStream(path.resolve(__dirname, '../packages/glyph', file));
    glyph.metadata = {
        unicode: [file.split('.')[0]],
        name: file,
    }
    fontStream.write(glyph);
});

fontStream.end();