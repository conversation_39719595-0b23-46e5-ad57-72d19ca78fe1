import contractTermination from './termination';
import exchangeRates from './exchange-rates';
import executionBottom from './execution-bottom';
import executionExchangeContract from './execution_exchange_contract';

export default {
  props: ['model'],
  components: {
    contractTermination,
    exchangeRates,
    executionBottom,
    executionExchangeContract
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      if (!vm.model.rights.execution_show_tab) {
        next({ path: 'core' });
      }
    });
  },
  template: `
    <div v-if='model' :key='model.update_rights_key'>
      <template v-if='model.proc_type != "schedule_exchange_contract"'>
        <contract-termination :model='model' />
        <exchange-rates v-if="model.exchange_rates" :model='model.exchange_rates' />
        <execution-bottom :model='model' />
      </template>
      <template v-else>
        <executionExchangeContract :model='model' />
      </template>
    </div>
  `,
}
