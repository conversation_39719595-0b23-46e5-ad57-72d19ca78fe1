import Model from "./model";
import { Language } from '@iac/core'
import { DataSource } from '@iac/data'

const Documentation = {
    props: {
        source: {

        },
        readonly: {
            type: Boolean,
            default: false
        },
        print: {
            type: Boolean,
            default: false
        },
        save_all: {
            type: Boolean,
            default: false
        },
        date: {
            default: new Date()
        },
        languages: {
            default: ["uz"]
        },
        entity_id: {
            type: String,
            // default: "1014151"
        }
    },
    data: function () {
        return {
            draggable_group: -1,
            dragover_item: -1,
            drag_item: -1,
            model: undefined
        }
    },
    computed: {
        document_languages(){
            return this.model.languages.filter((lng)=>{
                return this.languages.includes(lng) || lng == 'uz'
            });
        }
    },
    watch: {
        entity_id: {
            async handler(value, oldValue) {
                if (this.model) {
                    this.$wait(async () => {
                        await this.model.set_entity(value);
                    })

                }
            }
        },
        date: {
            handler(value, oldValue) {
                //if (this.model) {
                    //this.$wait(async () => {
                        this.init();
                    //})
                //}
            }
        },
        source: {
            async handler(value, oldValue) {
                //console.log("---------------source----------------",value, oldValue);
                //if (this.model) {
                    let _v = value?.id || value
                    let _o = oldValue?.id || oldValue
                    if(_v == _o)
                        return;

                    this.init();
               // }
            }
        }
    },
    mounted() {
        this.init();
    },
    methods: {
        printContent() {
            // page-break-after: always;
            const prtHtml = this.$refs.documentation.outerHTML;
            let stylesHtml = '';
            for (const node of [...document.querySelectorAll('link[rel="stylesheet"], style')]) {
                stylesHtml += node.outerHTML;
            }

            // Open the print window
            const WinPrint = window.open('', 'popup_window', `left=0,top=0,width=${screen.width},height=${screen.height},toolbar=0,scrollbars=0,status=0,fullscreen=yes`);

            WinPrint.document.write(`<!DOCTYPE html>
<html>
  <head>
    <base href="/">
    ${stylesHtml}
    <style>div{
        font-size:12px;
        text-align: justify;
        page-break-after: auto;
    }</style>
  </head>
  <body style='font-size:25px'>
    ${prtHtml}
  </body>
</html>`);
            setTimeout(() => {
                WinPrint.document.close();
                WinPrint.focus();
                WinPrint.print();
                WinPrint.close();
            }, 200);


        },
        saveALL() {
            this.$wait(async () => {
                await this.model.save();
            })
        },
        init() {
            this.$wait(async () => {
                this.model = await Model.get({
                    date: this.date,
                    entity_id: this.entity_id,
                    type: this.source?.id || this.source || 'procurement'
                })
            })
        },
        dragStart(group, index) {
            this.draggable_group = group;
            this.drag_item = index;
        },
        dragend(event) {
            this.draggable_group = -1;
            this.drag_item = -1
            this.dragover_item = -1
        },
        dragover(index) {
            this.dragover_item = index
            return false;
        },
        dragenter(event, group) {
            if (this.draggable_group != group) {
                return false;
            }
            event.stopPropagation();
            event.preventDefault();
            return true;

        },
        dragleave(event) {
            this.dragover_item = -1
        },
        async drop(index) {
            let group_index = this.draggable_group;



            let items = [...this.model.data[group_index]];
            let item = items[this.drag_item];
            items.splice(this.drag_item, 1);

            if (this.drag_item < index) {
                index--;
            }
            items.splice(index, 0, item);

            this.draggable_group = -1;
            this.drag_item = -1
            this.dragover_item = -1
            if (this.save_all) {
                this.model.has_save = true;
                this.model.data[group_index] = items
                return;
            }

            let { error, data } = await this.model.save_item(group_index, undefined, items)
            if (error) {
                await Vue.Dialog.MessageBox.Error(error)
            } else {
                this.model.data[group_index] = items
            }



        },
        async deleteItem(index, i) {
            if (await Vue.Dialog.MessageBox.Question(Language.t("question.delete_section"), Language.t("question.title_warning")) != Vue.Dialog.MessageBox.Result.Yes) {
                return;
            }
            let items = [...this.model.data[index]];
            items.splice(i, 1);

            if (this.save_all) {
                this.model.has_save = true;
                this.model.data[index] = items
                return;
            }

            let { error, data } = await this.model.save_item(index, undefined, items)
            if (error) {
                await Vue.Dialog.MessageBox.Error(error)
            } else {
                this.model.data[index] = items
            }
        },
        async edit(group, index) {

            let items = this.model.data[group];
            if (index == undefined) {
                index = items ? items.length : 0
            }

            let item = items && items[index];

            //await this.model.save_item(group,item)
            if (this.readonly)
                return;
            var result = await Vue.Dialog.MessageBox.Form({
                size: "lg",
                fields: this.document_languages.map((lng) => {
                    return {
                        name: lng,
                        label: lng,
                        type: "text",
                        value: item ? item[lng] : undefined,
                        status: undefined,
                        required: true

                    }
                }),
                onClose: async (params) => {
                    if (this.save_all) {
                        this.model.has_save = true;
                        return {
                            data: params
                        }
                    }
                    return this.model.save_item(group, index, params);
                }
            })

            if (!result || result == 2) // Cancel || No
                return;

            let _item = item || {}

            this.document_languages.forEach(lng => {
                Vue.set(_item, lng, result[lng])
            });


            if (item) {
                return;
            }

            Vue.set(this.model.data, group, this.model.data[group] || [])
            this.model.data[group].push(_item)

        },
        updateValue(event) {
            //this.value = event.id || event
            //this.$emit("change", this.value)
            //this.init();
        }
    },
    template: `
    <div>

        <ui-scroller class=''>
        
        
        <div ref='documentation' class='iac-documentation'>
            <template v-if='model && model.template'>
            
                <section :class='section.wait ? "iac-wait" : ""' v-for='section, index in model.template'  v-on:dragenter='e=>dragenter(e,index)'>
                    <div class='title'>
                        <div v-for='lng in document_languages'><b>{{index+1}}</b> {{section.title[lng] || ""}}</div>
                    </div>
                    <div class='system'>
                        <div class='item' v-for='item, i in section.items' >

                            <ui-markdown-view :content='"**"+(index+1)+"."+(i+1)+"** "+(item[lng]||"")'  v-for='lng in document_languages' />
                            <span v-if='!readonly' style='flex: 0 0 30px; text-align: right;'></span>
                        </div>
                    </div>
                    <div :class='readonly ? "system" : "custom"'>
                        <transition-group name="flip-list" mode="out-in"  >
                            <div v-if='item'  v-on:click='edit(index,i)' :key='item' class='item' v-for='item, i in model.data[index]' 
                            
                            v-on:dragstart="dragStart(index, i)"
                            v-on:dragend='dragend'
                            v-on:dragleave.prevent='dragleave'

                            :draggable=!readonly >
                                
                                <ui-markdown-view :content='"**"+(index+1)+"."+(i+section.items.length+1)+"** "+(item[lng] || "")'  v-for='lng in document_languages' />

                                <span v-if='!readonly' style='flex: 0 0 30px; text-align: right;'><ui-btn type='danger xs' v-on:click.native.stop.prevent='deleteItem(index,i)'><icon>trash</icon></ui-btn></span>
                                
                                <template v-if='index == draggable_group'>
                                    <div :class='dragover_item == i ? "dragover drop-top" : "drop-top"'
                                        v-on:drop.prevent="drop(i)" 
                                        v-on:dragover.prevent="dragover(i)"/>
                                    <div :class='dragover_item == i+1 ? "dragover drop-bottom" : "drop-bottom"'
                                        v-on:drop.prevent="drop(i+1)" 
                                        v-on:dragover.prevent="dragover(i+1)" />
                                </template>
                            </div>
                        </transition-group>
                    </div>
                    <div v-if='!readonly && model.entity_id' class='add_item'>
                        <span v-on:click='edit(index)'>{{$t("add")}}</span>
                    </div>

                </section>
            </template>            
        </div>
        <template slot='footer' v-if='model'>
            <ui-btn v-if='model.save && save_all && !readonly' type='primary' v-on:click.native='saveALL'>{{$t("save")}}</ui-btn>
            <ui-btn v-if='readonly && print' type='success' v-on:click.native='printContent'>{{$t("print")}}</ui-btn>
        </template>
        </ui-scroller>
    </div>
    `
}


Vue.component('iac-documentation', Documentation);

Vue.Fields['documentation'] = { is: 'iac-documentation' }