import Procedure from './procedure';
import { Develop } from '@iac/kernel'

@Procedure.Registration("selection")
export default class Selection extends Procedure {

    constructor(context = {}) {

        super(context);
    }

    get procedure_actions() {
        return [
            { buttons: 1, group: 'general', order: 1000 },
            { buttons: ( (this.gos || this.gup) ) ? 2 : 3, group: 'tech_fields', order: 1000, category: ['quality_fields','tech_fields'] },
            /*{ buttons: 3, group: 'commercial_fields', order: 1000,
            hidden:(button)=>{
                if(button == 2 && this.method_marks == 'min_price')
                    return true;
            } },*/
        ]
    }

    static async init_context(context) {

        context.props.tech_fields_setting = {
            group: 'tech_fields',
            type: "setting",
            label: "quality_tech_fields",
            order: 3000,
        }

        /*
        context.props.tech_fields_actions = {
            group: 'tech_fields',
            type: "setting",
            order: 3000,
            actions: [
                {label:"Добавить комментарий к группе"},
                {type: "sep"},
                {label: "Удалить все комментарии у группы", icon: "trash"}
            ]
        }*/


        /*context.props.proc_custom2.type = 'string'
        context.props.proc_custom2.actions = [
            {
                label: "details", type: "action", method: "procedure.requirement", params: {
                    size: "lg",
                    id: "proc_custom2",
                    proc_id: "1000491"

                }
            }
        ]*/

        /*
        context.props.general_comm_src_setting = {
            group: 'general/comm_src',
            type: "setting",
            //label: "Новое наименование группы",
            //hidden: true,
            order: 3000,
            //actions: [
            //    {label: "Добавить элемент"},
            //    {label: "Удалить выбранные"},
            //    {label: "Очистить все записи"}
            //],
            //attr: {
            //    expanded: true
            //}
        }*/


        //=================START=================
        //Временный костыль для сокрытия на фронте полей в формах, пока не поправили на бэке.
        // TODO:Удалить как исправят на бэке. Согласовано на планерке с Костей и ДД от 4 апреля 2022
        if (context.status !== "close" && context.part_id) {
            [
                //form1
                "contragent_face_proposal_",
                "contragent_phone_proposal_",
                "contragent_title_form_1_proposal_",
                //form2
                "contragent_title_form_2_proposal_",
                //form3
                "contragent_title_form_3_proposal_",
                "legal_address_proposal_",
                "legal_contact_proposal_",
                "register_proposal_",
                //form4
                "contragent_title_form_4_proposal_"
            ]
                .map(item => `${item}${context.part_id}`)
                .forEach(item => context.props[item] && (context.props[item].hidden = true))
        }
        //==================END==================
        return {

        }
    }
}