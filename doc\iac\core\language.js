export default `
## Language

> Класс отвечает за локализацию всей системы




    import {Language} from '@iac/core'

    // Метод возвращает перевод по ключу
    // options на данный момент имеет только один аргумент count для работы с plural
    Language.t(key,options)

    // Переменная хронит текущую локаль uz-UZ@cyrillic | uz-UZ@latin | en-US | ru-RU
    Language.local

    // Переменная хронит текущую локаль uz | uzl | en | uz
    Language._local




Для Vue компонентов доступен внутренний метод \`$t(key,options)\`


> Для передачи текущей локали на сервер используется расширение (extensions) для Http провайдера, которое ко всем запросам добавляет заголовок X-DBRPC-Language

> Также есть debug настройка lng_debug которая позволяет отследить наличие перевода или ключа. Включается через интерфейс

`