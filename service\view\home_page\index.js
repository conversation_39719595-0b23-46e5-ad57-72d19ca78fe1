import Brand from './../../components/brand';
import IacFooter from './../../components/footer';
import NavTop from '../../components/nav_top';
import IacRegistries from './registries';
import IacProcedures from './procedures';
import IacStatistics from './statistics';
import LatestNews from './latest_news';
import IacFaq from './faq';
import IacPartners from './partners';
import {DataSource} from '@iac/data';

export default {
  components:{
    NavTop,
    Brand,
    IacFooter,
    IacRegistries,
    IacProcedures,
    IacStatistics,
    LatestNews,
    IacFaq,
    IacPartners,
  },
  data() {
    return {
      model: undefined,
      type: {
        label: "!type",
        type: "entity",
        value: "tender",
        dataSource: DataSource.get(["tender","contest","ad","reduction"]),
      },
      search: {
        label: "!search",
        value: undefined
      }
    };
  },
  methods: {
    submit(event){
      event.preventDefault();

      let type = (this.type.value && this.type.value.exp) ? this.type.value.id : this.type.value;
      let search = this.search.value || "";

      let path = `/procedure/${type}?text=${search}`;
      this.$router.push({path: path})
    }
  },
  template: `
    <div class='iac-home-page' style='background: #fff;'>
      <template>
        <nav-top style='position: relative; z-index: 2' />
        <section class='head section-block'>
          <div class='iac-container'>
            <div>
              <h1 class='head-title'>{{ $t('hp.info.title') }}</h1>
              <div class='head-subtitle'>{{ $t('hp.info.desc') }}</div>
            </div>
            <iac-procedures style='position: relative' />
          </div>          
        </section>
        <iac-partners v-if='$settings.partners' :style='"background: "+($settings.partners._background || "#fbfbfb")'  />
        <iac-statistics v-if='$settings.map' :style='"background: "+($settings.map._background)' />
        <iac-registries style='background: #fbfbfb' />
        <iac-faq  />
        <latest-news v-if='$settings.news' :style='"background: "+($settings.news._background || "#fbfbfb")' />

      </template>
      <iac-footer />
    </div>
  `,
};
