import { Language } from "@iac/core"; 

var services = {
  props: ["actions"],
  data: function(){
    return {
      dropdown: undefined,
    }
  },
  methods: {
    isExternalLink(url) {
      if (url.slice(0, 4) !== 'http') {
        return false;
      }
      return !url.includes(location.href);
    },
    showMenu(){
      if(this.dropdown)
        return this.hideMenu();
      this.dropdown = true;
    },
    hideMenu(){
      this.dropdown = undefined
    },
  },
  template: `
    <div class='ui-action dropdown-right'>
      <div class='toggle' v-on:click='showMenu()' v-on-clickaway="hideMenu">
        <icon style='font-size: 20px;border-radius: 5px;'>action</icon>
      </div>
      <div class='content' v-if='dropdown'>

      <template v-for='action in actions'>
        <div class='item' style='color: #fff; background: #666;' v-on:click.prevent.stop=''>
          <div class='content1 action1' style='padding: 3px 6px;'>
            {{action.label}}
          </div>
        </div>

        <div class='item' v-for='item in action.items'>
          <div class='content action'>

          <a :title='item.desc' v-if='isExternalLink(item.url)' :href='item.url' class='handler' target='_blank' style='text-decoration: none;'>
            <icon/>
            <span  class='label' style='text-overflow: ellipsis;overflow: hidden;'>{{ $t(item.label) }}</span>
          </a>
          <router-link :title='item.desc' v-else :to='item.url' class='handler' style='text-decoration: none;'>
            <icon/>
            <span class='label'  style='text-overflow: ellipsis;overflow: hidden;'>{{ $t(item.label) }}</span>
          </router-link>


          </div>
        </div>

      </template>
        

      



      </div>
    </div>
  `
}

export default {
  data: function(){
    return {
      rightItems: [],
      dropdown: undefined,
      left: 0,
      width: 0
    }
  },
  computed: {
    actions(){
      return this.top_menu || []
    },
    top_menu(){
      return this.$content.top_menu;
    }
  },
  beforeDestroy() {
    if (this.$refs.frame.contentWindow)
      this.$refs.frame.contentWindow.removeEventListener('resize', this.onFrameResize);
      Language.onUpdate.unbind(this.onFrameResize);
  },
  mounted: function () {
    this.$refs.frame.contentWindow.addEventListener('resize', this.onFrameResize);  
    Language.onUpdate.bind(this.onFrameResize);
  },
  methods: {
    onFrameResize() {

      let items = this.actions.filter((i,index)=>{
        return this.$refs?.items?.childNodes[index+2].offsetTop > 0
      })

      //if(this.rightItems && this.rightItems.length != items.length){
        this.rightItems = items;
      //}

    },
    showMenu(index){
      if(this.dropdown == index+1){
        return this.hideMenu();
      }
      this.left = this.$refs?.items?.childNodes[index+2].offsetLeft
      this.width = this.$refs?.items?.childNodes[index+2].offsetWidth
      this.dropdown = index+1;
    },
    hideMenu(){
      this.dropdown = undefined
    },
    isExternalLink(url) {
      if (url.slice(0, 4) !== 'http') {
        return false;
      }
      return !url.includes(location.href);
    }
  },
  components:{
    services
  },
  template: `
    <nav class='iac-nav-links'>
        <div class='content' :style='actions.length == rightItems.length ? "height: 0" : ""'>
          <div class='items' ref='items' v-on-clickaway="hideMenu">
            <div class='item' style='padding: 0;width: 0px;'>&nbsp;</div>
            <div class='item' :class='index+1 == dropdown ? "active":""' v-on:click='showMenu(index)' v-for='action,index in actions'>
              <span style='font-weight: 500;'>{{action.label}}</span>
              <icon>arrow</icon>
            </div>
          </div>
          <iframe ref='frame' class='frame' />
        </div>  
        <div class='action' style='flex: 0 0 auto;display: flex;align-items: center;'>
          
          <services v-if='rightItems.length > 0' :actions='rightItems'/>

        </div>  
        <div :key='dropdown' class='dropdown' v-if='dropdown && dropdown <= (actions.length-rightItems.length)' :style='"left:"+left+"px; width: "+width+"px"'>
          
          <div class="content">
            <template v-for='item in actions[dropdown-1].items'>
              <a :title='item.desc' v-if='isExternalLink(item.url)' :href='item.url' class='item' target='_blank'>
                {{ $t(item.label) }}
              </a>
              <router-link :title='item.desc' v-else :to='item.url' class='item'>
                {{ $t(item.label) }}
              </router-link>
            </template>
          </div>

        </div>        
    </nav>
  `,
};
