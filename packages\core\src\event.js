const Key = {
    Events: Symbol('Events'),
    Listeners: Symbol('Listeners')
}

var getEvents = function (events) {
    return events.replace(/\b(\S)/g, function (s) {
        return 'on' + s.toUpperCase();
    }).split(' ').map((event) => {
        return this[event];
    }).filter((event) => {
        return event ? true : false;
    });
}

export default function (target, key, descriptor, context) {
    target.on = target.on || function (events, fn) {
        getEvents.call(this, events).forEach((event) => {
            event.bind(fn);
        })
    };

    target.unbind = target.unbind || function (events, fn) {
        getEvents.call(this, events).forEach((event) => {
            event.unbind(fn);
        })
    };

    Object.defineProperty(target, "listeners", {
        configurable : true,
        get: function () {
            let listeners = [];
            for(let event in this[Key.Events]){
                listeners = listeners.concat(this[Key.Events][event][Key.Listeners]);
            }
            return listeners
        }
    });

    target.listeners1 = function () {
        let prototype = Object.getPrototypeOf(this);
        let events = [];
        Object.getOwnPropertyNames(prototype).forEach(prop => {
            if(this[prop][Key.Listeners])
                events = events.concat(this[prop][Key.Listeners])
        });
        return events;
    }

    let value = descriptor.value && typeof descriptor.value == 'function' ? descriptor.value : undefined;
    return {
        get: function () {
            let _this = this;
            _this[Key.Events] = _this[Key.Events] || {};
            if (!_this[Key.Events][key]) {
                let event = async function (data) {
                    if (value)
                        await value.call(_this, {
                            name: key,
                            data: data
                        });

                    for (let i = 0; i < event[Key.Listeners].length; i++) {
                        let fn = event[Key.Listeners][i];
                        await fn.call(_this, {
                            name: key,
                            data: data
                        });
                    }
                }
                event[Key.Listeners] = [];
                event.bind = (fn) => {
                    if (!fn) return
                    event[Key.Listeners].push(fn);
                    if (target.onEventListener) {
                        target.onEventListener.call(_this, 'bind')
                    }
                }
                event.unbind = (fn) => {
                    let index = event[Key.Listeners].indexOf(fn);
                    if (index < 0)
                        return;
                    event[Key.Listeners].splice(index, 1);
                    if (target.onEventListener) {
                        target.onEventListener.call(_this, 'unbind')
                    }
                }
                _this[Key.Events][key] = event;
            }
            return _this[Key.Events][key];
        }
    }
}