import { Assets } from '@iac/core';
import Develop from "../../develop";

Vue.Dialog("DevelopSettingDlg", {
  data: function () {
    return {
      develop: Develop,
      loading:true,
    }
  },
  async mounted() {
    this.$wait(async () => {
      await Assets.script('iac.doc.js', true);
      await Assets.css('https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/vs.min.css');
      this.loading = false
    })

  },
  template: `
      <div class='develop_dlg'>
        <header>Девелоперские настройки</header>
        <template  v-if="!loading">
        <main v-if='$user_develop.develop_tools'>
          <ui-layout :fields='develop.fields' />
        </main>
        <main v-else>
          <ui-error code='403' :message='$t("NoAccess")'  />
        </main>
        </template>
        <footer>
          <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
        </footer>
      </div>
    `
})