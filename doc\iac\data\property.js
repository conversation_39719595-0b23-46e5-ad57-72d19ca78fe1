import {Entity} from '@iac/data'

export default {
    data: function () {
        return {
            status_model: new Entity({
                props: {
                    field1: {
                        type: "date-time",
                        status: {
                            type: "error",
                        }
                    },
                    field2: {
                        status: {
                            type: "error",
                            message: "Описание статуса"
                        }
                    },
                    field3: {
                        status: {
                            type: "success",
                        }
                    },
                    field4: {
                        type: "model",
                        label: "!",
                        fields: {
                            field1: {
                                group: "<component>",
                                status: {
                                    type: "error",
                                    message: "Описание статуса1"
                                }
                            },
                            field2: {
                                group: "<component>",
                                status: {
                                    type: "success",
                                    message: "Описание статуса2"
                                }
                            },
                        }
                    }
                }
            })
        }
    },
    template: `
## Property
> 
### Атрибуты
- [x] type - тип 
- [ ] label
- [ ] value
- [ ] group - отвечает за расположение
- [ ] hidden
- [ ] order
- [ ] dataSource
- [ ] actions
- [ ] has_del
- [ ] min
- [ ] max
- [ ] multiple
- [ ] required
- [ ] widget
- [ ] preffix
- [ ] suffix
- [ ] widget
- [x] status
- [ ] attr
- [ ] meta
### Свойства
- [ ] propertyModel
- [ ] model
### События 
- [ ] onUpdate
- [ ] onChangeProperty - толька для типа model
### Методы
- [ ] constructor
- [ ] validate(status = true)
- [ ] setAttributes(attributes)

---

## Атрибуты
#### type
> Тип отображаемого филда

- string (по умолчанию)
- model 
- <router-link to='/doc/@iac/data/property_widget'>widget</router-link>
- markdown
- eimzo
- link
- data-view
- data-grid
- entity
- date-time
- date
- time
- action
- enum
- enum-tree
- tag
- range
- bool
- file
- text
- html
- static
- info (устаревший)

расширенные типы
- entity-edit
- layout-static
- member_image
- phone
- product
- rate

### status
> На данный момент имеются всего 2 статуса: **error** и **success** но планируется добавить **info** и **warning**</br>
> При изменении значения филда статус сбрасывается

<ui-layout-group class='horizontal_2' horizontal >
<doc-code :code='status_model.properties.field1.status' />
<ui-field :model='status_model.properties.field1'/>

<doc-code :code='status_model.properties.field2.status' />
<ui-field :model='status_model.properties.field2'/>

<doc-code :code='status_model.properties.field3.status' />
<ui-field :model='status_model.properties.field3'/>
</ui-layout-group>

> Для филдов которые в компонентной группе <> описание статусов,по техничиским причинам, выводится ввиде title

<ui-field :model='status_model.properties.field4'/>
`}