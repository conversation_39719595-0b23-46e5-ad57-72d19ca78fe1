import Vue from "vue";
import { Context } from "@iac/kernel";

const Component = {
  props: ["model"],
  data: function () {
    return {
      user: Context.User
    }
  },
  template: `
    <ui-data-view-item :model='model'>
        <template slot='header'>
            <div>
              <iac-date :title='$t("exchange.session_date")' v-if='model.session?.event_start' :date="model.session.event_start" :full="true" />
            </div>
        </template>
        
        <template slot='title'>
            <div>{{ model.product?.product_name || $t('exchange.unnamed_product') }}</div>
        </template>

        <template slot='props'>
            <div v-if='model.parent_id'>
              <label>{{ $t('exchange.contract_number') }}:</label>
              <div>{{ model.parent_id }}</div>
            </div>
            
            <div v-if='model.unit'>
              <label>{{ $t('exchange.unit') }}:</label>
              <div><ui-ref source='ref_unit' :value='model.unit' /></div>
            </div>
            
            <div v-if='model.type_of_packing'>
              <label>{{ $t('exchange.packing_type') }}:</label>
              <div>{{ model.type_of_packing.name }}</div>
            </div>
            
            <div v-if='model.unit_of_packing'>
              <label>{{ $t('exchange.packing_unit') }}:</label>
              <div><ui-ref source='ref_unit' :value='model.unit_of_packing' /></div>
            </div>

            <div v-if='model.amount'>
              <label>{{ $t('exchange.lots_amount') }}:</label>
              <div>{{ model.amount }}</div>
            </div>

            <div v-if='model.max_price'>
              <label>{{ $t('start_price_lot') }}:</label>
              <div><iac-number :value='model.max_price' delimiter=' ' part='2' /></div>
            </div>

            <div v-if='model.stock_address && model.stock_address.meta && model.stock_address.meta.area'>
              <label>{{ $t('storage_location') }}:</label>
              <div>{{ model.stock_address.meta.area.name }}</div>
            </div>
        </template>
    </ui-data-view-item>
  `
}
Vue.component("template-exposition_list", Component)