export default {
    name: 'renderTable',
    props: {
      searchResult: {
        type: Array,
        required: true
      }
    },
    data() {
      return {
        focusedItem: undefined
      }
    },
    methods: {
      setCurrentItem(item) {
        this.$emit('setCurrentItem', item)
      }
    },
    template: `
    <div class="iac--ktru-burse__table"  @mouseleave="focusedItem=undefined" @keydown.enter="e=>setCurrentItem(focusedItem)">
      <div v-for="item in searchResult" @mouseover="focusedItem=item" :class="{ active: focusedItem==item }" @click="e=>setCurrentItem(item)">
        <div :title="item.product.temporary.currentName">{{item.product.temporary.currentName}}</div>
      </div>
    </div>
    `
  }