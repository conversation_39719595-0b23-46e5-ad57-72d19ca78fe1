import Vue from 'vue';
import { Develo<PERSON> } from '@iac/kernel';

const Component = {
  props: {
    value: {},
    delimiter: {},
    round: {},
    part: {},
    zero: {
      default: true
    },

  },
  data() {
    return {
      number: undefined,
    };
  },
  watch: {
    value: {
      immediate: true,
      async handler(value) {
         this.number = parseFloat(value);
      }
    }
  },
  computed: {
    display() {
      if (isNaN(this.number)) {
        return;
      }
      let res = this.number;
      const part = parseInt(this.part, 10);
      const strNumber = isNaN(part) ? res + '' : (this.round == true ? (""+Math.round(res*Math.pow(10,part))/Math.pow(10,part)) : res.toFixed(part));
      let [whole, fraction] = strNumber.split('.');
      res = whole;

      if (this.delimiter) {
        let delimited = whole.split('');
        for (let i = delimited.length - 4, j = 0; i >= 0; i--, j++) {
          if (j % 3 === 0) {
            delimited[i] += this.delimiter;
          }
        }
        res = delimited.join('');
      }
      if (this.round === 'up') {
        const numberFraction = this.number.toString().split('.')[1];
        if (part > 0) {
          if (fraction < numberFraction) {
            if (fraction[fraction.length - 1] === '9') {
              if (fraction.length > 1) {
                fraction = fraction.slice(0, -2) + fraction[fraction.length - 2]++ + '0';
              } else {
                fraction = '0'.repeat(part);
                res++;
              }
            } else {
              fraction = fraction.slice(0, -1) + (+fraction[fraction.length - 1] + 1);
            }
          }
        }
      }
      if (fraction !== undefined) {
        if(fraction != "00" || this.zero)
          res += `.${fraction}`;
      }
      return res;
    }
  },
  template: `
    <span>
      <span style='white-space: nowrap'>{{ display }}</span> <template v-if='$develop.number_debug'>({{ value }})</template>
    </span>
  `,
};

Vue.component('iac-number', Component);
