import Vue from "vue"

var Section = {
    props: {
        type: String,
    },
    computed: {
        classes() {
            return [
                (() => {
                    if (this.type)
                        return this.type.split(' ').map((type) => {
                            return `iac-section-${type}`
                        }).join(" ");
                })()]
        }
    },
    template: `
        <div class='iac-section' v-bind:class="classes">
            <div class='iac-section-container'>
                <slot/>
            </div>
            <iac-banners v-if='type=="header"' class='iac-section-container' position='header' />
        </div>
    `
}

var Page = {
    props: {
        title: {

        }
    },
    data: function(){
        return {
            full: false
        }
    },
    computed: {
        classes() {
            return [
                "iac-page",
                {
                    full: this.full
                },
                (() => {

                })()]
        }
    },
    template: `
        <iac-maximize :maximize='full'  style='padding: 0;'>
            <iac-section type='header'>
                <slot name='breadcrumb'/>
                <ol v-if='0' class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>Заголовок страницы</li>
                </ol>

                <div class='title'>
                    <h1>{{title}}</h1>
                    <icon style='font-size: 40px; cursor: pointer;' v-on:click='full = !full' >{{full ? "minimize" : "maximize"}}</icon>
                </div>
            </iac-section>
            <slot/>
        </iac-maximize>
    `
}

Vue.component("iac-page",Page);
Vue.component("iac-section",Section);

