import { Http } from '@iac/core';
import { Tick, Settings } from '@iac/kernel';

export default {
  data() {
    return {
      systemDate: undefined,
    }
  },
  async mounted() {
    await this.updateTime();
    Tick.onTick.bind(this.updateTime);
  },
  destroyed() {
    Tick.onTick.unbind(this.updateTime);
  },
  methods: {
    async updateTime() {
      this.systemDate = new Date(Http.api.dateNow());
    }
  },
  computed: {
    timeZone() {
      if (Settings._country == 'KG') {
        return "Asia/Bishkek"
      }
      return "Asia/Tashkent"
    },
    time() {
      return this.systemDate ?
        this.systemDate.toLocaleTimeString("ru-RU", { timeZone: this.timeZone }) : '--:--';
    },
    date() {
      return this.systemDate ? this.systemDate.toLocaleDateString("ru-RU", { timeZone: this.timeZone }) : ''
    },
    gmt() {
      if (Settings._country == 'KG') {
        return "GMT+6"
      }
      return "GMT+5"
    }
  },
  template: `
    <time class='clock'>
      <icon class='clock__icon'>calendar</icon>
      <span>{{ date }}</span>

      <icon class='clock__icon'>clock</icon>
      <span class='clock__time'>{{ time }} ({{gmt}})</span>
 
    </time>
  `
}