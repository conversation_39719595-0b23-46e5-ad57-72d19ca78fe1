import { Http, Language } from '@iac/core'
import { DataSource, RemoteStore } from '@iac/data'

export default {
    data() {
        return {
            groups: {general: []},
            error: undefined,
        }
    },
    mounted(){
        this.$wait(async ()=>{
            let {error,data} = await Http.api.rpc('report')
            this.error = error
           if(data){
            data.forEach(item => {
                let group_name = item.group || 'general'
                this.groups[group_name] = this.groups[group_name] || []
                this.groups[group_name].push(item);
            });
            console.log(data);
           }
        })
    },
    methods: {
        createReportDialog(model, type) {

            this.$wait(async () => {
                const [ref, template] = model.report.split(":tmpl")
                const renderData = { ref, template, type, params: {} }
                const { error, data } = await Http.reportAsync.rpc("render_free_report_async", renderData);

                if (error) {
                    if (error.code == "AbortError")
                        return;
                    Vue.Dialog.MessageBox.Error(error)
                } else {
                    Vue.Dialog.MessageBox.Success(this.$t('reports_order_success'));
                }
            });
        }
    },
    template: `
        <div>
        <template v-if='!error'>
            <ui-layout-group :label="key !== 'general' ? key : null" v-for='group, key in groups'>
                <div v-for='model,index in group'>
                    <ui-data-view-item :model='model' style='background: #fff; border: 1px solid #eee; margin-bottom: -1px'>
                        <div slot='title'>
                            <span>{{index+1}}. {{model.report_name}}</span>
                        </div>
                        <div slot='props'>                                
                            <ui-btn-group>
                                <ui-btn :key='format' type='info xs' v-for='format in model.operations' v-on:click.native='createReportDialog(model,format)'>{{format}}</ui-btn>
                            </ui-btn-group>
                        </div>
                        <div v-if='model.description' slot='description' style='color: #777;'>{{model.description}}</div>
                    </ui-data-view-item>
                </div>
            </ui-layout-group>
        </template>
                <ui-error v-else :code='error.status' :message='error.message' :details='error.details' :number='error.number' />
        </div>
    `
}
