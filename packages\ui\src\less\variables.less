//== Colors
@gray-base:                 #000;
@gray-darker:               lighten(@gray-base, 13.5%); // #222
@gray-dark:                 lighten(@gray-base, 20%);   // #333
@gray:                      lighten(@gray-base, 33.5%); // #555
@gray-light:                lighten(@gray-base, 46.7%); // #777
@gray-lighter:              lighten(@gray-base, 93.5%); // #eee

@brand-primary:             darken(#428bca, 6.5%); // #337ab7
@brand-secondary:           #868e96;
@brand-success:             #5cb85c;
@brand-info:                #5bc0de;
@brand-warning:             #f0ad4e;
@brand-danger:              #d9534f;

@font-size-base:            14px;
@font-size-large:           ceil((@font-size-base * 1.25)); // ~18px
@font-size-small:           ceil((@font-size-base * 0.85)); // ~12px

@font-size-h1:              floor((@font-size-base * 2.6)); // ~36px
@font-size-h2:              floor((@font-size-base * 2.15)); // ~30px
@font-size-h3:              ceil((@font-size-base * 1.7)); // ~24px
@font-size-h4:              ceil((@font-size-base * 1.25)); // ~18px
@font-size-h5:              @font-size-base;
@font-size-h6:              ceil((@font-size-base * 0.85)); // ~12px

@padding-base-vertical:     6px;
@padding-base-horizontal:   12px;

@padding-large-vertical:    10px;
@padding-large-horizontal:  16px;

@padding-small-vertical:    5px;
@padding-small-horizontal:  10px;

@padding-xs-vertical:       1px;
@padding-xs-horizontal:     5px;

@border-radius-base:        4px;
@border-radius-large:       6px;
@border-radius-small:       3px;


@line-height-base:          1.5; 
@line-height-computed:      floor((@font-size-base * @line-height-base));

@line-height-large:         1.3333333;
@line-height-small:         1.5;

@grid-columns: 12;
@grid-gutter-width: 6px;

@primary-link: #009ab8;

@import '../components/button/variables.less';
@import '../components/control/variables.less';
@import '../components/alert/variables.less';