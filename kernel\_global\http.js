import { Http, Language, Guid } from '@iac/core'
import * as HttpExtensions from './../extensions/http'
import Config from './../config'
import Context from './../context'

let key = Guid._newGuid();
// Инициализация http хостов
let channelParams = {};
let socketConfig = {
    channelParams: () => {
        channelParams.key = key;
        channelParams.location = location.href;
        channelParams.token = Context.User.access_token;
        channelParams.lng = (() => {
            return Language._local_socket;
        })()
        return channelParams
    },
    onReceiveOk: async (message) => {
        //console.log("onReceiveOk", message);
    },
    onReceiveError: async (response = {}, reason) => {

        if (response.code === "access_denied" || reason == "unauthorized") {
            await Context.User.refreshToken();
            channelParams.key = key;
            channelParams.response = response;
            channelParams.location = location.href
            channelParams.token = Context.User.access_token;
            channelParams.lng = (() => {
                return Language._local_socket;
            })()
            return true;
        }
    }
}

// Сервер авторизации
Http.addHost("oauth", () => ({ host: Config.oauth_server, path: 'oauth' })).addExtension([
    HttpExtensions.Blackout
]);

// Новый сервер авторизации
Http.addHost("auth", () => ({ host: Config.auth_server, path: 'auth' })).addExtension([
    HttpExtensions.Auth,
    HttpExtensions.Language,
    HttpExtensions.Blackout,
]);

// Апи сервер 
Http.addHost("report", () => ({ host: Config.api_server, path: 'report/render/free-report' })).addExtension([
    HttpExtensions.Auth,
    HttpExtensions.Language,
    HttpExtensions.Blackout,
    HttpExtensions.Question,
]);

Http.addHost("reportAsync", () => ({ host: Config.api_server, path: 'report/render/free-report-async' })).addExtension([
    HttpExtensions.Auth,
    HttpExtensions.Language,
    HttpExtensions.Blackout,
    HttpExtensions.Question,
]);

Http.addHost("api", () => ({ socket: socketConfig, host: Config.api_server, path: 'rpc' })).addExtension([
    HttpExtensions.Auth,
    HttpExtensions.Language,
    HttpExtensions.Blackout,
    HttpExtensions.Question,
]);

// Сервер процедурного процессора
Http.addHost("proc", () => ({ socket: socketConfig, host: Config.pp_server, path: 'urpc' })).addExtension([
    HttpExtensions.ms_pp_instance,
    HttpExtensions.Auth,
    HttpExtensions.Language,
    HttpExtensions.Blackout,
    HttpExtensions.Question,
]);

// Сервер загрузки файлов
Http.addHost("upload", () => ({ host: Config.upload_server, path: 'upload' })).addExtension([
    HttpExtensions.Auth,
    HttpExtensions.Language,
    HttpExtensions.Blackout
]);

// Сервер загрузки файлов
Http.addHost("file", () => ({ host: Config.upload_server, path: 'file' })).addExtension([
    HttpExtensions.Auth,
    HttpExtensions.Language,
    HttpExtensions.Blackout
]);

Http.default = "api";
