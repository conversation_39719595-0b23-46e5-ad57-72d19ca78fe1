
import Separator from './separator'
import documentWindow from './documentWindow'

var terminalWindow = {
    name: "terminalWindow",
    props: ["window", "horizontal", "allow"],
    data: function () {
        return {
            startResize: undefined
        }
    },
    components: {
        documentWindow,
        Separator,
        terminalWindow
    },
    computed: {
        child_count() {
            return this.child_s.length
        },
        child_s() {
            return Object.keys(this.window.child).map((key) => {
                let child = this.window.child[key];
                child.key = key
                return child;
            })
        }
    },
    methods: {
        onMouseUp(e) {
            e.preventDefault()
            e.stopPropagation();

            if (this.startResize) {
                window.removeEventListener('mouseup', this.onMouseUp);
                window.removeEventListener('mousemove', this.onMouseMove);
                this.startResize = undefined;

                this.window.space.save();

            }

        },
        onMouseMove(e) {
            e.preventDefault()
            e.stopPropagation();
            if (!this.startResize)
                return;
            let delta_x = e.x - this.startResize.point.x;
            let delta_y = e.y - this.startResize.point.y;

            let first = this.startResize.first
            let last = this.startResize.last

            delta_x = (first.node.size + last.node.size) * delta_x / (first.target.clientWidth + last.target.clientWidth + 5)
            delta_y = (first.node.size + last.node.size) * delta_y / (first.target.clientHeight + last.target.clientHeight + 5)

            let first_size = first.mem + (this.horizontal ? delta_x : delta_y);
            let last_size = first.mem + (this.horizontal ? delta_x : delta_y);

            let _100_x = (first.node.size + last.node.size) * 100 / (first.target.clientWidth + last.target.clientWidth)
            let _100_y = (first.node.size + last.node.size) * 100 / (first.target.clientHeight + last.target.clientHeight)

            if (this.horizontal && first_size > _100_x && last_size > _100_x) {
                first.node.size = first.mem + delta_x
                last.node.size = last.mem - delta_x
            } else if (!this.horizontal && first_size > _100_y && last_size > _100_y) {
                first.node.size = first.mem + delta_y
                last.node.size = last.mem - delta_y
            }
        },
        onMouseDown(event, child) {
            event.stopPropagation();
            event.preventDefault()
            let first = { target: event.target.previousElementSibling };
            let last = { target: event.target.nextElementSibling };
            if (this.window.child) {
                let prev_key = undefined;
                for (let key in this.window.child) {
                    if (key == child.key) {
                        break;
                    }
                    prev_key = key
                }
                first.node = this.window.child[prev_key];
                last.node = this.window.child[child.key];
            }

            first.mem = first.node.size;
            last.mem = last.node.size;

            this.startResize = {
                point: {
                    x: event.x,
                    y: event.y
                },
                first: first,
                last: last,
            }

            window.addEventListener("mousemove", this.onMouseMove, false)
            window.addEventListener("mouseup", this.onMouseUp, false)
        }
    },
    template: `
        <template>
            <div :class='"terminal-window "+(horizontal && "horizontal")' v-if='window.child && !window.items' >
                <template :key='child.key' v-for='child,index in child_s'>
                    <separator :window='window' :index='child.key' v-if='index' v-on:mousedown.native.prevent.stop="(e)=>onMouseDown(e,child)"/>
                    <terminalWindow :key='child.key+"_"+index' :horizontal='!horizontal' :window='child' :allow='allow' :style='"flex: 1 1 "+(child.size)+"%"'/>
                </template>
            </div>
            <documentWindow v-else-if='window.items' :horizontal='horizontal' :window='window' :allow='allow' :style='"flex: 1 1 "+(window.size)+"%"'  />
            <div v-else></div>
        </template>
    `,
}

export default terminalWindow