import { Settings } from "@iac/kernel";
import Vue from 'vue';
import './index.less';

const IacSocialIcons = {
    name: 'IacSocialIcons',
    data: function () {
        return {
            soc_icons: {
                facebook: `<svg width='28' height='28' viewBox='0 0 28 28' fill='none'>
                    <path d='M15 11.5V9.5C15 8.948 15.448 8.5 16 8.5H17V6H15C13.343 6 12 7.343 12 9V11.5H10V14H12V22H15V14H17L18 11.5H15Z' fill='#212226'/>
                    </svg>`,
                telegram: `<svg width='28' height='28' viewBox='0 0 24 24'>
                    <path d='m5.491 11.74 11.57-4.461c.537-.194 1.006.131.832.943l.001-.001-1.97 9.281c-.146.658-.537.818-1.084.508l-3-2.211-1.447 1.394c-.16.16-.295.295-.605.295l.213-3.053 5.56-5.023c.242-.213-.054-.333-.373-.121l-6.871 4.326-2.962-.924c-.643-.204-.657-.643.136-.953z' fill='#212226'/>
                    </svg>`,
                instagram: `<svg width='28' height='28' viewBox='0 0 28 28' fill='none'>
                    <rect x='7' y='7' width='14' height='14' rx='4' fill='#212226'/>
                    <circle class='insta-circle' cx='14' cy='14' r='3' fill='white' stroke='#212226'/>
                    <circle class='insta-circle' cx='18.375' cy='9.625' r='0.875' fill='white'/>
                    </svg>`,
                linkedin: `<svg width='28' height='28' viewBox='0 0 28 28' fill='none'>
                    <path d="M10.2,21.3H6.9V10.7h3.3V21.3z M10,8.8C9.6,9.1,9.1,9.3,8.5,9.3S7.5,9.1,7.1,8.8C6.7,8.4,6.6,8,6.6,7.5s0.2-1,0.6-1.3 C7.5,5.8,8,5.7,8.5,5.7c0.6,0,1,0.2,1.4,0.5c0.4,0.3,0.6,0.8,0.6,1.3C10.5,8,10.3,8.4,10,8.8z M22.4,21.1h-3.2v-5.7 c0-0.7-0.2-1.2-0.5-1.6c-0.4-0.4-0.8-0.6-1.5-0.6c-0.6,0-1.1,0.2-1.5,0.6c-0.4,0.4-0.5,0.9-0.5,1.6v5.7h-3.2V10.7h3.2v1.4 c0.3-0.5,0.8-0.8,1.3-1.1c0.5-0.3,1.2-0.4,1.9-0.4c1.2,0,2.2,0.4,2.9,1.2c0.7,0.8,1.1,1.9,1.1,3.3V21.1z" fill="#212226"/>
                    </svg>`
            }
        }
    },
    computed: {
        socials() {
            return (this.$settings._soc || []).map((soc) => {
                let key = undefined
                if (soc.indexOf('facebook') >= 0) {
                    key = 'facebook'
                } else if (soc.indexOf('instagram') >= 0) {
                    key = 'instagram'
                } else if (soc.indexOf('t.me') >= 0) {
                    key = 'telegram'
                } else if (soc.indexOf('linkedin.com') >= 0) {
                    key = 'linkedin'
                } else {
                    return {}
                }

                return {
                    url: `https://${soc}`,
                    icon: this.soc_icons[key],
                    title: key
                }
            })
        }
    },
    template: `
        <ul class='iac-service-social'>
            <li v-for='social in socials' v-if='social && social.url'>
                <a :title='$t(social.title)' :href='social.url' target='_blank'>
                    <div v-html='social.icon'>
                    </div>
                </a>
            </li>
        </ul>
        
    `
};

Vue.component('iac-social-icons', IacSocialIcons);

export default IacSocialIcons;