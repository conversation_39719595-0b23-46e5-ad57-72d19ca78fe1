import Vue from "vue";
import { Config } from '@iac/kernel'

const Component = {
    props: ["model"],
    data() {
        return {}
    },
    methods: {
        getFileUrl(id) {
            return `${Config.api_server}/file/${id}`
        }
    },
    template: `
        <ui-data-view-item :model='model'>            
            <template slot='header'>
                <div>
                  {{model.id}}
                </div>
                <div>
                  <span><iac-date :date='model.plan_date_release' withoutTime /></span>
                </div>
            </template>
            
            <template slot='title'>
                <div>{{model.product_name}}</div>
            </template>

            <template slot='description'>
                <div class='clamp_2' v-if="model.face_fullname">
                    <label>{{$t("shipment_creator")}}: </label>
                    <span :title='model.face_fullname'>{{ model.face_fullname }}</span>
                </div>
                <div class='clamp_2' v-if="model.company_fulltittle">
                    <label>{{ $t('company') }}: </label>
                    <span :title='model.company_fulltittle'>{{ model.company_fulltittle }}</span>
                </div>
                <div class='clamp_2'>
                    <label>{{$t("shipment_contract")}}: </label>
                    <span :title='model.claim_ids.join(", ")'>{{ model.claim_ids.join(", ") }}</span>
                </div>
                <div class='clamp_2' v-if='model.files && model.files.length > 0'>
                    <label>{{$t('file')}}: </label>
                    <span>
                        <span v-for="(file,index) in model.files">
                            &nbsp; <a :href='getFileUrl(file)'>file #{{ index }}</a>
                        </span>
                    </span>
                </div>
            </template>
            <template slot='props'>
                <div>
                    <label>{{ $t('unit') }}:</label>
                    <div>
                        <span :title='model.unit'>{{ model.unit }}</span>
                    </div>
                </div> 
                <div>
                    <label>{{ $t('amount') }}:</label>
                    <div>
                        <iac-number  :value='model.amount' delimiter=' ' part='2' :zero='false' />
                    </div>
                </div> 
                <div>
                    <label>{{$t('shipment_booked')}}:</label>
                    <div>
                        <iac-number  :value='model.booked' delimiter=' ' part='2' :zero='false' />
                    </div>
                </div>                 
                <div>
                    <label>{{$t("shipment_remainder")}}:</label>
                    <div>
                        <iac-number  :value='model.amount-model.booked' delimiter=' ' part='2' :zero='false' />
                    </div>
                </div>            

                <div v-if='0 && model.files && model.files.length > 0'>
                    <label>{{$t('file')}}:</label>
                    <div>
                        <div v-for="(file,index) in model.files">
                            <a :href='getFileUrl(file)'>file #{{ index }}</a>
                        </div>
                    </div>
                </div>
            </template>
        
        </ui-data-view-item>
    `
}

Vue.component("template-shipment_schedule", Component)
