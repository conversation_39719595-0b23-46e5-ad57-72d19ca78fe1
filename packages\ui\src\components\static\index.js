import Language from "../../../../core/src/language";

export var Static = {
    name: "ui-static",
    props: ['label', 'status', 'value', 'lng',"dataSource"],
    computed:{
        component_status() {
            if (typeof this.status == "string") {
                return {
                    type: this.status,
                    message: undefined
                }
            }
            return this.status;
        },
        classes() {
            return [
                (() => {
                    return this.component_status ? ["status", this.component_status.type] : '';
                })()
            ]
        },
        html(){
            if(this.label && (this.value === undefined || this.value === ''))
                return "<span style='opacity: 0.2; font-size: 14px;'>"+Language.t('no_data')+"</span>";
            if(Array.isArray(this.value)){
                return this.convert_array(this.value);
            }
            return this.convert_text(this.value);
        }
    },
    methods: {
        convert_text(text=''){
            let html = this.lng ? Language.t(text+"") : ""+text;
            return html.replace(/\n\n/gi,"</br>")
        },
        convert_array(array){
            let items = array.map((text)=>{
                return `<li>${this.convert_text(text)}</li>`
            })
            return `<ul>${items.join("")}</ul>`
        }
    },
    template: `
        <div v-if='dataSource'>
            <ui-ref class='ui-static' v-if='value' :value='value' :source='dataSource'/>
            <span v-else style='opacity: 0.2; font-size: 14px;'>{{$t('no_data')}}</span>
        </div>
        <div v-else class='ui-static' v-html="html" v-bind:class="classes"></div>
    `
}