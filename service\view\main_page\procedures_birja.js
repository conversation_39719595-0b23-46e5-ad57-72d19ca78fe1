import { DataSource, RefStore, Query } from '@iac/data';


var search_val = {
    value: undefined
  }

const org_company = ctx => {
  return {
    inn: ctx?.initiator?.company_details?.inn,
    company_title: ctx?.initiator?.company_details?.title,
    company_id: ctx?.initiator?.company_details?.id
  }
};

const contragent_company = ctx => {
  return {
    inn: ctx?.contragent?.company_details?.inn,
    company_title: ctx?.contragent?.company_details?.title,
    company_id: ctx?.contragent?.company_details?.id
  }
};

const company = ctx => {
  return {
    org_company: org_company(ctx),
    contragent_company: contragent_company(ctx)
  }
}


export default {
    data(){
      return {
        expositionList: new DataSource({
          limit: 9,
          store: {
            ref: "ref_exchanges_exposition",
            injectQuery: (params) => {
              params.limit = 9;
              params.op = "read";
              params.filters = {...params.filters, scope: "public"};
              params.data = {"raw_json": true};
              return params;
            },
          },
          template: "template-exposition_list",
        }),

        exchangeDealsList: new DataSource({
          limit: 6,
          valueExp: "number",
          store: new RefStore({
            method: "contract_ref",
            ref: "exchange_contract_public_registry",
            context: (context) => {
              const goodName = context.good?.product_name || '';
              const contractType = context.type_of_contract?.name || '';
              
              return {
                proc_type: 'schedule_exchange_contract',
                ...context,
                ...company(context),
                ref_status: "exchange_contract_status",
                contract_close_at: context.inserted_at || context.contract_close_at,
                contract_name: goodName,
                contract_type: contractType
              }           
            }, 
            injectQuery: (params) => {
              params.limit = 6;
              return params;
            },
          }),
          template: "template-exchange_contract"
        })
      }
    },
    mounted(){
        search_val.value = undefined
    },
    components: {
        procedureSearch: {
            props: ["type"],
            data: function(){
              return {
                search_val: search_val
              }
            },
            computed: {
              label(){
                return `search.${this.type}.placeholder`;
              }
            },
            methods: {
              search(){
                let path = this.type == 'exchange_deals'? 'registry/exchange_deals' : this.type
                if(search_val.value)
                  this.$router.push({path: `${path}?queryText=${search_val.value}`})
                else
                  this.$router.push({path: `${path}`})
              }
            },
            template: `
              <form  @submit.prevent='search' style='max-width: 700px; margin: 0 auto; width: 100%;'>
                <ui-control-group>
                  <ui-input icon='search' :label='label' v-model='search_val.value' />
                  <ui-btn type='primary'>{{$t('search')}}</ui-btn>
                </ui-control-group>
              </form>
            `
          }
    },
    template: `
    <ui-layout-tab class='proc_tab' name='procedures_b'>
        <ui-layout-group key='bkl' label='bkl.short'>
            <procedure-search type='bkl' />
            <iac-view-bkl :limit='10' />
            <div style='text-align:center; margin: 8px'><router-link class='ui-btn ui-btn-primary' to='/bkl'>{{ $t('hp.goto_bkl_page') }}</router-link></div>
        </ui-layout-group>

        <ui-layout-group key='exchange_schedule' label='exchange.schedule'>
          <iac-exchange-schedule style='margin-bottom: 30px;' />
        </ui-layout-group>

        <ui-layout-group v-if='$settings.exchange && $settings.exchange._financial_quotation' key='financial_quotation' label='financial_quotation.short'>
            <procedure-search type='financial_quotation' />
            <iac-view-financial_quotation  :limit='10' />
            <div style='text-align:center; margin: 8px'><router-link class='ui-btn ui-btn-primary' to='/financial_quotation'>{{ $t('hp.goto_financial_quotation_page') }}</router-link></div>
        </ui-layout-group>

        <ui-layout-group v-if='$settings.exchange && $settings.exchange._exposition_list' key='exposition_list' label='nav.exposition_list'>
            <procedure-search type='exposition_list' />
            <ui-data-view :toolbar='false' :search='false' :showMore='false' :dataSource='expositionList' />
            <div style='text-align:center; margin: 8px'><router-link class='ui-btn ui-btn-primary' to='/exposition_list'>{{ $t('hp.goto_exposition_list') }}</router-link></div>
        </ui-layout-group>

        <ui-layout-group v-if='$settings.exchange && $settings.exchange._exchange_deals' key='exchange_deals' label='nav.exchange_deals'>
            <procedure-search type='exchange_deals' />
            <ui-data-view :toolbar='false' :search='false' :showMore='false' :dataSource='exchangeDealsList' />
            <div style='text-align:center; margin: 8px'><router-link class='ui-btn ui-btn-primary' to='/registry/exchange_deals'>{{ $t('hp.goto_exchange_deals') }}</router-link></div>
        </ui-layout-group>

    </ui-layout-tab>
    `
}