export default {
  props: ['item', 'actions', 'editable'],
  data() {
    return {
      isClosed: true
    }
  },
  computed: {
    icon() {
      return this.isClosed ? '+':'–';
    }
  },
  template: `
    <div class="faq-item">
      <div class="faq-header" @click="isClosed = !isClosed">
        <span class="faq-expander">{{icon}}</span>
        <div class="faq-question" :class='{open: !isClosed}'>{{item.question}}</div>
        <div class="faq-tools" v-if='editable'>
          <ui-btn type='primary' @click.stop.native='actions.editItem(item)' :title='$t("edit")'><icon>doc</icon></ui-btn>
          <ui-btn type='danger' @click.stop.native='actions.deleteItem(item)' :title='$t("delete")'><icon>delete</icon></ui-btn>
          <ui-btn :type='item.show ? "success": "warning"' @click.stop.native='actions.toggleShow(item)' :title='item.show ? $t("visible") : $t("invisible")'><icon>ok</icon></ui-btn>
        </div>
      </div>
      <div class="faq-answer" :class='{closed: isClosed}'>{{item.answer}}</div>
    </div>
  `
}