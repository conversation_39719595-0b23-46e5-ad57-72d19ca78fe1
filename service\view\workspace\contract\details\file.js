import { Http, Event, Language, Action } from '@iac/core'
import { Config, Context } from '@iac/kernel'

let edit_file = Vue.Dialog("editContractFile",{
    props: ["value","onSave","readonly","number"],
    data: function(){
        return {
            source: this.value
        }
    },
    mounted(){
        this.$wait(async ()=>{
            if(this.number){
                let { error, data } = await Http.api.rpc('contract_ref', {
                    op: "get_contract_body",
                    ref: "contract_public_registry",
                    data: {number: this.number},
                  });;
                if(data){
                    this.value = data.body
                }
            }
        });
    },
    methods: {
        save(){
            this.$wait(async ()=>{
                if(this.onSave){
                    let {error,data} = await this.onSave(this.source);
                    if(!error){
                        Vue.Dialog.MessageBox.Question(Language.t('contract.save.close_question')).then((result)=>{
                            if(result == Vue.Dialog.MessageBox.Result.Yes)
                                this.Close();
                        })
                    }
                }
            })
        },
        print(){
            const contentWindow = this.$refs.frame.contentWindow;
            contentWindow.document.open();
            contentWindow.document.write(this.value);
            contentWindow.print();
            contentWindow.document.close();
        }
    },
    template: `
        <div>
            <header>{{$t('contract')}}</header>
            <main>
                <template v-if='readonly'>
                    <ui-scroller class='' style='border: 1px solid #ccc'>
                        <div class='jodit_wysiwyg' v-html='value' style='overflow-x: auto;padding: 10px;'/>
                    </ui-scroller>
                    <iframe ref='frame' style='display: none;' />
                </template>
                <ui-html v-else :readonly='readonly' v-model='source' />

            </main>
            <footer style='position: sticky; bottom: 20px;pointer-events: none;'>
                <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
                <ui-btn type='primary' v-if='readonly' v-on:click.native='print'>{{$t('print')}}</ui-btn>
                <ui-btn type='primary' v-if='!readonly' v-on:click.native='save'>{{$t('save')}}</ui-btn>
            </footer>
        </div>
    `
})

export default {
    props: ["model"],
    data: function(){
        return {
            attach: false
        }
    },
    methods:{
        add_file(){
            this.$refs.input.value = null;
            this.$refs.input.click()
        },
        async edit_file(){
            await edit_file.Modal({
                size: 'full',
                readonly: !this.model.rights.put_contract_body,
                value: this.model.contract_body?.body,
                onSave: async (contract_body)=>{
                    return await this.model.put_contract_body({
                       body: contract_body || '',
                       meta: {is_plain_body: true}
                    })
                }
            })
            
        },
        async delete_file(){
            this.$wait(async ()=>{
                await this.model.put_contract_body(null)
            });
        },
        async open_file(e) {
            e.preventDefault();
            if(this.model.contract_body?.body || this.model.contract_body?.body ==''){// && this.model.rights.put_contract_body){
                return this.edit_file();
            }

            this.$wait(async ()=>{
                if(!this.model.contract_body?.body){
                    await Context.User.refreshToken();
                    window.location = `${this.model.contract_body.link}?token=${Context.User.access_token}`
                    return;
                }
            })

        },
        put_contract_body(body){
            this.$wait(async ()=>{
                await this.model.put_contract_body(body)
            });
        },
        onChange(event){
            this.$wait(async ()=>{
                let file = event.target.files && event.target.files[0];
                
                if (file.size > 15*1024*1024) {
                  Vue.Dialog.MessageBox.Error(Language.t('file_size_exceeds_some_kb').replace('_____',Number(15*1024).toFixed(2)))
                  this.$refs.input.value = null;
                  return;
                }


                let formData = new FormData();

                let file_name = file.name || "";
                //file_name = `${this.$t('contract')}_№${this.model.number}.${file_name.split('.').pop()}`;

                //formData.append('scope_contract_participant', this.model.number);
                formData.append('data', file, file_name);
                let response = await Http.upload.form('user/put_contract_body', formData);
                if (response.error) {
                    Vue.Dialog.MessageBox.Error(response.error);
                }else{
                    await this.model.put_contract_body(response.data && response.data.result)
                }
            })
        }
    },
    template: `
    <div class='grid'>
        <div class='row' v-if='!model.contract_body && (model.rights.put_contract_body || model.contract_body)'>
            <h2>{{$t('contract.file')}}</h2>
        </div>

        <div class='row' v-if='model.contract_body || model.rights.put_contract_body'>
            <label class='col-sm-3'>{{$t(!model.contract_body ? 'contract.uploading_file' : 'contract.file') }}:</label>
            <iac-contract-file :readonly='!model.rights.put_contract_body' :value='model.contract_body' v-on:change='put_contract_body'  class='col-sm-5' />
        </div>       
    </div>    
    `
}