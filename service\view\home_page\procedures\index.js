import { DataSource, RemoteStore, RefStore, Query } from '@iac/data';
import IacCarousel from '../../../components/carousel';
import { Context } from '@iac/kernel'

var search_val = {
  value: undefined
}

export default {
  data() {
    return {
      search_val: search_val,
      user: Context.User,
      tenders: new DataSource({
        store: new RefStore({
          ref: 'ref_tender_public',
          injectQuery: (params) => {
            params.fields = ["green","id","publicated_at","status","name","good_count","close_at","totalcost","currency", "lang","part_count","meta","remain_time","lot_count"]
            params.limit = 6;

            return params;
          },
        }),
        template: 'template-tender'
      }),
      selection: new DataSource({
        store: new RefStore({
          ref: 'ref_selection_public',
          injectQuery: (params) => {
            params.fields = ["green","id","publicated_at","status","name","good_count","close_at","totalcost","currency", "lang","part_count","meta","remain_time","lot_count"]

            params.limit = 6;
            return params;
          },
        }),
        template: 'template-selection'
      }),
      master_agreement: new DataSource({
        store: new RefStore({
          ref: 'ref_master_agreement_public',
          injectQuery: (params) => {
            params.fields = ["green","id","publicated_at","status","name","good_count","close_at","totalcost","currency", "lang","part_count","meta"]

            params.limit = 6;
            return params;
          },
        }),
        template: 'template-master_agreement'
      }),
      ad: new DataSource({
        query: new Query({
          is_national: {
            value: false,
            hidden: true,
            sync: false,
          },
        }),
        store: new RefStore({
          ref: 'ref_online_shop_public',
          injectQuery: (params) => {
            params.fields = ["green","product","unit","debug_info","id","publicated_at","status","name","price","close_at","totalcost","currency", "amount","min_amount","images","owner_legal_area_id","product_name","remain_time"]
            params.limit = 6;
            params.filters = params.filters || {};
            params.filters.is_gos_shop = true;
            return params;
          },
        }),
      }),
      nad: new DataSource({
        query: new Query({
          is_national: {
            value: true,
            hidden: true,
            sync: false,
          },
        }),
        store: new RefStore({
          ref: 'ref_online_shop_public',
          injectQuery: (params) => {
            params.fields = ["green","product","unit","debug_info","id","publicated_at","status","name","price","close_at","totalcost","currency", "amount","min_amount","images","owner_legal_area_id","product_name","remain_time"]
            params.limit = 6;
            params.filters = params.filters || {};
            params.filters.is_gos_shop = true;
            return params;
          },
        }),
      }),
      reduction: new DataSource({
        store: new RefStore({
          ref: 'ref_reduction_object_public',
          injectQuery: (params) => {
            params.fields = ["green","contract_pay_percent","id","publicated_at","status","name","good_count","close_at","totalcost","currency", "lang","part_count","meta","start_price","last_price","remain_time"]
            params.limit = 6;
            return params;
          },
        }),
        template: 'template-reduction'
      }),
    }
  },
  mounted(){
    search_val.value = undefined
  },
  components: {
    IacCarousel,
    procedureSearch: {
      props: ["type"],
      data: function(){
        return {
          search_val: search_val
        }
      },
      computed: {
        label(){
          return `search.${this.type}.placeholder`;
        }
      },
      methods: {
        search(){
          if(search_val.value)
            this.$router.push({path: `procedure/${this.type}?queryText=${search_val.value}`})
          else
            this.$router.push({path: `procedure/${this.type}`})
        }
      },
      template: `
        <form  @submit.prevent='search' style='max-width: 700px; margin: 0 auto; width: 100%;'>
          <ui-control-group>
            <ui-input icon='search' :label='label' v-model='search_val.value' />
            <ui-btn type='primary'>{{$t('search')}}</ui-btn>
          </ui-control-group>
        </form>
      `
    }
  },
  computed: {
    adOptions() {
      return {
        items: 1,
        gutter: 24,
        nav: false,
        loop: false,
        rewind: true,
        autoplayTimeout: 5000,
        responsive: {
          600: {
            items: 2,
          },
          1000: {
            items: 3,
          },
          1300: {
            items: 4,
          },
        }
      };
    },
  },
  template: `
    <template>


        <ui-layout-tab class='proc_tab' name='procedures'>
          
          <ui-layout-group key='tender' label='tender' v-if='$settings.procedures && $settings.procedures.tender'>
            <procedure-search type='tender' />
            <ui-data-view type='grid' :search='false' :toolbar='false' :dataSource='tenders'/>
            <div style='text-align:center; margin: 8px'><router-link class='ui-btn ui-btn-primary' to='/procedure/tender'>{{ $t('hp.goto_tender_page') }}</router-link></div>
          </ui-layout-group>

          <ui-layout-group key='selection' label='selection'>
            <procedure-search type='selection' />
            <ui-data-view type='grid' :search='false' :toolbar='false' :dataSource='selection'/>
            <div style='text-align:center; margin: 8px'><router-link class='ui-btn ui-btn-primary' to='/procedure/selection'>{{ $t('hp.goto_selection_page') }}</router-link></div>
          </ui-layout-group>

          <ui-layout-group key='reduction' v-if='$settings.procedures && $settings.procedures.auction' label='reduction'>
            <procedure-search type='reduction' />
            <ui-data-view type='grid' :search='false' :toolbar='false' :dataSource='reduction'/>
            <div style='text-align:center; margin: 8px'>
              <router-link class='ui-btn ui-btn-primary' to='/procedure/reduction'>{{ $t('hp.goto_reduction_page') }}</router-link>
            </div>
          </ui-layout-group>

          <ui-layout-group key='ad' v-if='$settings.procedures && $settings.procedures.e_shop' label='nav.electronic_shop'>
            <procedure-search type='ad' />
            <ui-list :dataSource='ad' class='tns-controls-center mb-12'>
              <iac-carousel v-if='props && props.items && props.items.length' slot='items'
                slot-scope='props' :model='adOptions'>
                <div v-for='item in props.items' :key='item.id'>
                  <widget-shop type='ad' :item='item' class='h-100' />
                </div>
              </iac-carousel>
            </ui-list>
            <div class='text-center'>
              <router-link class='ui-btn ui-btn-primary' to='/procedure/ad'>{{ $t('hp.goto_ad_page') }}</router-link>
            </div>
          </ui-layout-group>

          <ui-layout-group key='nad' v-if='$settings.procedures && $settings.procedures.e_shop && $settings.procedures.e_shop.national_shop' label='nav.national_shop'>
            <procedure-search type='nad' />
            <ui-list :dataSource='nad' class='tns-controls-center mb-12'>
              <iac-carousel v-if='props && props.items && props.items.length' slot='items'
                slot-scope='props' :model='adOptions'>
                <div v-for='item in props.items' :key='item.id'>
                  <widget-shop type='nad' :item='item' class='h-100' />
                </div>
              </iac-carousel>
            </ui-list>
            <div class='text-center'>
              <router-link class='ui-btn ui-btn-primary' to='/procedure/nad'>{{ $t('hp.goto_ad_page') }}</router-link>
            </div>
          </ui-layout-group>

          <ui-layout-group key='master_agreement' v-if='$settings.procedures && $settings.procedures.master_agreement' label='master_agreement'>
            <procedure-search type='master_agreement' />
            <ui-data-view type='grid' :search='false' :toolbar='false' :dataSource='master_agreement'/>
            <div style='text-align:center; margin: 8px'><router-link class='ui-btn ui-btn-primary' to='/procedure/master_agreement'>{{ $t('hp.goto_master_agreement') }}</router-link></div>
          </ui-layout-group>          

          
        </ui-layout-tab>

</template> 
  `
}