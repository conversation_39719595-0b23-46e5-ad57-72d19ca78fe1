import Vue from 'vue'
import { Entity, DataSource, ArrayStore } from '@iac/data'


class BankAccountInfo extends Entity {
    constructor(context = {}) {
        super(context)
        this.bank_requisites = context.bank_requisites;
    }
    props() {
        return {
            bank_requisites: {
                label: "!",
                group: 'company.bank_requisites',
                type: "model",
                readonly: true,
                fields: {
                    bank_name: {
                        type: "static",
                        label: "-company.bank_name"
                    },                        
                    bank_bik: {
                        type: "static",
                        label: "-company.bank_code_bik"
                    },
                    bank_swift: {
                        type: "static",
                        label: "-company.swift"
                    },
                    bank_country: {
                        type: "static",
                        label: "-company.bank_country"
                    },
                    bank_address: {
                        type: "static",
                        label: "-company.bank_address"
                    },
                    account: {
                        type: "static",
                        label: "-company.client_account"
                    },
                    account_currency: {
                        type: "static",
                        label: "-company.account_currency"
                    }
                }
            }
         }
    }  
}

const Component = {
  props: ["model"],
   methods: {
          async info() {
              let items = await Vue.Dialog({
                  props: ["info"],
                  data: function () {
                      return {
                          model: new BankAccountInfo(),
                      }
                  },
                  mounted() {
                      this.$wait(async () => {
                          this.model = new BankAccountInfo(this.info)
                      });
                  },
                  template: `
                      <div>
                          <main>
                              <ui-layout :fields='model.fields'/>
                          </main>
                          <footer>
                              <ui-btn type='secondary' v-on:click.native='Close()'>{{$t("close")}}</ui-btn>
                            </footer>
                      </div>
                  `
              }).Modal({info: this.model
              })
  
  
          }
      },
    template: `
    <ui-data-view-item :model='model'>
      <template slot='header'>
            <div v-if='model.meta && model.meta.gnk_status !== undefined'>
            <div v-if='model.meta.gnk_status === 0' class='color-green'>{{$t('account_active')}}</div>
            <div v-else class='color-red'>{{$t('account_inactive')}}</div>
            <div v-else > </div>
            </div>
            <div >{{model.is_linked? $t('central_organization_account') : ''}}</div>
        </template>
        
      <template slot='title'>
            <span v-on:click='info' v-if="model.is_basic" class="link-style" :title="$t('hp.library.title')">{{model.name}}</span>
            <span v-else>{{model.name}}</span>
        </template>
        
      <template slot='description'>
            <div><label>{{$t('bank_name')}}: </label><span>{{model.bank_name}}</span></div>
            <div><label>{{$t('bank_account')}}: </label><span>{{model.bank_account}}</span></div>
            <div v-if='model.external_company'><label>SWIFT: </label><span>{{model.bank_mfo_code}}</span></div>
            <div v-else><label>{{$t([$settings._country+'.bank_mfo_code','bank_mfo_code'])}}: </label><span>{{model.bank_mfo_code}}</span></div>
        </template>
        
      <template slot='props'>
            <div v-if='model.share_count && !model.is_linked'><label>{{$t('common_access')}}: </label><div>{{model.share_count}}</div></div>
            <div v-if='model.bank_country'><label>{{$t('country')}}: </label><div>{{ model.bank_country }}</div></div>
            <div v-if='model.currency'><label>{{$t('currency')}}: </label><div>{{ model.currency }}</div></div>
            <div><label>{{$t('is_main')}}: </label><div>{{ model.is_main ? $t('yes') : $t('no') }}</div></div>
        </template>
    </ui-data-view-item>
    `
};

Vue.component("template-bank_account", Component)