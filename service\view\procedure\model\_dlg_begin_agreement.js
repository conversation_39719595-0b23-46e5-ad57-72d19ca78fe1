import { Http, Language } from '@iac/core'
import { DataSource, Query, RemoteStore } from '@iac/data'

import Router from '../../../router';

export default Vue.Dialog("ProcedureBeginAgreement",{
    props: ["proc_id"],
    data: function () {
        return {
            comment: undefined,
            type: undefined,
            dataSource: new DataSource({
                displayExp: "title",
                query: new Query({
                    proc_id: {
                        value: this.proc_id
                    }
                }),
                store: new RemoteStore({
                    key: "object",
                    host: Http.proc,
                    method: "get_objects_for_agreementation",
                })
            })
        }
    },
    methods: {
        async create() {
            let params = {
                proc_id: this.proc_id,
                comment: this.comment,
                object: this.type && this.type.object,
                data: this.type && { ...this.type.data },
            }
            let { data, error } = await Http.proc.rpc("begin_agreement", params);
            if (error)
                return await Vue.Dialog.MessageBox.Error(error)

            if (data && data.id) {
                this.Close();
                Router.push(`/workspace/agreement/procedure/${data.id}`)
            }

        }
    },
    template: `
    <div>
        <header>{{$t('agreement.create')}}</header>
        <main>
            <ui-entity v-model='type' :label='$t("approval_type")' :dataSource='dataSource' />
            <ui-text v-model='comment'  label='comment'/>
        </main>
        <footer>
            <ui-btn type='secondary' @click.native='Close()'>{{$t('cancel')}}</ui-btn>
            <ui-btn type='primary' v-on:click.native='create'>{{$t('create')}}</ui-btn>    
        </footer>
    </div>
`
})