import { Config, Context } from '@iac/kernel'
import { Language } from '@iac/core'
import { Util } from '@iac/core'

var statusDetails = {
    props: ["item"],
    computed: {
        status() {
            return this.item.status
        },
        details() {
            if (Object.keys(this.item[this.status] || {}).length <= 0) {
                return;
            }
            return this.item[this.status]
        }
    },
    methods: {
        async download_file(file) {
            await Context.User.refreshToken();
            const url = `${Config.api_server}/file/${file.id}?token=${Context.User.access_token}`

            var a = document.createElement("a");
            document.body.appendChild(a);
            a.style = "display: none";
            a.href = url;
            a.click();
            window.URL.revokeObjectURL(url);
            a.remove();
        },
        show_details() {
            Vue.Dialog.MessageBox.Form({
                fields: [
                    {
                        label: "-date",
                        type: "widget",
                        widget: {
                            name: "iac-date",
                            props: {
                                fullWithSeconds: true,
                                date: this.details.date,
                            },
                        },
                        hidden: () => !this.details.date,
                        readonly: true,
                        value: this.details.date
                    },
                    {
                        label: "-comment",
                        type: "static",
                        readonly: true,
                        value: this.details.comment,
                        hidden: () => !this.details.comment,
                    },
                    {
                        label: "-Прикрепленные файлы",
                        type: "file",
                        multiple: true,
                        readonly: true,
                        value: this.details.files,
                        meta: {
                            url: (value) => {
                                if (!value.id)
                                    return;
                                return `${Config.api_server}/file/${value.id}`
                            },
                        },
                        hidden: () => !this.details.files
                    }
                ]
            }, this.status, 3)
        }
    },
    template: `
        <div>
            <ui-ref v-if='details' v-on:click.native='show_details' style='cursor: pointer; text-decoration: underline;' source='shipment_docs_status' :value='status'/> 
            <ui-ref v-else source='shipment_docs_status' :value='status'/> 
        </div>
    `
}

export default {
    props: ['model'],
    data: function () {
        return {
            columns: [
                { field: 'date', summary: `${Language.t('total')}:`},
                { field: 'comment', label: 'comment', style: "width:100%" },
                { field: "count", label: Language.t('shipment_quantity'), type: "float", summary: "SUM", style: "text-align:center; white-space: nowrap;", suffix: this.model.exchangeBody?.good?.unit },
                { field: "files", label: Language.t('documents')},
                "status"
            ]
        }
    },
    methods: {
        async download_file(file) {
            await Context.User.refreshToken();
            const url = `${Config.api_server}/file/${file.id}?token=${Context.User.access_token}`

            var a = document.createElement("a");
            document.body.appendChild(a);
            a.style = "display: none";
            a.href = url;
            a.click();
            window.URL.revokeObjectURL(url);
            a.remove();
        },
        onItemHandler(start) {
            if (start)
                this.$el.classList.add("iac-wait");
            else
                this.$el.classList.remove("iac-wait");
        }
    },
    components: {
        statusDetails
    },
    template: `
        <div>
            <ui-field :model='model.exchangeBody.properties.status'/>

            <ui-action v-on:item_handler='onItemHandler' style='margin-bottom: 20px;' :buttons='true' :actions='model.shipment_docs.actions' />

            <ui-data-grid :readonly='true' :dataSource='model.shipment_docs' :summary='true' :columns='columns' :buttons="false" :scroller='true'>
                <template slot='date' slot-scope='props'><div> <iac-date :date='props.item.date' withoutTime/></div></template>
                <template slot='files' slot-scope='props'>
                    <template v-if='props.item.files && props.item.files.length'>
                        <div v-for='file in props.item.files' style='text-overflow: ellipsis;overflow: hidden;max-width: 150px;'>
                            <a style='white-space: nowrap;' href='javascript:void(0)'  v-on:click='download_file(file)'>{{ file.name }}</a>
                        </div>
                    </template>
                    <template v-else>&nbsp;</template>
                </template>
                <template slot='status' slot-scope='props'>
                    <statusDetails :item='props.item'/>
                </template>
            </ui-data-grid>
        </div>
    `
}
