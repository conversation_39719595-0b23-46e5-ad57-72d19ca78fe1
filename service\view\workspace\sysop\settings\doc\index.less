.settings-doc {
    p{
        margin: 0;
        padding: 5px 3px;
    }
    ul {
        padding-left: 40px;
        //padding-inline-start: 40px;
    }
    .info {
        margin-bottom: -1px;
        display: flex;
        background: #fff;
        align-items: center;
        justify-content: space-between;
        padding: 5px;
        border: 1px solid #ccc;
    }

    table {
        width: 100%;
        background: #fff;
        font-size: 14px;

        tbody {
            tr {
                td {
                    vertical-align: top;
                    border-right: 1px solid #ccc;
                    border-bottom: 1px solid #ccc;
                    padding: 5px;
                    min-width: 400px;

                    &.action {
                        
                        min-width: unset;
                    }

                    &:first-child {
                        min-width: unset;
                        border-left: 1px solid #ccc;
                    }

                    &.editable {
                        &.change {
                            background: #fbf3e3;
                        }

                        &.new {
                            background: #dff0d8;
                        }
                    }

                }

                &.title {
                    font-weight: bold;
                    text-transform: uppercase;
                    font-size: 13px;
                }

                &.error {
                    &:not(:last-child) {
                        td:first-child {
                            background: #fbe5e5;
                        }
                    }
                }
            }

            &:first-child {
                tr:first-child td {
                    border-top: 1px solid #ccc;
                }
            }

        }
    }
}