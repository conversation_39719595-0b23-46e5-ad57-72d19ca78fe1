import { DataSource, Query, ArrayStore, RemoteStore, RefStore } from '@iac/data'
import Static from './../../static'

/**
 *
ref_type_procedure
ref_bank_mfo
ref_soogu
ref_economic_acivity_type
ref_bank_account_type
ref_bad_suppliers
ref_unit

ref_tax_office
ref_country
ref_company_type
ref_transport
ref_incoterms
ref_transaction_nature
ref_customs_post
ref_customs_fee
ref_tnved
ref_product_movement_feature
ref_currency_full
ref_state_purchasers
ref_expenses
ref_single_supplier_directory
ref_insurance
ref_type_business
ref_type_deposit
ref_type_periodicity
ref_method_marks

 */


// Регистрация нестандартных статических списков
[
  { name: "contract_status_private", ref: "contract_status", source: "contract_ref", params: { scope: "private" } },
  { name: "contract_status_public", ref: "contract_status", source: "contract_ref", params: { scope: "public" } },
  { name: "contract_status", ref: "contract_status", source: "contract_ref" },

  { name: "exchange_contract_status_private", ref: "exchange_contract_status", source: "contract_ref", params: { scope: "private" } },
  { name: "exchange_contract_status_public", ref: "exchange_contract_status", source: "contract_ref", params: { scope: "public" } },
  { name: "exchange_contract_status", ref: "exchange_contract_status", source: "contract_ref" },

  { name: "online_shop_contract_status_private", ref: "online_shop_contract_status", source: "contract_ref", params: { scope: "private" } },
  { name: "online_shop_contract_status_public", ref: "online_shop_contract_status", source: "contract_ref", params: { scope: "public" } },
  { name: "online_shop_contract_status", ref: "online_shop_contract_status", source: "contract_ref" },

  { name: "custom_contract_status_private", ref: "custom_contract_status", source: "contract_ref", params: { scope: "private" } },
  { name: "custom_contract_status_public", ref: "custom_contract_status", source: "contract_ref", params: { scope: "public" } },
  { name: "custom_contract_status", ref: "custom_contract_status", source: "contract_ref" },

  { name: "auction_contract_status_private", ref: "auction_contract_status", source: "contract_ref", params: { scope: "private" } },
  { name: "auction_contract_status_public", ref: "auction_contract_status", source: "contract_ref", params: { scope: "public" } },
  { name: "auction_contract_status", ref: "auction_contract_status", source: "contract_ref" },

  { name: "burse_product_request_status_private", ref: "burse_product_request_status", source: "contract_ref", params: { scope: "private" } },
  
	{ name: "arbitration_complaint_status_private", ref: "arbitration_complaint_status", source: "contract_ref", params: { scope: "private" } },
  { name: "arbitration_complaint_status_public", ref: "arbitration_complaint_status", source: "contract_ref", params: { scope: "public" } },
  { name: "arbitration_complaint_status", ref: "arbitration_complaint_status", source: "contract_ref" },

  { name: "ref_ad_status_private", ref: "ref_status_ad", params: { scope: "private" } },
  { name: "ref_ad_status_public", ref: "ref_status_ad", params: { scope: "public" } },
  { name: "ref_ad_status", ref: "ref_status_ad" },
  { name: "status_ad", ref: "ref_status_ad" },

  { name: "ref_selection_status_private", ref: "ref_status_selection", params: { scope: "private" } },
  { name: "ref_selection_status_participant", ref: "ref_status_selection", params: { scope: "participant" } },
  { name: "ref_selection_status_public", ref: "ref_status_selection", params: { scope: "public" } },
  { name: "ref_selection_status", ref: "ref_status_selection" },
  { name: "status_selection", ref: "ref_status_selection" },
  { name: "status_multilot_selection", ref: "ref_status_selection" },

  { name: "ref_cargo_procedure_status_private", ref: "ref_status_cargo_procedure", params: { scope: "private" } },
  { name: "ref_cargo_procedure_status_participant", ref: "ref_status_cargo_procedure", params: { scope: "participant" } },
  { name: "ref_cargo_procedure_status_public", ref: "ref_status_cargo_procedure", params: { scope: "public" } },
  { name: "ref_cargo_procedure_status", ref: "ref_status_cargo_procedure" },
  { name: "status_cargo_procedure", ref: "ref_status_cargo_procedure" },


  { name: "ref_master_agreement_status_private", ref: "ref_status_master_agreement", params: { scope: "private" } },
  { name: "ref_master_agreement_status_participant", ref: "ref_status_master_agreement", params: { scope: "participant" } },
  { name: "ref_master_agreement_status_public", ref: "ref_status_master_agreement", params: { scope: "public" } },
  { name: "ref_master_agreement_status", ref: "ref_status_master_agreement" },
  { name: "status_master_agreement", ref: "ref_status_master_agreement" },


  { name: "ref_agreement_request_status_private", ref: "ref_status_agreement_request", params: { scope: "private" } },
  { name: "ref_agreement_request_status_participant", ref: "ref_status_agreement_request", params: { scope: "participant" } },
  { name: "ref_agreement_request_status_public", ref: "ref_status_agreement_request", params: { scope: "public" } },
  { name: "ref_agreement_request_status", ref: "ref_status_agreement_request" },
  { name: "status_agreement_request", ref: "ref_status_agreement_request" },


  { name: "status_request", ref: "ref_status_request" },
  { name: "ref_spaces_request_ad", ref: "ref_spaces_request_ad" },
  { name: "status_permissive_documents", ref: "permissive_documents", source: "company_ref", op: "statuses", type: "pd_goods" },


  { name: "private_status_reduction", ref: "ref_status_reduction", params: { scope: "private" } },
  { name: "participant_status_reduction", ref: "ref_status_reduction", params: { scope: "participant" } },
  { name: "public_status_reduction", ref: "ref_status_reduction", params: { scope: "public" } },
  { name: "status_reduction", ref: "ref_status_reduction" },

  { name: "contract_preconditions", ref: "__preconditions", source: "contract_ref" },
  { name: "contract_actions", ref: "__actions", source: "contract_ref" },

  { name: "shipment_docs_status", ref: "shipment_docs_status", source: "contract_ref" },
].forEach((el) => {

  if (typeof el == 'string') {
    el = {
      name: el
    }
  }
  let { name, source = "ref", ref = el.name, params, op = 'read' } = el;

  Static.reg(name, {
    method: source,
    params: {
      op: op,
      ref: ref,
      filters: {
        ...params
      }
    }
  })
});

// Регистрация источников для маленьких справочников которые кэшурются в памяти клиента на разных языках
// Добавляем если есть необходимость их использовать в фильтрах или в выпадашке
[
  "ref_unit",
  "ref_type_procedure",
  "ref_bank_account_type",

  "ref_ad_status_private", // Обратите внимание что это нестандартный справочник и имеет кастомное наименование (его перед кешированием необходимо зарегистрировать)
  "ref_ad_status_public",
  "ref_ad_status",
  "status_ad",

  "ref_selection_status_private", // Обратите внимание что это нестандартный справочник и имеет кастомное наименование (его перед кешированием необходимо зарегистрировать)
  "ref_selection_status_participant",
  "ref_selection_status_public",
  "ref_selection_status",
  "status_selection",
  "status_multilot_selection",
  
  "ref_cargo_procedure_status_private", // Обратите внимание что это нестандартный справочник и имеет кастомное наименование (его перед кешированием необходимо зарегистрировать)
  "ref_cargo_procedure_status_participant",
  "ref_cargo_procedure_status_public",
  "ref_cargo_procedure_status",
  "status_cargo_procedure",

  "ref_master_agreement_status_private", // Обратите внимание что это нестандартный справочник и имеет кастомное наименование (его перед кешированием необходимо зарегистрировать)
  "ref_master_agreement_status_participant",
  "ref_master_agreement_status_public",
  "ref_master_agreement_status",
  "status_master_agreement",

  "ref_agreement_request_status_private", // Обратите внимание что это нестандартный справочник и имеет кастомное наименование (его перед кешированием необходимо зарегистрировать)
  "ref_agreement_request_status_participant",
  "ref_agreement_request_status_public",
  "ref_agreement_request_status",
  "status_agreement_request",

  "status_request",
  "ref_spaces_request_ad",
  "status_permissive_documents",

  "private_status_reduction",
  "participant_status_reduction",
  "public_status_reduction",
  "status_reduction",

  "contract_status_private",
  "contract_status_public",
  {name: "contract_status",search: true},

  "exchange_contract_status_private",
  "exchange_contract_status_public",
  {name: "exchange_contract_status",search: true},

  "online_shop_contract_status_private",
  "online_shop_contract_status_public",
  {name: "online_shop_contract_status",search: true},

  "custom_contract_status_private",
  "custom_contract_status_public",
  {name: "custom_contract_status",search: true},

  "auction_contract_status_private",
  "auction_contract_status_public",
  {name: "auction_contract_status",search: true},  

  "burse_product_request_status_private",
  
	"arbitration_complaint_status_private",
	"arbitration_complaint_status_public",
  {name: "arbitration_complaint_status",search: true}, 

  "ref_billing_status",

  { name: "ref_transfer_status", key: "id" },

  { name: "contract_preconditions", key: "value" },
  { name: "contract_actions", key: "value" },
  {name: "ref_terminal_errors", key: "id"},

  "shipment_docs_status"

].forEach((el) => {

  if (typeof el == 'string') {
    el = {
      name: el
    }
  }
  let { name, key = 'code',search = false } = el;
  DataSource.reg(name, () => {
    return new DataSource({
      valueExp: key,
      search: search,
      store: new ArrayStore({
        key: key,
        data: Static.get(name),
      })
    })
  });
});


// Большие справочники и реестры
[
  // Пример { name: "contract_status_private", key: "code", show_code: false, ref: "ref_status_ad", params: { scope: "private" } },
  { name: "ref_bank_mfo_", ref: "ref_bank_mfo", key: ["id","name"] },
  { name: "ref_bank_mfo", key: "id" },
  "ref_soogu",
  { name: "ref_economic_acivity_type_", ref: "ref_economic_acivity_type", key: ["code","name"] },
  "ref_economic_acivity_type",
  "ref_company_type",
  "ref_bad_suppliers",

].forEach((el) => {
  if (typeof el == 'string') {
    el = {
      name: el
    }
  }

  let { name, key = 'code', show_code = true, search = true, ref = el.name, params } = el;
  DataSource.reg(name, () => {
    return new DataSource({
      valueExp: key,
      search: search,
      displayExp: (item) => {
        if (!item[key] || !show_code)
          return item.name;
        return `${item[key]}: ${item.name}`
      },
      store: new RefStore({
        key: key,
        ref: ref,
        params: params
      })
    })
  });
});

/*
Static.map("ref_type_procedure").then((result)=>{
  console.log("ref_type_procedure", result)
})*/
