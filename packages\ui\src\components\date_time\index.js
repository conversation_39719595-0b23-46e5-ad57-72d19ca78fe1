import DataSource from "../../../../data/src/data_source"
import ArrayStore from "../../../../data/src/array_store"
var dateDlgComponent = {
    props: ["title", "value"],
    template: `
    <div>
        <header>{{title}}</header>
        <main>
            <ui-control-group>
                
            </ui-control-group>
        </main>
        <footer>
            <ui-btn type='secondary' @click.native='Close()'>{{$t('cancel')}}</ui-btn>
            <ui-btn type='primary' v-on:click.native='set'>{{$t('set')}}</ui-btn>
        </footer>
    </div>
    `
}

var timeDlgComponent = {
    props: ["title", "value"],
    data: function () {
        return {
            hours: this.value ? this.value.split(":")[0] : "00",
            minutes: this.value ? this.value.split(":")[1] : "00",
            dataSourceH: new DataSource({
                store: new ArrayStore({
                    data: Array(24).fill(0).map((value, hour) => {
                        var _hour = ('00' + hour).slice(-2);
                        return { id: _hour, name: _hour }
                    })
                })
            }),
            dataSourceM: new DataSource({
                store: new ArrayStore({
                    data: Array(12).fill(0).map((value, index) => {
                        let minute = index * 5;
                        var _minute = ('00' + minute).slice(-2);
                        return { id: _minute, name: _minute }
                    })
                })
            })
        }
    },
    methods: {
        set() {
            this.value = `${this.hours.exp ? this.hours.exp.value : this.hours}:${this.minutes.exp ? this.minutes.exp.value : this.minutes}`;
            this.Close(this.value)
        }
    },
    template: `
        <div>
            <header>{{title}}</header>
            <main>
                <ui-control-group>
                    <ui-entity label='hours' v-model='hours' :dataSource='dataSourceH' />
                    <ui-entity label='minutes' v-model='minutes' :dataSource='dataSourceM' />
                </ui-control-group>
            </main>
            <footer>
                <ui-btn type='secondary' @click.native='Close()'>{{$t('cancel')}}</ui-btn>
                <ui-btn type='primary' v-on:click.native='set'>{{$t('set')}}</ui-btn>
            </footer>
        </div>
    `
}


var TimeDropdown = {
    template: `
        <div>

        </div>
    `
}

export var DateTime = {
    name: "ui-date-time",
    props: {
        type: {
            type: String,
        },
        value: {
            type: String,

        }
    },

    methods: {
        async onselect() {

            let dlgComponent = timeDlgComponent
            if (this.type == 'date') {
                dlgComponent = dateDlgComponent;

            }
            let value = await Vue.Dialog(dlgComponent).Modal({
                value: this.value,
                title: this.$attrs.label
            })

            if (value) {
                this.value = value;
                this.$emit("input", value)
            }
        }
    },
    computed: {
        opened() {
            if (this.value || this.value === 0) {
                return true
            }
            return false;
        }
    },
    components: {
        timeDropdown: TimeDropdown
    },
    template: `
        <ui-control 
            v-bind="$attrs"
            :opened="opened"
        >
            <div class='control' v-on:click='onselect'>
                <label>{{value}}</label>
            </div>
        </ui-control>
    `
}


export var CtrlDate = {
    name: "ui-ctrl-date",
    props: ["value","week"],
    data: function () {
        return {
            year: undefined,
            month: undefined,
            day: undefined,

            _year: undefined,
            _month: undefined,
            _day: undefined,

            show_year: false,
        }
    },
    computed: {
        offset(){
            let w = new Date(this.year, this.month, 1).getDay() - 1;
            if(w<0)
            w = 6;

            return w;// Math.max(0, new Date(this.year, this.month, 1).getDay() - 1);
        },
        start(){
            return new Date(this.year, this.month, 0).getDate() - this.offset + 1;
        },
        month_days(){
            return new Date(this.year, this.month + 1, 0).getDate();
        },
        weeks_count(){
            return Math.ceil((this.month_days+this.offset)/7);
        },
        weeks(){
            let start = this.start - 1;
            let i = 0;
            let month = this.month-1;
            let year = this.year;
            if(month < 0){
                month = 11;
                year--;
            }
            
            return Array(this.weeks_count).fill(0).map(()=>{
                let week_active = false;
                let items = Array(7).fill(0).map(()=>{
                    start++;
                    if(i == this.offset || i == this.month_days + this.offset){
                        start = 1;
                        month++;
                        
                        if(month == 12){
                            month = 0;
                            year++;
                        }
                    }
                    let active = year == this._year && this._month == month && this._day == start
                    //console.log("month",`${start}.${month}.${year}`,`${this._day}.${this._month}.${this._year}`);
                    if(active){
                        week_active = true
                    }
                   let current = year == this.current.year && this.current.month == month && this.current.day == start
                    i++;
                    return {
                        day: start,
                        month: month,
                        active: active,
                       current: current
                    };
                })
                return {
                    items: items,
                    active: week_active,
                };
            })
        },
        days() {
            var days = this.month_days
            let last_d = new Date(this.year, this.month, 0).getDate();


            return Array(this.offset).fill(0).map((item, index)=>{
                return -(last_d - this.offset + index +1)
            }).concat(Array(days).fill(0).map((item, index) => {
                return index + 1;
            }))

        },
        current() {
            let current = new Date();
            return {
                year: current.getFullYear(),
                month: current.getMonth(),
                day: current.getDate()
            }
        }
    },
    watch: {
        value: {
            immediate: true,
            async handler(value, oldVal) {
                if (value) {
                    value = value.split("-");
                    this._year = this.year = Number(value[0]);
                    this._month = this.month = Number(value[1]) - 1;
                    this._day = this.day = Number(value[2]);
                } else {
                    value = new Date();
                    this.year = value.getFullYear();
                    this.month = value.getMonth();
                    this.day = value.getDate();
                }
            }
        }
    },
    methods: {
        offsetYear(offset) {
            this.year = this.year + offset;
        },
        offsetMonth(offset) {
            let month = this.month + offset;
            let year = this.year;

            if (month > 11) {
                month = 0;
                year = year + 1;
            }
            if (month < 0) {
                month = 11;
                year = year - 1;
            }

            this.month = month;
            this.year = year;
        },
        set(day) {
            let month = ('00' + (this.month + 1)).slice(-2);
            day = ('00' + day).slice(-2);
            this.$emit("input", `${this.year}-${month}-${day}`)
        },
        set_day(item){
            
            let date = new Date(this.year,item.month,item.day)

            if(this.week){
                let w = date.getDay()
                if(w == 0)
                    w = 7;
                    date.setDate(date.getDate() - (w-1));
            }
            let month = ('00' + (date.getMonth()+1)).slice(-2);
            let day = ('00' + date.getDate()).slice(-2);

            this.$emit("input", `${date.getFullYear()}-${month}-${day}`)
        },
        day_classes(item) {
            let m_y = this.year == this._year && item.month == this._month
            let m__y = this.year == this.current.year && item.month == this.current.month
            return [
                {
                    "current": m__y && item.day == this.current.day,
                    "value": m_y && item.day == this._day,
                    "another": item.month != this.month
                }
            ]
        },
        classes(day) {

            let m_y = this.year == this._year && this.month == this._month
            let m__y = this.year == this.current.year && this.month == this.current.month
            return [
                {
                    "current": m__y && day == this.current.day,
                    "value": m_y && day == this._day,
                }
            ]
        },
        selectYear() {
            this.show_year = true
        },
        onWheel(event) {
            if (event.deltaY < 0) {
                this.year = this.year - 1
            } else if (event.deltaY > 0) {
                this.year = this.year + 1
            }
        },
        setYear(year) {
            if (year)
                this.year = year
            this.show_year = false
        }
    },
    template: `
        <div class='ui-ctrl-date' :key='value'>
            <div class='year'>
                <span v-on:click='()=>{offsetYear(-1)}'/>
                <span class='value' v-on:click='selectYear' v-on:wheel.prevent='onWheel'>
                    <div>{{year}}</div>
                    <div v-if='show_year' class='list' v-on-clickaway="()=>setYear()">
                        <div :class='i == 5 && "active"' v-for='i in 9' v-on:click.stop.prevent='()=>setYear(year+i-5)'>{{year+i-5}}</div>
                    </div>
                </span>
                <span v-on:click='()=>{offsetYear(1)}'/>
            </div>
            <div class='month'>
                <span v-on:click='()=>{offsetMonth(-1)}'/>
                <span class='value'>{{$t("month_"+month)}}</span>
                <span v-on:click='()=>{offsetMonth(1)}'/>
            </div>
            <div class="weeks">
                <span>{{$t('Mon')}}</span>
                <span>{{$t('Tue')}}</span>
                <span>{{$t('Wed')}}</span>
                <span>{{$t('Thu')}}</span>
                <span>{{$t('Fri')}}</span>
                <span>{{$t('Sat')}}</span>
                <span>{{$t('Sun')}}</span>
            </div>   
            <div class='days' :class='week ? "select_week" : ""'>
                <div class='week' :class='week.active ? "active" : ""' v-for='week in weeks' >
                    <span v-on:click='set_day(item)'  v-bind:class="day_classes(item)" :key='item.month*item.day'  v-for='item in week.items' style='flex: 0 0 14.28%;    text-align: center;    display: flex;'>
                        <span>{{item.day}}</span>
                    </span>
                </div>

                <span v-if='0' v-for='day in days'  v-bind:class="classes(day)">
                    <span v-on:click='()=>{set(day)}'>{{day || ''}}</span>
                </span>
            </div>             
        </div>
    `
}

export var InputDate = {
    name: "ui-input-date",
    props: ["value", "disabled","week"],
    data: function () {
        return {
            control: false,
            year: undefined,
            month: undefined,
            day: undefined,

            dropdownTop: false,
            dropdownRight: false
        }
    },
    watch: {
        value: {
            immediate: true,
            async handler(value, oldVal) {
                if(value == oldVal){
                    return;
                }
                if (value) {
                    
                    if(this.week){
                        let date = new Date(value)
                        let w = date.getDay()
                        if(w == 0)
                            w = 7;
                        if(w!=1){
                            date.setDate(date.getDate() - (w-1));
                            let month = ('00' + (date.getMonth()+1)).slice(-2);
                            let day = ('00' + date.getDate()).slice(-2);
                            return this.$emit("input", `${date.getFullYear()}-${month}-${day}`);
                        }
                    }

                    value = value.split("-");
                    this.year = Number(value[0]);
                    this.month = Number(value[1]);
                    this.day = Number(value[2]);

                }
            }
        },
        control: {
            immediate: true,
            async handler(val, oldVal) {
                if (val == true) {
                    this.reposition();
                    window.addEventListener('scroll', this.reposition);
                } else {
                    window.removeEventListener('scroll', this.reposition)
                }
            }
        },
    },
    beforeDestroy() {
        window.removeEventListener('scroll', this.reposition)
    },
    computed: {
        display() {
            if (this.value && !this.week) {
                return `${this.day} ${this.$t("month_c_" + (this.month - 1))} ${this.year}`
            }else if (this.value && this.week) {
                
                let date_from = new Date(this.year,this.month-1,this.day)
                let date_to = new Date(this.year,this.month-1,this.day+6)              

                let day_from = ('00' + date_from.getDate()).slice(-2);
                let month_from = ('00' + (date_from.getMonth()+1)).slice(-2);

                let day_to = ('00' + date_to.getDate()).slice(-2);
                let month_to = ('00' + (date_to.getMonth()+1)).slice(-2);

                return `${day_from}.${month_from}.${date_from.getFullYear() % 100} - ${day_to}.${month_to}.${date_to.getFullYear() }`
            }
            return this.week ? "__.__ - __.__.____" : "__ ______ ____";
        },
        listeners: function () {
            var vm = this
            return Object.assign({},
                this.$listeners,
                {
                    input: function (event) {
                        vm.value = event
                        vm.control = false;
                        vm.$emit('input', event)
                    },
                }
            )
        },
        classes() {
            return [
                (() => {

                })(),
                {
                    "dropdown-top": this.dropdownTop,
                    "dropdown-right": this.dropdownRight,
                }
            ]
        }
    },

    methods: {
        input(value) {
            this.value = value;
            this.control = false;
            this.$emit("input", value)
        },
        reposition() {

            const elClientRect = this.$el.getBoundingClientRect();
            let clCenter = {
                x: elClientRect.left + elClientRect.width / 2,
                y: elClientRect.top + elClientRect.height / 2
            }
            let htmlSize = {
                width: document.documentElement.clientWidth,
                height: document.documentElement.clientHeight,
            }

            this.dropdownRight = (clCenter.x > htmlSize.width / 2)
            this.dropdownTop = (clCenter.y > htmlSize.height / 2)
        },
        show_control(value) {
            this.control = value;
            this.$emit('dropdown', value)
        }
    },
    template: `
        <div class='ui-input-date' v-bind:class="classes">
            <span v-if='disabled' class='value'>{{display}}</span>
            <span v-else class='value' v-on:click='()=>show_control(true)'>{{display}}</span>
            <div v-if='control' class='control' v-on-clickaway="()=>show_control(false)"><ui-ctrl-date :week='week'  v-bind:value='value' v-on="listeners"  /></div>
        </div>
    `
}

export var InputTime = {
    name: "ui-input-time",
    props: {
        value: {

        },
        disabled: {
            type: Boolean,
            default: false
        },
        range: {
            type: Array,
        }
    },
    data: function () {
        return {
            hours_control: false,
            minutes_control: false,
            hour: undefined,
            minute: undefined,

            dropdownTop: false,
            dropdownRight: false,
        }
    },
    watch: {
        value: {
            immediate: true,
            async handler(value, oldVal) {
                if (value) {
                    value = value.split(":");
                    this.hour = ('00' + value[0]).slice(-2);
                    this.minute = ('00' + value[1]).slice(-2);
                }else{
                    this.hour = undefined;
                    this.minute = undefined;
                }
            }
        },
        control: {
            immediate: true,
            async handler(val, oldVal) {
                if (val == true) {
                    this.reposition();
                    window.addEventListener('scroll', this.reposition);
                } else {
                    window.removeEventListener('scroll', this.reposition)
                }
            }
        }
    },
    beforeDestroy() {
        window.removeEventListener('scroll', this.reposition)
    },
    computed: {
        control() {
            if (this.hours_control || this.minutes_control)
                return true;
        },
        min() {
            return (this.range && this.range[0]) ? this.range[0].split(":").map((item) => {
                return Number.parseInt(item || 0)
            }) : [0, 0]
        },
        max() {
            return (this.range && this.range[1]) ? this.range[1].split(":").map((item) => {
                return Number.parseInt(item || 0)
            }) : [23, 59]
        },
        hours() {
            let h_min = this.min[0]
            let h_max = this.max[0]

            return Array(h_max - h_min + 1).fill(0).map((value, hour) => {
                var _hour = ('00' + (hour + h_min)).slice(-2);
                return _hour
            })
        },
        minutes() {

            let m_min = 0;
            let m_max = 59;

            if (this.hour == this.min[0]) {
                m_min = this.min[1]
            }

            if (this.hour == this.max[0]) {
                m_max = this.max[1]
            }

            return Array(Number.parseInt((m_max - m_min + 5) / 5)).fill(0).map((value, minute) => {
                var _minute = ('00' + (m_min + minute * 5)).slice(-2);
                return _minute
            })
        },
        classes() {
            return [
                (() => {

                })(),
                {
                    "dropdown-top": this.dropdownTop,
                    "dropdown-right": this.dropdownRight,
                }
            ]
        }
    },
    methods: {
        set_hour(hour) {
            this.hours_control = false;
            this.hour = hour
            if (!this.minute)
                this.minute = "00"

            if (this.hour == this.min[0] && this.minute < this.min[1]) {
                this.minute = this.min[1]
            }

            if (this.hour == this.max[0] && this.minute > this.max[1]) {
                this.minute = this.max[1]
            }

            this.$emit("input", `${this.hour}:${this.minute}:00`)
        },
        set_minute(minute) {
            this.minutes_control = false;
            this.minute = minute
            if (!this.hour)
                this.hour = "00"

            this.$emit("input", `${this.hour}:${this.minute}:00`)
        },
        reposition() {

            const elClientRect = this.$el.getBoundingClientRect();
            let clCenter = {
                x: elClientRect.left + elClientRect.width / 2,
                y: elClientRect.top + elClientRect.height / 2
            }
            let htmlSize = {
                width: document.documentElement.clientWidth,
                height: document.documentElement.clientHeight,
            }

            this.dropdownRight = (clCenter.x > htmlSize.width / 2)
            this.dropdownTop = (clCenter.y > htmlSize.height / 2)
        },
        show_hours(value) {
            this.hours_control = value;
            this.$emit('dropdown', value)
        },
        show_minutes(value) {
            this.minutes_control = value;
            this.$emit('dropdown', value)

        }
    },
    template: `
        <div class='ui-input-time' v-bind:class="classes">
            <div class='value hour'>
                <span v-if='disabled'>{{hour || "__"}}</span>
                <span v-else v-on:click='()=>show_hours(true)'>{{hour || "__"}}</span>
                
                <div v-if='hours_control' v-on-clickaway="()=>show_hours(false)" class='control'>
                    <div v-for='hh in hours' v-on:click='()=>{set_hour(hh)}'>{{hh}}</div>
                </div>
            </div>:<div class='value minute'>
                <span v-if='disabled'>{{minute || "__"}}</span>
                <span v-else v-on:click='()=>show_minutes(true)'>{{minute || "__"}}</span>
                <div v-if='minutes_control' v-on-clickaway="()=>show_minutes(false)" class='control'>
                    <div v-for='mm in minutes' v-on:click='()=>{set_minute(mm)}'>{{mm}}</div>
                </div>
            </div>
        </div>
    `
}

export var UiDate = {
    name: "ui-date",
    props: {
        value: {

        },
        label: {

        },
        type: {
            type: String
        },
        disabled: {
            type: Boolean
        },
        readonly: {
            type: Boolean
        },
        required: Boolean,
        has_del: Boolean,
        range: {
            type: Array
        },
        actions: {},
        prefix: {},
        suffix: {}
    },
    data: function () {
        return {
            dropdown: false,
            date: undefined,
            time: undefined
        }
    },
    watch: {
        value: {
            immediate: true,
            async handler(value, oldVal) {
                if (value) {

                    let index = value.indexOf('T');

                    if (index >= 0) {
                        let date = new Date(value);

                        let hours = ('00' + (date.getHours())).slice(-2);
                        let minutes = ('00' + date.getMinutes()).slice(-2);
                        this.time = `${hours}:${minutes}:00`;

                        let year = date.getFullYear();
                        let month = ('00' + (date.getMonth() + 1)).slice(-2);
                        let day = ('00' + date.getDate()).slice(-2);
                        this.date = `${year}-${month}-${day}`;

                        if(this.type == 'week' || this.type == 'date'){
                            this.emit();
                        }

                    } else {
                        value = value.split(" ");
                        switch (this.type) {
                            case "time":
                                this.time = value[0]
                                break;
                            case "date":
                                this.date = value[0]
                                break;
                            default:
                                this.date = value[0]
                                this.time = value[1]
                                break;
                        }
                    }
                } else {
                    this.date = undefined;
                    this.time = undefined;
                }
            }
        }
    },
    computed: {
        classes() {
            return [
                { non_value: !this.opened }
            ]
        },
        opened() {
            if (this.value) {
                return true
            }
            return false;
        },
        style() {
            if (this.dropdown)
                return 'z-index: 2'
        }
    },
    methods: {
        action(event) {
            if (event == 'clear') {
                this.value = undefined;
                this.time = undefined;
                this.date = undefined;
                this.dropdown = false;
                this.$emit('input', undefined)
            }
        },
        emit() {


            switch (this.type) {
                case "time":
                    this.value = this.time;
                    this.$emit("input", this.time)
                    break;
                case "week":
                case "date":
                    this.value = this.date;
                    this.$emit("input", this.date)
                    break;
                default:
                    this.value = `${this.date} ${this.time}`;
                    this.$emit("input", `${this.date} ${this.time}`)
                    break;
            }
        },
        set_date(date) {
            this.date = date;
            if (!this.time) {
                this.time = "00:00:00"
            }
            this.dropdown = false
            this.emit();
        },
        set_time(time) {
            this.time = time
            if (!this.date) {
                let date = new Date();
                let year = date.getFullYear();
                let month = ('00' + (date.getMonth() + 1)).slice(-2);
                let day = ('00' + date.getDate()).slice(-2);
                this.date = `${year}-${month}-${day}`
            }
            this.dropdown = false
            this.emit();
        }
    },
    template: `
        <ui-control class='ui-date'
            v-bind="$attrs"
            :opened="true"
            :disabled='disabled'
            :readonly='readonly'
            :required='required'
            :has_del='has_del'
            v-bind:class="classes"
            v-on:action='action'
            :style='style'
            :prefix='prefix'
            :suffix='suffix'
        >
            <div v-if='prefix' slot='prefix'>{{prefix}}</div>
            <div v-if='suffix' slot='suffix'>{{suffix}}</div>
            <div class='control'>
                <ui-input-date :week='type == "week"' :disabled='disabled || readonly' v-if='type != "time"' v-bind:value='date' v-on:input="set_date" v-on:dropdown='(value)=>dropdown=value' />
                <ui-input-time :disabled='disabled || readonly' v-if='type != "date" && type != "week"' v-bind:value='time' :range='range' v-on:input="set_time" v-on:dropdown='(value)=>dropdown=value' />
            </div>
        </ui-control>        
    `
}
