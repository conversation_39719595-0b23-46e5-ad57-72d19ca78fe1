import { Http } from '@iac/core';
import { DataSource, RemoteStore, Query } from '@iac/data';

const solveDialog = Vue.Dialog({
  props: ['text', 'id'],
  data() {
    return {
      solve_text: this.text,
    };
  },
  template: `
    <div>
      <main>
        <ui-text v-model='solve_text'></ui-text>
      </main>
      <footer>
        <ui-btn type="secondary" @click.native="Close()">{{ $t('close') }}</ui-btn>
        <ui-btn type="primary" @click.native="save">{{ $t('send') }}</ui-btn>
      </footer>
    </div>
  `,
  methods: {
    async save() {
      await this.wait(async () => {
        const { id, solve_text } = this;
        const { data, error } = await Http.api.rpc('solve_error', {
          id, solve_text,
        });
        if (error === undefined) {
            this.Close(true);
            if (data.message) {
              await Vue.Dialog.MessageBox.Success(data.message);
            }
        } else {
          await Vue.Dialog.MessageBox.Error(error);
        }
      });
    },
  },
});

export default {
  data() {
    return {
      errors: new DataSource({
        query: new Query({
          status: {
            type: 'enum',
            dataSource: ['new', 'done'],
            value: [
              'new',
              'done',
            ],
          },
        }),
        displayExp: 'selected_text',
        store: new RemoteStore({
          method: 'get_errors',
          icon: 'ok',
          context: (context) => {
            const { id, comment, solve_text } = context
            if (context.url.slice(0, 4) === 'http') {
              context.url = `/${context.url.split('/').slice(3).join('/')}`;
            }
            const text = solve_text || comment;
            context.actions = [
              {
                label: 'solve',
                icon: 'ok',
                hidden: context.status === 'done',
                handler: async () => {       
                  await solveDialog.Modal({
                    id,
                    text,
                  });
                  this.errors.reload();
                },
              },
            ];
            return context;
          },
        }),
      }),
    };
  },
  template: `
    <iac-access :access='$policy.system_errors'>
      <iac-section type='header'>
        <h1>{{ $t('nav.errors') }}</h1>
      </iac-section>
      <iac-section>
        <ui-data-tile :dataSource='errors' >
          <div class='tile widget-error card-stat' slot='template' slot-scope='props'>
            <div class='fill'>
              <div>{{ props.item.selected_text }}</div>
              <div>{{ props.item.comment }}</div>
              <div>{{ props.item.solve_text }}</div>
            </div>
            <div class='props'>
              <div>
                <label>{{ $t('status') }}:</label>
                <span>{{ $t(props.item.status) }}</span>
              </div>
              <div>
                <label>{{ $t('url') }}:</label>
                <router-link :to='props.item.url'>{{ props.item.url }}</router-link>
              </div>
            </div>
          </div>
        </ui-data-tile>
      </iac-section>
    </iac-access>
  `,
};
