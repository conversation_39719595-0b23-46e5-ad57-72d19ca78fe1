.iac--ebp-search-tree {

  &-node {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: flex-start;
  }

  &-category {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: flex-start;
    margin-top: 2px;

    >div:first-child {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      background-color: #eaeaea;
      padding: 5px;
      border: 1px solid #dadada;
      border-radius: 4px;
      cursor: pointer;

      line-height: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-wrap: break-word;

      font-size: 12px;
      flex: 0 0 auto;
      font-weight: bold;

      >span {
        margin-left: 4px;

        &.showable {
          font-weight: bold;
          transition: transform 0.5s ease-in-out;
          color: #00859f;

          &.show {
            transform: rotate(90deg);
          }
        }

        &:last-child {
          margin-left: 4px;
          text-overflow: ellipsis;
          white-space: nowrap;
          word-wrap: break-word;
          overflow: hidden;
        }
      }
    }

    >div:nth-child(2) {
      padding-left: 10px;
    }
  }

  &-product {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    justify-content: flex-start;
    background-color: #eaeaea;
    padding: 5px;
    border: 1px solid #dadada;
    border-radius: 4px;
    margin-top: 2px;
    cursor: pointer;

    line-height: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: break-word;
    flex: 0 0 auto;
    font-size: 11px;

    >span {
      margin-left: 4px;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-wrap: break-word;
      overflow: hidden;
    }
  }
}