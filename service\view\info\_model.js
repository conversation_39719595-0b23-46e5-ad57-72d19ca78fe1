import { Http } from '@iac/core'
import { Entity, DataSource, ArrayStore, RemoteStore } from '@iac/data'

export default class Page  extends Entity {
    constructor(context = {}) {
        context.data = context.data || {};
        super(context)
        this.image = context.image || context.data.image || undefined;
        this.title = context.title || context.data.title;
        this.description =context.description || context.data.description;
        this.content = context.content || context.data.content;
    }
    props() {
        return {
            image: {
                type: 'string'
            },
            title: {
                type: 'text'
            },
            description: {
                type: 'text'
            },
            content: {
                label: 'page_content',
                type: 'html',
            },
        }
    }

    async save(){
        const { error, data } = await Http.api.rpc("set_page", {
            id: this.id,
            data:{
                image: this.image,
                title: this.title,
                description: this.description,
                content: this.content,
            }
        });

        if (error) {
            if(!this.setError(error)){
               await Vue.Dialog.MessageBox.Error(error);
            }
        }

        return { error, data };
    }

    static async get(id) {
        let { error, data } = await Http.api.rpc("get_page", { id: id });
        return {
            error,
            data: !error && new Page(data)
        }
    }
}