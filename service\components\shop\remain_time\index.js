export default {
  props: ['value'],
  data() {
    return {
      remainTime: undefined,
    };
  },
  mounted() {
    this.remainTime = this.value;
    this.timer = setInterval(() => {
      if (this.remainTime > 0) {
        this.remainTime -= 1;
        return;
      }
      clearInterval(this.timer);
    }, 1000);
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  computed: {
    minutes() {
      return Math.floor(this.remainTime / 60);
    },
    hours() {
      return Math.floor(this.minutes / 60);
    },
    days() {
      return Math.floor(this.hours / 24);
    },
  },
  template: `
    <div class='remain-time'>
      <div class='remain-time__item'>
        <span class='subtitle subtitle--small mb-n4 d-block'>{{ days }}</span>
        {{ $t('remain_time.days') }}
      </div>
      <div class='remain-time__item'>
        <span class='subtitle subtitle--small mb-n4 d-block'>{{ hours % 24 }}</span>
        {{ $t('remain_time.hours') }}
      </div>
      <div class='remain-time__item'>
        <span class='subtitle subtitle--small mb-n4 d-block'>{{ minutes % 60 }}</span>
        {{ $t('minute') }}
      </div>
      <div class='remain-time__item'>
        <span class='subtitle subtitle--small mb-n4 d-block'>{{ remainTime % 60 }}</span>
        {{ $t('second') }}
      </div>
    </div>
  `,
};
