import {Config,Context} from '@iac/kernel'

export default {
    props: ["model"],
    data: function () {
        return {
            error: undefined,
            reports: undefined,
            User: Context.User,
        }
    },
    async mounted() {
        let { error, data } = await this.model.report.get_available_reports();
        if (!error) {
            this.reports = Object.keys(data).map((key) => {
                return {
                    label: key,
                    items: data[key]
                };
            })
        }
        this.error = error;

    },
    methods: {
        async report(report, render_type) {
            let { error, data } = await this.model.report.generate_report({
                report_type: report.report_type,
                response_type: "link",
                template_id: report.template_id,
                render_type: render_type,
                params: {
                    a_id: this.model.id,
                    ...report
                }
            });
            if (!error) {
                window.location = `${Config.api_server}/${data}&token=${this.User.access_token}`;
            }
        }
    },
    template: `
        <ui-layout-group class='iac-reports'>
            <ui-layout-group v-key='group.label' :label='group.label' v-for='group in reports'>
                <div class='report-item' v-key='"item_"+key' v-for='(item,key) in group.items'>
                    <div class='content'>
                        <div class='name'>{{item.name}}</div>
                        <ui-btn-group class='types' v-if='item.render_types.length > 0'>
                            <ui-btn v-if='item.render_types.length > 1' type='primary' v-key='type+"_item_"+key' v-for='type in item.render_types' @click.native='report(item, type)'>
                                {{type}}
                            </ui-btn>
                            <ui-btn v-else type='primary' @click.native='report(item, item.render_types[0].type)'>
                                {{$t('download')}}
                            </ui-btn>

                        </ui-btn-group>
                    </div>
                </div>
            </ui-layout-group>
        </ui-layout-group>
    `
}
