import Event from './../../core/src/event'
import Entity from './entity';

export default class Query extends Entity {
    // События
    @Event onUpdate() {
        this.validate();
    };

    constructor(options, ext) {
        let props = options

        /*if(props){
            Object.keys(props).forEach((key)=>{
                let field = props[key]
                if(field && (field.type == 'enum' || field.type == 'enum-tree')){
                    field.compact == true;
                    if(field.compact == false){
                        field.compact = undefined;
                    }else{
                        field.compact = true;
                    }
                }
            })
        }*/


        super({ props }, ext);
        this.search_query = undefined;


    }

    props(){
        return {
            queryText: {
                label: "!search",
                has_del: true,
                icon: "search",
                hidden: true
            },
            order_by: {
                type: "entity",
                has_del: true,
                hidden: true,
                label: "!sort",
                icon: 'sort',
                attr: {
                    style: "min-width: 150px"
                }
            }
        }
    }

    search(query) {
        if (this.search_query == query)
            return;
        this.search_query = query
        this.onUpdate();
    }

    set(props, update = true) {
        this.lock = true;
        for (let prop in props) {
            let property = this.properties[prop];
            /*if (!property) {
                property = this.addProperty({
                    name: prop,
                });
            }*/
            if (property) {

                if ((property.type == 'enum' || property.type == 'enum-tree' ||property.multiple) && !Array.isArray(props[prop])) {
                    if (props[prop] == '' || props[prop] == undefined) {
                        props[prop] = undefined
                    } else {
                        props[prop] = [props[prop]]
                    }
                }

                property.value = props[prop];
            }
        }
        this.lock = false;
        if (update)
            this.onUpdate();
        else
            this.validate()
    }

    getLocal(all = true) {
        let local = this.fields.filter((field)=>{
            if(all)
                return true;

            if(typeof field.sync == 'function')
                return field.sync();
            return field.sync;
        }).reduce((pre, curr, index) => {
            if (curr.meta && curr.meta.send == false)
                return pre;
            pre[curr.name] = (curr.value && curr.value.exp) ? curr.value.exp.value : curr.value

            if (typeof pre[curr.name] == 'function')
                pre[curr.name] = pre[curr.name]();

            if ((curr.type == 'enum' || curr.type == 'enum-tree' ||curr.multiple) && pre[curr.name] && Array.isArray(pre[curr.name]) && pre[curr.name].length <= 0) {
                pre[curr.name] = undefined;
            }

            if (curr.multiple && Array.isArray(pre[curr.name])) {
                pre[curr.name] = pre[curr.name].map((value) => {
                    return (value.exp) ? value.exp.value : value
                })
            }

            return pre
        }, {})
        return local;
    }

    get local() {
        return this.getLocal();
    }
    get address() {
        
        return this.getLocal(false);
    }
}