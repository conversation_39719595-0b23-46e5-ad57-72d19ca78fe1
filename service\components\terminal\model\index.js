import { Guid } from '@iac/core'
import { Entity } from '@iac/data'


import Space from './space'


let default_items = {
    "1:100.1:70.1:51": [
        { tile: "bkl_exchange_contract", group: 1, params: {} },
        { tile: "exchange_contract", group: 1, params: {} }
    ],
    "1:100.1:70.2:28": [
        { tile: "order_book", group: 1, params: {} },
        { tile: "proposal", group: 1, params: {} },
        { tile: "history", group: 1, params: {} }
    ],
    "1:100.1:70.3:19.1:40": [
        { tile: "timer", params: {} }
    ],
    "1:100.1:70.3:19.2:60": [
        { tile: "settings", params: {} }
    ],
    "1:100.2:30": [
        { tile: "proposal", params: {} }
    ],
}



export default class TerminalModel {
    constructor(context) {
        this.name = context.name;
        this.theme = context.theme;
        this.active_item = context.active_item

        this.items = context.items.map((space) => {
            return new Space({
                manager: this,
                visible: space.visible,
                label: space.label,
                groups: space.groups,
                items: space.items
            })
        })
    }

    get visible_items() {
        return this.items.filter((space) => {
            return space.visible
        })
    }

    async toggle_visible(space_index) {

        let space = this.items[space_index];
        let active_space = this.visible_items[this.active_item];

        let show = !space.visible;
        if (!show && this.visible_items.length <= 1)
            return;

        space.visible = show;
        if (active_space == space) {
            let active_item = Number(this.active_item);
            let las_visible = this.visible_items.length - 1;

            active_space = this.visible_items[Math.min(active_item, las_visible)];
        }
        active_space._active = true


        this.active_item = this.visible_items.reduce((acc, item, index) => {
            if (item._active != undefined) {
                acc = index
            }
            return acc
        }, 0)

        this.items.forEach((item) => {
            item._active = undefined
        })
        this.save();
    }

    add() {
        let space = new Space({
            groups: {
                0: {id: 0}
            },
            manager: this,
            label: "Space",
            visible: true,
            items: default_items
        })
        this.items.push(space)
        this.active_item = this.visible_items.length - 1;
        this.save();
    }
    delete(index) {
        if (this.visible_items && this.visible_items.length <= 1)
            return;
        let delete_space = this.visible_items[index];
        let delete_index = undefined;
        this.items.forEach((space, i) => {
            if (space == delete_space) {
                delete_index = i
            }
        });

        this.items.splice(delete_index, 1);
        if (this.active_item > index) {
            this.active_item--;
        } else if (this.active_item == index) {
            this.active_item = Math.max(Math.min(this.active_item, this.visible_items.length - 1), 0)
        }
        this.save();
    }
    setActive(index) {
        this.active_item = index
    }

    save() {
        let params = {
            theme: this.theme,
            items: this.items.map((item) => {
                return item.getStruct()
            })
        }
        localStorage.setItem(`terminal_${this.name}`, JSON.stringify(params))
    }

    static get(name = "terminal") {

        let terminal_struct = localStorage.getItem(`terminal_${name}`)
        try {
            terminal_struct = JSON.parse(terminal_struct);
        } catch (e) {
            terminal_struct = undefined;
        }



        if (!terminal_struct) {
            terminal_struct = {
                items: [
                    {
                        label: "Space_1",
                        items: default_items
                    }
                ]
            }
        }

        return new TerminalModel({
            name: name,
            active_item: undefined,
            ...terminal_struct,
        })
    }
}

