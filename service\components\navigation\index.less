
@import url('./explorer/index.less');

.iac-service-nav-mask{
    background-color:   rgba(0,0,0,.5);
    bottom: 0;
    left: 0;
    opacity: 0;
    position: fixed;
    right: 0;
    top: 0;
    transition: opacity .2s linear;
    visibility: hidden;
    z-index: 0;
}

.iac-service-nav{
    background:#fff;
    border-right: 1px solid #F3F3F3;
    bottom: 0;
    font-size: @14px;
    padding: 112px 0 0;
    position: fixed;
    top: 0;
    transform: translateX(-100%);
    transition: transform .15s cubic-bezier(.4,0,.2,1);
    width: 280px;
    z-index: 202;
    //box-shadow: 1px 0 20px 1px rgba(0,0,0,0.1);

    display: flex;
    flex-direction: column;
    
    overflow: hidden;

    &:hover{
        overflow-y: auto;
    }


    >.brand-lockup{
        border-bottom: 1px solid #F3F3F3;
        border-top: 1px solid #F3F3F3;
        position: absolute;
        top: 0;
        width: 100%;

        align-items: center;
        display: flex;
        height: 80px;
        margin-top: 32px;
        margin-right: auto;
        padding-left: 24px;

        .logo{
          filter: grayscale(100%);
        }

        .info .title{
            color: #666;
            font-size: 16px;
            
        }
        .info .desc{
            display: none;
        }
    }

    >.primary{
        flex: 1 0 auto;
        //padding: 20px 0;
        width: 280px;
    }
    >.secondary{
        flex: 0 0 auto;
        min-height: 100px;
        border-top: 1px solid #eee;
    }
}

.iac-container{
    padding: 0 38px;
    margin: 0 auto;
    width: 100%;
    max-width: 1300px;
    position: relative;
}

.left-nav {
  font-size: @14px;
  line-height: 1.43;

  &__list {
    list-style: none;
    margin: 0;
    padding: 0 !important;
  }

  &__item {
    border-bottom: 1px solid @light-gray;
    overflow: hidden;
  }

  &__link {
    //display: block;
    padding: 20px 20px 20px 36px;
    color: @black;
    text-decoration: none;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

    display: flex;
    justify-content: space-between;

    .left-nav__link-content{
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &.disabled {
      opacity: 0.3;
      color: #f00;
    }

    &:hover {
      color: @brand-primary;
    }
  }
  &.compact{
    .left-nav__link {
      padding-left: 20px;
      padding-right: 20px;
      .left-nav__link-content{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.nav-sup {
  font-size: 1em;
  color: @red;
}

.dropdown {
  position: relative;

  &__link {
    position: relative;
    cursor: pointer;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      right: 25px;
      margin-top: -3px;
      width: 10px;
      height: 6px;
      background-image: url("data:image/svg+xml,%3Csvg width='10' height='7' viewBox='0 0 10 7' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1.175 0.158447L5 3.97511L8.825 0.158447L10 1.33345L5 6.33345L0 1.33345L1.175 0.158447Z' fill='%23009ab8'/%3E%3C/svg%3E");
      opacity: 0.6;
      transition: transform .3s ease;
      filter: grayscale(100%) brightness(0.2);
    }

    &:hover {

      &::after {
        filter: none;
      }
    }

    &--arrow-static {
      &::after {
        display: inline-block;
        position: static;
        margin-left: 5px;
        vertical-align: middle;
      }
    }
  }

  &--opened {

    .dropdown__link {

      &::before {
        opacity: 1;
        transform: rotateZ(180deg);
      }
    }

    .dropdown-menu {
      display: block;
    }
  }
}

.dropdown-menu {
  display: none;
  position: relative;
  margin: -12px 0 15px 0;
  padding: 0;
  list-style: none;

  > li {
    border: none;
    > a {
      padding: 4px 25px 4px 36px;
      color: @gray;
      text-decoration: none;
      display: flex;
      justify-content: space-between;

      &:hover,
      &.router-link-active {
        color: @primary-link;
      }
    }
  }

  &--right {
    position: absolute;
    top: 100%;
    right: 0;
    margin: 0;
    padding: 10px 5px;
    width: 100%;
    min-width: 160px;
    background-color: @white;
    border-radius: 4px;

    > li {

      > a {
        padding-left: 4px;
      }
    }
  }
}

@media screen and (max-width:1320px) {
    .side_bar_show.side_bar{
        overflow: hidden;
    }
    .side_bar_show.side_bar .iac-service-nav-mask{
        opacity: 1;
        visibility: visible;
        z-index: 201;
    }  
}

@media screen and (min-width:1321px) {
    .iac-service-nav {
        transform:translateX(0);
        z-index:202
    }
    .side_bar .iac-container {
        max-width:1620px;
        padding-left:320px
    }
}

.side_bar_show.side_bar .iac-service-nav{
    transform: translateX(0);
    transition-duration: 235ms;
}


nav.tab_menu {
  min-height: 100%;
  display: flex;
  >.tab{
    flex: 0 0 auto;
    background: #333;
    >.item{
      width: 48px;
      height: 48px;

      font-size: 24px;
      align-items: center;
      justify-content: center;
      display: flex;
      color: #fff;
      opacity: 0.5;
      &.active{
        opacity: 1;
      }
    }

  }
  >.menu{
    flex: 1 1 100%;
    ul{
      padding: 0;
      margin: 0;
      list-style: none;
      li{
        padding: 0 8px;
        border-bottom: 1px solid #eee;
        line-height: 48px;
        
      }
    }
  }
}