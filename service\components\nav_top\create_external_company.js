import { ecp,<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@iac/kernel'
import { Http, Guid, Language } from '@iac/core'
import { ModelProvider } from '@iac/core'

class CreateCompanyModel extends ModelProvider['company_model']{
    constructor(context){
        super(context)
    }

    props(){
        return {
            inn: {
                group: 'general',
                required: true,
                label: "-inn"
            }
        }
    }
}

let Dialog = Vue.Dialog({
    props: ["change_user"],
    data: function () {
        return {
            external_company: false,
            request_id: (Guid.newGuid()).toString(),
            channel: undefined,
            companyModel: undefined,
            ecp: {
                type: 'ecp',
                label: 'reg_company.ecp',
                has_del: true,
                required: true,
                value: undefined,
                description: Language.t('reg_company.ecp_description'),
                attr: {
                    createPkcs7: false
                },
                readonly: ()=>{
                    return this.external_company
                },
                hidden: !ecp.provider
            },
            agree_to_offer: {
                type: 'bool',
                value: false,
                label: [`${Settings._country}.agree_to_offer`,'agree_to_offer']
            }
        }
    },
    mounted: function () {
        this.channel = Http.api.socket.join(`event:${this.request_id}`, (channel) => {

        });
    },
    destroyed: function () {
        if (this.channel)
            Http.api.socket.leave_channel(this.channel);
    },
    methods: {
        async get_gnk_company_info(id){
            let {error,data} = await Http.api.rpc("get_gnk_company_info",{
                id: id
            });
            return {
                error,data
            }
        },
        async next() {
            this.$wait(async () => {
                let ecp = this.ecp?.value;
                let company = {};
                if (!this.external_company) {
                    let { O, T, CN, UID, TIN, PINFL, legal } = Develop.new_ecp_develop ? ecp.info : ecp.vo;
                    T = T && T.toLocaleLowerCase();
                    if (T == 'директор')
                        T = 'direktor'

                    if (!legal) {
                        T = 'direktor'
                    }

                    company = {
                        full_title: O,
                        title: O,
                        inn: TIN,
                        [(T || "contact").toLowerCase()]: {
                            name: CN,
                            inn: UID,
                            pinfl: PINFL
                        }
                    }

                    // Получаем данные из GNK
                    let { error, data } = await this.get_gnk_company_info(legal ? TIN : PINFL)
                    if (error) {
                        this.companyModel = undefined;
                        Vue.Dialog.MessageBox.Error(error);
                        return;
                    } else if (!data?.exists) {
                        this.companyModel = undefined;
                        Vue.Dialog.MessageBox.Error({
                            message: legal ? Language.t("register_company.no_exists") : Language.t('register_company.error_ecp')
                        });
                        return;
                    } else {
                        company.oked_code = data?.company?.oked
                        company.company_type_code = data?.company?.opf
                        company.legal_index = data?.companyBillingAddress?.postcode
                        company.legal_address = data?.companyBillingAddress?.streetName
                    }
                }


                this.companyModel = new ModelProvider['company_model']({
                    external_company: this.external_company,
                    ...company,
                    fulltitle: company.full_title,
                    director_name: company.direktor?.name,
                    director_inn: company.direktor?.inn,
                    director_pinfl: company.direktor?.pinfl,

                    oked_code: company.oked_code,
                    company_type_code: company.company_type_code,

                    postal_index: company.legal_index,
                    postal_address: company.legal_address,

                    legal_index: company.legal_index,
                    legal_address: company.legal_address,
                });
            })


        },
        async prev(){
            this.companyModel = undefined;
        },
        async send() {
            if (!this.companyModel)
                return;
            this.$wait(async () => {
                let ecp = this.ecp && this.ecp.value && (Develop.new_ecp_develop ? this.ecp.value : this.ecp.value.vo);

                let { error, data } = await this.companyModel.save(!this.companyModel.external_company && ecp);
                if (error) {
                    if(error.code != "AbortError")
                        await Vue.Dialog.MessageBox.Error(error);
                } else {
                    this.change_user(data.user_id)
                    this.Close();
                    this.$router.push('/workspace')
                }

            });
        },
        get_company(TIN) {
            return new Promise(async (resolve, reject) => {

                this.channel.on('juridic_info', (data = {}) => {
                    resolve(data.juridic_info);
                });

                let { error, data } = await Http.api.rpc("send_event", {
                    "event_name": "juridic_info",
                    "id": this.request_id,
                    "payload": {
                        "inn": TIN
                    }
                })

                if (error) {
                    resolve();
                }

                setTimeout(() => {
                    resolve();
                }, 5000)
            })
        },
        async select_key(ecp) {
            
            return;
            if (!ecp || (Develop.new_ecp_develop && !ecp.info)) {
                this.companyModel = undefined;
                return;
            }

            this.$wait(async () => {
                let { O, T, CN, UID, TIN, PINFL } = Develop.new_ecp_develop ? ecp.info : ecp.vo;
                T = T && T.toLocaleLowerCase();
                if (T == 'директор')
                    T = 'direktor'
                // Копируем из ЭЦП
                let company = {
                    full_title: O,
                    title: O,
                    inn: TIN,
                    [(T || "contact").toLowerCase()]: {
                        name: CN,
                        inn: UID,
                        pinfl: PINFL
                    }
                }

                let juridic_info;// = TIN && await this.get_company(TIN);

                if (!juridic_info || TIN != juridic_info?.company?.tin) {
                    juridic_info = undefined;
                    //if (await Vue.Dialog.MessageBox.Question(Language.t('getting_information_from_the_state_tax_committee_failed_do_you_want_to_continue_registration_manually')) != Vue.Dialog.MessageBox.Result.Yes)
                    //    this.Close()
                }

                // Копируем из ГНК
                if (juridic_info) {
                    company.full_title = juridic_info.company?.name || company.full_title
                    company.title = juridic_info.company?.shortName || company.title

                    company.company_type_code = juridic_info.company?.opf
                    //company.oked_code =  juridic_info.company?.oked
                    company.soogu_code = juridic_info.company?.soogu

                    if (juridic_info?.director) {
                        let director = juridic_info?.director
                        company.direktor = {
                            name: `${director.lastName} ${director.firstName} ${director.middleName}`,
                            inn: director.tin,
                            pinfl: director.pinfl
                        }
                    }
                }

                this.companyModel = new ModelProvider['company_model']({
                    ...company,
                    fulltitle: company.full_title,
                    director_name: company.direktor?.name,
                    director_inn: company.direktor?.inn,
                    director_pinfl: company.direktor?.pinfl
                });
            })
        }
    },
    template: `
        <div>
            <header>{{$t('register_company')}}</header>
            <main :key='companyModel'>
                <template v-if='!companyModel'>
                    <label><input v-model='external_company' type='radio' :value="false">{{$t('is_residen')}}</input></label>
                    <ui-layout-group style='margin-top: 10px; padding: 0 20px;'><ui-field v-if='!ecp.hidden' :model="ecp" v-on:change='select_key' /></ui-layout-group>
                    <label><input v-model='external_company' type='radio' :value="true">{{$t('is_non_residen')}}</input></label>
                </template>

                <template v-else>
                    <ui-layout :fields='companyModel.fields' />
                    <ui-layout-group label="public_offer">
                        <ui-field :model="agree_to_offer" />
                    </ui-layout-group>
                </template>

            </main>
            <footer>
                <ui-btn type='secondary' v-on:click.native='Close'>{{$t('close')}}</ui-btn>
                <ui-btn type='primary' title="$t('next')" v-if='!companyModel' :disabled='(!ecp.hidden && !ecp.value && !external_company)' v-on:click.native='next()'>{{$t('next')}}</ui-btn>
                <ui-btn type='primary' v-if='companyModel' v-on:click.native='prev()'>{{$t('back')}}</ui-btn>
                <ui-btn type='primary' :title="$t('wp.reg_company')" v-if='companyModel' :disabled='(!companyModel.external_company && !ecp.hidden && !ecp.value) || !agree_to_offer.value' v-on:click.native='send()'>{{$t('wp.reg_company')}}</ui-btn>
            </footer>
        </div>
    `
})

export default Dialog