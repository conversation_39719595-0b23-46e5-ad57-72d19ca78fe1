import { Entity} from '@iac/data'
import { Http, Event } from '@iac/core'
import Stage from './stage';

export default class AgreementEntity extends Entity {
    @Event async onChangeProperty(event) {
        if (!event.data)
        return;

        if(this.save){
            this.wait = true;
            await this.save();
            this.wait = false;
        }
    }
    constructor(context) {
        super(context)
        this.stages_count = context.stages_count;

        this.stage = context.stage;
        this.wait = false;
    }

    static async stage(proc_id) {
        let response = await Http.api.rpc("get_agreement_stages", { "procedure_id": proc_id });
        if (response.error)
            return { error: response.error };

        if (response.data && response.data.length > 0) {
            return {
                data: new Stage({
                    id: response.data[0].id,
                    procedure_id: proc_id
                })
            }
        }

        response = await Http.api.rpc('create_agreement_stage', { id: proc_id, title: `stage ${proc_id}` });
        if (response.error)
            return { error: response.error };
        return {
            data: new Stage({
                id: response.data,
                procedure_id: proc_id
            })
        }    

    }
}