.ui-control{
    border: 1px solid @control-default-border;
    margin-bottom: 15px;
    border-radius: @control-border-radius-base;
    position: relative;
    box-sizing: border-box;
    display: flex;
    min-width: 250px;
    background: #fff;

    &:hover{
        //border-color: #2596CC;
        //z-index: 1;
    }
    >.icon{
        flex: 0 0 auto;
        text-align: center;
        color: #7F8182;
        //padding-top: 20px;
        font-size: 14px;
        line-height: 20px;
        margin-left: 15px;
        user-select: none;
        align-self: center;
    }

    >.container{
        flex: 1 1 auto;
        position: relative;
        min-width: 0;
        margin: 0 15px;
        >label{
            font-size: 14px;
            line-height: 20px;
            top: 12px;
            left: 0;
            right: 0px;
            color: #B9B8B8;
            position: absolute;
            transition: all 0.3s;
    
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        >.control{
            border: none; //1px solid #f00;
            min-height: 42px;
            width: 100%;
            box-sizing: border-box;
            background: transparent;
            position: relative;
            
            font-size: 14px;
            line-height: 20px;
            color: #201D1D;
            outline: none;
            display: flex;
            align-items: center;
        }
    }
    >.toolbar {
        display: flex;
        flex: 0 0 auto;
        //margin-top: 3px;
        align-self: flex-start;
        height: 42px;
        align-items: center;
        >.ui-action{
            //margin-left: -8px;
            //margin-top: -2px;
            margin-left: -3px;
        }
    }
    >.prefix,
    >.suffix {
        display: flex;
        flex: 0 0 auto;
        align-self: flex-start;
        height: 42px;
        align-items: center;
        color: #606060;
        font-size: 14px;
        &.prefix{
            margin: 0 -10px 0 15px;
        }
        >*{
            margin: 0 3px; 
        }
    }

    &.required{
       >.prefix{
        &::after{
            content: "*";
            color: #cc3c3c;
        }
       }
    }

    >.toolbar >.actions{
        flex: 0 0 auto;
        display: flex;
        align-items: center;
        //margin-top: 2px;
        //
        &:last-child{
            margin-right: 3px;
        }
        
        >.action{
            flex: 0 0 30px;
            width: 30px;
            height: 30px;
            display: block;
            border-radius: 50%;
            user-select: none;
            line-height: 30px;
            text-align: center;
            font-size: 10px;
            color: #868e96;
            
            &:last-child{
                //margin-right: 5px;
            }
            &:first-child{
                //margin-left: -15px;
            }
            &.delete{
                cursor: pointer;
                &:hover{
                    background: #868e96;
                    color: #fff;
                }
            }
            &.select{
                font-size: 7px;
                //font-size: 20px;
                //color: #2596CC;
                cursor: pointer;
                transition: transform 0.3s;
                &:hover{
                    //background: #2596CC;
                    //color: #fff;
                }
            }
            &.status{
                font-size: 8px;
                >icon{
                    border: 1px solid;
                    border-radius: 50%;
                    width: 20px;                   
                    height: 20px;                   
                    line-height: 20px;  
                }
            }
        }
    }
    >.field-action{
        padding: 12px 8px 0 2px;

    }
    >.dropdown{
        position: absolute;
        top: 100%;
        background: #FFFFFF;
        border: 1px solid @control-default-border;
        box-shadow: 0px 3px 7px rgba(0, 0, 0, 0.1);
        //width: 100%;
        border-radius: @control-border-radius-base;
        box-sizing: content-box;
        left: -1px;
        z-index: 10;   
        transition: transform 0.3s;   

        min-width: 100%;
        max-width: 280px;
    }

    &.opened{
        >.container >label{
            top: 11px;
            font-size: 12px;
            line-height: 15px;
            color: #2596CC;
            display: none;
        }
    }
     
    &.disabled,
    &.readonly{
        //border-style: dashed;
        box-shadow: none;
        >.toolbar >.actions{
            display: none;
        }
        &.disabled {
            >.icon{
                color: @control-default-border;
            }  
            >.container >label{
                color: #D4D4D4;
            }
        }

        background: #f6f6f6 !important;;
        border-color: #e6e6e6;
    }
    &.wait{
        >.icon{
            content: "test"
        }
        &::after{
            content: '';
            background:rgba(255,255,255,0.5);
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;
        }
    }
    &.status {
        //z-index: 1;
    }
    &.error{
        border-color: @control-error-border;
        >.dropdown{
            border-color: @control-error-border;
        }
        >.toolbar >.actions>.status > icon{
            border-color: @control-error-border;
            color: @control-error-border;
            &::before{
               content: "delete" 
            }
        }
        &.opened >.container >label{
            color: @control-error-border;
        }
    }
    &.success{
        border-color: @control-success-border;
        >.dropdown{
            border-color: @control-success-border;
        }
        >.toolbar >.actions>.status > icon{
            color: @control-success-border;
            border-color: @control-success-border;
            &::before{
               content: "ok" 
            }
        }
        &.opened >.container >label{
            //color: @control-success-border;
        }
    }
    &.warning{
        border-color: @control-warning-border;
        >.dropdown{
            border-color: @control-warning-border;
        }
        >.toolbar >.actions>.status > icon{
            color: @control-warning-border;
            border-color: @control-warning-border;
            &::before{
               content: "ok" 
            }
        }
        &.opened >.container >label{
            //color: @control-success-border;
        }
    }
    &.info{
        border-color: @control-info-border;
        >.dropdown{
            border-color: @control-info-border;
        }
        >.toolbar >.actions>.status > icon{
            color: @control-info-border;
            border-color: @control-info-border;
            &::before{
               content: "ok" 
            }
        }
        &.opened >.container >label{
            //color: @control-success-border;
        }
    }
    &.dropdown{
        z-index: 2;
        &.dropdown-top{
            border-top-left-radius: unset;
            border-top-right-radius: unset; 
            >.dropdown{
                border-bottom-left-radius: unset;
                border-bottom-right-radius: unset;

                top: 0;
                transform: translateY(-100%);
                box-shadow: 0px -3px 7px rgba(0, 0, 0, 0.1);
            }    
        }
        &:not(.dropdown-top){
            border-bottom-left-radius: unset;
            border-bottom-right-radius: unset;  
            >.dropdown{
                border-top-left-radius: unset;
                border-top-right-radius: unset; 
            }
        }
        >.toolbar >.actions >.select{
            transform: rotateZ(180deg);
        }     
    }
}

.ui-control-group{

    box-sizing: border-box;
    display: flex; 
    margin-bottom: 15px;

    >.ui-control,
    >.ui-btn,
    >.ui-field {
        margin-bottom: 0;
        &.ui-btn{
            flex: 0 0 auto;
        }
        &:not(.ui-btn){
            flex: 1 1 100%;
            min-width: 100px;
            >.field-content{
                flex: 1 1 100%;
                min-width: 100px;
            }
        }

        &.inline{
            flex: 0 0 auto;
            min-width: unset;
            >.field-content{
                flex: 0 0 auto;
                min-width: unset;
            }
        }
        &:first-child {
            &.inline{
                flex: 1 0 auto;
                min-width: unset;
                >.field-content{
                    flex: 1 0 auto;
                    min-width: unset;
                }
            }
        }
        &:not(:first-child) {
            &.ui-control,
            &.ui-btn,
            >.field-content>.field-container >.field-control >.ui-action button,
            >.field-content>.field-container >.field-control >.ui-control{
                margin-left: -1px;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }
        }
        &:not(:last-child){
            &.ui-control,
            &.ui-btn,
            >.field-content>.field-container >.field-control >.ui-control{
                border-top-right-radius: 0;
                border-bottom-right-radius: 0; 
            }            
        }
        .ui-control{
            min-width: 120px;
            &.status {
                z-index: 1;
            }
        }
    }
    
    /*>.control{
        border: 1px solid @control-default-border;
        padding: 8px;
        border-radius: @control-border-radius-base;
        display: flex;
        align-items: center;
    }*/

    /*
    >.ui-field,
    >.ui-control,
    >.ui-action,
    >.ui-btn{
        &:not(:first-child) >.ui-control,
        &:not(:first-child){
            margin-left: -1px;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
        &:not(:last-child) >.ui-control,
        &:not(:last-child){
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;    
        }
        &.ui-field,
        >.ui-control,
        &.ui-control
        {

            flex: 1 1 100%;
            box-shadow: none;
            margin-bottom: 0;
            min-width: 140px;
            &.status{
                z-index: 2;
            }

            &.inline{
                flex: 0 0 auto;
                min-width: unset;
            }
        } 
        >.ui-action{
            height: 100%;
            icon{
                color: #2596cc;
            }
            
        } 
        >.ui-info{
            padding: 16px 0;
        }
    }*/
}