export default Vue.Dialog({
  data: function () {
    return {
      error: undefined,
    }
  },
  props: ['model', 'title', 'text', 'fields', 'button', 'method', 'close'],
  methods: {
    async create() {
      await this.wait(async () => {

        let fields = this.fields.filter((field) => {
          if (field.hidden && typeof field.hidden == 'function') {
            return !field.hidden();
          }
          return !field.hidden;
        }).reduce((prev, curr) => {
          if (curr.readonly && curr.readonly()) {
            return prev;
          }
          prev[curr.name] = curr.value && curr.value.exp ? curr.value.exp.value : curr.value;
          return prev;
        }, {});

        if (this.method === undefined) {
          this.Close(fields);
          return;
        }

        const { error, data } = await this.model.action(this.method, {
          ...fields,
        });
        if (error !== undefined){
          this.error = error.message;
          if (!error.data || error.data.length <= 0) {
            return;
          }

          let fields = this.fields.reduce((prev, curr) => {
            prev[curr.name] = curr;
            prev[curr.name].status = undefined;
            return prev;
          }, {});

          error.data.forEach(item => {
            let field = fields[item.name];
            if(!field) {
              return;
            }

            field.status = {
              type: "error",
              message: item.message,
            };
          });
          //await Vue.Dialog.MessageBox.Error(error);
        } else {
          this.Close(data);
        }
      });
    }
  },
  template: `
    <div>
      <header>{{ title }}</header>
      <main v-if='error' class='error'>{{ error }} </main>
      <main class='iac-dialog-body--contract'>
        <p v-if='text'>{{ text }}</p>
        <ui-layout :fields='fields' />
      </main>
      <footer>
        <ui-btn v-if="close" type='secondary' @click.native='Close()'>{{ close }}</ui-btn>
        <ui-btn type='primary' @click.native='create'>{{ button }}</ui-btn>
      </footer>
    </div>
  `
});