import {ClearModel, DemoModel, GroupModel, ComponentsModel} from './model'
import Properties from './properties'
import Struct from './struct'
import { DataSource, Query,  ArrayStore, RemoteStore, RefStore, Entity } from '@iac/data'

class DocSettingModel extends Entity {
    constructor(context){
        super(context)
        this.languages = ["uz"];
        //this.entity_id = {id: "1014171",name: "влад 2603/0"}
    }

    props(){
        let $this = this;
        return {
            template_date: {
                type: "date-time",
                label: "!template_date",
                group: "-!f/!0",
                has_del: true,
                onChange: (value)=>{
                    let attr = this.properties.documentation.attr = this.properties.documentation.attr || {}
                    attr.date = value;

                }
            },
            entity_id: {
                type: "entity",
                group: "-!f/!0",
                label: "!entity_id",
                has_del: true,
                dataSource: {
                    search: true,
                    store: {
                        ref: "ref_tender_private",
                        injectQuery: (params)=>{
                            params.filters = params.filters || {}
                            params.filters.status = ["draft"]
                            params.filters.relation = "owner"
                            return params;
                        }
                    }
                },
                onChange: (value)=>{
                    let attr = this.properties.documentation.attr = this.properties.documentation.attr || {}
                    attr.entity_id = value;
                    
                }
            },
            languages: {
                type: "enum",
                label: "!",
                group: "-!f",
                dataSource: ["uz","ru"],
                onChange: (value)=>{
                    let attr = this.properties.documentation.attr = this.properties.documentation.attr || {}
                    attr.languages = value.length > 0 ? value : undefined;
                }
            },
            doc_readonly: {
                type: "bool",
                group: "-f/!1",
                onChange: (value)=>{
                    this.properties.documentation.readonly = value
                }
            },
            doc_print: {
                type: "bool",
                group: "-f/!1",
                hidden: ()=>{
                    return !this.doc_readonly
                },
                onChange: (value)=>{
                    this.properties.documentation.readonly = value
                    let attr = this.properties.documentation.attr = this.properties.documentation.attr || {}
                    attr.print = value;
                }
            },
            save_all: {
                type: "bool",
                group: "-f/!1",
                hidden: ()=>{
                    return this.doc_readonly
                },
                onChange: (value)=>{
                    let attr = this.properties.documentation.attr = this.properties.documentation.attr || {}
                    attr.save_all = value;
                }
            },
            document_type: {
                label: '--document_type',
                type: "entity",
                //value: '64d60f66-ff27-4787-8ea7-ea92c2ba6d80',
                dataSource: {
			  	    displayExp: 'meta.title',
                    valueExp: 'type',
                    descExp: 'meta.comment',
                    store: {
                        key: 'type',
                        ref: 'ref_documentation_pages',
                        op: 'public_list_types',
                        params: {
                            get company_id(){
                                return $this.entity_id?.company_id
                            },
                            get date(){
                                return $this.template_date && new Date($this.template_date)
                            }
                        }
                    }
			    },
                has_del: true,
                bind: {
                    'hidden': '!entity_id'
                },
                attr: {
                    not_found: "У вас нет шаблонов закупочной документации"
                }
            },
            documentation: {
                type: "documentation",
                label: "!",
                readonly: this.doc_readonly,
                bind: {
                    source: 'document_type.type || document_type',
                    'hidden': '!entity_id'
                },
                //source: "64d60f66-ff27-4787-8ea7-ea92c2ba6d80",
                attr: {
                    // entity_type: "procedure",
                    entity_id: undefined,//  "1014151",
                    languages: this.languages,
                    date: undefined,// "2025-03-20T05:07:45.204147Z",
                    save_all: this.save_all,
                    print: false
                }
            }
        }
    }
}



const DemoComponent = {
    data: function(){
        return {
            properties: true,
            model_doc_setting: new DocSettingModel(),
            model_components: new ComponentsModel(),
            model_group: new GroupModel(),
            model_1: new DemoModel(),
            model_2: new ClearModel(),
            dataSource: new DataSource({
                columns1: {
                    name: {

                    },
                    total_price: {
                        type: "float"
                    }
                },
                query: new Query({

                }),
                store: new RefStore({
                    method: "contract_ref",
                    ref: 'contract_public_registry',
                    injectQuery: (params) => {
            
                        return params;
                    }
                }),
                template: {
                    props:["model"],
                    template: `
                    <ui-data-view-item :model='model'>
                        <template slot='title'>
                            <router-link to='/'>{{model.contract_name}}</router-link>
                        </template>

                    </ui-data-view-item>
                    `
                }
              })
        }
    },
    components: {
        iacProperties: Properties,
        struct: Struct,
    },
    template: `
        <div>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('demo')}}</li>
                </ol>
                <div class='title'>
                    <h1>Демо страница компонентов</h1>
                    <ui-btn :type='properties && "primary"' v-on:click.native='properties=!properties'>Настройки</ui-btn>
                </div>
            </iac-section>
            <iac-section>
                <div>
                    <ui-layout-tab>
                        <ui-layout-group label='Группы' :horizontal='true' style='display: flex' :actions='model_group.actions'>
                            <ui-layout :fields='model_group.fields'   style=''/>
                        </ui-layout-group>
                        <ui-layout-group label='Компоненты' style='display: flex' :actions='model_components.actions'>
                            <ui-layout :fields='model_components.fields'   style=''/>

                            <div><ui-badge :model='{error:1,warning:33,success:2}'>121</ui-badge></div>


                            <h3>Варианты комбинаций типов</h3>
                            <table>
                                <tr>
                                    <td><ui-btn type='default'>default</ui-btn></td>
                                    <td><ui-btn type='default' disabled>default disabled</ui-btn></td>
                                    <td><ui-btn type='default empty'>default empty</ui-btn></td>
                                    <td><ui-btn type='default empty' disabled>default empty disabled</ui-btn></td>
                                </tr>                               
                                <tr>
                                    <td><ui-btn type='primary'>primary</ui-btn></td>
                                    <td><ui-btn type='primary' disabled>primary disabled</ui-btn></td>
                                    <td><ui-btn type='primary empty'>primary empty</ui-btn></td>
                                    <td><ui-btn type='primary empty' disabled>primary empty disabled</ui-btn></td>
                                </tr>
                                <tr>
                                    <td><ui-btn type='secondary'>secondary</ui-btn></td>
                                    <td><ui-btn type='secondary' disabled>secondary disabled</ui-btn></td>
                                    <td><ui-btn type='secondary empty'>secondary empty</ui-btn></td>
                                    <td><ui-btn type='secondary empty' disabled>secondary empty disabled</ui-btn></td>
                                </tr>
                                <tr>
                                    <td><ui-btn type='info'>info</ui-btn></td>
                                    <td><ui-btn type='info' disabled>info disabled</ui-btn></td>
                                    <td><ui-btn type='info empty'>info empty</ui-btn></td>
                                    <td><ui-btn type='info empty' disabled>info empty disabled</ui-btn></td>
                                </tr>
                                <tr>
                                    <td><ui-btn type='warning'>info</ui-btn></td>
                                    <td><ui-btn type='warning' disabled>warning disabled</ui-btn></td>
                                    <td><ui-btn type='warning empty'>warning empty</ui-btn></td>
                                    <td><ui-btn type='warning empty' disabled>warning empty disabled</ui-btn></td>
                                </tr>
                                <tr>
                                    <td><ui-btn type='success'>success</ui-btn></td>
                                    <td><ui-btn type='success' disabled>success disabled</ui-btn></td>
                                    <td><ui-btn type='success empty'>success empty</ui-btn></td>
                                    <td><ui-btn type='success empty' disabled>success empty disabled</ui-btn></td>
                                </tr>
                                <tr>
                                    <td><ui-btn type='danger'>danger</ui-btn></td>
                                    <td><ui-btn type='danger' disabled>danger disabled</ui-btn></td>
                                    <td><ui-btn type='danger empty'>danger empty</ui-btn></td>
                                    <td><ui-btn type='danger empty' disabled>danger empty disabled</ui-btn></td>
                                </tr>
                            </table>

                            <h3>Размеры</h3>
                            <p><ui-btn type='primary xs'>primary xs</ui-btn></p>
                            <p><ui-btn type='primary sm'>primary sm</ui-btn></p>
                            <p><ui-btn type='primary'>primary</ui-btn></p>
                            <p><ui-btn type='primary lg'>primary lg</ui-btn></p>

                            <h3>Группа</h3>
<p><ui-btn-group>
    <ui-btn type='primary'>btn_1</ui-btn><ui-btn type='primary'>btn_2</ui-btn><ui-btn type='primary'>btn_3</ui-btn><ui-btn type='primary'>btn_4</ui-btn>
</ui-btn-group></p>

<p><ui-btn-group>
    <ui-btn type='primary'>btn_1</ui-btn><ui-btn type='primary empty'>btn_2</ui-btn><ui-btn type='secondary'>btn_3</ui-btn><ui-btn type='secondary empty'>btn_4</ui-btn>
</ui-btn-group></p>

<p><ui-btn-group type='sm'>
    <ui-btn type='primary'>btn_1</ui-btn><ui-btn type='primary'>btn_2</ui-btn><ui-btn type='primary' disabled>btn_3 disabled</ui-btn><ui-btn type='primary'>btn_4</ui-btn>
</ui-btn-group></p>

<p><ui-btn-group type='xs'>
    <ui-btn type='danger'>btn_1</ui-btn><ui-btn type='warning'>btn_2</ui-btn><ui-btn type='success'>btn_3</ui-btn><ui-btn type='info'>btn_4</ui-btn>
</ui-btn-group></p>


                        </ui-layout-group>
                        <ui-layout-group label='Списки'  style='display: flex'>
                            <ui-data-view :dataSource='dataSource'>
                                <ui-data-view-item slot='item' slot-scope='props' :props='props' :model='props.item'>
                                    <template slot='title'>
                                        <label>
                                            <input type='checkbox' :value='props.item.id' v-model='dataSource.checkedItems' />
                                            <router-link to='/'>{{props.item.contract_name}}</router-link>
                                        </label>
                                    </template>
                                    <template slot='props'>
                                        <div><label>{{$t('tender.max_price')}}:</label> <div><iac-number :value='props.item.total_price' delimiter=' ' part='2'/>&nbsp;{{props.item.currency}}</div></div>
                                        <div><label>{{$t('tender.max_price')}}:</label> <div><iac-number :value='props.item.total_price' delimiter=' ' part='2'/>&nbsp;{{props.item.currency}}</div></div>
                                        <div><label>{{$t('tender.max_price')}}:</label> <div><iac-number :value='props.item.total_price' delimiter=' ' part='2'/>&nbsp;{{props.item.currency}}</div></div>
                                        <div><label>{{$t('tender.max_price')}}:</label> <div><iac-number :value='props.item.total_price' delimiter=' ' part='2'/>&nbsp;{{props.item.currency}}</div></div>
                                    </template>
                                </ui-data-view-item>

                            </ui-data-view>
                        </ui-layout-group>
                        <ui-layout-group label='Структура собственности'  style='display: flex'>
                            <struct />
                        </ui-layout-group>
                        <ui-layout-group label='Демо' :horizontal='true' style='display: flex' :actions='model_1.actions' :status='{error: 3}'>
                            <div>
                                <ui-layout :fields='model_1.fields'   style=''/>
                                <ui-data-view v-if='0' :dataSource='model_1.properties.subscribe.dataSource' />
                            </div>
                            <iac-properties v-if='properties' :model='model_1' style='flex: 0 0 300px; margin-left: 10px; background: #fff; padding: 5px; border: 1px solid #eee; border-radius: 5px' />
                        </ui-layout-group>
                        <ui-layout-group label='С чистого листа' :horizontal='true' style='display: flex' :actions='model_2.actions'>
                            <ui-layout :fields='model_2.fields'   />
                            <iac-properties v-if='properties' :model='model_2' style='flex: 0 0 auto; margin-left: 10px; background: #fff; padding: 5px; border: 1px solid #eee; border-radius: 5px' />
                        </ui-layout-group>  


                        <ui-layout-group label='testAuth' :status='{error: 1, warning: 7}' >
                            <p><ui>
                                <li><a href='/demo?tabid_tab=6&auth=<EMAIL>'><EMAIL> </a> ФИЗ Прутков Кузьма Иванович</li>
                                <li><a href='/demo?tabid_tab=6&auth=<EMAIL>:618'><EMAIL>:618 </a> NAVOIY DEKLARANT SERVIS MCHJ</li>
                                <li><a href='/demo?tabid_tab=6&auth=<EMAIL>:684'><EMAIL>:684 </a> O'RIKZOR NEO GRAND MCHJ</li>
                                <li><a href='/demo?tabid_tab=6&auth=<EMAIL>:685'><EMAIL>:685 </a> O'RIKZOR филиал 1123123</li>
                                <li><a href='/demo?tabid_tab=6&auth=<EMAIL>:557'><EMAIL>:557 </a> OOO National Defence</li>
                            </ul></p>
                            <p><ui>
                                <li><a href='/demo?tabid_tab=6&auth=<EMAIL>'><EMAIL></a> Верисоко Константин Тисофеевич</li>
                                <li><a href='/demo?tabid_tab=6&auth=<EMAIL>:1234567'><EMAIL>:1234567</a> Верисоко Константин Тисофеевич</li>
                            </ul></p>


                        </ui-layout-group>


                        <ui-layout-group label='Big grid':status='{error: 7,success:3}' >
                            <ui-big-grid v-if='0' :countItems='50' style='margin-bottom: 200px'/>

<div style='color: #333; font-size: 14px;'>
<p>В процессе оценки тендерных предложений секретарь тендерной комиссии вправе направлять участникам запросы через функционал системы для подтверждения или разъяснения той или иной информации, указанной в тендерном предложении. При получении таких запросов участникам необходимо ответить секретарю и предоставить запрашиваемую информацию</p>
<p>Если участники тендера представят предложения в разных валютах, суммы предложений при оценке будут пересчитаны в единую валюту по курсу Центрального банка Республики Узбекистан на момент создания процедуры и остаются неизменными до определения победителей</p>
<p>Победителем признается участник тендера, предложивший лучшие условия исполнения договора на основе критериев, указанных в тендерной документации и предложении.</p>
<p>При наличии арифметических или иных ошибок секретарь тендерной комиссии путем согласования с тендерной комиссией вправе отклонить тендерное предложение либо определить иные условия его дальнейшего рассмотрения, известив об этом участника тендера.</p>
</div>



                            <ui-layout-tab :sticky='true' name='test'>
                                <ui-layout-group label='Лот 1'>
                                    <p>Описания и требования к лоту 1</p>
                                    <ui-big-grid  :countItems='50' style='margin-bottom: 200px'/>
                                    <ui-big-grid  :countItems='2000' />
                                </ui-layout-group>
                                <ui-layout-group label='Лот 2'>
                                    <p>Описания и требования к лоту 2</p>
                                    <ui-big-grid  :countItems='3000' />
                                </ui-layout-group>
                                <ui-layout-group label='Лот 3'>
                                    <p>Описания и требования к лоту 3</p>
                                    <ui-big-grid  :countItems='4000' />
                                </ui-layout-group>
                                <ui-layout-group label='Лот 4'>
                                    <p>Описания и требования к лоту 4</p>
                                    
                                    <ui-big-grid  :countItems='5000' />
                                </ui-layout-group>
                            </ui-layout-tab>


                        </ui-layout-group> 
                        <ui-layout-group label='Закупочная документация'>
                            <div>
                                <ui-layout :fields='model_doc_setting.fields' />
                            </div>
                        </ui-layout-group> 
                    </ui-layout-tab>
                    
                </div>
            </iac-section>
        </div>
    `
}

Vue.component("demo-component", DemoComponent);