import TileSetting from './../setting'
import { Context, Develop } from '@iac/kernel'

export default class OrderBookSetting extends TileSetting {
    constructor(context) {

        context.zip = context.zip || 0;

        super(context)
        let head = window.document.head || window.document.getElementsByTagName('head')[0]

        this.style = window.document.createElement('style');
        this.style.setAttribute('type', 'text/css');
        head.appendChild(this.style);
        this.update();
    }

    update() {
        if (!Develop.terminal_select_color)
            return;
        let { multicolor_0: m_0 = "#70F7", multicolor_1: m_1 = "#00F5", multicolor_2: m_2 = "#07F4", multicolor_3: m_3 = "#07F2" } = this.multicolor || {};
        if (this.style)
            this.style.innerHTML = `
        .iac-order-book.indicator_multicolor > .content .amount.indicator_0 {
            background: linear-gradient(90deg, transparent 5px, ${m_0});
          }
          .iac-order-book.indicator_multicolor > .content .amount.indicator_1 {
            background: linear-gradient(90deg, transparent 5px, ${m_1});
          }
          .iac-order-book.indicator_multicolor > .content .amount.indicator_2 {
            background: linear-gradient(90deg, transparent 5px, ${m_2});
          }
          .iac-order-book.indicator_multicolor > .content .amount.indicator_3 {
            background: linear-gradient(90deg, transparent 5px, ${m_3});
          }
          .iac-order-book.indicator_multicolor.indicator_start > .content .start_color_0 {
            background: ${m_0};
          }
          .iac-order-book.indicator_multicolor.indicator_start > .content .start_color_1 {
            background: ${m_1};
          }
          .iac-order-book.indicator_multicolor.indicator_start > .content .start_color_2 {
            background: ${m_2};
          }
          .iac-order-book.indicator_multicolor.indicator_start > .content .start_color_3 {
            background: ${m_3};
          }
          .iac-order-book.indicator_multicolor > .content .bid .indicators > .indicator_0 {
            background: ${m_0};
          }
          .iac-order-book.indicator_multicolor > .content .bid .indicators > .indicator_1 {
            background: ${m_1};
          }
          .iac-order-book.indicator_multicolor > .content .bid .indicators > .indicator_2 {
            background: ${m_2};
          }
          .iac-order-book.indicator_multicolor > .content .bid .indicators > .indicator_3 {
            background: ${m_3};
          }               
    
        `;
    }
    onChangeProperty(data) {
        if (!data)
            return;
        this.update()
        super.onChangeProperty(data)
    }
    props() {
        return {            
            start_top: {
                type: "bool",
                label: "Отображать стартовые объемы сверху",
            },
            start_top_fix: {
                type: "bool",
                label: "Зафиксировать сверху",
                hidden: () => {
                    return !this.start_top
                },
            },            
            zip: {
                type: "range",
                label: "Степень сжатия данный",
                min: 0,
                max: 3,
                bind: {
                    description: ()=>{
                        switch(this.zip){
                            case 1:
                                return "_Группировка только чужих предложений" 
                            case 2:
                                return "_Группировка отдельно чужих и моих предложений" 
                            case 3:
                                return "_Группировка по цене всех предложений" 
                            default:
                                return "_Без сжатия (без группировки)"
                        }
                    }
                }
            },

            show_my: {
                type: "bool",
                label: "Подчеркивать мои ставки",
                hidden: () => {
                    if (this.zip != 3) {
                        return true
                    }
                },
            },

            indicator: {
                type: "entity",
                has_del: true,
                dataSource: [
                    { id: "monochrome", name: "Одноцветная" },
                    { id: "gradient", name: "Градиентная" },
                    { id: "multicolor", name: "Многоцветная" },
                ]
            },
            indicator_start: {
                type: "bool",
                label: "Подсвечивать стартовые объемы",
                hidden: () => {
                    if (!this.indicator) {
                        return true
                    }
                },
            },
            multicolor: {
                type: "model",
                label: "!",
                attr: {
                    react: true
                },
                hidden: () => {
                    if (!Develop.terminal_select_color)
                        return true;
                    if (!this.indicator || this.indicator.id != "multicolor") {
                        return true
                    }
                },
                fields: {
                    multicolor_0: {
                        label: "!по графику",
                        has_del: true,
                        attr: {
                            react: true
                        },
                    },
                    multicolor_1: {
                        label: "!нереализованный",
                        has_del: true,
                        attr: {
                            react: true
                        },
                    },
                    multicolor_2: {
                        label: "!аннулированный",
                        has_del: true,
                        attr: {
                            react: true
                        },
                    },
                    multicolor_3: {
                        label: "!дополнительный",
                        has_del: true,
                        attr: {
                            react: true
                        },
                    }
                }
            },

        }
    }
}