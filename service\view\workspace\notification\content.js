import { DataSource, RemoteStore, RefStore, Query } from "@iac/data"
import { Http, Language } from "@iac/core"
import { Context,Settings } from "@iac/kernel"

export  default {
    data: function () {
        return {
            tid: undefined,
            wait: false,
            dataSource: new DataSource({
                query: new Query({
                    asia: {
                        type: "widget",
                        label: "!",
                        group: '!-top',
                        hidden: ()=>{
                            return !Settings.tasia
                        },
                        order: 2,
                        widget: {
                            name: {
                                methods: {
                                    async go_to_tasia() {
                                        let { error, data: url } = await Http.api.rpc("get_telegram_subscribe_url")
                                        if (error || !url) {
                                            return
                                        }
                                        window.open(url, '_blank').focus()
                                    },
                                },
                                template: `
                                <div style='text-align: right;'>
                                    <div style=' height: 44px; display: inline-flex; align-items: center; justify-content: end; cursor: pointer;' v-on:click='go_to_tasia'>
                                        <div style='width: 36px; height: 36px; border-radius: 50%; overflow: hidden; margin-right: 7px;' class='icon'>
                                            <img alt="Tender.asia" src="img/tasia.jpg"/>
                                        </div>
                                        <div style='color: #000; font-size: 14px;'>{{$t('account_go_to_tasia')}}</div>
                                    </div>
                                </div>
                                `
                            }
                        }
                    },
                    actions: {
                        label: "!",
                        type: "action",
                        group: '!-top',
                        buttons: true,
                        actions: [
                            {
                                label: "action_mark_notifications_as_read",
                                btn_type: "info",
                                question: Language.t("question_mark_notifications_as_read"),
                                handler: async () => {
                                    this.wait = true;

                                    let { error, data } = await Http.api.rpc("notification_ref", {
                                        ref: "notification",
                                        op: "read_all"
                                    })
                                    this.wait = false;

                                    if (error) {
                                        return Vue.Dialog.MessageBox.Error(error)
                                    }

                                    Context.User.user_statistics.unread_notifications = 0;
                                    this.dataSource.items.forEach((item) => {
                                        item.meta.read = true;
                                    })
                                },
                                get disabled() {
                                    return Context.User.user_statistics.unread_notifications <= 0;
                                }
                            }
                        ],
                        attr: {
                            style: "margin-bottom: 0;"
                        }
                    }
                }),
                store: new RefStore({
                    method: "notification_ref",
                    ref: "notification",
                    context: (context) => {

                        context.meta = context.meta || {};
                        context.meta.read = context.meta.read || undefined;

                        if (!Context.Access.policy['system_mail_list']) {
                            Object.defineProperty(context, "bindClass", {
                                configurable: true,
                                enumerable: true,
                                get: () => {
                                    if (!context.meta.read)
                                        return "ui-alert ui-alert-success";
                                },
                            });
                            return context;
                        }

                        context.bindClass = [
                            (() => {
                                if (context.status == 'ready_for_send')
                                    return "ui-alert ui-alert-warning";
                                if (context.status == 'send_error')
                                    return "ui-alert ui-alert-danger";

                            })(),
                        ]

                        return context;
                    }
                })
            }),
            columns: [
                { field: 'id', label: '№' },
                { field: 'last_try', style: 'white-space: nowrap;', label: "send_date", type: "date" },
                { field: 'title', style: 'width: 100%;text-align: left;' },
                Context.Access.policy['system_mail_list'] && "to",
                Context.Access.policy['system_mail_list'] && "status",
            ]
        }
    },
    computed: {
        classes() {
            return [
                "top_filter",
                {
                    "iac-wait": this.wait
                }
            ]
        },
    },
    mounted() {
        setTimeout(() => {
            Context.User.onNotifySended.bind(this.onNotifySended)
        }, 2000)

    },
    beforeDestroy() {
        if (this.tid) {
            clearTimeout(this.tid)
            this.tid = undefined
        }

        Context.User.onNotifySended.unbind(this.onNotifySended)
    },
    methods: {
        onNotifySended(event) {

            if (this.tid)
                return;
            this.tid = setTimeout(() => {
                if (!this.tid)
                    return;
                this.dataSource.reload();
                clearTimeout(this.tid)
                this.tid = undefined
            }, 2000)
        },
        async read_current(id) {
            //let {error, data} = await Http.api.rpc("notification_get",{
            //    id: id
            //})
            
            Vue.Dialog({
                props: ["id"],
                data: function () {
                    return {
                        error: undefined,
                        model: undefined,
                    }
                },
                computed: {
                    htm() {
                        //return this.model.html;
                        if (!this.model)
                            return;
                        let body = /\s?<body[^>]*?>(.*?)<\/body>\s?/si.exec(this.model.html)
                        if (!body || !body[1])
                            return;

                        return body[1].replace(/\s?<tr[^>]*?>.*?<\/tr>\s?/si, '');
                    }
                },
                mounted() {
                    this.$wait(async () => {
                        let { error, data } = await Http.api.rpc("notification_get", {
                            id: this.id
                        })
                        this.error = error;
                        this.model = data;
                    })
                },
                template: `
                    <div class='notification_details'>
                        <header v-if='model'>{{model.subject}}</header>
                        <main v-if='error'>
                            <ui-error :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>    
                        </main>
                        <main v-if='model'>
                            <div v-html='htm'/>
                        </main>
                        <footer>
                            <ui-btn type='secondary' @click.native='Close'>{{$t("close")}}</ui-btn>
                        </footer>
                    </div>
                `
            }).Modal({
                id: id
            })
        },
        async read(model) {
            //let {error, data} = await Http.api.rpc("notification_get",{
            //    id: id
            //})
            
            Vue.Dialog({
                props: ["model"],
                data: function () {
                    return {
                        //error: undefined,
                        //model: undefined,
                    }
                },
                mounted() {
                    if (Context.Access.policy['system_mail_list'] || this.meta.read)
                        return;
                    //this.meta.read = true;

                    Http.api.rpc("notification_ref", {
                        ref: "notification",
                        op: "update",
                        filters: {
                            id: this.model.id
                        },
                        data: {
                            meta: { ...this.meta, read: true }
                        }
                    }).then(({ error, data }) => {
                        if (!error) {
                            this.meta.read = true;
                            Context.User.user_statistics.unread_notifications--;
                            if (Context.User.user_statistics.unread_notifications <= 0)
                                Context.User.user_statistics.unread_notifications = 0;
                        }
                    })
                },
                computed: {
                    meta() {
                        return this.model.meta
                    },
                    htm() {
                        //return this.model.content;
                        if (!this.model)
                            return;
                        let body = /\s?<body[^>]*?>(.*?)<\/body>\s?/si.exec(this.model.content)
                        if (!body || !body[1])
                            return;

                        return body[1].replace(/\s?<tr[^>]*?>.*?<\/tr>\s?/si, '');
                    }
                },
                template: `
                    <div class='notification_details'>
                        <header v-if='model'>{{model.title}}</header>
                        <main v-if='model'>
                            <div v-html='htm'/>
                        </main>
                        <footer>
                            <ui-btn type='secondary' @click.native='Close'>{{$t("close")}}</ui-btn>
                        </footer>
                    </div>
                `
            }).Modal({
                model: model
            })
        }
    },
    template: `
                <ui-data-grid  v-bind:class="classes" :dataSource='dataSource' :columns='columns'>
                    <template slot='title' slot-scope='props'>
                        <a href='' @click.prevent='read(props.item)'>{{props.item.title}}</a>
                    </template>
                    <template slot='last_try' slot-scope='props'><iac-date :date='props.item.last_try' full icon></iac-date></template>
                </ui-data-grid>
    `
}