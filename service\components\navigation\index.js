import Brand from '../brand'
import { Context } from '@iac/kernel'
import ServiceMenu from './menu'
import IacBilling from './../billing';

import WorkspaceMenu from './workspace_menu'
import WorkspaceNavigation from './workspace_navigation'

export default {
    data: function () {
        return {
            menu: ServiceMenu,
            access: Context.Access,
            user: Context.User,
        }
    },
    components: {
        Brand: Brand,
        WorkspaceMenu: WorkspaceMenu,
        WorkspaceNavigation: WorkspaceNavigation,
        IacBilling,
    },
    mounted: function () {
        this.updateSideBar();
        this.access.onUpdate.bind(this.updateSideBar);
    },
    destroyed: function () {
        this.access.onUpdate.unbind(this.updateSideBar);
        let body = document.getElementsByTagName('body')[0];
        if (!body)
            return;
        body.classList.remove("side_bar")
    },
    computed: {
        items(){
          return this.menu.items;
        },
        menu_items_length() {
            return this.menu.items.length;
        }
    },
    methods: {
        updateSideBar() {

            if (this.menu.items.length <= 0)
                return;

            let body = document.getElementsByTagName('body')[0];
            if (!body)
                return;
            body.classList.add("side_bar");
        },
        hide() {
            let body = document.getElementsByTagName('body')[0];
            if (!body)
                return;
            body.classList.remove('side_bar_show');
        }
    },
    template: `
        <div v-if='items.length'>
            <div class='iac-service-nav-mask' v-on:click='hide'/>
            <div class='iac-service-nav'>
                <Brand/>
                <div class='primary'>
                    <WorkspaceNavigation v-if='$develop.nav_menu_develop'  :items='items' />
                    <WorkspaceMenu v-else :items='items' />
                </div>  
                <div class='secondary' v-if='!$develop.nav_menu_develop'>
                  <iac-billing v-if='user && user.team_id' />
                </div>          
            </div>
        </div>
    `
}