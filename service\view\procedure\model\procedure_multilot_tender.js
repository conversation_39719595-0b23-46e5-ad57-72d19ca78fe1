import Procedure from './procedure';
import { <PERSON>elo<PERSON> } from '@iac/kernel'
@Procedure.Registration("multilot_tender")
export default class Tender extends Procedure {

    constructor(context = {}) {
        super(context);
    }

    get procedure_actions() {
        return [
            { buttons: 1, group: 'general', order: 1000 },
            { buttons:  ( (this.gos || this.gup) ) ? 0 : 3,  group: 'quality_fields', order: 299 },
            { buttons:  ( (this.gos || this.gup) ) ? 2 : 3, group: 'tech_fields', order: 1000 },
        ]
    }

    static async init_context(context) {
        return {

        }
    }
}
