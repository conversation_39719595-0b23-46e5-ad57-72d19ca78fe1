import { DataSource, RefStore, Query } from '@iac/data'
import { Http } from '@iac/core'

export default {
    data: function () {
        return {
            observer: undefined,
            error: undefined,
            html: undefined,
            filter: new Query({
                empty: {
                    group: '!g-',
                    type: "action",
                    buttons: true,
                    label: "-download",
                    order: -1,
                    actions: ["pdf", "xls", "html", "json"].map((type) => {
                        return {
                            label: type,
                            btn_type: "info",
                            handler: async () => {
                                this.$wait(async () => {
                                    let { error, data } = await this.requestData(type, "blob")
                                    if (error && error.code == "AbortError")
                                        return;
                                    if (error) {
                                        Vue.Dialog.MessageBox.Error(error)
                                    } else {
                                        // Сохраняем файл
                                        var a = document.createElement("a");
                                        document.body.appendChild(a);
                                        a.style = "display: none";
                                        let blob = new Blob([data], { type: "octet/stream" });
                                        let url = window.URL.createObjectURL(blob);
                                        a.href = url;
                                        a.download = `report.${type}`;
                                        a.click();
                                        window.URL.revokeObjectURL(url);
                                        a.remove();
                                    }
                                });
                            }
                        }
                    })
                },
                from: {
                    group: '!g-/<date>',
                    type: "date",
                    has_del: true,
                    hidden: true,
                },
                to: {
                    group: '!g-/<date>',
                    type: "date",
                    has_del: true,
                    hidden: true,
                },
                _action: {
                    type: "action",
                    label: "!",
                    group: '!g-/<date>',
                    buttons: true,
                    actions: [
                        {
                            label: "balance.refresh", handler: () => {
                                this.onFilterUpdate();
                            }
                        }
                    ],
                    attr: {
                        style: "text-align: right;"
                    }
                }
            }),
        }
    },
    mounted: function () {
        this.filter.onUpdate.bind(this.onFilterUpdate);
        if (this.filter) {
            this.filter.set(this.$router.currentRoute.query);
        }
    },
    beforeDestroy: function () {
        if (this.observer && (this?.$refs?.frame?.contentWindow?.document?.body)) {
            this.observer.unobserve(this.$refs.frame.contentWindow.document.body);
        }
    },
    destroyed: function () {
        this.filter.onUpdate.unbind(this.onFilterUpdate);
    },
    methods: {
        async requestData(type, format = 'text') {
            let ref = this.$route.params.id.split(":tmpl")
            return Http.report.rpc("render_free_report", {
                ref: ref[0],//"info_tender_region",
                template: ref[1],//"tender_table_9",
                type: type,
                params: {
                    from: this.filter.local.from && new Date(this.filter.local.from),
                    to: this.filter.local.to && new Date(this.filter.local.to)
                }
            }, {
                format: format
            });
        },
        setSize(w, h) {
            this.$refs.frame.style = `height: ${h + 3}px; width: ${w}px;`;

            if (this.observer && (this?.$refs?.frame?.contentWindow?.document?.body)) {
                this.observer.unobserve(this.$refs.frame.contentWindow.document.body);
            }
        },
        wrapReportHtml(raw_html) {
            if (!this.$refs.frame) return;

            const el = document.createElement('html');
            el.innerHTML = raw_html;
            el.lastElementChild.style = `overflow: hidden; margin: 0;`;

            const data = el.innerHTML;
            const $this = this;
            const frame_document = this.$refs.frame.contentWindow.document;

            frame_document.open();
            frame_document.write(data);
            frame_document.close();

            const observer = new ResizeObserver(() => {
                $this.setSize(frame_document.body.scrollWidth, frame_document.body.scrollHeight);
            })
            observer.observe(frame_document.body);
            this.observer = observer;

            return data;
        },
        async onFilterUpdate() {
            this.$wait(async () => {
                let { error, data } = await this.requestData("html")

                if (error && error.code == "AbortError") {
                    //error = undefined
                    //data = '';
                    return;
                }

                this.error = error;

                if (!error)
                    this.html = this.wrapReportHtml(data);
            });


            this.$router.replace({ query: this.filter.address }).catch(error => {

            });
        }
    },
    template: `
    <div class='page-report'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li><router-link to='/workspace/old_report'>{{$t('nav.reports')}}</router-link></li>
                <li>{{$t('report.page_title')}}</li>
            </ol>
            <div class='title'>
                <h1 style='margin: 0;'>{{$t('report.page_title')}}</h1>
            </div>
        </iac-section>

        <iac-section>
            <div><ui-layout :fields='filter.fields' /></div>
            <div>
                <ui-error v-if='error' :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
                <ui-scroller class='report-wrapper'>
                    <iframe ref="frame" class="report-frame" frameBorder="0" />
                </ui-scroller>
            </div>
            
        </iac-section>
    </div>
    `
}
