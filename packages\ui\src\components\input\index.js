const blurTypes = ['float', 'password'];

export var Input = {
    name: "ui-input",
    props: {
        icon: String,
        label: String,
        status: String,
        value: {},
        readonly: <PERSON><PERSON><PERSON>,
        disabled: <PERSON><PERSON><PERSON>,
        name: String,
        type: String,
        wait: <PERSON><PERSON><PERSON>,
        actions: Array,
        has_del: <PERSON><PERSON><PERSON>,
        required: <PERSON><PERSON><PERSON>,
        min: {
            default: -100000000000000
        },
        max: {
            default: 100000000000000
        },
        prefix: String,
        suffix: String,
        trim: {
            default: true
        },
        predicate: {
            //type: "String"
        }
    },
    data: function () {

        let _min = this.min + "";
        let _max = this.max + '';

        let _min_eq = true;
        let _max_eq = true;

        if (_min[0] == '!') {
            _min = _min.substr(1);
            _min_eq = false;
        }

        if (_max[0] == '!') {
            _max = _max.substr(1);
            _max_eq = false;
        }

        if(this.type == 'float'){
            _min_eq = _max_eq = true;
        }

        return {
            show_password: false,
            focus: false,
            ctrl_value: this.get_ctrl_value((this.value != undefined && this.value != null) ? this.value : ""),
            min_value: _min,
            max_value: _max,
            min_eq: _min_eq,
            max_eq: _max_eq,
            shake: false
        }
    },
    watch: {
        value: {
            immediate: true,
            async handler(value, oldVal) {
                if (value == oldVal)
                    return;
                this.ctrl_value = this.get_ctrl_value((value != undefined && value != null) ? value : "")
            }
        }
    },
    computed: {
        ctrl_type() {
            if (this.type == "number")
                return "text"

            if (this.type == "password" && this.show_password)
                return "text"

            return this.type || "text";
        },
        classes() {
            return [
                {
                    "apply-shake": this.shake,
                    "input-error": this.shake
                }
            ]
        },
        opened() {
            if ((this.focus && !this.readonly && !this.disabled) || this.value || this.value === 0) {
                return true
            }

            if (this.ctrl_value) {
                return true
            }

            return false;
        },
        listeners: function () {
            var vm = this
            return Object.assign({},
                this.$listeners,
                {
                    input: function (event) {
                        let value = event.target.value;
                        if(vm.trim && value && (value.charAt(0) == ' ' || value.charAt(0) == '\n' || value.charAt(0) == '\t')) {
                            value = value.trim();
                            vm.shake = true;
                            setTimeout(()=>{vm.shake = false},500)
                        }
                        vm.set_value(value, 'input');
                    },
                    change: function (event) {
                        vm.set_value(event.target.value, 'change');
                    }
                }
            )
        }
    },
    methods: {
        $predicate(value){
            if(!this.predicate)
                return true
            if(typeof this.predicate == 'string'){
                var regExp = new RegExp(this.predicate, "gm");
                return regExp.test(value)
            }else if(typeof this.predicate == 'function'){
                return this.predicate(value);
            }else{
                return true
            }
            
        },
        get_ctrl_value(value) {
            if ((this.type != 'number' && this.type != 'float') || !value)
                return value;

            value = (value + "").replace(/ /g, '');
            value = value.replace(/,/g, '.');

            let index = value.indexOf(".")
            let delimited = value;
            if (index >= 0) {
                value = value.substr(0, index + 3);
                delimited = value.substr(0, index);
            }

            if (this.type != 'float')
                return value;

            delimited = delimited.split('');
            for (let i = delimited.length - 4, j = 0; i >= 0; i--, j++) {
                if (j % 3 === 0) {
                    delimited[i] += " "
                }
            }
            delimited = delimited.join('')
            if (index >= 0) {
                delimited = delimited + value.substr(index);
            }

            return delimited;
        },
        set_value(value, event) {

            if (this.type == 'number' || this.type == 'float') {
                value = (value + "").replace(/ /g, '');
            } 

            
            if(value && !this.$predicate(value)){
                this.$refs.input.value = this.ctrl_value
                    this.shake = true;
                    setTimeout(()=>{this.shake = false},500)
                    return
            }
            

            /*if(value && this.regexp){
               

                var regExp = new RegExp(this.regexp, "gm");
                if (!regExp.test(value)){

                    this.$refs.input.value = this.ctrl_value
                    this.shake = true;
                    setTimeout(()=>{this.shake = false},500)
                    return
                }
            }*/

            if (this.type == 'number' || this.type == 'float') {
                //value = (value + "").replace(/ /g, '');

                let number = Number(value + "");



                if (value == '-') {
                    this.ctrl_value = this.get_ctrl_value(value);
                    this.$refs.input.value = this.ctrl_value
                    return
                }

                if (Number.isNaN(number) && value != '-') {
                    value = (this.ctrl_value + "").replace(/ /g, '');
                    this.shake = true;
                    setTimeout(()=>{this.shake = false},500)
                } else {

                    if(value != undefined && value != '' && value != null){
                        if((this.min_eq && number < this.min_value) || (!this.min_eq && number <= this.min_value)){
                            this.$refs.input.value = this.ctrl_value
                            this.shake = true;
                            setTimeout(()=>{this.shake = false},500)
                            return
                        }

                        if((this.max_eq && number > this.max_value) || (!this.max_eq && number >= this.max_value)){
                            this.$refs.input.value = this.ctrl_value
                            this.shake = true;
                            setTimeout(()=>{this.shake = false},500)
                            return
                        }
                    }
                }
            }

            this.ctrl_value = this.get_ctrl_value(value);
            this.$refs.input.value = this.ctrl_value

            if (this.type == 'number' || this.type == 'float') {
                value = (this.ctrl_value + "").replace(/ /g, '');
            }

            //value = this.ctrl_value;

            if (value == '' || value == null)
                value = undefined
            if (event && value != this.value)
                this.$emit(event, value)
        },
        updateFloat(event) {
            if (this.ctrl_type === 'float') {
                this.set_value(event.target.value, 'change');
            }
        },
        action(event) {
            if (event == 'clear') {
                this.value = undefined;
                //this.$emit('input', undefined)
                this.$emit('change', undefined)
            }
        },
        blur(e) {
            this.focus = false;
            if (blurTypes.includes(this.type)) {
                this.set_value(e.target.value, 'change');
            }
        }
    },
    template: `<ui-control class='ui-input' v-bind:class="classes" 
    :icon='icon' :label='label || name' :opened='opened' :has_del='has_del' :status='status' v-on:action='action' :readonly='readonly' :wait='wait' :disabled='disabled' :actions='actions' :required='required'>
    <input ref='input' class='control' :name="name" :type="ctrl_type"
            v-bind="$attrs"
            v-bind:value="ctrl_value"
            v-on="listeners"
            :disabled="disabled"
            :readonly="readonly"

            @keypress.enter='updateFloat'
            @focus="focus = true"
            @blur="blur"
            :title='readonly && ctrl_value'
        /> 

        <slot slot='prefix' name='prefix'/>

        <div v-if='prefix' slot='prefix'>{{prefix}}</div>
        <div v-if='suffix' slot='suffix'>{{suffix}}</div>

        <div v-if='type=="password" && show_password' class='action eye-off' slot='toolbar' v-on:click='show_password=false;'><icon>eye-off</icon></div>
        <div v-if='type=="password" && !show_password' class='action eye' slot='toolbar' v-on:click='show_password=true;'><icon>eye</icon></div> 
        <slot slot='toolbar' name='toolbar'/>
    </ui-control>`
}
