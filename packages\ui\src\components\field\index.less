.ui-field {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
    min-width: 250px;
    position: relative;

    &.field-border {
        border-radius: 4px;
        padding: 8px;
        border: 1px solid #F3F3F3;

        &.status-error {
            border-color: @control-error-border;
        }

        &.status-success {
            border-color: @control-success-border;
        }

        &.status-warning {
            border-color: @control-warning-border;
        }

        &.status-info {
            border-color: @control-info-border;
        }

        .ui-layout-group {
            >.label {
                border: none;
                margin: 0;

                >.title {
                    font-size: 16px;
                    font-weight: 400;
                    padding: 0;
                }
            }
        }

    }

    &.overflow {

        &.compact {

            //max-height: 250px;
            // overflow: hidden;   
            // &:hover{
            // overflow-y: auto;   
            // } 
            >.overflow-compact {
                max-height: 250px;
                overflow: hidden;
                overflow-y: scroll;
            }

            &:hover>.overflow-compact {
                overflow-y: auto;
            }
        }

        >.action-background {
            position: sticky;
            bottom: 0;
            width: 100%;

            height: 30px;
            margin-bottom: -30px;
            background: linear-gradient(0, #FFF 50%, #FFF0);
        }

        >.action {
            position: sticky;
            bottom: 0;
            cursor: pointer;

            text-align: left;
            font-size: 14px;
            line-height: 30px;
            color: #1f7dae;
            z-index: 1;

            /* &::before {
                content: " ";
                position: absolute;
                left: 0;
                right: 0;
                bottom: 0;
                height: 30px;
                pointer-events: none;
                z-index: -1;
                background: linear-gradient(0, #FFF 70%, #FFF0);
            }*/

            &:hover {
                text-decoration: underline;
            }

        }
    }

    >.field-content {
        display: flex;
        flex-direction: column;

        >.field-label {
            flex: 1 1 auto;
            //margin-bottom: 4px;
            display: flex;
            align-items: flex-end;

            >.label-wrapper {
                margin-bottom: 4px;
            }

            >.label-wrapper {

                >.field-description {
                    color: #969595;
                    margin-top: 4px;
                    font-size: 12px;
                }

                >label {
                    font-size: 14px;
                    line-height: 20px;
                    color: #737373;

                    >span {
                        vertical-align: middle;
                        word-break: break-word;
                    }

                    >.description {
                        background: #B9B8B8;
                        display: inline-block;
                        width: 14px;
                        height: 14px;
                        line-height: 14px;
                        border-radius: 50%;
                        color: #fff;
                        font-size: 11px;
                        text-align: center;
                        cursor: pointer;

                        >div {
                            pointer-events: none;
                            left: 0;
                            right: 0;
                            min-width: 200px;
                            max-width: 320px;
                            text-align: left;
                            display: none;
                            background: #201D1D;
                            color: #fff;
                            z-index: 11;
                            position: absolute;
                            padding: 6px 12px;
                            border-radius: 4px;
                            margin-top: 6px;
                            transform: translateX(-10px);
                            font-size: 12px;
                            line-height: 16px;

                            ul {
                                font-size: 12px;
                                line-height: 16px;

                                li::marker {
                                    color: #aaa;
                                }
                            }

                            &::before {
                                content: "";
                                display: block;
                                width: 6px;
                                height: 6px;
                                background: #201D1D;
                                position: absolute;
                                top: -3px;
                                transform: rotateZ(45deg);
                                left: 13px;
                            }
                        }

                        &:hover {
                            background: #201D1D;

                            >div {
                                display: block;
                            }
                        }
                    }
                }
            }
        }

        >.field-container {
            >.status {
                margin-top: 4px;
                font-size: 12px;

                &.error {
                    color: @control-error-border;
                }

                &.success {
                    color: @control-success-border;
                }

                &.warning {
                    color: @control-warning-border;
                }

                &.info {
                    color: @control-info-border;
                }
            }


            >.field-description {
                color: #969595;
                margin-top: 4px;
                font-size: 12px;
            }

            >.field-control {
                flex: 0 0 auto;

                >.ui-info,
                >.ui-action,
                >.ui-control {
                    margin-bottom: -1px;

                    &:not(:last-child) {
                        border-bottom-left-radius: 0;
                        border-bottom-right-radius: 0;
                        //border-bottom: none;
                        box-shadow: none;
                    }

                    &:not(:first-child) {
                        //border-top: 1px solid #eee;
                        border-top-left-radius: 0;
                        border-top-right-radius: 0;
                    }
                }

                >.ui-action {
                    height: 44px;
                }
            }
        }
    }


    &.horizontally {
        >.field-content {
            flex-direction: row;
            flex-wrap: wrap;

            >.field-label {
                flex: 1 1 50%;
                align-items: flex-start;
                color: #969595;
                min-width: 210px;

                >.label-wrapper {
                    width: 100%;
                    position: relative;
                    //min-width: 220px;
                    padding-right: 30px;

                    &::before {
                        content: "";
                        display: block;
                        position: absolute;
                        border-bottom: 1px dashed #DADADA;
                        height: 15px;
                        width: calc(100% - 10px);

                    }

                    >label {
                        background: #fff;
                        position: relative;
                        //padding-right: 10px;

                    }
                }
            }

            >.field-container {
                flex: 1 1 50%;
                //flex-basis: calc(100% - 200px);
                min-width: 250px;

                >.field-control>.ui-control {
                    min-width: 250px;
                }
            }


            >.field-label {
                >.label-wrapper {
                    margin-top: 12px;
                }
            }
        }

        &.field-type-entity-edit,
        &.field-type-static,
        &.field-type-link,
        &.field-type-link,
        &.field-type-widget,
        &.field-type-markdown {
            >.field-content>.field-label {
                >.label-wrapper {
                    margin-top: 0;
                }
            }
        }

        &.field-type-bool {
            margin-bottom: 30px;
        }

        &_fix {
            >.field-content>.field-label {
                max-width: 210px;
            }
        }

        &_fix2 {
            >.field-content>.field-label {
                max-width: 420px;
            }
        }
    }


    /*


    >label{
        font-size: 14px;
        line-height: 20px;
        color: #201D1D;
    }

    */
}


.ui-field-wrapper {

    .header {
        color: #969595;
        line-height: 18px;
        line-height: 30px;
        font-size: 13px;
        >.eye{
            font-size: 20px;
            cursor: pointer;
            position: relative;
            &:hover{
                color: @primary-link;
            }
        }
    }

    .body {
        margin-bottom: 8px;
        .info-wrapper {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -8px;

            .info {
                margin: 0 8px;
                min-width: 250px;
                flex: 1 1 0;
                display: flex;
                flex-direction: column;

                .title {
                    font-size: 16px;
                    line-height: 24px;
                    color: #201D1D;
                    flex: 1 1 100%;
                }

                .label {
                    color: #969595;
                    font-size: 14px;
                    line-height: 20px;
                }

                .content {
                    line-height: 20px;
                    font-size: 14px;
                    color: #555555;
                    background: #F3F3F3;
                    border: 1px solid #ccc;
                    padding: 12px 20px;
                    border-radius: 4px;
                }
            }


        }

        >.description {
            font-size: 13px;
            color: #555;
            word-break: break-word;


            >div {
                margin-block: 8px;
                border-left: 2px solid #F3F3F3;
                padding-left: 8px;

                label {
                    color: #969595;
                }
            }
        }
    }


}