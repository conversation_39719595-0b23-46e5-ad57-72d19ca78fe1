export default class Assets {

  static file(name, executor) {
    if (!Assets[`_asset_file_promise_${name}`]) {
      Assets[`_asset_file_promise_${name}`] = new Promise(executor)
    }
    return Assets[`_asset_file_promise_${name}`];
  }

  static script(name, add_version = false) {
    return Assets.file(name, (resolve, reject) => {
      let _script = document.createElement('script');

      if (name.indexOf('http') != 0){
        name = add_version ? `/js/${name}?v=${build_version}` : `/js/${name}`
      }
      _script.setAttribute('src', name);

      _script.onload = () => {
        resolve();
      }
      document.head.appendChild(_script)
    })
  }

  static css(name) {
    return Assets.file(name, (resolve, reject) => {
      let _link = document.createElement('link');
      if (name.indexOf('http') == 0)
        _link.setAttribute('href', `${name}`);
      else
        _link.setAttribute('href', `/css/${name}`);

      _link.setAttribute('rel', 'stylesheet');
      _link.onload = () => {
        resolve();
      }
      document.head.appendChild(_link)
    })
  }
}