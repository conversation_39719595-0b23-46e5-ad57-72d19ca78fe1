import { DataSource, RefStore, Entity } from '@iac/data'
import { Http, Language } from '@iac/core'
import { Context, Config } from '@iac/kernel'
import "@iac/service/templates/company"

const $p = Context.Access.policy;
const $t = Language.t;

class IssuingSchedule extends Entity {
    constructor(context = {}) {
        super(context)
        this.idArray = context.idArray;
    }

    validate_date() {
        this.properties.plan_date_from.status = undefined
        this.properties.plan_date_to.status = undefined

        if (this.plan_date_from && new Date(this.plan_date_from) <= new Date()) {
            this.properties.plan_date_from.status = {
                type: "error",
                message: Language.t("select_date_not_today")
            }
            return true;//plan_date_release
        }

        if (this.plan_date_from && this.plan_date_to && new Date(this.plan_date_from) > new Date(this.plan_date_to)) {
            this.properties.plan_date_to.status = {
                type: "error",
                message: Language.t("warning_end_date")
            }
            return true;
        }
    }

    props() {
        return {
            plan_date_from: {
                label: "schedule_start_date",
                type: "date",
                onChange: () => {
                    this.validate_date();
                }
            },
            plan_date_to: {
                label: "schedule_end_date",
                type: "date",
                onChange: () => {
                    this.validate_date();
                }
            },
            claim_ids: {
                label: "selling_requests",
                value: this.idArray,
                type: "static"
            },
            files: {
                label: "attached_files",
                type: "file",
                multiple: true,
            }
        }
    }

    async loadFiles() {
        if (this.files && this.files.length > 0) {
            const errorData = [];
            for (let index in this.files) {
                let fieldData = this.files[index];
                if (!fieldData.file)
                    continue;
                let formData = new FormData();
                formData.append('scope_tender_participant', this.proc_id);
                formData.append('data', fieldData.file, fieldData.file.name);

                let { data, error } = await Http.upload.form('tender/attach', formData);
                if (error) {
                    errorData.push({ i: index, message: error.message })
                } else {
                    this.files[index] = {
                        id: data.uuid,
                        name: data.meta.name,
                        meta: {
                            "type": data.meta.type,
                            "content_type": data.meta.content_type,
                            "type_group": data.meta.group,
                            "size": data.meta.size
                        }
                    }
                }
            }

            if (errorData && errorData.length > 0) {
                this.properties.files.status = {
                    type: "error",
                    data: errorData
                }
            }
        }
    }

    async create() {
        await this.loadFiles()
        const dataForSending = {
            files: (this.files || []).map(item => item.id),
            plan_date_from: this.plan_date_from,
            plan_date_to: this.plan_date_to,
            claim_ids: this.claim_ids
        }
        return await Http.api.rpc("ref_schedule", {
            ref: "exc_schedule_pgv",
            op: "create",
            data: dataForSending
        })

    }
}

const query = () => {
    return {
        status: {
            group: '!filter-',
            type: 'entity',
            has_del: true,
            dataSource: {
                store: {
                    ref: "ref_status_exchanges_contracts"
                }
            },
        },
        inserted_at_gte: {
            group: "quotation_list.close_at_period",
            type: "date",
            label: "from",
            has_del: true,
            bind: {
                status: `inserted_at_error && {"type":"error"}`
            },
        },
        inserted_at_lte: {
            group: "quotation_list.close_at_period",
            type: "date",
            label: "to",
            has_del: true,
            bind: {
                status: `inserted_at_error && {"type":"error"}`
            },
        },
        inserted_at_error: {
            sync: false,
            group: "quotation_list.close_at_period",
            type: "model",
            label: "!",
            bind: {
                value: "inserted_at_gte >= inserted_at_lte",
                status: `inserted_at_error && {
                    "type": "error",
                    "message": "${Language.t('to_from_error')}"
                }`
            },
        },
        min_price: {
            group: "quotation_list.price/<h>",
            type: "number",
            label: "from",
            has_del: true,
            min: 0,
            bind: {
                status: `price_error && {"type":"error"}`
            },
        },
        max_price: {
            group: "quotation_list.price/<h>",
            type: "number",
            label: "to",
            has_del: true,
            min: 0,
            bind: {
                status: `price_error && {"type":"error"}`
            },
        },
        price_error: {
            sync: false,
            group: "quotation_list.price",
            type: "model",
            label: "!",
            bind: {
                value: "min_price > max_price",
                status: `price_error && {
                    "type": "error",
                    "message": "${Language.t('to_from_error')}"
                }`
            },
        },
    }
}

export default {
    props: {},
    data() {
        let $this = this;
        return {
            _currentCheckedItem: undefined,
            dataSource: new DataSource({
                valueExp: ["id", "checkGroup", "amount_of_good_in_lot", "ed_izm", "name"],
                store: new RefStore({
                    method: "ref",
                    ref: "ref_exchanges_contracts",
                    context: context => {
                        context.ed_izm = context.description[4].text
                        Object.defineProperty(context, "checkGroup", {
                            configurable: true,
                            enumerable: true,
                            get: () => {
                                let group = 0;
                                group |= (context.status == 'approved') ? 1 : 0
                                return group;
                            }
                        })

                        Object.defineProperty(context, "checkbox", {
                            configurable: true,
                            enumerable: true,
                            get: () => {
                                let enable = false
                                enable = enable || context.status == "approved"
                                enable = enable && (!$this.currentSelectedItem ||
                                    ($this.currentSelectedItem.unit == context.unit && $this.currentSelectedItem.name == context.name))
                                if (this.dataSource.checkedItems.length > 0) {
                                    if(this.dataSource.checkedItems?.[0]?.ed_izm != context.ed_izm)
                                        return false
                                    if (this.dataSource.checkedItems?.[0]?.name != context.name)
                                        return false
                                }

                                return enable
                            },
                        });

                        context.actions = (context?.actions || []).map(action => {
                            if (action.is_reload) {
                                action.response = ({ data }) => {
                                    data && this.ds.reload();
                                };
                            }

                            return action;
                        })

                        return context
                    },
                    injectQuery: (params) => {
                        params.filters.inserted_at_error = undefined
                        params.filters.price_error = undefined
                        return params;
                    }
                }),
                query: query(),
                actions: [
                    {
                        get label() {
                            const count = $this.dataSource.checkedItems.length
                            const countGroup = $this.dataSource.checkedItems.filter((item) => ((item.checkGroup & 1) != 0)).length

                            if (countGroup == count)
                                return `${countGroup} - ${Language.t("create_pgv")}`

                            return `${countGroup} (${count}) ${Language.t("create_pgv_capital")}`
                        },
                        icon: "check2",
                        hidden: () => {
                            return !(Context.Access.policy['exc_pgv_create'] && this.dataSource.checkedItems.filter((item) => ((item.checkGroup & 1) != 0)).length)
                        },
                        handler: () => {
                            const idsArray = this.dataSource.checkedItems
                                .filter((item) => ((item.checkGroup & 1) != 0))
                                .map((item) => item.id)
                            this.create_issuing_schedule(idsArray)
                        }
                    },
                    {
                        label: "create_procedure.exchange_contract",
                        type: "request",
                        host: "proc",
                        method: "create_procedure",
                        params: {
                            type: "exchange_contract"
                        },
                        question: Language.t("question_create_request"),
                        hidden: () => {
                            return !(Context.Access.policy["exchange_exchange_exposition_my_crud"] || Context.Access.policy['exchange_exchange_contract_company_crud'])
                        }
                    }
                ]
            })
        }
    },
    computed: {
        currentSelectedItem() {
            if (!(this.dataSource._items && (this.dataSource.checkedItems?.length))) {
                this._currentCheckedItem = undefined
                return
            } else if (!this._currentCheckedItem) {
                this._currentCheckedItem = this.dataSource._items.find(item => item.id == this.dataSource.checkedItems[0].id)
            }
            return this._currentCheckedItem
        }
    },
    methods: {
        create_issuing_schedule(idArray) {
            const model = new IssuingSchedule({ idArray })
            Vue.Dialog({
                props: ["model"],
                methods: {
                    async save() {
                        const { error, data } = await this.model.create()
                        if (!error && data && data.id != undefined) {
                            if (Context.Access.policy['exc_pgv_show']) {
                                if (await Vue.Dialog.MessageBox.Question("Хотите перейти в реестр ПГВ?", Language.t("selling_plan_created")) == Vue.Dialog.MessageBox.Result.Yes) {
                                    this.$router.push({ path: '/workspace/exchange/issuing_schedule' })
                                }
                                this.Close()
                            } else {
                                Vue.Dialog.MessageBox.Success(Language.t("selling_plan_created"))
                                this.Close()
                            }
                        } else if (error) {
                            Vue.Dialog.MessageBox.Error(error);
                        }
                    }
                },
                template: `
                  <div>
                    <header>{{$t("create_schedule")}}</header>
                    <main>
                        <ui-layout :fields='model.fields' />
                    </main>
                    <footer>
                        <ui-btn type='primary' v-on:click.native='save'>{{$t('Create')}}</ui-btn>
                        <ui-btn type='secondary' v-on:click.native='Close'>{{$t('Close')}}</ui-btn>
                    </footer>
                  </div>
                `
            }).Modal({ model })
        },
    },
    computed: {
        access_policy(){
            if(this.scope =='private')
                return this.$policy.exchange_exchange_contract_my_crud || this.$policy.exchange_exchange_contract__crud || this.$policy.exchange_exchange_company_crud || this.$policy.exchange_exchange_contract_vote || this.$policy.exchange_exchange_contract_moderate || this.$policy.exchange_exchange_exposition_my_crud
            return true
        }
    },
    template: `
    <iac-access :access='access_policy' key='quotation_list'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.quotation_list')}}</li>
            </ol>
            <div class='title'>
                <h1>{{$t('nav.quotation_list')}}</h1>
            </div>
        </iac-section>

        <iac-section>
            <ui-layout-tab>
                <ui-layout-group key='quotation_list' label='quotation_list'>
                    <ui-layout-group>
                        <ui-data-view :showCheckedItems='false' :dataSource='dataSource'/>
                    </ui-layout-group>
                </ui-layout-group>
            </ui-layout-tab>
        </iac-section>
    </iac-access>
    `
}