import Procedure from './procedure';

@Procedure.Registration("reduction")
export default class Reduction extends Procedure {

    constructor(context = {}) {
        super(context);
    }

    static async init_context(context) {
        if (["close", "cancel", "rejected", "moderated"].includes(context.status) && context.props.time_to_close) {
            context.props.time_to_close.hidden = true
        }
        return {
            
        }
    }
}