
import { DataSource } from '@iac/data';
import { Language } from '@iac/core';

var Component = {
    props: ["limit"],
    data() {
        return {
            source: new DataSource({
                query: {
                    queryText: {
                        label: "!search.bkl.placeholder",
                        group: "!filter-",
                        hidden: ()=>{
                            return this.limit;
                        }
                    },
                    week: {
                        type: "week",
                        label:"!",
                        group: "!filter-",
                        has_del: true,
                        hidden: ()=>{
                            return this.limit;
                        },
                        attr: {
                            style: "max-width: 200px"
                        }
                    }
                },
                store: {
                    method: "contract_ref",
                    ref: "trading_week_stats",
                    context: (context)=>{
                        context.percent = context.diff*100/context.price
                        if(context.percent < 0)
                            context.bindClass = "danger";// "background: #f2dede; color: #a94442;border-radius: 3px; padding: 3px;";
                        else if(context.percent > 0)
                            context.bindClass = "success";// "background: #dff0d8; color: #3c763d;border-radius: 3px; padding: 3px;";

                        context.product_properties = []
                        for (let key in context.properties) {
                            context.product_properties.push({prop_name: key,value: context.properties[key]})
                        }
                        return context;
                    }
                },
                columns: [
                    {field: "name",style: "width: 100%;"},
                    {field: "price",style: "text-align: right;"},
                    {field: "diff",style: "text-align: right;"},
                    {field: "percent",style: "text-align: right;"},
                    "date",
                ]
            })
        }
    },   
    methods: {
        show_properties(model) {
            Vue.Dialog({
                props: ['model'],
                template: `
                  <div>
                    <header>{{model.product_name || model.name}}</header>
                    <main>
                      <iac-layout-static :value='model.product_properties || model.properties' />
                    </main>
                    <footer>
                      <ui-btn type='secondary' v-on:click.native='Close'>{{$t('Close')}}</ui-btn>
                    </footer>
                  </div>
                `
            }).Modal({
                model: model
            })
        }
    },
    template: `
        <ui-data-grid :readonly='true' class='top_filter iac-view-financial_quotation' :dataSource='source' :columns='source.columns'>
            <template slot='name' slot-scope='props'>
                <div class='name' v-on:click.prevent.stop='show_properties(props.item)'>{{props.item.name}}</div>
                <div class='props clamp_2'>
                    <span :title='prop.prop_name' v-for='prop in props.item.product_properties'>{{prop.value}}</span>
                </div>
            </template>
            <template slot='date' slot-scope='props'>
                <div><iac-date :date='props.item.begin_at' withoutTime /></div>
                <div><iac-date :date='props.item.end_at' withoutTime /></div>
            </template>
            <template slot='price' slot-scope='props'>
                <iac-number :value='props.item.price_per_unit'  delimiter=' ' part='2' />
            </template>
            <template slot='diff' slot-scope='props'>
                <iac-number :value='props.item.price_diff'  delimiter=' ' part='2' />
            </template>
            <template slot='percent' slot-scope='props'>
                <div><iac-number :value='props.item.price_percentage_diff'  delimiter=' ' part='2' />&nbsp;%</div>
            </template>
        </ui-data-grid>
    `
}

Vue.component("iac-view-financial_quotation",Component)