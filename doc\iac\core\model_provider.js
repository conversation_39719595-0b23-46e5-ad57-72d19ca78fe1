export default `
## ModelProvider
> Декоратор глобальной регистрации классов для их использования без явного указании пути

Регистрация класса:

    import {ModelProvider} from '@iac/core'

    @ModelProvider("registration_face")
    class Registration extends Model {

    }

Наследование от ранее зарегистрированного класса:

    import {ModelProvider} from '@iac/core'

    class AcceptFaceInvitationModel extends ModelProvider['registration_face'] {
    
    }

`