export default {
    props: ["model"],
    template: `
    <div class='grid' v-if='model.base'>
        <div class='row'>
            <h2>{{$t('contract.base')}}</h2>
        </div>

        <div class='row'>
            <label class='col-sm-3'>{{ $t('contract.' + model.proc_type + '_id') }}:</label>
            <div class='col-sm-5'><router-link :to='"/procedure/"+model.base.tender_id'>№ {{model.base.tender_id}}</router-link></div>
        </div>
        <div class='row'>
            <label class='col-sm-3'>{{ $t('contract.currency') }}:</label>
            <div class='col-sm-5'>{{model.contract_currency}}</div>
        </div>        
        <div class='row'>
            <label class='col-sm-3'>{{ $t('contract.' + model.proc_type + '_start_cost') }}:</label>
            <div class='col-sm-5'><iac-number :value='model.base.start_cost' delimiter=' ' part='2'/> {{model.base_currency}}</div>
        </div>
        <div class='row'>
            <label class='col-sm-3'>{{$t('contract.winner_totalcost')}}:</label>
            <div class='col-sm-5'><iac-number :value='model.base.winner_totalcost' delimiter=' ' part='2'/> {{model.contract_currency}}</div>
        </div>
        <div class='row'>
            <label class='col-sm-3'>{{$t('contract.difference')}}:</label>
            <div class='col-sm-5'><iac-number :value='model.base.difference' delimiter=' ' part='2'/> {{model.base_currency}}</div>
        </div>
    </div>    
    `
}