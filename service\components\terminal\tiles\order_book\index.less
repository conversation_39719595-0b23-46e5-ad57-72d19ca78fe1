.make-indicator(@color_0, @color_1, @color_2, @color_3) {
    


    >.content .amount {
        &.indicator {
            &_0 {
                background: linear-gradient(90deg, transparent 5px, @color_0);
            }

            &_1 {
                background: linear-gradient(90deg, transparent 5px, @color_1);
            }

            &_2 {
                background: linear-gradient(90deg, transparent 5px, @color_2);
            }

            &_3 {
                background: linear-gradient(90deg, transparent 5px, @color_3);
            }
        }
    }

    &.indicator_start >.content .start_color {
        &_0 {
            background: @color_0;
        }

        &_1 {
            background: @color_1;
        }

        &_2 {
            background: @color_2;
        }

        &_3 {
            background: @color_3;
        }
    }

    >.content .bid .indicators {
        >.indicator {
            &_0 {
                background: @color_0;
            }

            &_1 {
                background: @color_1;
            }

            &_2 {
                background: @color_2;
            }

            &_3 {
                background: @color_3;
            }
        }
    }
}

.iac-order-book {
    display: flex;
    flex-direction: column;
    min-height: 0;

    &.indicator {
        &_monochrome {
            .make-indicator(#92ad9a, #92ad9a, #92ad9a, #92ad9a);
        }

        &_gradient {
            .make-indicator(#92ad9a, #aac1b1, #bccfc2, #ccdbd1);
        }

        &_multicolor {
            .make-indicator(#70F7, #00F5, #07F4, #07F2);
        }
    }

    >.panel{
        border-bottom: 1px solid #a9a9a9;
    }


    >.content {
        flex: 1 1 100%;
        overflow-y: scroll;

        >table {
            width: 100%;
            border: 0;

            &.striped {
                tbody tr {
                    &:nth-of-type(2n+1) {
                        box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.05);
                        color: #212529;
                    }
                }
            }

            thead {
                height: 40px;
                tr{
                    background: #dfdfdf;
                    th {
                        font-weight: normal;
                        text-align: left;
                        padding: 2px 6px;
                        white-space: nowrap;
                        border-top: 1px solid #fff;
                        border-left: 1px solid #fff;
                        border-right: 1px solid #a9a9a9;
                    }
                }
            }

            td {
                border-bottom: 1px solid #eee;
                border-right: 1px solid #eee;
            }

            .bid {
                display: flex;
                justify-content: space-around;

                .amount {
                    padding: 3px;
                    flex: 0 0 auto;
                }

                .item {
                    flex-grow: 1;
                    flex-shrink: 1;

                    >.indicators {
                        display: flex;
                        //z-index: -1;    
                        height: 100%;

                    }

                }
            }
        }
    }

    &.show_my{
        .item {
            &_2{
                border-bottom: 2px solid #666;
                cursor: pointer;
                &:hover{
                    box-shadow: inset 0px 0px 50px #0002;
                    border-right: 1px solid #9b9b9b;
                    border-left: 1px solid #9b9b9b;
                }
            }
        }
    }

    &.start_top_fix {
        .start_info {
            position: sticky;
            top: 40px;
            z-index: 1;
            background: #fff;
            tr:last-child{
                td{
                    border-bottom: 2px solid #4d6082;
                }
            }
        }
    }
}


.iac-order-book1 {
    height: 100%;
    display: flex;
    flex-direction: column;

    >.content {
        overflow: auto;
        flex: 1 1 100%;
    }

    .bid {
        .details {
            z-index: 1;
            cursor: pointer;

            .info {
                display: none;
                pointer-events: none;
            }

            &:hover {
                background: #eee7;
                z-index: 10;

                .info {
                    z-index: 2;
                    display: block;
                }
            }
        }
    }

    .bids {
        display: inline-table;
        width: 100%;

        >.bid {
            position: relative;
            border-bottom: 1px solid #ccc;

            // 
            &.in {
                background: #ffcd8b;
            }

            >.cell {
                display: table-cell;
                position: relative;
                pointer-events: none;
                padding: 2px;

                &.all {
                    width: 100%;
                }
            }

            >.indicator {
                position: absolute;
                left: 0;
                top: 0;
                right: 0;
                bottom: 0;

                &.all {
                    background: rgb(195, 238, 209);
                    border-right: 1px solid #000;
                }

                &.my {
                    top: 1px;
                    bottom: 1px;

                    >div {
                        position: absolute;
                        height: 100%;
                        //background: #0002;
                        border-left: 1px solid #0001;
                        border-right: 1px solid #0001;
                        cursor: pointer;

                        &:hover {

                            //    background: #0003;
                            &::after {
                                background: #0003;
                            }
                        }

                        &::after {
                            content: " ";
                            position: absolute;
                            left: 0;
                            top: 80%;
                            right: 0;
                            bottom: 0;
                            background: #0002;
                            border-bottom: 1px solid #0003;
                        }
                    }
                }
            }
        }
    }
}