import { DataSource, RemoteStore, Query } from '@iac/data'
import Item from './item'
import { Http, Language } from '@iac/core';
import ProcedureEntity from './entity';
import { Develop, Settings } from '@iac/kernel';



export default class Lot extends ProcedureEntity {
    constructor(context = {}, proc) {
        context.props = { ...context.fields }
        super(context, proc)

        this.proc = proc;
        this.status = undefined;
        this._items = undefined;
        this.lot_id = `${Language.t('procedure.lot')} #${this.id}`
        this.name =  context.title ||  context.name || this.lot_id

    }

    get desc(){
        return this.title
    }

    get actions() {
        return [
            /*{
                label: "add_field",
                handler: async () => {

                },
                hidden: !this.access('set_field')
            },*/
            {
                label: "add_position",
                handler: async () => {
                    let { data, error } = await Http.proc.rpc("create_item", { proc_id: this.proc.id, lot_id: this.id });
                    if (error) {
                        await Vue.Dialog.MessageBox.Error(error)
                    } else {
                        this.items.reload();
                    }
                },
                hidden: !this.access('create_item')
            }
        ]
    }

    async del_item(item) {
        let { data, error } = await Http.proc.rpc("del_item", { proc_id: this.proc.id, lot_id: this.id, item_id: item.id });
        if (error) {
            await Vue.Dialog.MessageBox.Error(error)
        } else {
            this.items.reload();
        }
    }

    get part_id() {
        return this.proc.part_id
    }

    async updateItemsFields(items) {
        if (!this._items || !this._items.items)
            return;
        for (let itemID in this._items.items) {
            let item = this._items.items[itemID];

            if (!items[item.id])
                continue;

            await item.updateFields(items[item.id].fields);
        }
    }

    async deleteItemsFields(items) {
        if (!this._items || !this._items.items)
            return;
        for (let itemID in this._items.items) {
            let item = this._items.items[itemID];

            if (!items[item.id])
                continue;

            await item.deleteFields(items[item.id].fields);
        }
    }

    async refresh_items() {
        if (!this.items || !this.items.listeners || this.items.listeners.length <= 0)
            return;
        let { error, data: items } = await this.items._store.load({
            skip: 0,
            take: this.items._items.length,
            query: this.items.query.local,
            search: this.items.query.search_query,
            //signal: this.items.controller.signal
        });
        if (error)
            return;
        items.forEach((item, index) => {
            item = this.items.init_item(item);
            item.index = index + 1;
        });
        this.items._items = items;
    }

    get items() {
        if (!this._items) {
            this._items = new DataSource({
                limit: typeof Settings.procedures?._position_limit == 'number' ? Settings.procedures?._position_limit : 10,
                query: new Query({
                    part_id: {
                        value: this.part_id
                    },
                    proc_id: {
                        hidden: true,
                        value: this.proc.id,
                    },

                    lot_id: this.id
                }),
                store: new RemoteStore({
                    host: Http.proc,
                    method: 'get_items',
                    context: (context) => {

                        let fields_status_item = this.proc.fields_status[this.id]?.[context.id];
                        if (fields_status_item) {
                            Object.keys(fields_status_item).forEach((name) => {
                                let field = fields_status_item[name];
                                if (field.value != undefined && field.value != null)
                                    context.fields[field.name].value = field.value;
                                if (context.fields[field.name]){
                                    context.fields[field.name].status = {
                                        type: field.type,
                                        message: field.message
                                    }
                                }else{
                                    delete fields_status_item[name];
                                }
                            });
                        }
                        return new Item(context, this);
                    }
                })
            })
        }
        return this._items;
    }

}