import Language from './../../core/src/language'
import Event from './../../core/src/event'
import DataSource from './data_source'
import IacObject from './object';
import Guid from './../../core/src/guid'

const Key = {
    Attributes: Symbol('Attributes'),
    Model: Symbol('Model')
}

if (!Array.prototype.equals) {
    Array.prototype.equals = function (array) {
        if (!array)
            return false;

        // compare lengths - can save a lot of time 
        if (this.length != array.length)
            return false;

        for (var i = 0, l = this.length; i < l; i++) {
            if (this[i] instanceof Array && array[i] instanceof Array) {
                if (!this[i].equals(array[i]))
                    return false;
            }
            else if (this[i] != array[i]) {
                return false;
            }
        }
        return true;
    }
    Object.defineProperty(Array.prototype, "equals", { enumerable: false });
}


export default class Property extends IacObject {

    @Event async onUpdate(event) {
        if (!event || !event.data)
            return;

        if (Array.isArray(event.data.value) && Array.isArray(event.data.old)) {
            let new_value = event.data.value.map((item) => {
                return (item && item.exp) ? item.exp.value : item
            })
            let old_value = event.data.old.map((item) => {
                return (item && item.exp) ? item.exp.value : item
            })
            if (new_value.equals(old_value))
                return;
        }

        // Пересмотреть подход с exp

        let value = event.data.value
        if (event.data.value != undefined && event.data.value.exp && (event.data.value.exp.value != undefined)) {
            value = event.data.value.exp.value;
        }

        let value_old = event.data.old;
        if (event.data.old != undefined && event.data.old.exp && (event.data.old.exp.value != undefined)) {
            value_old = event.data.old.exp.value;
        }

        if (this.multiple) {
            if (Array.isArray(value)) {
                value = value.map((item) => {
                    return (item && item.expValue) ? item.expValue : item
                })
            }

            if (Array.isArray(value_old)) {
                value_old = value_old.map((item) => {
                    return (item && item.expValue) ? item.expValue : item
                })
            }


            if ((Array.isArray(value) && value.equals(value_old)) || value == value_old)
                return;

        }



        if (value_old == value)
            return;

        if (this.type == "enum" || this.type == "enum-tree") {
            if (Array.isArray(value) && value.length <= 0 && event.data.old == undefined)
                return;
            if (Array.isArray(event.data.old) && event.data.old.length <= 0 && value == undefined)
                return;
        }

        if (this.type == "file" && this.Upload && value){
            this.wait = true;

            let _upload = false;
            if (Array.isArray(value)) {
                _upload = value.filter((item)=>{
                    return !!item.file
                }).length > 0
            }else{
                _upload = !!value.file
            }

            if(_upload)
                this._value = value = await this.Upload(value);

            this.wait = false;
        }

        if (this[Key.Model] && this[Key.Model].onChangeProperty) {
            this[Key.Model].onChangeProperty({
                name: this.name,
                value: value,
                old_value: value_old,
                property: this
            });
        }

        if (this.onChange) {
            this.onChange(value, value_old)
        }

    };

    reCalcValue() {
        console.log("-----------------------reCalcValue");
        if (!this.type == 'model')
            return;

        let value = this.fields.filter((field) => {
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).map((field) => {
            if (field.type == 'model') {
                field.reCalcValue();
            }

            let value = field.value;

            if (field.value && field.value.exp && field.value.exp.value != undefined) {
                value = field.value.exp.value
            }

            return {
                name: field.name,
                value: value
            }
        }).filter((field) => {
            return field.value != undefined
        }) || [];

        if (value.length <= 0)
            value = undefined;
        else
            value = value.reduce((prev, curr) => {
                prev[curr.name] = curr.value
                return prev;
            }, {})

        this._value = value
        this.status = undefined;

    }

    @Event async onChangeProperty(event) {
        if (this.lock && this.type == 'model')
            return

        if (this.type == 'model' && event.data) {
            this._value = this._value || {};
            this._value[event.data.name] = event.data.value
        }

        let value = this.fields.filter((field) => {
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).map((field) => {
            let value = field.value;

            if (field.value && field.value.exp && field.value.exp.value != undefined) {
                value = field.value.exp.value
            }

            return {
                name: field.name,
                value: value
            }
        }).filter((field) => {
            return field.value != undefined
        }) || [];

        if (value.length <= 0)
            value = undefined;
        else
            value = value.reduce((prev, curr) => {
                prev[curr.name] = curr.value
                return prev;
            }, {})

        let old = this._value;
        this._value = value
        this.status = undefined;
        this.onUpdate({ old, value });
    }

    constructor(context) {
        super({
            id: context.name,
            props: context.attributes && context.attributes.fields
        });

        this.onChange = (context.attributes && context.attributes.onChange) || function () {

        }

        this.name = context.name;
        this[Key.Model] = context.model;

        this.compact = undefined;
        this.preffix = undefined;
        this.suffix = undefined;
        this.attr = undefined;
        this.wait = false;
        this.label = undefined;
        this.group = undefined;
        this.type = undefined;
        this.dataSource = undefined;
        this.readonly = undefined;
        this.hidden = false;
        this.order = 0;
        this.border = false;
        this.template = undefined;
        this.wrapper = undefined;
        this.source = undefined;
        this.actions = undefined;
        this._meta = undefined;
        this.status = undefined;
        this.has_del = undefined;
        this.min = undefined;
        this.max = undefined;
        this.multiple = undefined;
        this.required = false;
        this.range = undefined;
        this.widget = undefined;
        this.show_comment = false;
        this.show_photo = false;
        this.sync = true;
        this.dataBind = undefined;
        this.bind = undefined;
        this.description = undefined;
        this.member = false;
        this.setAttributes(context.attributes);

        this._validate = context.attributes.validate || function () { }

        this._value = this[Key.Model][this.name] != undefined ? this[Key.Model][this.name] : context.attributes.value;

        if (this.type == 'model' && this._value) {
            this.lock = true;
            this.fields.forEach((field) => {
                if (this._value[field.name] && !Array.isArray(this._value[field.name]) && typeof this._value[field.name] == 'object') {
                    field.value = { ...this._value[field.name] }
                } else {
                    field.value = this._value[field.name]
                }
            })
            this.lock = false;
        }
        var descriptor = Object.getOwnPropertyDescriptor(this[Key.Model], this.name);
        this.value_descriptor = descriptor

        Object.defineProperty(this[Key.Model], this.name, {
            configurable: true,
            enumerable: true,
            get: () => {
                if (this.type == "model") {

                }
                return this._value;
            },
            set: (value) => {
                if ((this.type == 'float' || this.type == 'number' || this.type == 'range') && value != undefined && value != null) {
                    try {
                        if(this.multiple && Array.isArray(value)){
                            value = value.map((val)=>{
                                return Number.parseFloat(val)
                            }).filter((val)=>{
                                return !Number.isNaN(val)
                            })
                        }else{
                            let val = Number.parseFloat(value)
                            if (!Number.isNaN(val)) {
                                value = val
                            }
                        }
                    } catch (e) {
                        console.log(e)
                    }
                }

                if (this.type == "bool" || this.type == "boolean") {
                    if (value === 'true') value = true;
                    if (value === 'false') value = false;
                }

                if (this.type == "entity" && this.dataSource && this.dataSource.store && this.dataSource.store.keyType == 'number' && typeof value == 'string') {

                    try {
                        let val = Number.parseFloat(value)
                        if (!Number.isNaN(val)) {
                            value = val
                        }
                    } catch (e) {
                        console.log(e)
                    }
                }

                if (this.type == "model" && value) {
                    this.lock = true
                    this.fields.forEach((field) => {
                        if (value[field.name] && !Array.isArray(value[field.name]) && typeof value[field.name] == 'object') {
                            field.value = { ...value[field.name] }
                        } else {
                            field.value = value[field.name]
                        }
                    })
                    this.lock = false;
                }

                if (descriptor && descriptor.set) {
                    descriptor.set(value)
                }
                let old = this._value;
                this._value = value

                if (old != value) {
                    let val1 = old?.exp?.value != undefined ? old?.exp?.value : old;
                    let val2 = value?.exp?.value  != undefined ? value?.exp?.value : value;
                    if (val1 != val2)
                        this.status = undefined;
                }

                this.onUpdate({ old, value });
            }
        })

    }

    @Event onDestroy() {
        // Востанавливаем дескриптор значения у родителя с сохранением значения
        if (this.value_descriptor) {
            this.value_descriptor.value = this._value;
            Object.defineProperty(this[Key.Model], this.name, this.value_descriptor);
        } else {
            Object.defineProperty(this[Key.Model], this.name, {
                configurable: true,
                enumerable: true,
                value: this._value
            });
        }
    }

    static validate = {}

    validate(status = true) {
        if(this.type == 'static' || this.type == 'link' || this.type == 'widget')
            return;
        let _error = this._validate(status);
        if (_error)
            return _error;
        else if(status && this.status && this.status.type == 'error'){
            this.status = undefined;
        }

        if (Property.validate[this.type]) {
            _error = Property.validate[this.type].call(this)
            if (_error)
                return _error;
        }

        if (this.required && (this.type == 'enum' || this.type == 'enum-tree') && Array.isArray(this.value) && this.value.length <= 0)
            return Language.t("required_field")
        
        if (this.required && this.multiple && Array.isArray(this.value) && this.value.length <= 0)
            return Language.t("required_field")

        if (this.required && !this.value)
            return Language.t("required_field")

        if (!this.type || this.type == 'text' || this.type == 'string') {
            if (this.min && this.value && this.value.length < this.min)
                return Language.t("required_min")
            if (this.max && this.value && this.value.length > this.max)
                return Language.t("required_max")
        }

        if (this.type == 'model') {
            let errors = this.fields.filter((field) => {
                if (field.hidden && typeof field.hidden == 'function') {
                    return !field.hidden();
                }
                return !field.hidden;
            }).map((field) => {
                let error = field.validate(status);

                return error ? {
                    name: field.name,
                    message: error != true ? error : undefined
                } : undefined;
            }).filter((error) => {
                return error
            })

            if (errors && errors.length > 0)
                if (!status)
                    return true
                else return this.setError({
                    data: errors
                })
        }
    }

    get propertyModel() {
        return Property;
    }

    get _attributes(){
        return this[Key.Attributes]
    }
    
    setAttributes(attributes) {
        this.key = Guid.newGuid();
        this[Key.Attributes] = { ...this[Key.Attributes], ...attributes };

        for (let key in attributes) {
            switch (key) {
                case 'status':
                case 'border':
                case 'template':
                case 'wrapper':
                case 'source':                    
                case "suffix":
                case "prefix":

                case 'order':
                case 'icon':
                case 'group':
                case 'label':
                case 'meta':
                case 'buttons':
                case 'actions':
                case 'has_del':
                case 'min':
                case 'max':
                case 'widget':
                case 'sync':
                case 'multiple':
                case 'attr':
                case 'member':
                case 'compact':
                case 'description':
                    this[key] = attributes[key];
                    break;
                case 'dataSource':
                    let dataSource = attributes['dataSource'];
                    this.dataSource = DataSource.get(dataSource);
                    break
                case 'type':
                    this[key] = attributes[key];
                    switch (attributes.type) {
                        case 'photo':
                            this.type = 'file';
                            this.show_photo = true;
                            break;
                        case 'file_comment':
                            this.type = 'file';
                            this.show_comment = true;
                            break;
                        case 'action':

                            break;
                        case 'model':
                            this.type = 'model';
                            if (attributes.fields || attributes.props) {
                                this.setProperties(attributes.fields || attributes.props)
                            }
                            break;

                    }
                    break;
                case 'props':
                case 'fields':
                    if (this.type == 'model' && !attributes.type) {
                        this.setProperties(attributes[key])
                    }
                    break;
                case 'required':
                case 'readonly':
                case 'hidden':
                    if (typeof attributes[key] != 'string') {
                        this[key] = attributes[key];
                        break;
                    }
                    this[key] = function () {
                        let names = Object.keys(this.model.properties).filter((name) => {
                            if (name.indexOf('.') >= 0)
                                return false;
                            return true;
                        });
                        let params = this.model.fields.filter((field) => {
                            if (field.name == this.name)
                                return true;
                            return !field.hidden;
                        }).map((field) => {
                            let value = field.value;
                            if (field.value && field.value.exp && field.value.exp.value != undefined) {
                                value = field.value.exp.value
                            }
                            return {
                                name: field.name,
                                value: value
                            }
                        }).reduce((prev, curr) => {
                            prev[curr.name] = curr.value
                            return prev;
                        }, {});

                        let fn = new Function(names.join(','), `
                            let ret; 
                            try{
                                ret = ${attributes[key]};
                            }catch(e){
                                return false;
                            }

                            return ret;
                        `);
                        return fn.apply(this, names.map((key) => {
                            return params[key];
                        }))
                    }

                    break;
                case 'range':
                    if (attributes[key])
                        this[key] = attributes[key].replace(/ /g, "").split('-')
                    break;
                case 'dataBind':
                    let dataBind = attributes.dataBind;
                    this.dataBind = dataBind;
                    let model = dataBind.model || this[Key.Model];
                    var descriptor = Object.getOwnPropertyDescriptor(model, dataBind.name);
                    Object.defineProperty(model, dataBind.name, {
                        configurable: true,
                        enumerable: true,
                        get: () => {
                            if (descriptor && descriptor.get)
                                return descriptor.get();
                        },
                        set: (value) => {
                            if (descriptor && descriptor.set) {
                                descriptor.set(value)
                            }

                            if (value && dataBind.field && value[dataBind.field]) {
                                if (dataBind.property == 'dataSource' && this[dataBind.property] != value[dataBind.field]) {
                                    this.value = undefined
                                }
                                this[dataBind.property] = dataBind.property == 'dataSource' ? DataSource.get(value[dataBind.field]) : value[dataBind.field]
                            } else if (value && !dataBind.field) {
                                if (dataBind.property == 'dataSource' && this[dataBind.property] != value) {
                                    this.value = undefined
                                }
                                this[dataBind.property] = value

                            } else {
                                if (dataBind.property == 'dataSource') {
                                    this.value = undefined
                                }
                                if (this.type == 'model' && dataBind.property == 'value') {
                                    this[dataBind.property] = {};
                                } else {
                                    this[dataBind.property] = undefined;
                                }
                            }
                        }
                    });
                    break;
                case 'bind':
                    break;
            }
            if (this.hidden && typeof this.hidden == 'function') {
                this.hidden.bind(this);
            }
        }

        if(attributes.bind){
            let bind = attributes.bind;
            for (let key in bind) {
                let object = this;
                let property = bind[key];

                let _keys = key.split('.');
                key = _keys.pop();
                
                _keys.forEach((name)=>{
                    object[name] = object[name] || {};
                    object = object[name]
                })

                                        
                if (key == 'dataSource') {
                    continue;
                }

                let f;

                
                if(key == 'value')
                    key = '_value'
                
                if(typeof property =='string' ){
                    let loop_error;
                    property = property.replace('\n','').replace(/(\.?[a-zA-Z_\'\"][а-яА-Я\w\s\.\"\']*)/gm, (name) => {
                        if(name[0] == '.' || name[0] == '\'' || name[0] == '"')
                            return name;
                        name = name.trimRight();
                        if(name.indexOf('this') == 0)
                            return name;

                        if(name.indexOf('new') == 0)
                            return name;
                        if(name.indexOf('JSON') == 0)
                            return name;
                        if(name.indexOf('Math') == 0)
                            return name;
                        if(name == this.name && key == '_value'){
                            loop_error = "Зацикленное выражение"
                        }
                        return "model." + name
                    })   
                    if(loop_error){
                        //this.status = {
                        //    type: "error",
                        //    message: loop_error
                        //}
                        continue
                    }

                    try{
                        f = new Function("model", `
                            try{
                                return ${property}
                            }catch(e){
                                //console.log(e);
                            }
                        `)
                    }catch(e){
                        break;
                    }

                }else if(typeof property =='function' ){
                    f = property; 
                }

                if(!f)
                    break;

                
                Object.defineProperty(object, key, {
                    configurable: true,
                    enumerable: true,
                    get: () => {
                        let val = f.call(this,this.model);
                        return (val || val == 0) ? val : undefined
                    },
                    set: (value)=>{
                        //console.log(value);
                    }
                });

                if (key == '_value') {
                    this.readonly = true;
                }

            }
        }




        if (this.multiple) {
            //console.log("_VALUE", this.value);
        }
    }

    get model() {
        return this[Key.Model];
    }
    get parent() {
        return this.model;
    }
    get meta() {
        return this._meta
    }

    set meta(meta) {
        this._meta = meta
    }

    get value() {
        return this._value;
    }

    set value(value) {
        this[Key.Model][this.name] = value;
    }

}