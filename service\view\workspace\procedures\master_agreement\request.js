import { Context, Develop } from '@iac/kernel';
import { DataSource, RefStore, Query } from '@iac/data';
import Router from '../../../../router';

let Grid = {
    props: ["source"],
    data: function () {
        return {
            columns: [
                { field: "id", label: 'request.id' },
                "master_agreement_id",
                { field: "publicated_at", type: "date" },
                { field: "name", label: 'request_name', style: "width:100%; text-align: left;" },
                { field: "sum_start", style: "white-space: nowrap; text-align: right;" },
                { field: "sum_win", style: "white-space: nowrap; text-align: right;" },
                { field: "agree_at", type: "date" },
                "status"
            ]
        }
    },
    computed: {
        debug() {
            return Develop.field_debug;
        }
    },
    methods: {
        openProducts(id) {
            Vue.Dialog({
                props: ["id"],
                data() {
                    return {
                        source: new DataSource({
                            query: {
                                id: this.id
                            },
                            store: new RefStore({
                                ref: 'ref_agreement_request',
                                context: (item) => {
                                    return item
                                },
                                injectQuery: async (params) => {
                                    params.fields = ["goods"]
                                    return params
                                },
                                inject: async (items) => {
                                    const goods = items?.[0]?.goods
                                    if (!goods?.length)
                                        return;

                                    let product_ids = {};
                                    goods.forEach((item) => {
                                        if (item.product_details?.name) {
                                            return item.product_name = item.product_details.name
                                        }
                                        const key = item.product_details.id
                                        product_ids[key] = product_ids[key] || { items: [] };
                                        product_ids[key].items.push(item)
                                    })

                                    await DataSource.get("ref_products").byKeys(Object.keys(product_ids))
                                        .then((products) => products
                                            .forEach((product_item) => product_ids[product_item.id]?.items
                                                ?.forEach((item) => item.product_name = product_item.name)
                                            )
                                        )
                                    return goods;
                                }
                            }),
                        })
                    }
                },
                methods: {
                    properties(product) {
                        Vue.Dialog({
                            props: ['model'],
                            template: `
                              <div>
                                <header>{{model.product_name || model.name}}</header>
                                <main>
                                  <iac-layout-static :value='model.product_properties' />
                                </main>
                                <footer>
                                  <ui-btn type='secondary' v-on:click.native='Close'>{{$t('Close')}}</ui-btn>
                                </footer>
                              </div>
                            `
                        }).Modal({ model: product })
                    }
                },
                template: `
                    <div>
                        <header>{{$t('link.products')}}</header>
                        <main>
                            <ui-list :sync='false' :dataSource='source'>
                                <div  slot='item' slot-scope='props' :model='props.item'> 
                                    <a v-if='props.item.product_properties' href='' v-on:click.prevent='properties(props.item)'>{{props.item.product_name}}&nbsp;</a>
                                    <span v-else>{{props.item.product_name}}&nbsp;</span>
                                    <div style='color: #bbb; display: flex; font-size: 13px; border-bottom: 1px solid #eee; margin-bottom: 16px;'>
                                        <div style='flex: 1 1 auto;'><iac-number :value='props.item.amount' delimiter=' '  part='2' /> <ui-ref source='ref_unit' :value='props.item.unit' /> </div>
                                        <div><iac-number :value='props.item.price' delimiter=' '  part='2' />{{$settings._default_currency}}</div>
                                    </div>
                                </div>
                            </ui-list>
                        </main>
                    </div>
                `
            }).Modal({ size: "right", id })
        }
    },
    template: `
        <ui-data-grid class='top_filter' :dataSource='source':columns="columns" buttons action_name='contract'>
            <template slot='id' slot-scope='props'>
                <iac-entity-edit v-if='props.item.id' :value='{id: props.item.id, type: "request", prefix: " "}' />
            </template>
            <template slot='master_agreement_id' slot-scope='props'>
                <iac-entity-edit v-if='props.item.master_agreement_id' :value='props.item.master_agreement_id' />
            </template>
            <template slot='publicated_at' slot-scope='props'>
                <iac-date :date='props.item.publicated_at' full icon></iac-date>
            </template>
            <template slot='name' slot-scope='props'>
                <router-link style="word-wrap: anywhere;" :to='{path: "/procedure/"+props.item.id+"/core"}' :title="props.item.name || $t('request')" class='alert-link clamp_5'>
                    {{props.item.name || $t('request')}}
                </router-link>
                <a @click.prevent='openProducts(props.item.id)' href='#'>
                    <div style="word-wrap: anywhere;" :title="props.item.goods_count" class="clamp_5">{{props.item.goods_count}} {{$t('product',{count:props.item.goods_count})}}</div>
                </a>
            </template>
            <template slot='sum_start' slot-scope='props'>
                <span v-if='props.item.start_totalcost' style='white-space: nowrap;'><iac-number :value='props.item.start_totalcost' delimiter=' ' part='2' />&nbsp;{{props.item.currency}}</span>
            </template>
            <template slot='sum_win' slot-scope='props'>
                <span v-if='props.item.winner_totalcost' style='white-space: nowrap;'><iac-number :value='props.item.winner_totalcost' delimiter=' ' part='2' />&nbsp;{{props.item.currency}}</span>
            </template>
            <template slot='agree_at' slot-scope='props'>
                <iac-date :date='props.item.agree_at' full icon></iac-date>
            </template>
            <template slot='status' slot-scope='props'>
                <ui-ref source='status_agreement_request' :value='props.item.status'></ui-ref>
            </template>
        </ui-data-grid>
    `
}

let search = new Query({
    text: {
        label: "!search",
        has_del: true,
        group: '!top-',
        order: -1
    }
});

export default {
    data() {
        return {
            request_subscription_cash: [],
            user: Context.User,
            owner_request: new DataSource({
                query: new Query({
                    relation: {
                        value: "owner",
                        type: "hidden",
                        sync: false,
                    },
                    status: {
                        group: 'top-',
                        label: "!status",
                        type: "entity",
                        has_del: true,
                        dataSource: "ref_agreement_request_status_private",
                        attr: {
                            style: "max-width: 250px"
                        }
                    }
                }, [search]),
                store: new RefStore({
                    ref: 'ref_agreement_request',
                    context(item) {
                        item.actions = [
                            {
                                get label() { return "contract" },
                                btn_type: item.info ? "success" : "primary",
                                handler: async () => Router.push(`/workspace/contract/${item.contract_number ?? item.info?.contract_number}/core`),
                                hidden: e => ((item.info || !item.contract_number) && (!item.info || !item.info.contract_number))
                            }
                        ]
                        return item
                    },
                    injectQuery: async (params) => {
                        params.fields = ["id", "master_agreement_id", "publicated_at", "name", "goods_count", "start_totalcost", "winner_totalcost", "currency", "agree_at", "status", "contract_number"]
                        params.query = params.filters.text;
                        params.filters.text = undefined;
                        return params;
                    }
                }),
            }),
            participant_request: new DataSource({
                query: new Query({
                    relation: {
                        value: "participant",
                        type: "hidden",
                        sync: false,
                    },
                    status: {
                        group: 'top-',
                        label: "!status",
                        type: "entity",
                        has_del: true,
                        dataSource: "ref_agreement_request_status_participant",
                        attr: {
                            style: "max-width: 250px"
                        }
                    }
                }, [search]),
                store: new RefStore({
                    ref: 'ref_agreement_request',
                    context(item) {
                        item.actions = [
                            {
                                get label() { return "contract" },
                                btn_type: item.info ? "success" : "primary",
                                handler: async () => Router.push(`/workspace/contract/${item.contract_number ?? item.info?.contract_number}/core`),
                                hidden: e => ((item.info || !item.contract_number) && (!item.info || !item.info.contract_number))
                            }
                        ]
                        item.bindClass = [
                            (() => {
                                if (item.contract_number)
                                    return "ui-alert ui-alert-success";
                            })(),
                        ]
                        return item
                    },
                    injectQuery: async (params) => {
                        params.fields = ["id", "master_agreement_id", "publicated_at", "name", "goods_count", "start_totalcost", "winner_totalcost", "currency", "agree_at", "status", "contract_number"]
                        params.query = params.filters.text;
                        params.filters.text = undefined;
                        return params;
                    }
                }),
            })
        };
    },
    components: {
        Grid: Grid
    },
    computed: {
        search_query() {
            return search;
        }
    },
    template: `
        <iac-access :access='$policy.master_agreement_list_own || $policy.master_agreement_list_participant'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{ $t('home') }}</router-link></li>
                <li>{{$t('nav.ma_request')}}</li>
            </ol>
            <div class='title' style='align-items: flex-end;'>
                <h1>{{$t('nav.ma_request')}}</h1>
            </div>
            
        </iac-section>
        <iac-section>
            <ui-layout-tab :clear='true'>
                <ui-layout-group key='owner_request'  v-if='$settings.procedures && $settings.procedures.master_agreement && $policy.master_agreement_list_own' label="inbox">
                    <Grid :source='owner_request' />
                </ui-layout-group>
                <ui-layout-group key='participant_request'  v-if='$settings.procedures && $settings.procedures.master_agreement && $policy.master_agreement_list_participant' label="participant_request">
                    <Grid :source='participant_request' />
                </ui-layout-group>
            </ui-layout-tab>
        </iac-section>
        </iac-access>
    `
}