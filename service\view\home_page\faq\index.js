import { DataSource, RefStore } from '@iac/data';
import FaqItem from '../../workspace/sysop/settings/faq/FaqItem';
import Faq from '../../workspace/sysop/settings/faq/model';
import { Language } from '@iac/core'

export default {
  components: {
    FaqItem: FaqItem
  },
  data() {

    return {
      faqList: new DataSource({
        store: new RefStore({
          method: 'common_ref',
          ref: 'ref_faqs',
          params: {
            locale: Language._local_socket,
            show: true
          },
          context: (context) => {
            return new Faq(context);
          }
        })
      })
    }
  },
  created() {
    this.faqList.load();
  },
  template: `
  <iac-section class='section-block' v-if='faqList.items && faqList.items.length > 0'>
      <h2 class='section-title'>{{ $t('hp.faq') }}</h2>
      <ui-layout-group>
        <faq-item v-for='item in faqList.items' :key='item.id' :item='item' :editable="false"/>
      </ui-layout-group>
  </iac-section>
  `
}
