import Vue from "vue";
import { Language } from '@iac/core'
import { Config } from '@iac/kernel'

const Component = {
  props: ["model"],
  data() {
    return {}
  },
  methods: {
    getFileUrl(id) {
      return `${Config.api_server}/file/${id}`
    }
  },
  template: `
        <ui-data-view-item :model='model'>            
            <template slot='header'>
                <div>
                  №{{model.id}}
                </div>
                <div>
                  <span><iac-date :date='model.plan_date_from' withoutTime /></span><span><iac-date :date='model.plan_date_to' withoutTime /></span>
                </div>
            </template>
            
            <template slot='title'>
                <div>{{model.product_name}}</div>
            </template>

            <template slot='description'>
                <div class='clamp_2'>
                    <label>Создатель: </label>
                    <span :title='model.face_fullname'>{{ model.face_fullname }}</span>
                </div>
                <div class='clamp_2'>
                    <label>{{ $t('company') }}: </label>
                    <span :title='model.company_fulltittle'>{{ model.company_fulltittle }}</span>
                </div>
                <div class='clamp_2'>
                    <label>Контракт: </label>
                    <span :title='model.claim_ids.join(", ")'>{{ model.claim_ids.join(", ") }}</span>
                </div>
                <div class='clamp_2'>
                    <label>{{ $t('unit') }}: </label>
                    <span :title='model.unit'>{{ model.unit }}</span>
                </div>
            </template>
            <template slot='props'>
            <div>
              <label>{{$t('file')}}:</label>
              <div>
                  <div v-for="(file,index) in model.files">
                      <a :href='getFileUrl(file)'>file #{{ index }}</a>
                  </div>
              </div>
            </div>
            </template>
        
        </ui-data-view-item>
    `
}

Vue.component("template-issuing_schedule", Component)
