import { DataSource, RefStore, ArrayStore, Query, Entity } from '@iac/data'
import { Http, Action, Language } from '@iac/core'
import { Context, Settings, Config } from '@iac/kernel'

let pb = false;

export default {
    data: function () {
        let $this = this;
        return {
            dataSource: new DataSource({
                form: {
                    fields: {
                        user_id: {},
                        ip_address: {}
                    },
                    actions: {
                        create: {
                            label: "add",
                            policy: "system_user_local_ip_permissions_edit",
                            reload: true,
                        },
                        update: "system_user_local_ip_permissions_edit",
                        delete: {
                            policy: "system_user_local_ip_permissions_edit",
                            question: Language.t("question_do_you_really_want_to_delete_this_record"),
                        },
                    }
                },
                store: {
                    method: 'ref_user_local_ip_permissions',
                    ref: 'user_local_ip_permissions',
                    injectQuery: params => {
                        params.filters = params.filters || {};
                        params.filters.date_start_error = undefined;
                        params.filters.date_end_error = undefined;
                        return params;
                    },
                },
                columns: ["id", "user_id", {field: "ip_address",  style: 'width: 100%;'}]
            }),
        }
    },
    template: `
        <iac-access :access='$policy.system_user_local_ip_permissions_edit' class='page-purchase'>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('user_local_ip_permissions')}}</li>
                </ol>
                <div class='title'>
                    <h1 style='margin: 0;'>{{$t('user_local_ip_permissions')}}</h1>
                </div>
            </iac-section>
            <iac-section>
            <ui-layout-group>
                <ui-data-grid :dataSource='dataSource' :columns='dataSource.columns' :buttons="true"/>
            </ui-layout-group>

            </iac-section>
        </iac-access>
    `
}